package com.mi.info.intl.retail.fieldforce.domain.position.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.mi.info.intl.retail.api.front.position.IntlPositionApiService;
import com.mi.info.intl.retail.api.front.position.dto.IntlPositionDTO;
import com.mi.info.intl.retail.core.org.service.OrganizePlatformService;
import com.mi.info.intl.retail.fieldforce.domain.user.service.IntlRmsUserService;
import com.mi.info.intl.retail.fieldforce.infra.database.dataobject.position.IntlRmsPersonnelPosition;
import com.mi.info.intl.retail.fieldforce.infra.database.dataobject.user.IntlRmsUser;
import com.mi.info.intl.retail.fieldforce.infra.database.mapper.position.IntlRmsPersonnelPositionMapper;
import com.mi.info.intl.retail.intlretail.service.api.mq.dto.RmsDbContentRequest;
import com.mi.info.intl.retail.utils.JsonUtil;
import com.xiaomi.nr.eiam.admin.dto.provider.user.UserBaseInfo;

/**
 * IntlRmsPersonnelPositionServiceImpl 单元测试类
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("RMS人员职位服务实现类测试")
@MockitoSettings(strictness = Strictness.LENIENT)
class IntlRmsPersonnelPositionServiceImplTest {

    @Spy
    @InjectMocks
    private IntlRmsPersonnelPositionServiceImpl personnelPositionService;

    @Mock
    private IntlRmsPersonnelPositionMapper intlRmsPersonnelPositionMapper;

    @Mock
    private IntlRmsUserService intlRmsUserService;

    @Mock
    private OrganizePlatformService organizePlatformService;

    @Mock
    private IntlPositionApiService intlPositionApiService;

    private IntlRmsPersonnelPosition testPersonnelPosition;
    private IntlRmsUser testUser;
    private IntlPositionDTO testPositionDTO;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        testPersonnelPosition = createTestPersonnelPosition();
        testUser = createTestUser();
        testPositionDTO = createTestPositionDTO();
    }

    @Test
    @DisplayName("getValidByUserId - 成功获取有效职位信息")
    void getValidByUserId_Success() {
        // Given
        String userId = "test-user-001";
        List<IntlRmsPersonnelPosition> expectedPositions = Lists.newArrayList(testPersonnelPosition);

        when(intlRmsPersonnelPositionMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(expectedPositions);

        // When
        List<IntlRmsPersonnelPosition> result = personnelPositionService.getValidByUserId(userId);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(testPersonnelPosition.getUserId(), result.get(0).getUserId());
        verify(intlRmsPersonnelPositionMapper, times(1)).selectList(any());
    }

    @Test
    @DisplayName("getValidByUserId - 用户ID为空返回空列表")
    void getValidByUserId_EmptyUserId() {
        // Given
        String userId = "";

        // When
        List<IntlRmsPersonnelPosition> result = personnelPositionService.getValidByUserId(userId);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(intlRmsPersonnelPositionMapper, never()).selectList(any());
    }

    @Test
    @DisplayName("getValidByUserId - 用户ID为null返回空列表")
    void getValidByUserId_NullUserId() {
        // Given
        String userId = null;

        // When
        List<IntlRmsPersonnelPosition> result = personnelPositionService.getValidByUserId(userId);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(intlRmsPersonnelPositionMapper, never()).selectList(any());
    }

    @Test
    @DisplayName("getValidByUserId - 用户ID为空白字符串返回空列表")
    void getValidByUserId_BlankUserId() {
        // Given
        String userId = "   ";

        // When
        List<IntlRmsPersonnelPosition> result = personnelPositionService.getValidByUserId(userId);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(intlRmsPersonnelPositionMapper, never()).selectList(any());
    }

    @Test
    @DisplayName("getValidByUserId - 没有找到有效职位信息")
    void getValidByUserId_NoValidPositions() {
        // Given
        String userId = "test-user-001";
        when(intlRmsPersonnelPositionMapper.selectList(any(LambdaQueryWrapper.class)))
            .thenReturn(Collections.emptyList());

        // When
        List<IntlRmsPersonnelPosition> result = personnelPositionService.getValidByUserId(userId);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(intlRmsPersonnelPositionMapper, times(1)).selectList(any());
    }

    @Test
    @DisplayName("updatePersonnelPosition - 成功更新职位信息")
    void updatePersonnelPosition_Success() {
        // Given
        IntlRmsPersonnelPosition personnelPosition = createTestPersonnelPosition();
        personnelPosition.setStateCode(1); // 有效状态

        when(intlPositionApiService.getIntlPositionDTOByPositionId(anyString()))
            .thenReturn(Optional.of(testPositionDTO));
        when(intlRmsUserService.getByUserId(anyString())).thenReturn(Optional.of(testUser));
        when(organizePlatformService.updatePersonStore(any(UserBaseInfo.class), anyList())).thenReturn(true);

        // When
        personnelPositionService.updatePersonnelPosition(personnelPosition);

        // Then
        verify(intlPositionApiService, times(1)).getIntlPositionDTOByPositionId(anyString());
        verify(intlRmsUserService, times(1)).getByUserId(anyString());
        verify(organizePlatformService, times(1)).updatePersonStore(any(UserBaseInfo.class), anyList());
    }

    @Test
    @DisplayName("updatePersonnelPosition - 职位不存在时跳过更新")
    void updatePersonnelPosition_PositionNotExist() {
        // Given
        IntlRmsPersonnelPosition personnelPosition = createTestPersonnelPosition();

        when(intlPositionApiService.getIntlPositionDTOByPositionId(anyString())).thenReturn(Optional.empty());

        // When
        personnelPositionService.updatePersonnelPosition(personnelPosition);

        // Then
        verify(intlPositionApiService, times(1)).getIntlPositionDTOByPositionId(anyString());
        verify(intlRmsUserService, never()).getByUserId(anyString());
        verify(organizePlatformService, never()).updatePersonStore(any(), any());
    }

    @Test
    @DisplayName("updatePersonnelPosition - 用户不存在时跳过更新")
    void updatePersonnelPosition_UserNotExist() {
        // Given
        IntlRmsPersonnelPosition personnelPosition = createTestPersonnelPosition();

        when(intlPositionApiService.getIntlPositionDTOByPositionId(anyString()))
            .thenReturn(Optional.of(testPositionDTO));
        when(intlRmsUserService.getByUserId(anyString())).thenReturn(Optional.empty());

        // When
        personnelPositionService.updatePersonnelPosition(personnelPosition);

        // Then
        verify(intlPositionApiService, times(1)).getIntlPositionDTOByPositionId(anyString());
        verify(intlRmsUserService, times(1)).getByUserId(anyString());
        verify(organizePlatformService, never()).updatePersonStore(any(), any());
    }

    @Test
    @DisplayName("updatePersonnelPosition - 用户未绑定MiId时跳过更新")
    void updatePersonnelPosition_UserNotBindMiId() {
        // Given
        IntlRmsPersonnelPosition personnelPosition = createTestPersonnelPosition();
        IntlRmsUser userWithoutMiId = createTestUser();
        userWithoutMiId.setHasBindMiId(false);

        when(intlPositionApiService.getIntlPositionDTOByPositionId(anyString()))
            .thenReturn(Optional.of(testPositionDTO));
        when(intlRmsUserService.getByUserId(anyString())).thenReturn(Optional.of(userWithoutMiId));

        // When
        personnelPositionService.updatePersonnelPosition(personnelPosition);

        // Then
        verify(intlPositionApiService, times(1)).getIntlPositionDTOByPositionId(anyString());
        verify(intlRmsUserService, times(1)).getByUserId(anyString());
        verify(organizePlatformService, never()).updatePersonStore(any(), any());
    }

    @Test
    @DisplayName("updatePersonnelPosition - 状态码为0时设置权限状态为1")
    void updatePersonnelPosition_StateCodeZero() {
        // Given
        IntlRmsPersonnelPosition personnelPosition = createTestPersonnelPosition();
        personnelPosition.setStateCode(0); // 无效状态

        when(intlPositionApiService.getIntlPositionDTOByPositionId(anyString()))
            .thenReturn(Optional.of(testPositionDTO));
        when(intlRmsUserService.getByUserId(anyString())).thenReturn(Optional.of(testUser));
        when(organizePlatformService.updatePersonStore(any(UserBaseInfo.class), anyList())).thenReturn(true);

        // When
        personnelPositionService.updatePersonnelPosition(personnelPosition);

        // Then
        verify(organizePlatformService, times(1)).updatePersonStore(any(UserBaseInfo.class), anyList());
    }

    @Test
    @DisplayName("updatePersonnelPosition - 状态码为1时设置权限状态为0")
    void updatePersonnelPosition_StateCodeOne() {
        // Given
        IntlRmsPersonnelPosition personnelPosition = createTestPersonnelPosition();
        personnelPosition.setStateCode(1); // 有效状态

        when(intlPositionApiService.getIntlPositionDTOByPositionId(anyString()))
            .thenReturn(Optional.of(testPositionDTO));
        when(intlRmsUserService.getByUserId(anyString())).thenReturn(Optional.of(testUser));
        when(organizePlatformService.updatePersonStore(any(UserBaseInfo.class), anyList())).thenReturn(true);

        // When
        personnelPositionService.updatePersonnelPosition(personnelPosition);

        // Then
        verify(organizePlatformService, times(1)).updatePersonStore(any(UserBaseInfo.class), anyList());
    }

    @Test
    @DisplayName("updatePersonnelPosition - 验证UserBaseInfo构建正确")
    void updatePersonnelPosition_VerifyUserBaseInfoConstruction() {
        // Given
        IntlRmsPersonnelPosition personnelPosition = createTestPersonnelPosition();
        personnelPosition.setStateCode(1);

        when(intlPositionApiService.getIntlPositionDTOByPositionId(anyString()))
            .thenReturn(Optional.of(testPositionDTO));
        when(intlRmsUserService.getByUserId(anyString())).thenReturn(Optional.of(testUser));

        // 使用ArgumentCaptor来验证UserBaseInfo的构建
        when(organizePlatformService.updatePersonStore(any(UserBaseInfo.class), anyList())).thenReturn(true);

        // When
        personnelPositionService.updatePersonnelPosition(personnelPosition);

        // Then
        verify(organizePlatformService, times(1)).updatePersonStore(any(UserBaseInfo.class), anyList());
    }

    // 辅助方法
    private IntlRmsPersonnelPosition createTestPersonnelPosition() {
        IntlRmsPersonnelPosition position = new IntlRmsPersonnelPosition();
        position.setId(1);
        position.setAssociationId("assoc-001");
        position.setUserId("test-user-001");
        position.setUserName("Test User");
        position.setPositionId("position-001");
        position.setStoreName("Test Store");
        position.setStateCode(1);
        position.setCreatedAt(System.currentTimeMillis());
        position.setUpdatedAt(System.currentTimeMillis());
        return position;
    }

    private IntlRmsUser createTestUser() {
        IntlRmsUser user = new IntlRmsUser();
        user.setId(1);
        user.setRmsUserid("test-user-001");
        user.setEmail("<EMAIL>");
        user.setMiId(123456L);
        user.setEnglishName("Test User");
        user.setMobile("13800138000");
        user.setCountryShortcode("CN");
        user.setJobId(1001);
        user.setHasBindMiId(true);
        return user;
    }

    private IntlPositionDTO createTestPositionDTO() {
        IntlPositionDTO dto = new IntlPositionDTO();
        dto.setPositionId("position-001");
        dto.setStoreCodeNew("store-001");
        dto.setStoreId("Test Store");
        return dto;
    }

    private RmsDbContentRequest createTestRmsRequest() {
        RmsDbContentRequest request = new RmsDbContentRequest();
        IntlRmsPersonnelPosition position = createTestPersonnelPosition();
        // 确保associationId不为null
        position.setAssociationId("assoc-001");
        request.setContent(JsonUtil.bean2json(position));
        return request;
    }
}
