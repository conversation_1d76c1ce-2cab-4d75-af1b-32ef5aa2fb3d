package com.mi.info.intl.retail.user.app.impl;

import com.mi.info.intl.retail.exception.RetailRunTimeException;
import com.mi.info.intl.retail.user.domain.UserInfoManager;
import com.mi.info.intl.retail.user.infra.mapper.IntlRmsUserMapper;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class UserServiceImplTest {

    @InjectMocks
    private UserServiceImpl userService;

    @Mock
    private IntlRmsUserMapper intlRmsUserMapper;

    @Mock
    private UserInfoManager userInfoManager;

    @Test
    @DisplayName("getUserListByMiIds - miIds为空抛出RetailRunTimeException")
    void getUserListByMiIds_Empty_Throws() {
        List<Long> miIds = new ArrayList<>();
        RetailRunTimeException ex = assertThrows(RetailRunTimeException.class,
                () -> userService.getUserListByMiIds(miIds));
        assertEquals("miId is empty", ex.getMessage());
    }

    @Test
    @DisplayName("getUserListByMiIds - miIds超过500抛出RetailRunTimeException")
    void getUserListByMiIds_TooLarge_Throws() {
        List<Long> miIds = new ArrayList<>();
        for (int i = 0; i < 501; i++) {
            miIds.add((long) i + 1);
        }
        RetailRunTimeException ex = assertThrows(RetailRunTimeException.class,
                () -> userService.getUserListByMiIds(miIds));
        assertEquals("miIds size is more than 500", ex.getMessage());
    }
} 