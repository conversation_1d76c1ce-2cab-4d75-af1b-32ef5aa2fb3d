package com.mi.info.intl.retail.fieldforce.domain.user.aggregate;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mi.info.intl.retail.cooperation.task.config.InspectionConfig;
import com.mi.info.intl.retail.cooperation.task.infra.entity.IntlInspectionTaskConf;
import com.mi.info.intl.retail.cooperation.task.infra.mapper.read.IntlInspectionTaskConfReadMapper;
import com.mi.info.intl.retail.fieldforce.domain.user.enums.PositionTypeEnum;
import com.mi.info.intl.retail.fieldforce.domain.user.enums.StoreGradeEnum;
import com.mi.info.intl.retail.fieldforce.infra.database.dataobject.position.UserPositionStore;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.BusinessDataInputRequest;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.BusinessDataResponse;

class UserInfoManagerTest {

    @InjectMocks
    private UserInfoManager userInfoManager;

    @Mock
    private IntlInspectionTaskConfReadMapper intlInspectionTaskConfReadMapper;

    @Mock
    private InspectionConfig inspectionConfig;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testGetUserPositions_Success() {
        // 准备请求参数
        BusinessDataInputRequest request = new BusinessDataInputRequest();
        request.setRegion("CN");
        request.setTitleCodeList(Arrays.asList(1));
        request.setStoreGradeList(Arrays.asList(StoreGradeEnum.S.getCode().toString()));
        request.setPositionTypeList(Arrays.asList(PositionTypeEnum.SIS.getCode()));
        request.setIsPromotion(0); // 无促

        // Mock任务配置
        IntlInspectionTaskConf taskConf = new IntlInspectionTaskConf();
        taskConf.setId(1); // 设置ID用于缓存
        taskConf.setUserTitleCodes("1"); // 设置用户职位代码
        taskConf.setSStoreInspectionFrequency("1月2次");
        taskConf.setAStoreInspectionFrequency("1月1次");
        taskConf.setBStoreInspectionFrequency("2月1次");
        taskConf.setCStoreInspectionFrequency("3月1次");
        taskConf.setDStoreInspectionFrequency("6月1次");
        taskConf.setPosInspectionTime(10);
        taskConf.setFrontInspectionTime(9);
        Mockito.when(intlInspectionTaskConfReadMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Arrays.asList(taskConf));

        // Mock InspectionConfig.getMidGrayList
        Mockito.when(inspectionConfig.getMidGrayList()).thenReturn(new ArrayList<>());

        // Mock用户数据
        UserPositionStore userPosition = new UserPositionStore();
        userPosition.setUserId("U001");
        userPosition.setUserName("张三");
        userPosition.setMid("M001");
        userPosition.setAreaId("CN110105");
        userPosition.setUserTitle(1);
        userPosition.setUserTitleName("店长");
        userPosition.setTimezoneCode(92);
        userPosition.setLanguageCode("zh-CN");
        userPosition.setCountryCode("CN");
        userPosition.setPositionCode("P001");
        userPosition.setPositionName("SIS店长");
        userPosition.setPositionType(PositionTypeEnum.SIS.getCode());
        userPosition.setPositionTypeName(PositionTypeEnum.SIS.getName());
        userPosition.setIsPromotion(0);
        userPosition.setStoreId("S001");
        userPosition.setStoreName("小米之家新世界店");
        userPosition.setStoreGrade(StoreGradeEnum.S.getCode());
        userPosition.setStoreGradeName(StoreGradeEnum.S.getName());


        // 执行测试
        List<BusinessDataResponse> result = userInfoManager.getUserPositions(request);

        // 验证结果
        assertFalse(result.isEmpty());
        assertEquals(1, result.size());

        BusinessDataResponse response = result.get(0);
        assertEquals("M001", response.getMid());
        assertEquals("张三", response.getName());
        assertEquals("CN", response.getAreaId());

        assertNotNull(response.getPositionList());
        assertEquals(1, response.getPositionList().size());
        assertEquals("P001", response.getPositionList().get(0).getPositionCode());
        assertEquals("SIS店长", response.getPositionList().get(0).getPositionName());
        assertEquals(Integer.valueOf(1), response.getPositionList().get(0).getTitleCode());
        assertEquals("店长", response.getPositionList().get(0).getTitleName());

        // 验证ExtInfo中包含正确的信息
        String extInfo = response.getPositionList().get(0).getExtInfo();
        assertTrue(extInfo.contains("\"storeId\":\"S001\""));
        assertTrue(extInfo.contains("\"timezoneCode\":92"));
        assertTrue(extInfo.contains("\"languageCode\":\"zh-CN\""));
        assertTrue(extInfo.contains("\"frequency\":\"1月2次\""));
    }

    @Test
    void testGetUserPositions_CachingBehavior() {
        // 准备请求参数
        BusinessDataInputRequest request = new BusinessDataInputRequest();
        request.setRegion("CN");
        request.setTitleCodeList(Arrays.asList(1));
        request.setStoreGradeList(Arrays.asList(StoreGradeEnum.S.getCode().toString()));
        request.setPositionTypeList(Arrays.asList(PositionTypeEnum.SIS.getCode()));
        request.setIsPromotion(0);

        // Mock任务配置 - 使用相同的ID来测试缓存
        IntlInspectionTaskConf taskConf = new IntlInspectionTaskConf();
        taskConf.setId(1); // 相同的ID
        taskConf.setUserTitleCodes("1");
        taskConf.setSStoreInspectionFrequency("1月2次");
        taskConf.setAStoreInspectionFrequency("1月1次");
        taskConf.setBStoreInspectionFrequency("2月1次");
        taskConf.setCStoreInspectionFrequency("3月1次");
        taskConf.setDStoreInspectionFrequency("6月1次");
        taskConf.setPosInspectionTime(10);
        taskConf.setFrontInspectionTime(9);
        Mockito.when(intlInspectionTaskConfReadMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Arrays.asList(taskConf));

        // Mock InspectionConfig.getMidGrayList
        Mockito.when(inspectionConfig.getMidGrayList()).thenReturn(new ArrayList<>());

        // Mock两个不同的用户数据，但使用相同的任务配置
        UserPositionStore userPosition1 = new UserPositionStore();
        userPosition1.setUserId("U001");
        userPosition1.setUserName("张三");
        userPosition1.setMid("M001");
        userPosition1.setAreaId("CN110105");
        userPosition1.setUserTitle(1);
        userPosition1.setUserTitleName("店长");
        userPosition1.setTimezoneCode(92);
        userPosition1.setLanguageCode("zh-CN");
        userPosition1.setCountryCode("CN");
        userPosition1.setPositionCode("P001");
        userPosition1.setPositionName("SIS店长");
        userPosition1.setPositionType(PositionTypeEnum.SIS.getCode());
        userPosition1.setPositionTypeName(PositionTypeEnum.SIS.getName());
        userPosition1.setIsPromotion(0);
        userPosition1.setStoreId("S001");
        userPosition1.setStoreName("小米之家新世界店");
        userPosition1.setStoreGrade(StoreGradeEnum.S.getCode());
        userPosition1.setStoreGradeName(StoreGradeEnum.S.getName());

        UserPositionStore userPosition2 = new UserPositionStore();
        userPosition2.setUserId("U002");
        userPosition2.setUserName("李四");
        userPosition2.setMid("M002");
        userPosition2.setAreaId("CN110105");
        userPosition2.setUserTitle(1);
        userPosition2.setUserTitleName("店长");
        userPosition2.setTimezoneCode(92);
        userPosition2.setLanguageCode("zh-CN");
        userPosition2.setCountryCode("CN");
        userPosition2.setPositionCode("P002");
        userPosition2.setPositionName("SIS店长");
        userPosition2.setPositionType(PositionTypeEnum.SIS.getCode());
        userPosition2.setPositionTypeName(PositionTypeEnum.SIS.getName());
        userPosition2.setIsPromotion(0);
        userPosition2.setStoreId("S002");
        userPosition2.setStoreName("小米之家王府井店");
        userPosition2.setStoreGrade(StoreGradeEnum.A.getCode());
        userPosition2.setStoreGradeName(StoreGradeEnum.A.getName());

        // 执行测试
        List<BusinessDataResponse> result = userInfoManager.getUserPositions(request);

        // 验证结果 - 应该有两个用户
        assertFalse(result.isEmpty());
        assertEquals(2, result.size());

        // 验证两个用户都使用了正确的频次（来自缓存的映射）
        // 由于HashMap的无序性，不能依赖返回顺序，需要通过mid来区分用户
        BusinessDataResponse zhangSanResponse = result.stream()
                .filter(r -> "M001".equals(r.getMid()))
                .findFirst()
                .orElse(null);
        assertNotNull(zhangSanResponse);
        String zhangSanExtInfo = zhangSanResponse.getPositionList().get(0).getExtInfo();
        assertTrue(zhangSanExtInfo.contains("\"frequency\":\"1月2次\"")); // S级门店

        BusinessDataResponse liSiResponse = result.stream()
                .filter(r -> "M002".equals(r.getMid()))
                .findFirst()
                .orElse(null);
        assertNotNull(liSiResponse);
        String liSiExtInfo = liSiResponse.getPositionList().get(0).getExtInfo();
        assertTrue(liSiExtInfo.contains("\"frequency\":\"1月1次\"")); // A级门店
    }

/*    @Test
    void testGetUserPositions_FilterByPositionType() {
        // 准备请求参数 - 只查询ES类型的职位
        BusinessDataInputRequest request = new BusinessDataInputRequest();
        request.setRegion("CN");
        request.setTitleCodeList(Arrays.asList(1));
        request.setStoreGradeList(Arrays.asList(StoreGradeEnum.S.getCode().toString()));
        request.setPositionTypeList(Arrays.asList(PositionTypeEnum.ES.getCode()));

        // Mock任务配置
        IntlInspectionTaskConf taskConf = new IntlInspectionTaskConf();
        taskConf.setId(1);
        taskConf.setUserTitleCodes("1");
        taskConf.setSStoreInspectionFrequency("1月2次");
        when(intlInspectionTaskConfReadMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Arrays.asList(taskConf));

        // Mock两个不同类型的职位数据
        UserPositionStore sisPosition = new UserPositionStore();
        sisPosition.setUserId("U001");
        sisPosition.setPositionType(PositionTypeEnum.SIS.getCode());
        sisPosition.setStoreId("S001");
        sisPosition.setMid("M001");

        UserPositionStore esPosition = new UserPositionStore();
        esPosition.setUserId("U002");
        esPosition.setPositionType(PositionTypeEnum.ES.getCode());
        esPosition.setStoreId("S001");
        esPosition.setMid("M001");

        List<UserPositionStore> userPositions = Arrays.asList(sisPosition, esPosition);
        when(userPositionStoreReadMapper.getUserPositionStore(eq("CN"), eq(Arrays.asList(1)), 
            eq(Arrays.asList(StoreGradeEnum.S.getCode().toString())))).thenReturn(userPositions);

        // 执行测试
        List<BusinessDataResponse> result = userInfoManager.getUserPositions(request);

        // 验证结果 - 只返回ES类型的职位
        assertFalse(result.isEmpty());
        assertEquals(1, result.size());
    }*/
}
