package com.mi.info.intl.retail.user.infra.entity;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertTrue;


@DisplayName("IntlRmsUserTest领域对象测试")
public class IntlRmsUserTest {

    @Nested
    @DisplayName("IntlRmsUserTest领域对象测试setTest")
    class SetTest {
        @Test
        @DisplayName("IntlRmsUserTestset")
        void testSeting() throws InterruptedException {
            IntlRmsUser domain = new IntlRmsUser();
            domain.setBrands("");
            domain.setTestAccount(1);
            domain.setKeySellingProducts("");
            assertTrue(domain.getBrands().equals(""), "OK");
            assertTrue(domain.getTestAccount().equals(1));
            assertTrue(domain.getKeySellingProducts().equals(""), "OK");
        }
    }

}