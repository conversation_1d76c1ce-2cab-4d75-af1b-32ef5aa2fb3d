package com.mi.info.intl.retail.fieldforce.app;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mi.info.intl.retail.api.fieldforce.user.dto.IntlRmsUserDTO;
import com.mi.info.intl.retail.exception.BizException;
import com.mi.info.intl.retail.fieldforce.domain.user.aggregate.UserInfoManager;
import com.mi.info.intl.retail.fieldforce.domain.user.service.impl.IntlRmsUserServiceImpl;
import com.mi.info.intl.retail.fieldforce.infra.database.dataobject.user.IntlRmsUser;
import com.mi.info.intl.retail.fieldforce.infra.database.mapper.user.IntlRmsUserMapper;

/**
 * UserServiceImpl 单元测试类
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("RMS用户服务实现类测试")
class IntlRmsUserServiceImplTest {

    @Spy
    @InjectMocks
    private IntlRmsUserServiceImpl userService;

    @Mock
    private IntlRmsUserMapper intlRmsUserMapper;

    @Mock
    private UserInfoManager userInfoManager;

    private IntlRmsUser testIntlRmsUser;
    private IntlRmsUserDTO expectedDto;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        testIntlRmsUser = new IntlRmsUser();
        testIntlRmsUser.setId(1);
        testIntlRmsUser.setRmsUserid("RMS_001");
        testIntlRmsUser.setCode("USER_001");
        testIntlRmsUser.setDomainName("test.user");
        testIntlRmsUser.setEnglishName("Test User");
        testIntlRmsUser.setCountryId("CN");
        testIntlRmsUser.setCountryName("China");
        testIntlRmsUser.setJobId(1001);
        testIntlRmsUser.setJobName("Manager");
        testIntlRmsUser.setEmail("<EMAIL>");
        testIntlRmsUser.setMobile("13800138000");
        testIntlRmsUser.setMiId(123456789L);
        testIntlRmsUser.setManagerId("MGR_001");
        testIntlRmsUser.setManagerName("Manager Name");
        testIntlRmsUser.setVirtualMiId("VM_001");
        testIntlRmsUser.setLanguageId("zh");
        testIntlRmsUser.setLanguageName("Chinese");
        testIntlRmsUser.setIsDisabled(0);
        testIntlRmsUser.setHasBindMiId(true);

        expectedDto = IntlRmsUserDTO.builder()
                .rmsUserid("RMS_001")
                .code("USER_001")
                .domainName("test.user")
                .englishName("Test User")
                .countryId("CN")
                .countryName("China")
                .jobId(1001)
                .jobName("Manager")
                .email("<EMAIL>")
                .mobile("13800138000")
                .miId(123456789L)
                .managerId("MGR_001")
                .managerName("Manager Name")
                .virtualMiId("VM_001")
                .languageId("zh")
                .languageName("Chinese")
                .isDisabled(0)
                .build();
    }

    @Test
    @DisplayName("getUserListByMiIds - miIds为空抛出RetailRunTimeException")
    void getUserListByMiIds_Empty_Throws() {
        List<Long> miIds = new ArrayList<>();
        BizException ex = assertThrows(BizException.class,
            () -> userService.getUserListByMiIds(miIds));
        assertEquals("miId is empty", ex.getMessage());
    }

    @Test
    @DisplayName("getUserListByMiIds - miIds超过500抛出RetailRunTimeException")
    void getUserListByMiIds_TooLarge_Throws() {
        List<Long> miIds = new ArrayList<>();
        for (int i = 0; i < 501; i++) {
            miIds.add((long) i + 1);
        }
        BizException ex = assertThrows(BizException.class,
            () -> userService.getUserListByMiIds(miIds));
        assertEquals("miIds size is more than 500", ex.getMessage());
    }

    @Test
    @DisplayName("根据域名获取用户 - 成功场景")
    void testGetIntlRmsUserByDomainName_Success() {
        // Given
        String domainName = "test.user";
        when(intlRmsUserMapper.selectByDomainName(domainName)).thenReturn(testIntlRmsUser);

        // When
        IntlRmsUserDTO result = userService.getIntlRmsUserByDomainName(domainName);

        // Then
        assertNotNull(result);
        assertEquals(expectedDto.getRmsUserid(), result.getRmsUserid());
        assertEquals(expectedDto.getDomainName(), result.getDomainName());
        assertEquals(expectedDto.getEnglishName(), result.getEnglishName());
        verify(intlRmsUserMapper, times(1)).selectByDomainName(domainName);
    }

    @Test
    @DisplayName("根据域名获取用户 - 用户不存在")
    void testGetIntlRmsUserByDomainName_UserNotFound() {
        // Given
        String domainName = "nonexistent.user";
        when(intlRmsUserMapper.selectByDomainName(domainName)).thenReturn(null);

        // When
        IntlRmsUserDTO result = userService.getIntlRmsUserByDomainName(domainName);

        // Then
        assertNull(result);
        verify(intlRmsUserMapper, times(1)).selectByDomainName(domainName);
    }

    @Test
    @DisplayName("根据域名获取用户 - 空域名")
    void testGetIntlRmsUserByDomainName_EmptyDomainName() {
        // Given
        String domainName = "";

        // When
        IntlRmsUserDTO result = userService.getIntlRmsUserByDomainName(domainName);

        // Then
        assertNull(result);
        verify(intlRmsUserMapper, never()).selectOne(any());
    }

    @Test
    @DisplayName("根据域名获取用户 - null域名")
    void testGetIntlRmsUserByDomainName_NullDomainName() {
        // Given
        String domainName = null;

        // When
        IntlRmsUserDTO result = userService.getIntlRmsUserByDomainName(domainName);

        // Then
        assertNull(result);
        verify(intlRmsUserMapper, never()).selectOne(any());
    }

    @Test
    @DisplayName("根据域名获取用户 - 数据库异常")
    void testGetIntlRmsUserByDomainName_DatabaseException() {
        // Given
        String domainName = "test.user";
        when(intlRmsUserMapper.selectByDomainName(domainName))
            .thenThrow(new RuntimeException("Database error"));

        // When
        IntlRmsUserDTO result = userService.getIntlRmsUserByDomainName(domainName);

        // Then
        assertNull(result);
        verify(intlRmsUserMapper, times(1)).selectByDomainName(domainName);
    }

    @Test
    @DisplayName("根据MiId获取用户 - 成功场景")
    void testGetIntlRmsUserByMiId_Success() {
        // Given
        Long miId = 12345L;
        Page<IntlRmsUser> page = new Page<>(1, 1, false);
        page.setRecords(Arrays.asList(testIntlRmsUser));

        when(intlRmsUserMapper.selectPage(any(Page.class), any(LambdaQueryWrapper.class)))
                .thenReturn(page);

        // When
        Optional<IntlRmsUserDTO> result = userService.getIntlRmsUserByMiId(miId);

        // Then
        assertTrue(result.isPresent());
        assertEquals(expectedDto.getMiId(), result.get().getMiId());
        assertEquals(expectedDto.getDomainName(), result.get().getDomainName());
        verify(intlRmsUserMapper, times(1)).selectPage(any(Page.class), any(LambdaQueryWrapper.class));
    }

    @Test
    @DisplayName("根据MiId获取用户 - 用户不存在")
    void testGetIntlRmsUserByMiId_UserNotFound() {
        // Given
        Long miId = 99999L;
        Page<IntlRmsUser> page = new Page<>(1, 1, false);
        page.setRecords(Collections.emptyList());

        when(intlRmsUserMapper.selectPage(any(Page.class), any(LambdaQueryWrapper.class)))
                .thenReturn(page);

        // When
        Optional<IntlRmsUserDTO> result = userService.getIntlRmsUserByMiId(miId);

        // Then
        assertFalse(result.isPresent());
        verify(intlRmsUserMapper, times(1)).selectPage(any(Page.class), any(LambdaQueryWrapper.class));
    }

    @Test
    @DisplayName("根据MiId获取用户 - null MiId")
    void testGetIntlRmsUserByMiId_NullMiId() {
        // Given
        Long miId = null;

        // When
        Optional<IntlRmsUserDTO> result = userService.getIntlRmsUserByMiId(miId);

        // Then
        assertFalse(result.isPresent());
        verify(intlRmsUserMapper, never()).selectPage(any(), any());
    }

    // @Test
    // @DisplayName("根据MiId获取用户 - 数据库异常")
    void testGetIntlRmsUserByMiId_DatabaseException() {
        // Given
        Long miId = 12345L;
        when(intlRmsUserMapper.selectPage(any(Page.class), any(LambdaQueryWrapper.class)))
            .thenThrow(new RuntimeException("Database error"));

        // When
        assertThrows(RuntimeException.class, () -> userService.getIntlRmsUserByMiId(miId));

        // Then
        verify(intlRmsUserMapper, times(1)).selectPage(any(Page.class), any(LambdaQueryWrapper.class));
    }


    @Test
    @DisplayName("根据域名列表获取用户 - 空列表")
    void testGetIntlRmsUserByDomainNames_EmptyList() {
        // Given
        List<String> domainNames = Collections.emptyList();

        // When
        List<IntlRmsUserDTO> result = userService.getIntlRmsUserByDomainNames(domainNames);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(intlRmsUserMapper, never()).selectList(any());
    }

    @Test
    @DisplayName("根据域名列表获取用户 - null列表")
    void testGetIntlRmsUserByDomainNames_NullList() {
        // Given
        List<String> domainNames = null;

        // When
        List<IntlRmsUserDTO> result = userService.getIntlRmsUserByDomainNames(domainNames);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(intlRmsUserMapper, never()).selectList(any());
    }

    @Test
    @DisplayName("根据域名列表获取用户 - 用户不存在")
    void testGetIntlRmsUserByDomainNames_UsersNotFound() {
        // Given
        List<String> domainNames = Arrays.asList("user1.test", "user2.test");
        when(intlRmsUserMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // When
        List<IntlRmsUserDTO> result = userService.getIntlRmsUserByDomainNames(domainNames);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(intlRmsUserMapper, times(1)).selectList(any(LambdaQueryWrapper.class));
    }

    @Test
    @DisplayName("根据域名列表获取用户 - 数据库异常")
    void testGetIntlRmsUserByDomainNames_DatabaseException() {
        // Given
        List<String> domainNames = Arrays.asList("user1.test", "user2.test");
        when(intlRmsUserMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenThrow(new RuntimeException("Database error"));

        // When
        List<IntlRmsUserDTO> result = userService.getIntlRmsUserByDomainNames(domainNames);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(intlRmsUserMapper, times(1)).selectList(any(LambdaQueryWrapper.class));
    }

    @Test
    @DisplayName("根据域名列表获取用户 - 部分用户存在")
    void testGetIntlRmsUserByDomainNames_PartialUsersFound() {
        // Given
        List<String> domainNames = Arrays.asList("user1.test", "user2.test", "user3.test");
        List<IntlRmsUser> users = Arrays.asList(testIntlRmsUser); // 只返回一个用户

        when(intlRmsUserMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(users);

        // When
        List<IntlRmsUserDTO> result = userService.getIntlRmsUserByDomainNames(domainNames);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("test.user", result.get(0).getDomainName());
        verify(intlRmsUserMapper, times(1)).selectList(any(LambdaQueryWrapper.class));
    }

    @Test
    @DisplayName("根据域名列表获取用户 - null用户列表")
    void testGetIntlRmsUserByDomainNames_NullUsersList() {
        // Given
        List<String> domainNames = Arrays.asList("user1.test", "user2.test");
        when(intlRmsUserMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(null);

        // When
        List<IntlRmsUserDTO> result = userService.getIntlRmsUserByDomainNames(domainNames);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(intlRmsUserMapper, times(1)).selectList(any(LambdaQueryWrapper.class));
    }

    // 辅助方法
    private IntlRmsUser createTestUser(String domainName, String userId) {
        IntlRmsUser user = new IntlRmsUser();
        user.setRmsUserid(userId);
        user.setDomainName(domainName);
        user.setEnglishName("Test User " + userId);
        user.setCountryId("CN");
        user.setCountryName("China");
        user.setMiId(12345L);
        user.setIsDisabled(0);
        return user;
    }
}