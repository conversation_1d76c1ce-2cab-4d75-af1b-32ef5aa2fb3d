package com.mi.info.intl.retail.fieldforce.domain.user.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import org.mockito.ArgumentCaptor;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.List;
import java.util.Optional;

import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import com.google.common.collect.Lists;
import com.mi.info.intl.retail.api.cooperation.audit.AuditLogApiService;
import com.mi.info.intl.retail.api.front.dto.RmsStoreInfoDto;
import com.mi.info.intl.retail.api.front.position.IntlPositionApiService;
import com.mi.info.intl.retail.exception.BizException;
import com.mi.info.intl.retail.exception.ErrorCodes;
import com.mi.info.intl.retail.fieldforce.domain.position.service.IntlRmsPersonnelPositionService;
import com.mi.info.intl.retail.fieldforce.domain.user.service.IntlRmsUserService;
import com.mi.info.intl.retail.fieldforce.infra.database.dataobject.position.IntlRmsPersonnelPosition;
import com.mi.info.intl.retail.fieldforce.infra.database.dataobject.user.IntlRmsUser;
import com.mi.info.intl.retail.intlretail.service.api.proxy.RmsProxyService;
import com.mi.info.intl.retail.model.UserInfo;
import com.mi.info.intl.retail.utils.TokenUtil;
import com.mi.info.intl.retail.utils.UserInfoUtil;
import com.xiaomi.nr.eiam.admin.provider.ToolAdminProvider;
import com.xiaomi.youpin.infra.rpc.Result;

@ExtendWith(MockitoExtension.class)
public class UserAccountServiceImplTest {

    @InjectMocks
    private UserAccountServiceImpl userAccountServiceImpl;

    @Mock
    private IntlRmsUserService intlRmsUserService;

    @Mock
    private IntlRmsPersonnelPositionService intlRmsPersonnelPositionService;

    @Mock
    private IntlPositionApiService intlPositionApiService;

    @Mock
    private RmsProxyService rmsProxyService;

    @Mock
    private RocketMQTemplate rocketMQTemplate;

    @Mock
    private AuditLogApiService auditLogApiService;

    @Mock
    private ToolAdminProvider toolAdminProvider;

    private UserInfo mockUserInfo;

    @BeforeEach
    public void setUp() {
        mockUserInfo = getUserInfoMock();
        // 设置allowedPositionIdList字段
        ReflectionTestUtils.setField(userAccountServiceImpl, "allowedPositionIdList", Lists.newArrayList(246, 247, 248, 249, 250));
    }

    @Test
    @DisplayName("绑定MiID - 成功场景")
    public void bindMiId_Success() {
        // 准备数据
        String userId = "test-user-001";
        IntlRmsUser user = getRmsUserMock();

        // Mock依赖方法
        when(intlRmsUserService.getByUserId(userId)).thenReturn(Optional.of(user));
        when(intlRmsUserService.updateUser(any())).thenReturn(true);

        // 设置topic属性
        ReflectionTestUtils.setField(userAccountServiceImpl, "bindMiIdTopic", "test-topic");

        // 模拟其他异步调用
        when(intlRmsPersonnelPositionService.getValidByUserId(userId)).thenReturn(Lists.newArrayList());
        when(intlPositionApiService.getValidStoreByPositionIds(anyList())).thenReturn(Lists.newArrayList());
        when(rmsProxyService.postRequestWithTokenAndUserId(anyString(), anyString(), any(), anyString(), anyString()))
            .thenReturn("Success");
        when(auditLogApiService.recordUserOperation(any())).thenReturn(true);

        Result<Boolean> result = Result.success(true);
        when(toolAdminProvider.addUserForDataMigration(any())).thenReturn(result);

        // 执行测试
        boolean bindResult;
        try (MockedStatic<UserInfoUtil> userInfoUtilMock = Mockito.mockStatic(UserInfoUtil.class);
            MockedStatic<TokenUtil> tokenUtilMock = Mockito.mockStatic(TokenUtil.class)) {
            userInfoUtilMock.when(UserInfoUtil::getUserContext).thenReturn(mockUserInfo);
            tokenUtilMock.when(TokenUtil::getUserToken).thenReturn("test-token");
            bindResult = userAccountServiceImpl.bindMiId();
        }

        // 验证结果
        assertTrue(bindResult);
        verify(intlRmsUserService).getByUserId(userId);
        verify(intlRmsUserService).updateUser(any());
        verify(rocketMQTemplate).convertAndSend(eq("test-topic"), anyString());
    }

    @Test
    @DisplayName("绑定MiID - 用户不存在")
    public void bindMiId_UserNotExist() {
        // 准备数据
        String userId = "test-user-001";
        // Mock依赖方法
        when(intlRmsUserService.getByUserId(userId)).thenReturn(Optional.empty());

        // 执行测试并验证异常
        BizException exception;
        try (MockedStatic<UserInfoUtil> userInfoUtilMock = Mockito.mockStatic(UserInfoUtil.class);
            MockedStatic<TokenUtil> tokenUtilMock = Mockito.mockStatic(TokenUtil.class)) {
            userInfoUtilMock.when(UserInfoUtil::getUserContext).thenReturn(mockUserInfo);
            tokenUtilMock.when(TokenUtil::getUserToken).thenReturn("test-token");
            exception = assertThrows(BizException.class, () -> {
                userAccountServiceImpl.bindMiId();
            });
        }
        // 验证结果
        assertEquals(ErrorCodes.USER_NOT_EXIST.getCode(), exception.getCode());
        verify(intlRmsUserService).getByUserId(userId);
        verify(intlRmsUserService, never()).updateUser(any());
    }

    @Test
    @DisplayName("绑定MiID - 同步RMS失败")
    public void bindMiId_SyncToRmsFailed() {
        // 准备数据
        String userId = "test-user-001";
        IntlRmsUser user = getRmsUserMock();

        // Mock依赖方法
        when(intlRmsUserService.getByUserId(userId)).thenReturn(Optional.of(user));
        when(intlRmsUserService.updateUser(any())).thenReturn(true);

        // 设置topic属性
        ReflectionTestUtils.setField(userAccountServiceImpl, "bindMiIdTopic", "test-topic");

        // 模拟异步调用
        when(intlRmsPersonnelPositionService.getValidByUserId(userId)).thenReturn(Lists.newArrayList());
        when(intlPositionApiService.getValidStoreByPositionIds(anyList())).thenReturn(Lists.newArrayList());
        when(rmsProxyService.postRequestWithTokenAndUserId(anyString(), anyString(), any(), anyString(), anyString()))
            .thenThrow(new RuntimeException("Sync to RMS failed"));

        Result<Boolean> result = Result.success(true);
        when(toolAdminProvider.addUserForDataMigration(any())).thenReturn(result);

        // 执行测试并验证异常
        BizException exception;
        try (MockedStatic<UserInfoUtil> userInfoUtilMock = Mockito.mockStatic(UserInfoUtil.class);
            MockedStatic<TokenUtil> tokenUtilMock = Mockito.mockStatic(TokenUtil.class)) {
            userInfoUtilMock.when(UserInfoUtil::getUserContext).thenReturn(mockUserInfo);
            tokenUtilMock.when(TokenUtil::getUserToken).thenReturn("test-token");
            // 执行测试并验证异常
            exception = assertThrows(BizException.class, () -> {
                userAccountServiceImpl.bindMiId();
            });
        }
        assertEquals(ErrorCodes.SYNC_USER_TO_RMS_FAILED.getCode(), exception.getCode());
        // 验证结果
        verify(intlRmsUserService).getByUserId(userId);
        // 验证结果
        verify(intlRmsUserService).getByUserId(userId);
        ArgumentCaptor<IntlRmsUser> userCaptor = ArgumentCaptor.forClass(IntlRmsUser.class);
        verify(intlRmsUserService, times(2)).updateUser(userCaptor.capture());

        // 获取所有调用参数
        List<IntlRmsUser> capturedUsers = userCaptor.getAllValues();
        // 验证第二次调用更新的参数（绑定异常，更新hasBindMiId为false）
        IntlRmsUser secondUpdateUser = capturedUsers.get(1);
        assertFalse(secondUpdateUser.getHasBindMiId());
    }

    @Test
    @DisplayName("绑定MiID - 同步组织中台失败")
    public void bindMiId_SyncToOrganizationFailed() {
        // 准备数据
        String userId = "test-user-001";
        IntlRmsUser user = getRmsUserMock();

        // Mock依赖方法
        when(intlRmsUserService.getByUserId(userId)).thenReturn(Optional.of(user));
        when(intlRmsUserService.updateUser(any())).thenReturn(true);

        // 设置topic属性
        ReflectionTestUtils.setField(userAccountServiceImpl, "bindMiIdTopic", "test-topic");

        // 模拟异步调用
        when(intlRmsPersonnelPositionService.getValidByUserId(userId)).thenReturn(Lists.newArrayList());
        when(intlPositionApiService.getValidStoreByPositionIds(anyList())).thenReturn(Lists.newArrayList());
        when(rmsProxyService.postRequestWithTokenAndUserId(anyString(), anyString(), any(), anyString(), anyString()))
            .thenReturn("Success");

        // 使用Mockito直接模拟Result对象的行为
        Result<Boolean> result = Result.success(false);
        when(toolAdminProvider.addUserForDataMigration(any())).thenReturn(result);

        // 执行测试并验证异常
        BizException exception;
        try (MockedStatic<UserInfoUtil> userInfoUtilMock = Mockito.mockStatic(UserInfoUtil.class);
            MockedStatic<TokenUtil> tokenUtilMock = Mockito.mockStatic(TokenUtil.class)) {
            userInfoUtilMock.when(UserInfoUtil::getUserContext).thenReturn(mockUserInfo);
            tokenUtilMock.when(TokenUtil::getUserToken).thenReturn("test-token");
            // 执行测试并验证异常
            exception = assertThrows(BizException.class, () -> {
                userAccountServiceImpl.bindMiId();
            });
        }
        assertEquals(ErrorCodes.SYNC_USER_TO_ORGANIZATION_FAILED.getCode(), exception.getCode());
        // 验证结果
        verify(intlRmsUserService).getByUserId(userId);
        verify(intlRmsUserService, times(2)).updateUser(any());
    }

    @Test
    @DisplayName("绑定MiID - 存在职位和门店信息")
    public void bindMiId_WithPositionsAndStores() {
        // 准备数据
        String userId = "test-user-001";
        IntlRmsUser user = getRmsUserMock();

        // 模拟职位数据
        IntlRmsPersonnelPosition position = new IntlRmsPersonnelPosition();
        position.setPositionId("position-001");

        // 模拟门店数据
        RmsStoreInfoDto storeInfo = new RmsStoreInfoDto();
        storeInfo.setCrssCode("store-001");

        // Mock依赖方法
        when(intlRmsUserService.getByUserId(userId)).thenReturn(Optional.of(user));
        when(intlRmsUserService.updateUser(any())).thenReturn(true);
        when(intlRmsPersonnelPositionService.getValidByUserId(userId)).thenReturn(Lists.newArrayList(position));
        when(intlPositionApiService.getValidStoreByPositionIds(anyList())).thenReturn(Lists.newArrayList(storeInfo));
        when(rmsProxyService.postRequestWithTokenAndUserId(anyString(), anyString(), any(), anyString(), anyString()))
            .thenReturn("Success");

        // 使用Mockito直接模拟Result对象的行为
        Result<Boolean> result = Result.success(true);
        when(toolAdminProvider.addUserForDataMigration(any())).thenReturn(result);

        // 设置topic属性
        ReflectionTestUtils.setField(userAccountServiceImpl, "bindMiIdTopic", "test-topic");

        // 执行测试
        boolean bindResult;
        try (MockedStatic<UserInfoUtil> userInfoUtilMock = Mockito.mockStatic(UserInfoUtil.class);
            MockedStatic<TokenUtil> tokenUtilMock = Mockito.mockStatic(TokenUtil.class)) {
            userInfoUtilMock.when(UserInfoUtil::getUserContext).thenReturn(mockUserInfo);
            tokenUtilMock.when(TokenUtil::getUserToken).thenReturn("test-token");
            bindResult = userAccountServiceImpl.bindMiId();
        }

        // 验证结果
        assertTrue(bindResult);
        verify(intlRmsUserService).getByUserId(userId);
        verify(intlRmsUserService).updateUser(any());
        verify(rocketMQTemplate).convertAndSend(eq("test-topic"), anyString());
    }

    @Test
    @DisplayName("绑定MiID - 用户已绑定MiId")
    public void bindMiId_UserAlreadyBound() {
        // 准备数据
        String userId = "test-user-001";
        IntlRmsUser user = getRmsUserMock();
        user.setHasBindMiId(true);
        when(intlRmsUserService.getByUserId(userId)).thenReturn(Optional.of(user));

        try (MockedStatic<UserInfoUtil> userInfoUtilMock = Mockito.mockStatic(UserInfoUtil.class);
            MockedStatic<TokenUtil> tokenUtilMock = Mockito.mockStatic(TokenUtil.class)) {
            userInfoUtilMock.when(UserInfoUtil::getUserContext).thenReturn(mockUserInfo);
            tokenUtilMock.when(TokenUtil::getUserToken).thenReturn("test-token");
            BizException exception = assertThrows(BizException.class, () -> {
                userAccountServiceImpl.bindMiId();
            });
            assertEquals(ErrorCodes.USER_HAS_BIND_MI_ID.getCode(), exception.getCode());
        }
    }

    public UserInfo getUserInfoMock() {
        UserInfo mockUserInfo = new UserInfo();
        mockUserInfo.setMiID(123456789L);
        mockUserInfo.setRmsUserId("test-user-001");
        return mockUserInfo;
    }

    public IntlRmsUser getRmsUserMock() {
        IntlRmsUser mockUser = new IntlRmsUser();
        mockUser.setRmsUserid("test-user-001");
        mockUser.setDomainName("testUser");
        mockUser.setEmail("<EMAIL>");
        mockUser.setMobile("***********");
        mockUser.setEnglishName("Test User");
        mockUser.setCountryShortcode("CN");
        mockUser.setJobId(*********); // Promoter - 映射到246
        mockUser.setJobName("Promoter");
        mockUser.setMiId(123456L);
        return mockUser;
    }
}