<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.info.intl.retail.user.infra.mapper.read.UserPositionStoreReadMapper">
    <resultMap id="userPositionStoreMap" type="com.mi.info.intl.retail.user.infra.entity.UserPositionStore">
        <result column="rms_userid" property="userId"/>
        <result column="domain_name" property="userName"/>
        <result column="mi_id" property="mid"/>
        <result column="area_code" property="areaId"/>
        <result column="job_id" property="userTitle"/>
        <result column="job_name" property="userTitleName"/>
        <result column="timezone_code" property="timezoneCode"/>
        <result column="language_code" property="languageCode"/>
        <result column="country_code" property="countryCode"/>
        <result column="position_code" property="positionCode"/>
        <result column="position_name" property="positionName"/>
        <result column="position_type" property="positionType"/>
        <result column="position_type_name" property="positionTypeName"/>
        <result column="is_promotion_store" property="isPromotion"/>
        <result column="store_id" property="storeId"/>
        <result column="store_name" property="storeName"/>
        <result column="grade" property="storeGrade"/>
        <result column="grade_name" property="storeGradeName"/>
        <result column="store_code" property="storeCode"/>
    </resultMap>

    <select id="getUserPositionStore" resultMap="userPositionStoreMap">
        SELECT distinct
        u.rms_userid, u.domain_name, u.job_id, u.job_name, u.mi_id, u.timezone_code, u.language_code,
        ct.country_code, ct.area_code,
        p.code as position_code, p.name as position_name, p.type as position_type, p.type_name as position_type_name,
        p.is_promotion_store,
        s.store_id, s.name as store_name, s.grade, s.grade_name, s.code as store_code
        FROM intl_rms_user u
        JOIN intl_rms_personnel_position up ON u.rms_userid = up.user_id
        JOIN intl_rms_position p ON up.position_id = p.position_id
        JOIN intl_rms_store s ON p.store_id = s.store_id
        JOIN intl_rms_country_timezone ct ON u.country_id = ct.country_id
        <where>
            <!-- 用户必须是启用状态 -->
            AND u.is_disabled = 0

            <!-- 关联的岗位和店铺必须是有效状态 -->
            AND up.state_code = 0
            AND p.state_code = 0
            AND s.state_code = 0

            <!-- 限定岗位类型范围 -->
            AND p.type IN (100000002, 100000006, 100000003, 100000004, 100000005)

            <!-- 用户必须关联了有效的MI账号 -->
            AND u.mi_id != 0
            AND u.mi_id is not null

            <!-- 按国家筛选 -->
            <if test="country != null">
                AND ct.country_code = #{country}
            </if>

            <!-- 按职位ID列表筛选 -->
            <if test="titles != null and titles.size() > 0">
                AND u.job_id IN
                <foreach item="title" collection="titles" open="(" separator="," close=")">
                    #{title}
                </foreach>
            </if>

            <!-- 按店铺等级名称列表筛选 -->
            <if test="storeGrades != null and storeGrades.size() > 0">
                AND s.grade_name IN
                <foreach item="grade" collection="storeGrades" open="(" separator="," close=")">
                    #{grade}
                </foreach>
            </if>

            <!-- 按mid列表筛选 -->
            <if test="midList != null and midList.size() > 0">
                AND u.mi_id IN
                <foreach item="mid" collection="midList" open="(" separator="," close=")">
                    #{mid}
                </foreach>
            </if>

        </where>
    </select>

    <select id="getUserPositionStoreWithStoreFilter" resultMap="userPositionStoreMap">
        SELECT distinct
        u.rms_userid, u.domain_name, u.job_id, u.job_name, u.mi_id, u.timezone_code, u.language_code,
        ct.country_code, ct.area_code,
        p.code as position_code, p.name as position_name, p.type as position_type, p.type_name as position_type_name,
        p.is_promotion_store,
        s.store_id, s.name as store_name, s.grade, s.grade_name, s.code as store_code
        FROM intl_rms_user u
        JOIN intl_rms_personnel_position up ON u.rms_userid = up.user_id
        JOIN intl_rms_position p ON up.position_id = p.position_id
        JOIN intl_rms_store s ON p.store_id = s.store_id
        JOIN intl_rms_country_timezone ct ON u.country_id = ct.country_id
        <where>
            <!-- 用户必须是启用状态 -->
            AND u.is_disabled = 0

            <!-- 关联的岗位和店铺必须是有效状态 -->
            AND up.state_code = 0
            AND p.state_code = 0
            AND s.state_code = 0

            AND u.mi_id != 0
            AND u.mi_id is not null
            <!-- 按国家筛选 -->
            AND ct.country_code = #{r.region}
            <!-- 按门店类型筛选（storeType对应intl_rms_store.type_name） -->
            <if test="r.storeType != null and r.storeType.size() > 0">
                AND s.type_name IN
                <foreach item="i" collection="r.storeType" open="(" separator="," close=")">
                    #{i}
                </foreach>
            </if>
            <!-- 按门店编码筛选（storeCodes对应intl_rms_store.code） -->
            <if test="r.storeCodes != null and r.storeCodes.size() > 0">
                AND s.code IN
                <foreach item="storeCode" collection="r.storeCodes" open="(" separator="," close=")">
                    #{storeCode}
                </foreach>
            </if>
            <!-- 按职位ID列表筛选 -->
            <if test="r.titleCodeList != null and r.titleCodeList.size() > 0">
                AND u.job_id IN
                <foreach item="title" collection="r.titleCodeList" open="(" separator="," close=")">
                    #{title}
                </foreach>
            </if>
            <!-- 按PositionCode列表筛选 -->
            <if test="r.positionCodeList != null and r.positionCodeList.size() > 0">
                AND p.code IN
                <foreach item="positionCode" collection="r.positionCodeList" open="(" separator="," close=")">
                    #{positionCode}
                </foreach>
            </if>
            <!-- 按mid列表筛选 -->
            <if test="midList != null and midList.size() > 0">
                AND u.mi_id IN
                <foreach item="mid" collection="midList" open="(" separator="," close=")">
                    #{mid}
                </foreach>
            </if>
            <!-- 过滤掉covered = 0状态的阵地，使用NOT EXISTS确保逻辑正确 -->
            <if test="r.type != null and r.type == 3">
                AND NOT EXISTS (
                SELECT 1 FROM intl_store_material_status ms
                WHERE ms.business_code = p.code AND ms.covered = 0
                )
            </if>
        </where>
    </select>
</mapper>
