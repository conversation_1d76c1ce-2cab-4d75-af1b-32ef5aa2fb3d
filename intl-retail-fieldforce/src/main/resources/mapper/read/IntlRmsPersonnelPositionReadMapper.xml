<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.info.intl.retail.user.infra.mapper.read.IntlRmsPersonnelPositionReadMapper">

    <resultMap id="BaseResultMap" type="com.mi.info.intl.retail.user.infra.entity.IntlRmsPersonnelPosition">
        <id property="id" column="id"/>
        <result property="associationId" column="association_id"/>
        <result property="userId" column="user_id"/>
        <result property="userName" column="user_name"/>
        <result property="positionId" column="position_id"/>
        <result property="storeName" column="store_name"/>
        <result property="modifiedOn" column="modified_on"/>
        <result property="createdOn" column="created_on"/>
        <result property="stateCode" column="state_code"/>


    </resultMap>

    <sql id="Base_Column_List">
        id
        ,association_id,user_id,user_name,position_id,store_name,
        modified_on,created_on,state_code
    </sql>
</mapper>
