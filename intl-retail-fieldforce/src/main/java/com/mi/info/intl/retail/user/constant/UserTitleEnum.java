package com.mi.info.intl.retail.user.constant;

import lombok.Getter;
import java.util.HashMap;
import java.util.Map;

/**
 * 售点类型枚举
 */
@Getter
public enum UserTitleEnum {
    Promoter(500900001, "Promoter"),
    Temporary_Promoter(100000027, "Temporary Promoter"),
    Xiaomi_Store_Promoter(100000026, "Xiaomi Store Promoter"),
    Xiaomi_Store_Leader(100000022, "Xiaomi Store Leader"),
    Floor_Salesman(100000001, "Floor Salesman"),
    Supervisor(500900002, "Supervisor"),
    Supervisor_Without_Promoters(100000051, "Supervisor without Promoters"),
    Merchandiser(100000024, "Merchandiser");
    private final Integer code;
    private final String name;

    UserTitleEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    /** RMS职位与组织中台职位映射 */
    public static final Map<Integer, Integer> RMS_ORG_TITLE_MAP = new HashMap<>();
    /** 组织中台与RMS职位映射（反向：中台→RMS） */
    public static final Map<Integer, Integer> ORG_RMS_TITLE_MAP = new HashMap<>();

    static {
        // 在静态代码块中初始化Map
        RMS_ORG_TITLE_MAP.put(500900001, 246); // Promoter
        RMS_ORG_TITLE_MAP.put(100000027, 247); // Temporary Promoter
        RMS_ORG_TITLE_MAP.put(500900002, 248); // Supervisor
        RMS_ORG_TITLE_MAP.put(100000051, 249); // Supervisor without Promoters
        RMS_ORG_TITLE_MAP.put(100000024, 250); // Merchandiser
        RMS_ORG_TITLE_MAP.put(100000026, 261); // Xiaomi Store Promoter

        // 初始化反向Map（通过正向Map的entrySet转换）
        for (Map.Entry<Integer, Integer> entry : RMS_ORG_TITLE_MAP.entrySet()) {
            ORG_RMS_TITLE_MAP.put(entry.getValue(), entry.getKey());
        }
    }
}
