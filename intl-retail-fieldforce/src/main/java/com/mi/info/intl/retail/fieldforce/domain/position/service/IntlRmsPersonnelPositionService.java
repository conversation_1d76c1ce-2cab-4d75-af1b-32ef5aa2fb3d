package com.mi.info.intl.retail.fieldforce.domain.position.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.info.intl.retail.fieldforce.infra.database.dataobject.position.IntlRmsPersonnelPosition;

/**
 * <AUTHOR>
 * @date 2025/9/2 18:44
 */
public interface IntlRmsPersonnelPositionService extends IService<IntlRmsPersonnelPosition> {

    /**
     * 根据用户账号获取阵地-用户关联信息，只查询有效状态的记录
     *
     * @param userId 用户账号
     * @return 阵地-用户关联信息
     */
    List<IntlRmsPersonnelPosition> getValidByUserId(String userId);
}
