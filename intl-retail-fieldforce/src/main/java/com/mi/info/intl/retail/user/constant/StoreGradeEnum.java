package com.mi.info.intl.retail.user.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 门店巡检等级枚举
 *
 * <AUTHOR>
 * @date 2025/06/10
 */
@Getter
@AllArgsConstructor
public enum StoreGradeEnum {
    
    S(342320001, "S"),
    A(100000000, "A"),
    B(100000001, "B"),
    C(100000002, "C"),
    D(100000003, "D");

    /**
     * 代码
     */
    private final Integer code;
    
    /**
     * 描述
     */
    private final String name;

    /**
     * 根据code获取枚举
     */
    public static StoreGradeEnum getByCode(Integer code) {
        for (StoreGradeEnum level : values()) {
            if (level.getCode().equals(code)) {
                return level;
            }
        }
        return null;
    }
}
