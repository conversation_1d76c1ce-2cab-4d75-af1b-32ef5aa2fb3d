package com.mi.info.intl.retail.retailer.mapper;

import com.mi.info.intl.retail.intlretail.service.api.management.dto.RetailerQueryReq;
import com.mi.info.intl.retail.retailer.entity.IntlRmsRetailer;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【intl_rms_retailer(RMS零售商表)】的数据库操作Mapper
 * @createDate 2025-07-18 14:29:54
 * @Entity com.mi.info.intl.retail.retailer.entity.IntlRmsRetailer
 */
public interface IntlRmsRetailerMapper extends BaseMapper<IntlRmsRetailer> {

    List<IntlRmsRetailer> selectByRetailerCode(@Param("retailerCodeList") List<String> retailerCodeList);

    List<IntlRmsRetailer> queryRetailerList(@Param("param") RetailerQueryReq param);
}




