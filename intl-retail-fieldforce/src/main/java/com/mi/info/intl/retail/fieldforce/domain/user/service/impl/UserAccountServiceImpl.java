package com.mi.info.intl.retail.fieldforce.domain.user.service.impl;

import static com.mi.info.intl.retail.core.config.CommonThreadPoolConfig.FIELD_FORCE_HANDLE_THREAD_POOL;

import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.mi.info.intl.retail.api.cooperation.audit.AuditLogApiService;
import com.mi.info.intl.retail.api.cooperation.audit.dto.AuditLogDTO;
import com.mi.info.intl.retail.api.country.CountryTimeZoneApiService;
import com.mi.info.intl.retail.api.fieldforce.user.IntlRmsUserApiService;
import com.mi.info.intl.retail.api.fieldforce.user.UserAccountApiService;
import com.mi.info.intl.retail.api.fieldforce.user.dto.IntlRmsUserDTO;
import com.mi.info.intl.retail.api.front.dto.RmsStoreInfoDto;
import com.mi.info.intl.retail.api.front.position.IntlPositionApiService;
import com.mi.info.intl.retail.component.ComponentLocator;
import com.mi.info.intl.retail.constant.CommonConstant;
import com.mi.info.intl.retail.cooperation.domain.audit.enums.BusinessModule;
import com.mi.info.intl.retail.cooperation.domain.audit.enums.LogType;
import com.mi.info.intl.retail.cooperation.domain.audit.enums.OperType;
import com.mi.info.intl.retail.core.crypto.AESUtil;
import com.mi.info.intl.retail.exception.BizException;
import com.mi.info.intl.retail.exception.ErrorCodes;
import com.mi.info.intl.retail.fieldforce.domain.position.service.IntlRmsPersonnelPositionService;
import com.mi.info.intl.retail.fieldforce.domain.user.enums.RmsRequestEnum;
import com.mi.info.intl.retail.fieldforce.domain.user.enums.UserTitleEnum;
import com.mi.info.intl.retail.fieldforce.domain.user.service.IntlRmsUserService;
import com.mi.info.intl.retail.fieldforce.domain.user.service.UserAccountService;
import com.mi.info.intl.retail.fieldforce.infra.database.dataobject.position.IntlRmsPersonnelPosition;
import com.mi.info.intl.retail.fieldforce.infra.database.dataobject.user.IntlRmsUser;
import com.mi.info.intl.retail.intlretail.service.api.fieldforce.user.dto.RmsBindMiIdReqDTO;
import com.mi.info.intl.retail.intlretail.service.api.fieldforce.user.resp.RmsPropertyInfo;
import com.mi.info.intl.retail.intlretail.service.api.fieldforce.user.resp.UserInfoResponse;
import com.mi.info.intl.retail.intlretail.service.api.proxy.RmsProxyService;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.model.UserInfo;
import com.mi.info.intl.retail.utils.JsonUtil;
import com.mi.info.intl.retail.utils.TokenUtil;
import com.mi.info.intl.retail.utils.UserInfoUtil;
import com.xiaomi.cnzone.maindatacommon.enums.OrganTypeEnums;
import com.xiaomi.nr.eiam.admin.dto.provider.user.AddUserReqForDataMigrationRequest;
import com.xiaomi.nr.eiam.admin.dto.provider.user.UserBaseInfo;
import com.xiaomi.nr.eiam.admin.dto.provider.user.UserPositionInfo;
import com.xiaomi.nr.eiam.admin.provider.ToolAdminProvider;
import com.xiaomi.nr.eiam.common.enums.ManageChannelEnum;
import com.xiaomi.nr.eiam.common.enums.PrivilegeStateEnum;
import com.xiaomi.nr.eiam.common.enums.SceneEnum;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2025/9/1 17:29
 */
@Slf4j
@Service
public class UserAccountServiceImpl implements UserAccountService, UserAccountApiService {

    @Value("${intl-retail.rocketmq.replace-mi.topic}")
    private String bindMiIdTopic;

    @Value("${intl-retail.bindMiId.syncOrganization.allowedPositionIds:246,247,248,249,250}")
    private List<Integer> allowedPositionIdList;

    private static final Executor EXECUTOR = FIELD_FORCE_HANDLE_THREAD_POOL.getExecutor();

    @DubboReference(group = "${organization.dubbo-group}", interfaceClass = ToolAdminProvider.class, check = false,
        timeout = 5000)
    private ToolAdminProvider toolAdminProvider;

    @Resource
    private IntlRmsUserService intlRmsUserService;

    @Resource
    private IntlRmsPersonnelPositionService intlRmsPersonnelPositionService;

    @Resource
    private IntlPositionApiService intlPositionApiService;

    @Resource
    private RmsProxyService rmsProxyService;

    @Resource
    private RocketMQTemplate rocketMQTemplate;

    @Resource
    private CountryTimeZoneApiService countryTimeZoneApiService;

    @Resource
    private AuditLogApiService auditLogApiService;

    @Resource
    private IntlRmsUserApiService intlRmsUserApiService;

    @Value("${itsm.key}")
    private String itsmKey; // 16 位密钥

    @Override
    public UserInfoResponse getCommonUserInfo() {
        String json =
            "{\"user_info\":{\"role\":1,\"channel_dot_keys\":[\"全渠道\"],\"own\":0,\"default_app_retail_mode\":2,\"user_id\":0,"
                + "\"realname\":\"zhulin\",\"switchable_app_retail_mode\":[2],"
                + "\"mockable\":\"N\",\"area_id\":\"ID\",\"channels\":[0],\"switchable_shadow_list\":[2],\"avata\":\"\",\"nickname\":\"zhulin\","
                + "\"role_key\":\"\",\"role_name\":\"\","
                + "\"global_area_id\":\"ID\",\"channels_sign\":[\"total\"],\"suspension\":false,"
                + "\"rms_property_info\":{\"store_id\":\"faeb92f2-d2a9-ec11-9840-000d3a0850b9\"}},"
                + "\"phone_property_info\":{\"role\":1,\"channel_dot_keys\":[\"全渠道\"],\"channels\":[0],\"role_key\":\"\","
                + "\"role_name\":\"\",\"channels_sign\":[\"total\"]}}";
        return JsonUtil.json2bean(json, UserInfoResponse.class);
    }

    @Override
    public CommonApiResponse<UserInfoResponse> getUserInfoGlobal() {
        UserInfoResponse userInfoResponse = getCommonUserInfo();

        Optional<IntlRmsUserDTO> optional =
            intlRmsUserApiService.getUserInfoFromCache(UserInfoUtil.getUserContext().getRmsUserId());
        if (!optional.isPresent()) {
            throw new BizException(ErrorCodes.USER_NOT_EXIST, UserInfoUtil.getUserContext().getMiID());
        }
        IntlRmsUserDTO user = optional.get();
        RmsPropertyInfo rmsPropertyInfo = userInfoResponse.getUserInfo().getRmsPropertyInfo();
        rmsPropertyInfo.setUserId(user.getRmsUserid());
        rmsPropertyInfo.setJobValue(Long.valueOf(user.getJobId()));
        rmsPropertyInfo.setJobTitle(user.getJobName());
        rmsPropertyInfo.setEmployeeCode(user.getCode());
        rmsPropertyInfo.setEnglishName(user.getEnglishName());
        rmsPropertyInfo.setUserAccount(user.getDomainName());
        rmsPropertyInfo.setUserCountryId(user.getCountryId());
        rmsPropertyInfo.setUserIdEncrypted(AESUtil.encrypt(itsmKey, user.getDomainName()));
        countryTimeZoneApiService.getCountryInfoFromCache(user.getCountryShortcode()).ifPresent(t -> {
            rmsPropertyInfo.setUserCountryShortCode(t.getCountryCode());
            rmsPropertyInfo.setUserCountryCode(t.getCode());
            rmsPropertyInfo.setCurrencyCode(t.getCurrencyCode());
        });
        userInfoResponse.getUserInfo().setAreaId(user.getCountryShortcode());
        userInfoResponse.getUserInfo().setGlobalAreaId(user.getCountryShortcode());
        userInfoResponse.getUserInfo().setRealname(user.getEnglishName());
        userInfoResponse.getUserInfo().setNickname(user.getEnglishName());
        String miTalk = user.getMiId() != null ? String.valueOf(user.getMiId()) : "";
        // 如果绑定真实miId，则使用真实miId，否则使用虚拟miId
        boolean hasBindMiId = BooleanUtils.isTrue(user.getHasBindMiId());
        rmsPropertyInfo.setMiIdVirtual(hasBindMiId ? miTalk : user.getVirtualMiId());
        rmsPropertyInfo.setMiTalk(miTalk);
        rmsPropertyInfo.setHasBindMiId(hasBindMiId);
        rmsPropertyInfo.setStoreId(null);
        userInfoResponse.getUserInfo().setRmsPropertyInfo(rmsPropertyInfo);
        return CommonApiResponse.success(userInfoResponse);

    }

    @Override
    public boolean bindMiId() {
        UserInfo userInfo = UserInfoUtil.getUserContext();
        // 待绑定的miId
        Long miId = userInfo.getMiID();
        String rmsUserId = userInfo.getRmsUserId();
        String token = TokenUtil.getUserToken();
        Optional<IntlRmsUser> optional = intlRmsUserService.getByUserId(rmsUserId);
        IntlRmsUser intlRmsUser = optional.orElse(null);
        if (Objects.isNull(intlRmsUser)) {
            throw new BizException(ErrorCodes.USER_NOT_EXIST, rmsUserId);
        }
        // 校验是否已经绑定，若已绑定，则提示错误
        if (BooleanUtils.isTrue(intlRmsUser.getHasBindMiId())) {
            throw new BizException(ErrorCodes.USER_HAS_BIND_MI_ID, rmsUserId);
        }
        // 校验用户岗位是否在有效岗位范围内，若不是，则绑定失败
        checkPositionInValidRange(intlRmsUser);

        Long originalMiId = intlRmsUser.getMiId();
        // 更新miId、是否绑定miId字段 intl_rms_user
        intlRmsUser.setMiId(miId);
        intlRmsUser.setHasBindMiId(Boolean.TRUE);
        intlRmsUserService.updateUser(intlRmsUser);

        // 并行处理同步组织中台、RMS、发送消息
        CompletableFuture<Void> syncToOrgFuture =
            CompletableFuture.runAsync(() -> bindMiIdSyncToOrganization(intlRmsUser), EXECUTOR);

        CompletableFuture<Void> syncToRmsFuture =
            CompletableFuture.runAsync(() -> bindMiIdSyncToRms(intlRmsUser, token), EXECUTOR);

        CompletableFuture<Void> sendMessageFuture =
            CompletableFuture.runAsync(() -> bindMiIdSendMessage(originalMiId, intlRmsUser.getMiId()), EXECUTOR);

        CompletableFuture<Void> allFutures =
            CompletableFuture.allOf(syncToOrgFuture, syncToRmsFuture, sendMessageFuture);
        try {
            allFutures.join();
        } catch (CompletionException e) {
            log.error("并行同步用户miId异常，  userId:{}", intlRmsUser.getRmsUserid(), e);
            // 若异常，则允许重新绑定，将绑定状态改为false
            intlRmsUser.setHasBindMiId(Boolean.FALSE);
            intlRmsUserService.updateUser(intlRmsUser);
            Throwable cause = e.getCause();
            if (cause instanceof BizException) {
                throw (BizException) cause;
            }
            throw new BizException(ErrorCodes.SYS_ERROR, e.getMessage());
        }
        // 添加审计日志
        AuditLogDTO auditLog = new AuditLogDTO();
        auditLog.setLogType(LogType.USER_OPER.getCode());
        auditLog.setOperUserId(intlRmsUser.getRmsUserid());
        auditLog.setOperUserName(intlRmsUser.getEnglishName());
        auditLog.setOperType(OperType.UPDATE.getCode());
        auditLog.setOperTime(System.currentTimeMillis());
        auditLog.setBusinessModule(BusinessModule.USER_MANAGE.getCode());
        auditLog.setBusinessKey(intlRmsUser.getRmsUserid());
        auditLog.setBeforeData(String.valueOf(originalMiId));
        auditLog.setAfterData(String.valueOf(intlRmsUser.getMiId()));
        auditLogApiService.recordUserOperation(auditLog);

        return true;
    }

    @Override
    public String getUserMigrateData() {
        return "";
    }

    @Override
    public void bindMiIdForSyncRmsUser(IntlRmsUserDTO syncUser, Long originalMiId) {
        if (Objects.isNull(syncUser)) {
            return;
        }
        IntlRmsUser intlRmsUser = ComponentLocator.getConverter().convert(syncUser, IntlRmsUser.class);

        CompletableFuture<Void> syncToOrgFuture =
            CompletableFuture.runAsync(() -> bindMiIdSyncToOrganization(intlRmsUser), EXECUTOR);
        CompletableFuture<Void> sendMessageFuture =
            CompletableFuture.runAsync(() -> bindMiIdSendMessage(originalMiId, syncUser.getMiId()), EXECUTOR);

        CompletableFuture<Void> allFutures = CompletableFuture.allOf(syncToOrgFuture, sendMessageFuture);
        try {
            allFutures.join();
        } catch (CompletionException e) {
            // 只输出错误日志，不抛异常，优化保证同步用户逻辑。
            log.error("RMS同步用户，处理MiId绑定异常，userId:{}", intlRmsUser.getRmsUserid(), e);
        }
    }

    /**
     * 校验用户岗位是否在可同步至组织中台有效岗位范围内
     * 
     * @param user 岗位id
     */
    private void checkPositionInValidRange(IntlRmsUser user) {
        Integer orgPositionId = UserTitleEnum.RMS_ORG_TITLE_MAP.get(user.getJobId());
        if (!allowedPositionIdList.contains(orgPositionId)) {
            throw BizException.buildFormatException(ErrorCodes.POSITION_NOT_ALLOWED_SYNC_TO_ORGANIZATION,
                user.getJobName());
        }
    }

    /**
     * 发送绑定miId消息
     * 
     * @param originalMiId 原miId
     * @param newMiId 绑定的miId
     */
    private void bindMiIdSendMessage(Long originalMiId, Long newMiId) {
        HashMap<String, Object> message = Maps.newHashMap();
        message.put("originalMiId", originalMiId);
        message.put("newMiId", newMiId);
        log.info("sendBindMiIdMessage. params: {}", JsonUtil.bean2json(message));
        rocketMQTemplate.convertAndSend(bindMiIdTopic, JsonUtil.bean2json(message));
    }

    /**
     * 同步用户信息到RMS
     *
     * @param intlRmsUser 用户信息
     * @param token token
     */
    private void bindMiIdSyncToRms(IntlRmsUser intlRmsUser, String token) {
        RmsBindMiIdReqDTO rmsBindMiIdReq = new RmsBindMiIdReqDTO();
        rmsBindMiIdReq.setMiId(String.valueOf(intlRmsUser.getMiId()));
        rmsBindMiIdReq.setUserAccount(intlRmsUser.getDomainName());
        rmsBindMiIdReq.setEmail(intlRmsUser.getEmail());
        rmsBindMiIdReq.setMobile(intlRmsUser.getMobile());
        Stopwatch stopwatch = Stopwatch.createStarted();
        try {
            log.info("syncToRms begin. rmsUserId: {}", intlRmsUser.getRmsUserid());
            String result = rmsProxyService.postRequestWithTokenAndUserId(CommonConstant.RMS_API_PREFIX.SUBMIT_DATA,
                RmsRequestEnum.BIND_MI_ID.getType(), rmsBindMiIdReq, token, intlRmsUser.getRmsUserid());
            // 注：此处返回的result若为null，表示成功。
            log.info("syncToRms end. rmsUserId: {}, result: {}, cost: {}", intlRmsUser.getRmsUserid(), result,
                stopwatch.stop());
        } catch (Exception e) {
            log.error("syncToRms failed. rmsUserId:{}", intlRmsUser.getRmsUserid(), e);
            throw BizException.buildFormatException(ErrorCodes.SYNC_USER_TO_RMS_FAILED, e.getMessage());
        }

    }

    /**
     * 同步用户信息到组织中台
     *
     * @param intlRmsUser 用户信息
     */
    private void bindMiIdSyncToOrganization(IntlRmsUser intlRmsUser) {
        String rmsUserId = intlRmsUser.getRmsUserid();
        // 查询人员-阵地关系，有效的
        List<IntlRmsPersonnelPosition> personnelPositions = intlRmsPersonnelPositionService.getValidByUserId(rmsUserId);
        List<String> positionIds =
            personnelPositions.stream().map(IntlRmsPersonnelPosition::getPositionId).collect(Collectors.toList());
        // 根据positionIds 查询阵地-门店关系
        List<RmsStoreInfoDto> storeInfos = intlPositionApiService.getValidStoreByPositionIds(positionIds);
        // 生成用户职位信息，此处过虑掉了crssCode为空的用户岗位，接口要求不能为空
        List<UserPositionInfo> positionList =
            storeInfos.stream().filter(store -> StringUtils.isNotBlank(store.getCrssCode())).map(storeInfo -> {
                UserPositionInfo position = new UserPositionInfo();
                position.setAreaId(storeInfo.getCountryShortcode());
                position.setOrganType(OrganTypeEnums.CHANNEL_ORGAN_STORE.getCode());
                position.setPositionId(UserTitleEnum.RMS_ORG_TITLE_MAP.get(intlRmsUser.getJobId()));
                position.setOrganCode(storeInfo.getCrssCode());
                position.setPrivilegeState(PrivilegeStateEnum.EFFECTIVE.getCode()); // 有效
                position
                    .setManageChannelList(Lists.newArrayList(ManageChannelEnum.BUSINESS_DISCUSSION_STORE.getCode())); //
                // 渠道零售
                return position;
            }).collect(Collectors.toList());

        // 调用组织中台同步用户信息接口， 组装请求数据
        AddUserReqForDataMigrationRequest userMigrationRequest =
            buildAddUserReqForDataMigrationRequest(intlRmsUser, positionList);
        try {
            Stopwatch stopwatch = Stopwatch.createStarted();
            log.info("toolAdminProvider.addUserForDataMigration begin. rmsUserId: {}", rmsUserId);
            Result<Boolean> result = toolAdminProvider.addUserForDataMigration(userMigrationRequest);
            log.info("toolAdminProvider.addUserForDataMigration end. rmsUserId: {}, result: {}, cost: {}", rmsUserId,
                JsonUtil.bean2json(result), stopwatch.stop());
            Assert.isTrue(Objects.equals(GeneralCodes.OK.getCode(), result.getCode()), result.getMessage());
            Assert.isTrue(BooleanUtils.isTrue(result.getData()), String
                .format("toolAdminProvider.addUserForDataMigration failed. error message:%s", result.getMessage()));
        } catch (Exception e) {
            log.error("toolAdminProvider.addUserForDataMigration failed. userId:{}", rmsUserId, e);
            throw BizException.buildFormatException(ErrorCodes.SYNC_USER_TO_ORGANIZATION_FAILED, e.getMessage());
        }
    }

    /**
     * 组装同步用户信息请求数据
     * 
     * @param intlRmsUser 用户信息
     * @param positionList 职位信息
     * @return 请求组织中台数据
     */
    private AddUserReqForDataMigrationRequest buildAddUserReqForDataMigrationRequest(IntlRmsUser intlRmsUser,
        List<UserPositionInfo> positionList) {
        AddUserReqForDataMigrationRequest userMigrationRequest = new AddUserReqForDataMigrationRequest();
        userMigrationRequest.setScene(SceneEnum.NEW_RETAIL.getScene());
        UserBaseInfo userBaseInfo = new UserBaseInfo();
        userBaseInfo.setMiId(intlRmsUser.getMiId());
        userBaseInfo.setName(intlRmsUser.getEnglishName());
        userMigrationRequest.setUserBaseInfo(userBaseInfo);
        userMigrationRequest.setUserPositionInfoList(positionList);
        userMigrationRequest.setOperator(intlRmsUser.getDomainName());
        userMigrationRequest.setLogSource(CommonConstant.SYSTEM_SOURCE.RMS);
        return userMigrationRequest;
    }

}
