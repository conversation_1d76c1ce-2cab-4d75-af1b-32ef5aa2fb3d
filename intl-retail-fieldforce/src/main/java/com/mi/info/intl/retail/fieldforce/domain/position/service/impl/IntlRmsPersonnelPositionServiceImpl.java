package com.mi.info.intl.retail.fieldforce.domain.position.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.mi.info.intl.retail.api.fieldforce.personposition.service.IntlPersonnelPositionApiService;
import com.mi.info.intl.retail.api.fieldforce.user.enums.OrgChannelEnum;
import com.mi.info.intl.retail.api.front.position.IntlPositionApiService;
import com.mi.info.intl.retail.api.front.position.dto.IntlPositionDTO;
import com.mi.info.intl.retail.core.feishu.service.SendMessageService;
import com.mi.info.intl.retail.core.org.service.OrganizePlatformService;
import com.mi.info.intl.retail.enums.AlarmKey;
import com.mi.info.intl.retail.exception.BizException;
import com.mi.info.intl.retail.fieldforce.domain.position.service.IntlRmsPersonnelPositionService;
import com.mi.info.intl.retail.fieldforce.domain.user.enums.UserTitleEnum;
import com.mi.info.intl.retail.fieldforce.domain.user.service.IntlRmsUserService;
import com.mi.info.intl.retail.fieldforce.infra.database.dataobject.position.IntlRmsPersonnelPosition;
import com.mi.info.intl.retail.fieldforce.infra.database.dataobject.user.IntlRmsUser;
import com.mi.info.intl.retail.fieldforce.infra.database.mapper.position.IntlRmsPersonnelPositionMapper;
import com.mi.info.intl.retail.intlretail.service.api.mq.dto.RmsDbContentRequest;
import com.mi.info.intl.retail.utils.JsonUtil;
import com.xiaomi.cnzone.maindatacommon.enums.OrganTypeEnums;
import com.xiaomi.hera.trace.context.TraceIdUtil;
import com.xiaomi.nr.eiam.admin.dto.provider.user.UserBaseInfo;
import com.xiaomi.nr.eiam.admin.dto.provider.user.UserPositionInfo;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2025/9/2 18:45
 */
@Service
@Slf4j
public class IntlRmsPersonnelPositionServiceImpl
        extends ServiceImpl<IntlRmsPersonnelPositionMapper, IntlRmsPersonnelPosition>
        implements IntlRmsPersonnelPositionService, IntlPersonnelPositionApiService {

    @Resource
    private IntlRmsUserService intlRmsUserService;
    @Resource
    private OrganizePlatformService organizePlatformService;

    @Resource
    private IntlPositionApiService intlPositionApiService;

    @Resource
    private SendMessageService sendMessageService;

    @Override
    public List<IntlRmsPersonnelPosition> getValidByUserId(String userId) {
        if (StringUtils.isBlank(userId)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<IntlRmsPersonnelPosition> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(IntlRmsPersonnelPosition::getUserId, userId).eq(IntlRmsPersonnelPosition::getStateCode, 0);
        return list(wrapper);
    }

    /**
     * 批量处理人员职位信息
     *
     * @param requests 包含人员职位信息的请求列表
     * @return 处理后的字符串列表（虽然当前实现中没有返回值）
     */
    private void batchProcessPersonnelPositions1(List<RmsDbContentRequest> requests) {
        if (CollectionUtils.isEmpty(requests)) {
            return;
        }
        // 遍历请求列表，对每个请求执行添加人员职位操作
        List<IntlRmsPersonnelPosition> update = Lists.newArrayList();
        List<IntlRmsPersonnelPosition> insert = Lists.newArrayList();
        for (RmsDbContentRequest request : requests) {
            IntlRmsPersonnelPosition intlRmsPersonnelPosition =
                    JsonUtil.json2bean(JsonUtil.bean2json(request.getContent()), IntlRmsPersonnelPosition.class);
            if (null == intlRmsPersonnelPosition || intlRmsPersonnelPosition.getAssociationId() == null) {
                continue;
            }
            LambdaQueryWrapper<IntlRmsPersonnelPosition> lambdaQuery = Wrappers.lambdaQuery();
            lambdaQuery.eq(IntlRmsPersonnelPosition::getAssociationId, intlRmsPersonnelPosition.getAssociationId());
            IntlRmsPersonnelPosition havePersonnelPosition = getOne(lambdaQuery);
            if (havePersonnelPosition != null) {
                intlRmsPersonnelPosition.setId(havePersonnelPosition.getId());
                intlRmsPersonnelPosition.setUpdatedAt(System.currentTimeMillis());
                update.add(intlRmsPersonnelPosition);
                // 两者状态不一样时，发送请求
                if (!Objects.equals(havePersonnelPosition.getStateCode(), intlRmsPersonnelPosition.getStateCode())) {
                    updatePersonnelPosition(intlRmsPersonnelPosition);
                }
                log.info("addPersonnelPosition_update:{}", intlRmsPersonnelPosition);
            } else {
                intlRmsPersonnelPosition.setCreatedAt(System.currentTimeMillis());
                intlRmsPersonnelPosition.setUpdatedAt(System.currentTimeMillis());
                insert.add(intlRmsPersonnelPosition);
                updatePersonnelPosition(intlRmsPersonnelPosition);
                log.info("addPersonnelPosition_insert:{}", intlRmsPersonnelPosition);
            }
        }
        if (CollectionUtils.isNotEmpty(update)) {
            // 批量更新或者插入
            updateBatchById(update);
        }
        if (CollectionUtils.isNotEmpty(insert)) {
            saveBatch(insert);
        }

    }

    public void updatePersonnelPosition(IntlRmsPersonnelPosition personnelPosition) {
        Optional<IntlPositionDTO> optional =
                intlPositionApiService.getIntlPositionDTOByPositionId(personnelPosition.getPositionId());
        if (!optional.isPresent()) {
            log.warn("updatePersonnelPosition:positionId not exist, positionId={}", personnelPosition.getPositionId());
            return;
        }
        if (StringUtils.isBlank(optional.get().getStoreCodeNew())) {
            log.warn("can not find crsscode {}", personnelPosition.getPositionId());
            return;
        }
        Optional<IntlRmsUser> intlRmsUserOptional = intlRmsUserService.getByUserId(personnelPosition.getUserId()); // 这里是userId而不是mid！！！
        if (!intlRmsUserOptional.isPresent()) {
            log.warn("updatePersonnelPosition:userId not exist, userId={}", personnelPosition.getUserId());
            return;
        }
        IntlRmsUser intlRmsUser = intlRmsUserOptional.get();
        if (!Boolean.TRUE.equals(intlRmsUser.getHasBindMiId())) {
            log.warn("updatePersonnelPosition:user has not bind miId, userId={}", personnelPosition.getUserId());
            return;
        }
        UserBaseInfo baseInfo = getUserBaseInfo(intlRmsUser);

        IntlPositionDTO intlPositionDTO = optional.get();
        UserPositionInfo position = new UserPositionInfo();
        position.setAreaId(intlRmsUser.getCountryShortcode());
        position.setOrganType(OrganTypeEnums.CHANNEL_ORGAN_STORE.getCode());
        position.setPositionId(UserTitleEnum.RMS_ORG_TITLE_MAP.get(intlRmsUser.getJobId()));
        position.setOrganCode(intlPositionDTO.getStoreCodeNew());
        // 有效
        position.setPrivilegeState(Objects.equals(personnelPosition.getStateCode(), 1) ? 0 : 1);
        position.setManageChannelList(Lists.newArrayList(OrgChannelEnum.CHANNEL_RETAIL.getCode())); // 渠道零售

        // 调用dubbo接口
        organizePlatformService.updatePersonStore(baseInfo, Lists.newArrayList(position));
    }

    @NotNull
    private static UserBaseInfo getUserBaseInfo(IntlRmsUser intlRmsUser) {
        UserBaseInfo baseInfo = new UserBaseInfo();
        // 确保是绑定过真实mid的记录
        baseInfo.setMiId(intlRmsUser.getMiId());
        baseInfo.setName(intlRmsUser.getEnglishName());
        return baseInfo;
    }

    /**
     * 批量处理员工阵地关联数据
     *
     * @param requests 请求列表
     */
    @Override
    public void batchProcessPersonnelPositions(List<RmsDbContentRequest> requests) {
        // 批量处理员工阵地关联：先解析请求，按业务主键 associationId 批量预查询已存在数据，然后分组执行插入/更新，并按动作上报指标
        List<IntlRmsPersonnelPosition> insertList = new ArrayList<>();
        List<IntlRmsPersonnelPosition> updateList = new ArrayList<>();

        try {
            List<IntlRmsPersonnelPosition> parsed = requests.stream().map(
                            request -> JsonUtil.json2bean(JsonUtil.bean2json(request.getContent()), IntlRmsPersonnelPosition.class))
                    .collect(Collectors.toList());
            if (parsed.isEmpty())
                return;

            // 批量预查询
            Map<String, IntlRmsPersonnelPosition> existing = new HashMap<>();
            List<String> associationIds =
                    parsed.stream().map(IntlRmsPersonnelPosition::getAssociationId).collect(Collectors.toList());
            LambdaQueryWrapper<IntlRmsPersonnelPosition> w = Wrappers.lambdaQuery();
            w.in(IntlRmsPersonnelPosition::getAssociationId, associationIds);
            List<IntlRmsPersonnelPosition> list = list(w);
            if (list != null && !list.isEmpty()) {
                existing.putAll(list.stream().collect(Collectors.toMap(IntlRmsPersonnelPosition::getAssociationId,
                        personnelPosition -> personnelPosition)));
            }

            long now = System.currentTimeMillis();

            // 将数据分为更新与新增两类分别处理
            for (IntlRmsPersonnelPosition newPersonnelPosition : parsed) {
                IntlRmsPersonnelPosition old = existing.get(newPersonnelPosition.getAssociationId());
                if (old != null) {
                    // 判断传入数据和已存在数据是否一致，不一致则更新
                    if (!comparePersonnelPosition(old, newPersonnelPosition)) {
                        newPersonnelPosition.setId(old.getId());
                        newPersonnelPosition.setUpdatedAt(now);
                        updateList.add(newPersonnelPosition);
                    }
                    // 两者状态不一样时，发送请求
                    if (!Objects.equals(newPersonnelPosition.getStateCode(), old.getStateCode())) {
                        updatePersonnelPosition(newPersonnelPosition);
                    }
                } else {
                    newPersonnelPosition.setCreatedAt(now);
                    newPersonnelPosition.setUpdatedAt(now);
                    insertList.add(newPersonnelPosition);
                    updatePersonnelPosition(newPersonnelPosition);
                }
            }
            // 分别处理插入和更新操作
            if (CollectionUtils.isNotEmpty(insertList)) {
                saveBatch(insertList);
            }
            if (CollectionUtils.isNotEmpty(updateList)) {
                updateBatchById(updateList);
            }
        } catch (Exception e) {
            sendMessageService.sendGroupTextMessage("admin_group", AlarmKey.RMS_SYNC_DB.getEmail(),
                    " 插入/更新失败：" + e.getMessage() + ", traceId=" + TraceIdUtil.traceId());
            log.error("batchProcessPersonnelPositions failed, insetList:{}, updateList:{}, error:{}", insertList,
                    updateList, e.getMessage(), e);
            throw new BizException("batchProcessPersonnelPositions SyncRmsDbError", e);
        }
    }

    /**
     * 比较两个员工阵地关联对象的字段是否相等
     *
     * @param old 原始员工阵地关联对象
     * @param p 新员工阵地关联对象
     * @return 如果所有字段都相等返回true，否则返回false
     */
    private boolean comparePersonnelPosition(IntlRmsPersonnelPosition old, IntlRmsPersonnelPosition p) {
        if (ObjectUtils.isEmpty(old) || ObjectUtils.isEmpty(p)) {
            return true;
        }
        return com.alibaba.nacos.common.utils.Objects.equals(old.getUserId(), p.getUserId())
                && com.alibaba.nacos.common.utils.Objects.equals(old.getPositionId(), p.getPositionId())
                && com.alibaba.nacos.common.utils.Objects.equals(old.getUserName(), p.getUserName())
                && com.alibaba.nacos.common.utils.Objects.equals(old.getStoreName(), p.getStoreName())
                && com.alibaba.nacos.common.utils.Objects.equals(old.getStateCode(), p.getStateCode());
    }
}
