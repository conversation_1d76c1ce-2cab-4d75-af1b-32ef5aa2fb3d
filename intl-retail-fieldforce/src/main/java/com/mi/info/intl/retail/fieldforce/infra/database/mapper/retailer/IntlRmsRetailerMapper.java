package com.mi.info.intl.retail.fieldforce.infra.database.mapper.retailer;


import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.info.intl.retail.fieldforce.infra.database.dataobject.retailer.IntlRmsRetailer;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.SearchRetailerReqDto;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.SearchRetailerResponseDto;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.RetailerQueryReq;

/**
 * <AUTHOR>
 * @description 针对表【intl_rms_retailer(RMS零售商表)】的数据库操作Mapper
 * @createDate 2025-07-18 14:29:54
 * @Entity com.mi.info.intl.retail.infra.database.dataobject.retailer.IntlRmsRetailer
 */
public interface IntlRmsRetailerMapper extends BaseMapper<IntlRmsRetailer> {

    List<IntlRmsRetailer> selectByRetailerCode(@Param("retailerCodeList") List<String> retailerCodeList);

    List<SearchRetailerResponseDto> queryRetailerByNameOrCode(@Param("limit") int limit, @Param("params") SearchRetailerReqDto searchRetailerReqDto);

    List<IntlRmsRetailer> queryRetailerList(@Param("param") RetailerQueryReq param);

    List<IntlRmsRetailer> queryRetailerListByCodes(@Param("codes") List<String> codes);
}




