package com.mi.info.intl.retail.user.constant;

import lombok.Getter;

/**
 * 售点类型枚举
 */
@Getter
public enum PositionTypeEnum {
    SIS(100000002, "SIS"), ES(100000006, "ES"), DZ(100000004, "DZ"), DC(100000003, "DC"), POS(100000005, "POS");
    private final Integer code;
    private final String name;

    PositionTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 根据code获取枚举
     */
    public static PositionTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (PositionTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
