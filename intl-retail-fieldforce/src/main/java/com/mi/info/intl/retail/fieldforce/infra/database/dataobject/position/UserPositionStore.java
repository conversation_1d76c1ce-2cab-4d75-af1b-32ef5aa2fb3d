package com.mi.info.intl.retail.fieldforce.infra.database.dataobject.position;

import lombok.Data;

@Data
public class UserPositionStore {
    private String userId;
    private String userName;
    private String mid;
    private String areaId;
    private Integer userTitle;
    private String userTitleName;
    private Integer timezoneCode;
    private String languageCode;
    private String countryCode;
    /**
    *阵地代码
    */
    private String positionCode;
    private String positionName;
    private Integer positionType;
    private String positionTypeName;
    private Integer isPromotion;
    private String storeId;
    private String storeName;
    private Integer storeGrade;
    private String storeGradeName;
    private String storeCode;
    private Integer channelTypeId;
    private String channelTypeName;
}