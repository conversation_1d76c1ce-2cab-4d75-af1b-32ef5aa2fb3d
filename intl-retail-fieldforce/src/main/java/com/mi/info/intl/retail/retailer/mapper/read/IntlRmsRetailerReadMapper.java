package com.mi.info.intl.retail.retailer.mapper.read;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.info.intl.retail.constant.DBTypeRoutingKey;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.SearchRetailerReqDto;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.SearchRetailerResponseDto;
import com.mi.info.intl.retail.retailer.entity.IntlRmsRetailer;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> zhouhengxing1
 * @date : 2024年07月24日
 * @className : IntlRmsRetailerReadMapper
 * @description : 只读零售商Mapper
 */
@DS(DBTypeRoutingKey.XMSTORE_BE_READ)
@Mapper
public interface IntlRmsRetailerReadMapper extends BaseMapper<IntlRmsRetailer> {

    List<SearchRetailerResponseDto> queryRetailerByNameOrCode(@Param("limit") int limit, @Param("params") SearchRetailerReqDto searchRetailerReqDto);

}