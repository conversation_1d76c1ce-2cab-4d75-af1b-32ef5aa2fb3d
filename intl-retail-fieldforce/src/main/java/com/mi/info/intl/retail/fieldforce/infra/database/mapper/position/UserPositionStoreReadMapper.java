package com.mi.info.intl.retail.fieldforce.infra.database.mapper.position;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.info.intl.retail.fieldforce.infra.database.dataobject.position.UserPositionStore;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.BusinessDataInputRequest;

public interface UserPositionStoreReadMapper extends BaseMapper<UserPositionStore> {

    List<UserPositionStore> getUserPositionStore(@Param("country") String country,
                                                 @Param("titles") List<Integer> titles, @Param("storeGrades") List<String> storeGrades,
                                                 @Param("midList") List<Long> midList, @Param("retailerCodeList") List<String> retailerCodeList);

    List<UserPositionStore> getPromoterUserPositions();
    /**
     * 根据条件查询用户阵地门店信息（支持门店类型和门店编码筛选,阵地编码）
     */
    List<UserPositionStore> getUserPositionStoreWithStoreFilter(@Param("r") BusinessDataInputRequest userInfoRequestDTO,
           @Param("midList") List<Long> midList);
    /**
     * 查询促销员用户阵地信息（新的MySQL查询逻辑）
     *
     * @param region 国家短代码（必填）
     * @param titleCodeList 职位代码列表
     * @param retailerCodeList 零售商代码列表
     * @param positionTypeList 阵地类型列表
     * @param positionCodeList 阵地代码列表
     * @param channelTypeList 渠道类型列表
     * @param lastMiId 游标分页：上一页最后一条记录的mi_id
     * @param pageSize 每页数量（用于LIMIT）
     * @return 促销员用户阵地信息列表
     */
    List<UserPositionStore> getPromoterUserPositions(@Param("region") String region,
            @Param("titleCodeList") List<Integer> titleCodeList,
            @Param("retailerCodeList") List<String> retailerCodeList,
            @Param("positionTypeList") List<Integer> positionTypeList,
            @Param("positionCodeList") List<String> positionCodeList,
            @Param("channelTypeList") List<Integer> channelTypeList,
            @Param("lastMiId") String lastMiId,
            @Param("pageSize") Integer pageSize);

    /**
     * 查询指定用户的所有阵地信息（用于补全最后一个用户的完整数据）
     *
     * @param region 国家短代码（必填）
     * @param mid 用户MI ID
     * @param titleCodeList 职位代码列表
     * @param retailerCodeList 零售商代码列表
     * @param positionTypeList 阵地类型列表
     * @param positionCodeList 阵地代码列表
     * @param channelTypeList 渠道类型列表
     * @return 指定用户的阵地信息列表
     */
    List<UserPositionStore> getPromoterUserPositionsByMid(@Param("region") String region,
            @Param("mid") String mid,
            @Param("titleCodeList") List<Integer> titleCodeList,
            @Param("retailerCodeList") List<String> retailerCodeList,
            @Param("positionTypeList") List<Integer> positionTypeList,
            @Param("positionCodeList") List<String> positionCodeList,
            @Param("channelTypeList") List<Integer> channelTypeList);

    /**
     * 根据midList查询促销员用户阵地信息（当midList有值时使用）
     *
     * @param region 国家短代码（必填）
     * @param midList MI ID列表
     * @return 促销员用户阵地信息列表
     */
    List<UserPositionStore> getPromoterUserPositionsByMidList(@Param("region") String region,
            @Param("midList") List<String> midList);


}
