package com.mi.info.intl.retail.fieldforce.infra.database.dataobject.position;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

/**
 * 员工阵地关联表
 *
 * @TableName intl_rms_personnel_position
 */
@TableName(value = "intl_rms_personnel_position")
@Data
public class IntlRmsPersonnelPosition implements Serializable {
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 唯一标识
     */
    @TableField(value = "association_id")
    private String associationId;

    /**
     * 员工
     */
    @TableField(value = "user_id")
    private String userId;

    /**
     * 员工标签
     */
    @TableField(value = "user_name")
    private String userName;

    /**
     * 阵地
     */
    @TableField(value = "position_id")
    private String positionId;

    /**
     * 阵地标签
     */
    @TableField(value = "store_name")
    private String storeName;

    /**
     * 修改时间
     */
    @TableField(value = "modified_on")
    private Date modifiedOn;

    /**
     * 创建时间
     */
    @TableField(value = "created_on")
    private Date createdOn;

    /**
     * 是否可用
     */
    @TableField(value = "state_code")
    private Integer stateCode;

    private Long createdAt;
    private Long updatedAt;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
