package com.mi.info.intl.retail.user.infra.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * RMS用户表
 *
 * @TableName intl_rms_user
 */
@TableName(value = "intl_rms_user")
@Data
public class IntlRmsUser implements Serializable {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * RMS用户唯一标识
     */
    @TableField(value = "rms_userid")
    private String rmsUserid;

    /**
     * 用户代码
     */
    @TableField(value = "code")
    private String code;

    /**
     * 用户账号
     */
    @TableField(value = "domain_name")
    private String domainName;

    /**
     * 英文名称
     */
    @TableField(value = "english_name")
    private String englishName;

    /**
     * 国家
     */
    @TableField(value = "country_id")
    private String countryId;

    /**
     * 国家标签
     */
    @TableField(value = "country_name")
    private String countryName;

    /**
     * 用户职位
     */
    @TableField(value = "job_id")
    private Integer jobId;

    /**
     * 用户职位标签
     */
    @TableField(value = "job_name")
    private String jobName;

    /**
     * 邮箱
     */
    @TableField(value = "email")
    private String email;

    /**
     * 手机号
     */
    @TableField(value = "mobile")
    private String mobile;

    /**
     * 小米账号（Mi ID）
     */
    @TableField(value = "mi_id")
    private Long miId;

    /**
     * 直属上级
     */
    @TableField(value = "manager_id")
    private String managerId;

    /**
     * 直属上级标签
     */
    @TableField(value = "manager_name")
    private String managerName;

    /**
     * 虚拟MID
     */
    @TableField(value = "virtual_mi_id")
    private String virtualMiId;

    /**
     * 用户语言
     */
    @TableField(value = "language_id")
    private String languageId;

    /**
     * 用户语言标签
     */
    @TableField(value = "language_name")
    private String languageName;

    /**
     * 用户语言code
     */
    @TableField(value = "language_code")
    private String languageCode;

    /**
     * 是否禁用
     */
    @TableField(value = "is_disabled")
    private Integer isDisabled;

    /**
     * 创建时间
     */
    @TableField(value = "created_on")
    private Date createdOn;

    /**
     * 修改时间
     */
    @TableField(value = "modified_on")
    private Date modifiedOn;

    /**
     * 用户时区code
     */
    @TableField(value = "timezone_code")
    private Integer timezoneCode;

    /**
     * 用户时区名称
     */
    @TableField(value = "timezone_name")
    private String timezoneName;

    /**
     * 写入时间
     */
    @TableField(value = "created_at")
    private Long createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at")
    private Long updatedAt;

    /**
     * 国家缩写
     */
    @TableField(value = "country_shortcode")
    private String countryShortcode;


    /***
     * 品牌.可多选
     */
    @TableField(value = "brands")
    private String brands;


    /***
     * 品类.可多选
     */
    @TableField(value = "key_selling_products")
    private String keySellingProducts;


    /***
     * 是否未测试账号，1是，0和null为否
     */
    @TableField(value = "test_account")
    private Integer testAccount;


    /**
     *'0-表示未绑定，1-表示已绑定',
     */
    @TableField(value = "has_bind_mi_id")
    private Integer hasBindMiId;

    /**
     * '0-表示关闭，1-开启'
     */
    @TableField(value = "is_offline")
    private Integer isOffline;


    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}