package com.mi.info.intl.retail.fieldforce.infra.database.mapper.user;


import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.info.intl.retail.fieldforce.infra.database.dataobject.user.IntlRmsUser;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【intl_rms_user(用户表)】的数据库操作Mapper
* @createDate 2025-06-04 16:49:44
* @Entity generator.domain.IntlRmsUser
*/
@Mapper
public interface IntlRmsUserMapper extends BaseMapper<IntlRmsUser> {

    IntlRmsUser selectByEmail(String email);

    IntlRmsUser selectByDomainName(String domainName);

    /**
     * 根据miId查询用户信息
     *
     * @param miId 用户miId
     * @return 用户信息
     */
    IntlRmsUser selectByMiId(@Param("miId") Long miId);

    /**
     * 批量根据domainName查询用户
     */
    List<IntlRmsUser> selectByDomainNames(List<String> domainNames);

    /**
     * 分页查询用户列表
     *
     * @param countryCode
     * @param titleCodeList
     * @param offset
     * @param pageSize
     * @return
     */
    List<IntlRmsUser> queryUserListByPage(@Param("countryCode") String countryCode,
                                          @Param("titleCodeList") List<Integer> titleCodeList,
                                          @Param("offset") Integer offset,
                                          @Param("pageSize") Integer pageSize);

    /**
     * 根据rmsUserIds查询用户信息列表
     * @param rmsUserIdList rmsUserIds
     * @return 用户信息列表
     */
    List<IntlRmsUser> getByRmsUserIds(@Param("rmsUserIdList") List<String> rmsUserIdList);

    /**
     * 批量更新用户信息
     * @param intlRmsUsers 用户信息列表
     */
    void batchUpdateUsers(@Param("users") List<IntlRmsUser> intlRmsUsers);

    /**
     * 批量保存用户信息
     * @param intlRmsUsers 用户信息列表
     */
    void batchSaveUsers(@Param("users") List<IntlRmsUser> intlRmsUsers);
}




