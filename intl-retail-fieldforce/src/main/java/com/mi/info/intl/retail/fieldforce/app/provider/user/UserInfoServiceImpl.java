package com.mi.info.intl.retail.fieldforce.app.provider.user;

import com.mi.info.intl.retail.intlretail.service.api.user.UserInfoService;
import com.mi.info.intl.retail.intlretail.service.api.user.dto.QueryUserInfoListReq;
import com.mi.info.intl.retail.intlretail.service.api.user.dto.UserInfo;
import com.mi.info.intl.retail.user.constant.UserTitleEnum;
import com.mi.info.intl.retail.user.infra.entity.IntlRmsUser;
import com.mi.info.intl.retail.user.infra.mapper.IntlRmsUserMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@DubboService(group = "${center.dubbo.group:}", interfaceClass = UserInfoService.class)
public class UserInfoServiceImpl implements UserInfoService {

    @Resource
    IntlRmsUserMapper intlRmsUserMapper;
    @Override
    public List<UserInfo> queryUserInfoList(QueryUserInfoListReq request) {
        log.info("queryUserInfoList, request: {}", request);
        if (request == null || request.getRegion() == null || request.getRegion().isEmpty()) {
            return Collections.emptyList();
        }
        // 将组织中台的titleCode, 转为RMS的code, 比如246需转换为500900001, ORG_RMS_TITLE_MAP中不包含该titleCode则不转换
        List<Integer> titleCodeList = request.getTitleCodeList() == null ?
                Collections.emptyList() :
                request.getTitleCodeList().stream()
                        .map(titleCode -> UserTitleEnum.ORG_RMS_TITLE_MAP.getOrDefault(titleCode, titleCode))
                        .collect(Collectors.toList());

        // 分页查询用户信息
        Integer offset = (request.getPageIndex() - 1) * request.getPageSize();
        List<IntlRmsUser> userInfoList = intlRmsUserMapper.queryUserListByPage(request.getRegion(),
                titleCodeList, offset, request.getPageSize());
        // 转换数据
        if (userInfoList != null && !userInfoList.isEmpty()) {

            return userInfoList.stream().map(userInfo -> {
                UserInfo userInfoDto = new UserInfo();
                userInfoDto.setMiId(userInfo.getMiId());
                userInfoDto.setJobId(convertRmsToOrgTitleCode(userInfo.getJobId(), request.getTitleCodeList()));
                return userInfoDto;
            }).collect(Collectors.toList());
        }

        return Collections.emptyList();
    }

    /**
     * 将RMS的titleCode转换回组织中台的titleCode
     * 通过反向查找ORG_RMS_TITLE_MAP来实现转换
     *
     * @return 组织中台的titleCode，如果找不到映射则返回原值
     */
    private Integer convertRmsToOrgTitleCode(Integer titleCode, List<Integer> titleCodeList) {
        Integer titleCodeReturn;
        // 转换为组织中台code
        Integer titleCodeORG = UserTitleEnum.RMS_ORG_TITLE_MAP.getOrDefault(titleCode, titleCode);
        // 返回的code需要和传入的code一致
        if (titleCodeList == null) {
            titleCodeReturn = titleCodeORG;
        } else if (titleCodeList.contains(titleCode)) {
            titleCodeReturn = titleCode;
        } else {
            titleCodeReturn = titleCodeORG;
        }
        return titleCodeReturn;
    }
}
