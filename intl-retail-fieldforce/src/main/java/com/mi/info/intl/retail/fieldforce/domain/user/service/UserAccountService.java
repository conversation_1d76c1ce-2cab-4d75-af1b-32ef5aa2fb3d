package com.mi.info.intl.retail.fieldforce.domain.user.service;

import com.mi.info.intl.retail.intlretail.service.api.fieldforce.user.resp.UserInfoResponse;
import com.mi.info.intl.retail.model.CommonApiResponse;

/**
 * 
 * <AUTHOR>
 * @date 2025/9/1 17:19
 */
public interface UserAccountService {

    UserInfoResponse getCommonUserInfo();

    /**
     * 绑定miId
     * 
     * @return 绑定结果，true成功，false失败
     */
    boolean bindMiId();

    /**
     * 获取用户信息全局
     *
     * @return {@link CommonApiResponse }<{@link UserInfoResponse }>
     */
    CommonApiResponse<UserInfoResponse> getUserInfoGlobal();

    /**
     * 导出用户迁移数据
     * @return
     */
    String getUserMigrateData();
}
