package com.mi.info.intl.retail.fieldforce.domain.retailer.service.impl;


import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.mi.info.intl.retail.api.country.CountryTimeZoneApiService;
import com.mi.info.intl.retail.api.country.dto.CountryDTO;
import com.mi.info.intl.retail.api.fieldforce.retailer.IntlRetailerApiService;
import com.mi.info.intl.retail.api.fieldforce.retailer.dto.IntlRetailerDTO;
import com.mi.info.intl.retail.api.front.position.dto.IntlPositionDTO;
import com.mi.info.intl.retail.bean.BasePage;
import com.mi.info.intl.retail.exception.BizException;
import com.mi.info.intl.retail.exception.ErrorCodes;
import com.mi.info.intl.retail.fieldforce.domain.retailer.service.IntlRetailerService;
import com.mi.info.intl.retail.fieldforce.infra.database.dataobject.retailer.IntlRmsRetailer;
import com.mi.info.intl.retail.fieldforce.infra.database.mapper.retailer.IntlRmsRetailerMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * 零售商服务实现类
 *
 * <AUTHOR>
 * @date 2025/08/01
 */
@Slf4j
@Service
public class IntlRetailerServiceImpl extends ServiceImpl<IntlRmsRetailerMapper, IntlRmsRetailer>
        implements IntlRetailerService, IntlRetailerApiService {

    @Resource
    private CountryTimeZoneApiService countryTimeZoneApiService;

    /**
     * 按国家代码获取零售商列表
     *
     * @param country 国家
     * @return {@link List }<{@link IntlRetailerDTO }>
     */
    @Override
    public List<IntlRetailerDTO> getRetailerListByCountryCode(CountryDTO country) {
        if (country == null) {
            log.error("country not exist ");
            throw new BizException(ErrorCodes.COUNTRY_CODE_NOT_EXIST);
        }
        LambdaQueryWrapper<IntlRmsRetailer> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IntlRmsRetailer::getCountryName, country.getCountryName());
        List<IntlRmsRetailer> list = this.list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        return list.stream().filter(t -> StringUtils.isNotEmpty(t.getCountryShortcode())).map(this::getIntlRetailerDTO)
                .collect(Collectors.toList());

    }

    /**
     * 转换零售商DTO
     *
     * @param item 物品
     * @return {@link IntlRetailerDTO }
     */
    private IntlRetailerDTO getIntlRetailerDTO(IntlRmsRetailer item) {
        IntlRetailerDTO dto = new IntlRetailerDTO();
        dto.setRetailerId(item.getRetailerId());
        dto.setRetailerName(item.getRetailerName());
        dto.setRetailerCode(item.getName());
        dto.setCountryName(item.getCountryName());
        dto.setCountryCode(item.getCountryShortcode());
        Optional<CountryDTO> country = countryTimeZoneApiService.getCountryInfoFromCache(item.getCountryShortcode());
        country.ifPresent(countryDTO -> dto.setRegionCode(countryDTO.getAreaCode()));
        dto.setChannelType(item.getRetailerChannelTypeName());
        dto.setCreatedAt(item.getCreatedAt());
        dto.setUpdatedAt(item.getUpdatedAt());
        return dto;
    }

    /**
     * 转换零售商DTO
     *
     * @param item 物品
     * @return {@link IntlRetailerDTO }
     */
    private IntlRetailerDTO buildIntlRetailerDTO(IntlRmsRetailer item) {
        IntlRetailerDTO dto = new IntlRetailerDTO();
        dto.setRetailerId(item.getRetailerId());
        dto.setRetailerName(item.getRetailerName());
        dto.setRetailerCode(item.getName());
        dto.setCountryName(item.getCountryName());
        dto.setCountryCode(item.getCountryShortcode());
        dto.setChannelType(item.getRetailerChannelTypeName());
        dto.setRetailerChannelType(item.getRetailerChannelType());
        dto.setCreatedAt(item.getCreatedAt());
        dto.setUpdatedAt(item.getUpdatedAt());
        return dto;
    }

    /**
     * 按国家代码获取零售商列表
     *
     * @param countryCode 国家代码
     * @return {@link List }<{@link IntlRetailerDTO }>
     */
    @Override
    public List<IntlRetailerDTO> getRetailerListByCountryCode(String countryCode) {
        Optional<CountryDTO> countryInfo = countryTimeZoneApiService.getCountryInfoByCode(countryCode);
        if (!countryInfo.isPresent()) {
            log.error("country code {} not exist ", countryCode);
            throw new BizException(ErrorCodes.COUNTRY_CODE_NOT_EXIST, countryCode);
        }
        return getRetailerListByCountryCode(countryInfo.get());
    }

    /**
     * 通过零售商代码获取零售商
     *
     * @param dto DTO
     * @return {@link Optional }<{@link IntlRetailerDTO }>
     */
    @Override
    public Optional<IntlRetailerDTO> getRetailerByRetailerCode(IntlPositionDTO dto) {
        String retailerCode = dto.getRetailerCode();
        if (StringUtils.isEmpty(retailerCode)) {
            return Optional.empty();
        }
        LambdaQueryWrapper<IntlRmsRetailer> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(IntlRmsRetailer::getName, retailerCode)
                .select(IntlRmsRetailer::getId);
        IntlRmsRetailer intlRmsRetailer = getOne(wrapper);
        if (intlRmsRetailer == null) {
            return Optional.empty();
        }

        return Optional.of(new IntlRetailerDTO().setId(intlRmsRetailer.getId()));
    }

    @Override
    public IntlRetailerDTO getRetailerByRetailerId(String retailerId) {
        if (StringUtils.isEmpty(retailerId)) {
            return null;
        }
        LambdaQueryWrapper<IntlRmsRetailer> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(IntlRmsRetailer::getRetailerId, retailerId);
        IntlRmsRetailer intlRmsRetailer =  getOne(wrapper);
        if (intlRmsRetailer == null) {
            return null;
        }
        return  getIntlRetailerDTO(intlRmsRetailer);
    }

    /**
     *  由于只用到了RetailerName，与单条查询有区别，缺少了Area等字段
     * @param retailerIds
     * @return
     */
    @Override
    public Map<String, IntlRetailerDTO> batchGetRetailerByRetailerIds(List<String> retailerIds) {
        if (retailerIds == null || retailerIds.isEmpty()) {
            return Collections.emptyMap();
        }
        LambdaQueryWrapper<IntlRmsRetailer> wrapper = Wrappers.lambdaQuery();
        wrapper.in(IntlRmsRetailer::getRetailerId, retailerIds);
        List<IntlRmsRetailer> list = list(wrapper);
        if (list == null || list.isEmpty()) {
            return Collections.emptyMap();
        }
        Map<String, IntlRetailerDTO> map = new HashMap<>();
        list.forEach(item -> {
            IntlRetailerDTO dto = new IntlRetailerDTO();
            dto.setRetailerId(item.getRetailerId());
            dto.setRetailerName(item.getRetailerName());
            dto.setRetailerCode(item.getName());
            dto.setCountryName(item.getCountryName());
            dto.setCountryCode(item.getCountryShortcode());
            dto.setChannelType(item.getRetailerChannelTypeName());
            dto.setCreatedAt(item.getCreatedAt());
            dto.setUpdatedAt(item.getUpdatedAt());
            map.put(dto.getRetailerId(), dto);
        });
        return map;
    }

    /**
     * 逐页获取零售商列表
     *
     * @param req req
     * @return {@link List }<{@link IntlRetailerDTO }>
     */
    @Override
    public List<IntlRetailerDTO> getRetailerListByPage(BasePage req) {
        LambdaQueryWrapper<IntlRmsRetailer> wrapper = Wrappers.lambdaQuery();
        wrapper.isNotNull(IntlRmsRetailer::getCountryShortcode);
        Page<IntlRmsRetailer> page = new Page<>(req.getPageNum(), req.getPageSize());
        List<IntlRmsRetailer> list = page(page, wrapper).getRecords();
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        return list.stream().map(this::buildIntlRetailerDTO).collect(Collectors.toList());
    }

    /**
     * 获取增量零售商
     *
     * @return {@link List }<{@link IntlRetailerDTO }>
     */
    @Override
    public List<IntlRetailerDTO> getIncrementRetailer() {
        LambdaQueryWrapper<IntlRmsRetailer> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(IntlRmsRetailer::getIsNew, 1).isNotNull(IntlRmsRetailer::getCountryShortcode);
        return this.list(wrapper).stream().map(this::buildIntlRetailerDTO).collect(Collectors.toList());
    }

    @Override
    public void updateIncrementRetailer() {
        LambdaUpdateWrapper<IntlRmsRetailer> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(IntlRmsRetailer::getIsNew, 1);
        update(updateWrapper.set(IntlRmsRetailer::getIsNew, 0));
    }

    @Override
    public Map<String, IntlRetailerDTO> getRetailersByRetailerCodes(List<String> retailerCodes) {
        Map<String, IntlRetailerDTO> result = new HashMap<>();
        if (CollectionUtils.isEmpty(retailerCodes)) {
            return result;
        }
        // 分批处理
        int batchSize = 100;
        for (int i = 0; i < retailerCodes.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, retailerCodes.size());
            List<String> batchDTOs = retailerCodes.subList(i, endIndex);

            try {
                // 调用批量查询接口
                List<IntlRetailerDTO> batchResult = getRetailersByRetailerCodesBatch(batchDTOs);
                batchResult.forEach(retailer -> result.put(retailer.getRetailerCode(), retailer));
            } catch (Exception e) {
                log.error("Batch query retailer info failed for batch: {}", retailerCodes, e);
                throw e;
            }
        }

        return result;
    }

    private List<IntlRetailerDTO> getRetailersByRetailerCodesBatch(List<String> retailerCodes) {
        LambdaQueryWrapper<IntlRmsRetailer> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(IntlRmsRetailer::getName, retailerCodes)
                .select(IntlRmsRetailer::getId, IntlRmsRetailer::getName);
        List<IntlRmsRetailer> intlRmsRetailers = baseMapper.selectList(queryWrapper);
        return intlRmsRetailers.stream()
                .map(retailer -> new IntlRetailerDTO()
                        .setId(retailer.getId())
                        .setRetailerCode(retailer.getName()))
                .collect(Collectors.toList());
    }

}

