package com.mi.info.intl.retail.fieldforce.domain.user.enums;

import java.util.HashMap;
import java.util.Map;

import lombok.Getter;

/**
 * 用户岗位类型枚举
 */
@Getter
public enum UserTitleEnum {
    PROMOTER(500900001, "Promoter"),
    TEMPORARY_PROMOTER(100000027, "Temporary Promoter"),
    SUPERVISOR(500900002, "Supervisor"),
    SUPERVISOR_WITHOUT_PROMOTERS(100000051, "Supervisor without Promoters"),
    MERCHANDISER(100000024, "Merchandiser"),
    XIAOMI_STORE_PROMOTER(100000026, "Xiaomi Store Promoter"),
    TRAINER(100000007, "Trainer"),
    TRAINING_MANAGER(389500003, "Training Manager"),
    TRAINING_OPERATOR(100000017, "Training Operator");

    private final Integer code;
    private final String name;

    UserTitleEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    /** RMS职位与组织中台职位映射 */
    public static final Map<Integer, Integer> RMS_ORG_TITLE_MAP = new HashMap<>();
    /** 组织中台与RMS职位映射（反向：中台→RMS） */
    public static final Map<Integer, Integer> ORG_RMS_TITLE_MAP = new HashMap<>();

    static {
        // 在静态代码块中初始化Map
        RMS_ORG_TITLE_MAP.put(500900001, 246); // Promoter
        RMS_ORG_TITLE_MAP.put(100000027, 247); // Temporary Promoter
        RMS_ORG_TITLE_MAP.put(500900002, 248); // Supervisor
        RMS_ORG_TITLE_MAP.put(100000051, 249); // Supervisor without Promoters
        RMS_ORG_TITLE_MAP.put(100000024, 250); // Merchandiser
        RMS_ORG_TITLE_MAP.put(100000026, 261); // Xiaomi Store Promoter
        RMS_ORG_TITLE_MAP.put(100000007, 286); // Trainer
        RMS_ORG_TITLE_MAP.put(389500003, 287); // Training Manager
        RMS_ORG_TITLE_MAP.put(100000017, 288); // Training Operator


        // 初始化反向Map（通过正向Map的entrySet转换）
        for (Map.Entry<Integer, Integer> entry : RMS_ORG_TITLE_MAP.entrySet()) {
            ORG_RMS_TITLE_MAP.put(entry.getValue(), entry.getKey());
        }
    }
}
