package com.mi.info.intl.retail.fieldforce.domain.user.util;

import com.mi.info.intl.retail.api.fieldforce.user.UserInfoResponse;
import com.mi.info.intl.retail.api.fieldforce.user.dto.IntlRmsUserDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

@Mapper
public interface RmsUserToResponseMapper {
    RmsUserToResponseMapper INSTANCE = Mappers.getMapper(RmsUserToResponseMapper.class);

    @Mappings({
            @Mapping(source = "rmsUserid", target = "userId"),
            @Mapping(source = "jobName", target = "jobValue"),
            @Mapping(source = "jobId", target = "jobTitle"),
            @Mapping(source = "code", target = "employeeCode"),
            @Mapping(source = "englishName", target = "englishName"),
            @Mapping(source = "domainName", target = "userAccount"),
//           @Mapping(source = "rmsUserid", target = "userIdEncrypted"), //暂定
            @Mapping(source = "countryId", target = "userCountryId"),
//            @Mapping(source = "countryId", target = "userCountryCode"), //暂定，需要从int_rms_country_timezone查询code
            @Mapping(source = "countryShortcode", target = "userCountryShortCode"), //直接从DB取值
//            @Mapping(source = "countryId", target = "currencyCode"), //
            @Mapping(source = "virtualMiId", target = "miIDVirtual"),
            @Mapping(source = "miId", target = "miTalk"),  //
            @Mapping(source = "hasBindMiId", target = "hasBindMiId")
    })
    UserInfoResponse toUserInfoResponse(IntlRmsUserDTO intlRmsUserDTO);
}
