package com.mi.info.intl.retail.user.infra.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.info.intl.retail.user.infra.entity.IntlRmsUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【intl_rms_user(用户表)】的数据库操作Mapper
* @createDate 2025-06-04 16:49:44
* @Entity generator.domain.IntlRmsUser
*/
@Mapper
public interface IntlRmsUserMapper extends BaseMapper<IntlRmsUser> {

    IntlRmsUser selectByEmail(String email);

    IntlRmsUser selectByDomainName(String domainName);

    /**
     * 根据miId查询用户信息
     *
     * @param miId 用户miId
     * @return 用户信息
     */
    IntlRmsUser selectByMiId(@Param("miId") Long miId);
}




