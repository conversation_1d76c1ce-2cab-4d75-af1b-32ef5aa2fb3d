package com.mi.info.intl.retail.fieldforce.domain.user.service;

import java.util.List;
import java.util.Optional;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.info.intl.retail.dto.UserModifyDataDTO;
import com.mi.info.intl.retail.enums.UserAttributeModifyType;
import com.mi.info.intl.retail.fieldforce.infra.database.dataobject.user.IntlRmsUser;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.BusinessDataInputRequest;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.BusinessDataResponse;

/**
 * <AUTHOR> 董鑫儒
 * @Description
 * @Date 创建于 2025/6/3 10:49
 */
public interface IntlRmsUserService extends IService<IntlRmsUser> {

    /**
     * 获取用户信息
     *
     * @param email 电子邮件
     * @return {@link IntlRmsUser }
     */
    IntlRmsUser getUserInfo(String email);

    IntlRmsUser getUserInfoDomainName(String email);

    /**
     * 查询用户阵地信息列表数据
     */
    List<BusinessDataResponse> getUserPositions(BusinessDataInputRequest userInfoRequest);

    /**
     * 根据用户id查询用户信息
     *
     * @param userId 用户id
     * @return 用户信息Optional
     */
    Optional<IntlRmsUser> getByUserId(String userId);

    /**
     * 根据用户mid查询用户信息
     *
     * @param userId
     * @return
     */
    Optional<IntlRmsUser> getUserByMid(Long userId);

    /**
     * 获取用户迁移数据
     *
     * @return {@link String }
     */
    String getUserMigrateData();

    /**
     * 修改用户属性
     *
     * @param userId 用户身份
     * @param modifyType
     * @param modifyData
     * @return
     */
    boolean modifyUserAttribute(Long userId, UserAttributeModifyType modifyType, UserModifyDataDTO modifyData);

    /**
     * 更新用户信息，并清除用户缓存
     *
     * @param user 用户信息，具有主键值
     * @return 是否成功
     */
    boolean updateUser(IntlRmsUser user);


    /**
     * 根据条件获取促销员用户数据及关联的阵地信息(新的MySQL查询逻辑)
     * @param request
     * @return
     */
    List<BusinessDataResponse> getPromoterUserPositions(BusinessDataInputRequest request);

    /**
     * 使用商店过滤器获取用户位置
     *
     * @param userInfoRequestDTO 用户信息请求DTO DTO
     * @return {@link List }<{@link BusinessDataResponse }>
     */
    List<BusinessDataResponse> getUserPositionsWithStoreFilter(BusinessDataInputRequest userInfoRequestDTO);
}
