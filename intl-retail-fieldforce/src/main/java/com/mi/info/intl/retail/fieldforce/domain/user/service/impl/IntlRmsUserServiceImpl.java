package com.mi.info.intl.retail.fieldforce.domain.user.service.impl;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.SimpleColumnWidthStyleStrategy;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.mi.info.intl.retail.api.country.CountryTimeZoneApiService;
import com.mi.info.intl.retail.api.country.dto.CountryDTO;
import com.mi.info.intl.retail.api.fieldforce.user.IntlRmsUserApiService;
import com.mi.info.intl.retail.api.fieldforce.user.dto.IntlRmsUserDTO;
import com.mi.info.intl.retail.api.fieldforce.user.dto.IntlRmsUserNewDto;
import com.mi.info.intl.retail.api.fieldforce.user.dto.UserInfoDTO;
import com.mi.info.intl.retail.component.ComponentLocator;
import com.mi.info.intl.retail.constant.CommonConstant;
import com.mi.info.intl.retail.dto.UserModifyDataDTO;
import com.mi.info.intl.retail.dto.rms.input.UserLanguageInput;
import com.mi.info.intl.retail.dto.rms.input.UserModifyInput;
import com.mi.info.intl.retail.dto.rms.input.UserOfflineInput;
import com.mi.info.intl.retail.dto.rms.input.UserPhoneInput;
import com.mi.info.intl.retail.enums.SwitchEnum;
import com.mi.info.intl.retail.enums.UserAttributeModifyType;
import com.mi.info.intl.retail.exception.BizException;
import com.mi.info.intl.retail.exception.ErrorCodes;
import com.mi.info.intl.retail.fieldforce.domain.user.aggregate.UserInfoManager;
import com.mi.info.intl.retail.fieldforce.domain.user.model.EasyExcelTable;
import com.mi.info.intl.retail.fieldforce.domain.user.service.IntlRmsUserService;
import com.mi.info.intl.retail.fieldforce.infra.database.dataobject.user.IntlRmsUser;
import com.mi.info.intl.retail.fieldforce.infra.database.mapper.user.IntlRmsUserMapper;
import com.mi.info.intl.retail.intlretail.service.api.fds.FdsService;
import com.mi.info.intl.retail.intlretail.service.api.proxy.RmsProxyService;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.BusinessDataInputRequest;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.BusinessDataResponse;
import com.mi.info.intl.retail.utils.JsonUtil;
import com.mi.info.intl.retail.utils.redis.RedisClient;
import com.mi.info.intl.retail.utils.redis.RedisKey;
import com.mi.info.intl.retail.utils.redis.RedisKeyEnum;
import com.xiaomi.cnzone.commons.utils.DateUtils;
import com.xiaomi.cnzone.commons.utils.JacksonUtil;

import cn.hutool.core.lang.Tuple;
import cn.hutool.core.thread.ThreadUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class IntlRmsUserServiceImpl extends ServiceImpl<IntlRmsUserMapper, IntlRmsUser>
    implements IntlRmsUserService, IntlRmsUserApiService {

    private static final String TYPE_UPDATE_LANGUAGE = "UpdateUserLanguage";
    private static final String TYPE_UPDATE_OFFLINE = "OfflineSetting";
    private static final String TYPE_UPDATE_USER_INFO = "UpdateUserInfo";

    @Autowired
    private UserInfoManager userInfoManager;
    @Resource
    private RedisClient redisClient;
    @Autowired
    private IntlRmsUserMapper intlRmsUserMapper;
    @Resource
    private FdsService fdsService;
    @Resource
    private CountryTimeZoneApiService countryTimeZoneApiService;

    @Resource
    private RmsProxyService rmsProxyService;

    /**
     * 从Redis Hash中获取用户信息
     */
    @Override
    public Optional<IntlRmsUserDTO> getUserInfoFromCache(String rmsUserId) {
        RedisKey redisKey = RedisKeyEnum.INTL_USER_INFO_CACHE.get(rmsUserId);
        // 仅获取需要的 rmsId 对应的 miId
        IntlRmsUserDTO intlRmsUserDTO = redisClient.getObj(redisKey, IntlRmsUserDTO.class);
        if (intlRmsUserDTO != null) {
            return Optional.of(intlRmsUserDTO);
        }

        IntlRmsUserDTO intlRmsUser = getByRmsUserId(rmsUserId);
        redisClient.set(redisKey, JsonUtil.bean2json(intlRmsUser));
        return Optional.ofNullable(intlRmsUser);
    }

    @Override
    public IntlRmsUser getUserInfo(String email) {
        if (StringUtils.isEmpty(email)) {
            return new IntlRmsUser();
        }
        return this.baseMapper.selectByEmail(email);
    }

    @Override
    public IntlRmsUser getUserInfoDomainName(String domainName) {
        return this.baseMapper.selectByDomainName(domainName);
    }

    @Override
    public List<BusinessDataResponse> getUserPositions(BusinessDataInputRequest userInfoRequest) {

        return userInfoManager.getUserPositions(userInfoRequest);
    }
    /**
     * 根据条件获取促销员用户数据及关联的阵地信息(新的MySQL查询逻辑)
     * @param request
     * @return
     */
    @Override
    public List<BusinessDataResponse> getPromoterUserPositions(BusinessDataInputRequest request) {
        return userInfoManager.getPromoterUserPositions(request);
    }

    @Override
    public Optional<IntlRmsUserNewDto> getUserByMiId(Long miId) {
        if (ObjectUtils.isEmpty(miId)) {
            log.error("getUserByMiId param:mid is null");
            return Optional.empty();
        }
        LambdaQueryWrapper<IntlRmsUser> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(IntlRmsUser::getMiId, miId);
        IntlRmsUser intlRmsUser = getOne(queryWrapper);
        if (ObjectUtils.isEmpty(intlRmsUser)) {
            log.error("getUserByMiId can not find user by miid:{}", miId);
            return Optional.empty();
        }
        IntlRmsUserNewDto intlRmsUserNewDto = new IntlRmsUserNewDto();
        ComponentLocator.getConverter().convert(intlRmsUser, intlRmsUserNewDto);
        return Optional.of(intlRmsUserNewDto);
    }

    /**
     * @param miIds
     * @return
     */
    @Override
    public Optional<List<IntlRmsUserNewDto>> getUserListByMiIds(List<Long> miIds) {
        if (CollectionUtils.isEmpty(miIds)) {
            log.error("getUserListByMiIds param:miIds is empty");
            throw new BizException("miId is empty");
        }
        if (miIds.size() > 500) {
            throw new BizException("miIds size is more than 500");
        }

        LambdaQueryWrapper<IntlRmsUser> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(IntlRmsUser::getMiId, miIds);
        List<IntlRmsUser> intlRmsUsers = list(queryWrapper);
        if (CollectionUtils.isEmpty(intlRmsUsers)) {
            return Optional.empty();
        }
        List<IntlRmsUserNewDto> intlRmsUserNewDtos =
                JacksonUtil.parseArray(JacksonUtil.toStr(intlRmsUsers), IntlRmsUserNewDto.class);
        return Optional.of(intlRmsUserNewDtos);
    }

    @Override
    public Optional<UserInfoDTO> queryUserByMiId(Long miId) {
        log.info("查询用户信息, miId: {}", miId);

        try {
            IntlRmsUser user = this.baseMapper.selectByMiId(miId);

            if (user != null) {
                log.info("查询用户信息成功, miId: {}, domainName: {}", miId, user.getDomainName());
                return Optional.of(convertToUserInfo(user));
            } else {
                log.warn("未找到用户信息, miId: {}", miId);
                return Optional.empty();
            }

        } catch (Exception e) {
            log.error("查询用户信息失败, miId: {}", miId, e);
            return Optional.empty();
        }
    }

    @Override
    public List<UserInfoDTO> getUserByName(String userName) {

        if (StringUtils.isNotEmpty(userName)) {
            log.info("查询用户信息, userName: {}", userName);
            List<IntlRmsUser> intlRmsUsers =
                    list(Wrappers.<IntlRmsUser>lambdaQuery().like(IntlRmsUser::getEnglishName, userName));
            if (CollectionUtils.isNotEmpty(intlRmsUsers)) {
                return intlRmsUsers.stream().map(this::convertToUserInfo).collect(Collectors.toList());
            }
        }

        List<IntlRmsUser> intlRmsUsers = list();

        return intlRmsUsers.stream().map(this::convertToUserInfo).collect(Collectors.toList());

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean modifyUserAttribute(Long mid, UserAttributeModifyType modifyType, UserModifyDataDTO modifyData) {
        IntlRmsUser user = getUserByMid(mid)
            .orElseThrow(() -> BizException.buildFormatException(ErrorCodes.USER_NOT_EXIST, mid));
        Tuple requestParam = getModifyInput(user, modifyType, modifyData);
        UserModifyInput userModifyInput = requestParam.get(0);
        userModifyInput.setUserId(user.getRmsUserid());
        String type = requestParam.get(1);
        // 更新当前用户信息
        this.updateUser(user);

        // 调用rms同步用户信息
        return syncUserInfoToRms(userModifyInput, type);
    }

    private Tuple getModifyInput(IntlRmsUser user, UserAttributeModifyType modifyType, UserModifyDataDTO modifyData) {
        switch (modifyType) {
            case MODIFY_LANGUAGE:
                // 此处前端只能获取到languageCode。更新时将intl_rms_user表的languageId、languageName清空，等待rms同步用户表时再更新
                Assert.isTrue(StringUtils.isNotBlank(modifyData.getLanguageCode()), "languageCode can't be blank.");
                user.setLanguageCode(modifyData.getLanguageCode());
                user.setLanguageId("");
                user.setLanguageName("");
                UserLanguageInput languageInput =
                    new UserLanguageInput(modifyData.getLanguageId(), modifyData.getLanguageCode());
                return new Tuple(languageInput, TYPE_UPDATE_LANGUAGE);
            case MODIFY_OFFLINE_UPLOAD:
                Assert.notNull(modifyData.getIsOfflineUpload(), "IsOfflineUpload can't be null.");
                boolean isOffline = BooleanUtils.isTrue(modifyData.getIsOfflineUpload());
                user.setIsOffline(isOffline ? SwitchEnum.ON.getValue() : SwitchEnum.OFF.getValue());
                UserOfflineInput offlineInput = new UserOfflineInput(isOffline);
                return new Tuple(offlineInput, TYPE_UPDATE_OFFLINE);
            case MODIFY_PHONE_NUM_AND_REMARK:
                String mobile = modifyData.getPhone();
                // 若未传入手机号，表示当前不修改手机号，调rms接口时传库中存的手机号
                if (StringUtils.isBlank(modifyData.getPhone()) && StringUtils.isNotBlank(user.getMobile())) {
                    mobile = user.getMobile();
                }
                if (StringUtils.isNotBlank(mobile)) {
                    user.setMobile(mobile);
                }
                UserPhoneInput phoneInput = new UserPhoneInput(mobile, modifyData.getRemark());
                return new Tuple(phoneInput, TYPE_UPDATE_USER_INFO);
            default:
                throw new BizException(ErrorCodes.SYS_ERROR, "modifyType is not supported");
        }
    }

    private boolean syncUserInfoToRms(UserModifyInput input, String type) {
        try {
            log.info("modifyUserAttribute.syncUserInfoToRms, type: {}, param: {}", type, JsonUtil.bean2json(input));
            rmsProxyService.postRequest(CommonConstant.RMS_API_PREFIX.SUBMIT_DATA, type, JsonUtil.bean2json(input));
            // 已确认接口成功返回时result为null，不用特别校验
            return true;
        } catch (Exception e) {
            log.error("modifyUserAttribute.syncUserInfoToRms failed. type: {}, userId: {}", type, input.getUserId(), e);
            throw BizException.buildFormatException(ErrorCodes.SYNC_USER_TO_RMS_FAILED, e.getMessage());
        }
    }

    @Override
    public List<IntlRmsUserDTO> getRmsUserByMiIds(List<Long> miIdList) {
        if (CollectionUtils.isEmpty(miIdList)) {
            return Lists.newArrayList();
        }

        List<IntlRmsUserDTO> intlRmsUserDTOList = new java.util.ArrayList<>();
        List<IntlRmsUser> rmsUserList = list(Wrappers.<IntlRmsUser>lambdaQuery().in(IntlRmsUser::getMiId, miIdList));

        if (rmsUserList.isEmpty()) {
            return intlRmsUserDTOList;
        }

        for (IntlRmsUser intlRmsUser : rmsUserList) {
            IntlRmsUserDTO rmsUserDTO = convertUser(intlRmsUser);
            intlRmsUserDTOList.add(rmsUserDTO);
        }
        return intlRmsUserDTOList;

    }

    @Override
    public IntlRmsUserDTO getRmsUserByUniqueName(String uniqueName) {
        RedisKey redisKey = RedisKeyEnum.RMS_USER_CACHE.get(uniqueName);
        IntlRmsUserDTO userCache = redisClient.getObj(redisKey, IntlRmsUserDTO.class);
        if (Objects.nonNull(userCache)) {
            return userCache;
        }
        IntlRmsUser intlRmsUser =
                this.getOne(Wrappers.<IntlRmsUser>lambdaQuery().eq(IntlRmsUser::getDomainName, uniqueName));
        IntlRmsUserDTO rmsUser = convertUser(intlRmsUser);
        if (Objects.isNull(rmsUser)) {
            return null;
        }
        redisClient.set(redisKey, JsonUtil.bean2json(rmsUser));
        return rmsUser;
    }

    @Override
    public IntlRmsUserDTO getRmsUserByMiId(Long miId) {
        RedisKey redisKey = RedisKeyEnum.RMS_USER_BY_MI_ID_CACHE.get(miId);
        IntlRmsUserDTO userCache = redisClient.getObj(redisKey, IntlRmsUserDTO.class);
        if (Objects.nonNull(userCache)) {
            return userCache;
        }
        IntlRmsUser intlRmsUser = this.getOne(Wrappers.<IntlRmsUser>lambdaQuery().eq(IntlRmsUser::getMiId, miId));
        IntlRmsUserDTO rmsUser = convertUser(intlRmsUser);
        redisClient.set(redisKey, JsonUtil.bean2json(rmsUser));
        return rmsUser;
    }

    /**
     * 根据域名获取用户信息
     *
     * @param domainName 域名
     * @return 用户信息DTO
     */
    @Override
    public IntlRmsUserDTO getIntlRmsUserByDomainName(String domainName) {
        if (StringUtils.isEmpty(domainName)) {
            return null;
        }
        try {
            IntlRmsUser intlRmsUser = this.baseMapper.selectByDomainName(domainName);
            if (intlRmsUser == null) {
                return null;
            }
            return convertUser(intlRmsUser);
        } catch (Exception e) {
            log.error("getIntlRmsUserByDomainName error: ", e);
        }
        return null;
    }

    /**
     * 根据MiId获取用户信息
     *
     * @param miId MiId
     * @return 用户信息DTO Optional
     */
    @Override
    public Optional<IntlRmsUserDTO> getIntlRmsUserByMiId(Long miId) {
        if (miId == null) {
            return Optional.empty();
        }
        LambdaQueryWrapper<IntlRmsUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(IntlRmsUser::getMiId, miId);

        return page(new Page<>(1, 1, false), wrapper).getRecords().stream().findFirst()
            .map(this::convertUser);
    }

    /**
     * 根据多个domainName批量查询用户信息
     *
     * @param domainNames 域账号列表
     * @return 用户信息列表
     */
    @Override
    public List<IntlRmsUserDTO> getIntlRmsUserByDomainNames(List<String> domainNames) {
        if (domainNames == null || domainNames.isEmpty()) {
            return Collections.emptyList();
        }
        try {
            List<IntlRmsUser> users =
                list(Wrappers.<IntlRmsUser>lambdaQuery().in(IntlRmsUser::getDomainName, domainNames));
            if (users == null || users.isEmpty()) {
                return Collections.emptyList();
            }
            return users.stream().map(this::convertUser).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("getIntlRmsUserByDomainNames error: ", e);
        }
        return Collections.emptyList();
    }

    /**
     * 根据用户id查询用户信息
     *
     * @param userId 用户id
     * @return 用户信息Optional
     */
    @Override
    public Optional<IntlRmsUser> getByUserId(String userId) {
        if (StringUtils.isBlank(userId)) {
            return Optional.empty();
        }
        LambdaQueryWrapper<IntlRmsUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(IntlRmsUser::getRmsUserid, userId);
        return Optional.ofNullable(getOne(wrapper));
    }

    @Override
    public Optional<IntlRmsUser> getUserByMid(Long mid) {
        if (null == mid || mid <= 0L) {
            return Optional.empty();
        }
        LambdaQueryWrapper<IntlRmsUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(IntlRmsUser::getMiId, mid);
        return Optional.ofNullable(getOne(wrapper));
    }

    /**
     * 转换IntlRmsUser为UserInfoDTO
     */
    private UserInfoDTO convertToUserInfo(IntlRmsUser user) {
        UserInfoDTO userInfo = new UserInfoDTO();
        userInfo.setMiId(user.getMiId());
        userInfo.setDomainName(user.getDomainName());
        userInfo.setJobId(user.getJobId());
        userInfo.setEnglishName(user.getEnglishName());
        userInfo.setCountryCode(user.getCountryShortcode());
        return userInfo;
    }

    private IntlRmsUserDTO convertUser(IntlRmsUser intlRmsUser) {
        if (Objects.isNull(intlRmsUser)) {
            return null;
        }
        return IntlRmsUserDTO.builder()
            .id(intlRmsUser.getId())
            .rmsUserid(intlRmsUser.getRmsUserid())
            .code(intlRmsUser.getCode())
            .domainName(intlRmsUser.getDomainName())
            .englishName(intlRmsUser.getEnglishName())
            .countryId(intlRmsUser.getCountryId())
            .countryName(intlRmsUser.getCountryName())
            .jobId(intlRmsUser.getJobId())
            .jobName(intlRmsUser.getJobName())
            .email(intlRmsUser.getEmail())
            .mobile(intlRmsUser.getMobile())
            .miId(intlRmsUser.getMiId())
            .managerId(intlRmsUser.getManagerId())
            .managerName(intlRmsUser.getManagerName())
            .virtualMiId(intlRmsUser.getVirtualMiId())
            .languageId(intlRmsUser.getLanguageId())
            .languageName(intlRmsUser.getLanguageName())
            .languageCode(intlRmsUser.getLanguageCode())
            .isDisabled(intlRmsUser.getIsDisabled())
            .countryShortcode(intlRmsUser.getCountryShortcode())
            .hasBindMiId(intlRmsUser.getHasBindMiId())
            .isOffline(intlRmsUser.getIsOffline())
            .brands(intlRmsUser.getBrands())
            .keySellingProducts(intlRmsUser.getKeySellingProducts())
            .testAccount(intlRmsUser.getTestAccount())
            .build();
    }

    /**
     * 计算迁移率
     *
     * @param migrated
     * @param total
     * @return
     */
    private String calculateMigrationRate(Integer migrated, Integer total) {
        if (total == null || total == 0) {
            return "0.00%";
        }
        double rate = (double) migrated / total * 100;
        return String.format("%.2f%%", rate);
    }

    /**
     * 设置反射获取的字段值
     *
     * @param table
     * @param jobName
     * @param total
     * @param migrated
     */
    private void setFieldsByReflection(EasyExcelTable table, String jobName, Integer total, Integer migrated) {
        try {
            Method totalMethod = table.getClass().getMethod("setTotalPeopleOf" + jobName, Integer.class);
            Method migratedMethod = table.getClass().getMethod("setMigratedPeopleOf" + jobName, Integer.class);
            Method rateMethod = table.getClass().getMethod("setMigrationRateOf" + jobName, String.class);

            totalMethod.invoke(table, total);
            migratedMethod.invoke(table, migrated);
            rateMethod.invoke(table, calculateMigrationRate(migrated, total));
        } catch (Exception e) {
            log.error("Error setting fields by reflection", e);

        }
    }

    /**
     * 查询用户迁移数据
     */
    @Override
    public String getUserMigrateData() {
        ArrayList<EasyExcelTable> excelData = new ArrayList<>();
        long start = System.currentTimeMillis();
        // 1.查询所有国家
        List<CountryDTO> countryInfos = countryTimeZoneApiService.getCountryInfo();
        ArrayList<String> countryNameList = new ArrayList<>();
        countryInfos.forEach(country -> countryNameList.add(country.getCountryName()));
        long mid = System.currentTimeMillis();
        log.info("start query country time:{}", mid - start);
        //2.查询不同国家的不同岗位用户总人数和已迁移人数
        List<String> jobList =
                Arrays.asList("Promoter", "Temporary Promoter", "Supervisor", "Supervisor without Promoters",
                        "Merchandiser");
        QueryWrapper<IntlRmsUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("country_name,job_name,COUNT(*) AS total, SUM(has_bind_mi_id) AS migrated")
                .in("country_name", countryNameList)
                .in("job_name", jobList)
                .groupBy("country_name", "job_name");
        List<Map<String, Object>> results = intlRmsUserMapper.selectMaps(queryWrapper);
        //3 将数据按照国家和岗位进行分组
        Map<String, Map<String, int[]>> countryJobDataMap = new HashMap<>();
        results.forEach(map -> {
            String countryName = map.get("country_name").toString();
            String jobName = map.get("job_name").toString().replace(" ", "");
            //mysql中的count操作返回什么类型？一般是Long，这里好像是BigDecimal
            Number totalNumber = (Number) map.get("total");
            int total = totalNumber != null ? totalNumber.intValue() : 0;
            Number migratedNumber = (Number) map.get("migrated");
            int migrated = migratedNumber != null ? migratedNumber.intValue() : 0;
            countryJobDataMap.computeIfAbsent(countryName, k -> new HashMap<>())
                    .put(jobName, new int[] {total, migrated});
        });
        //4.填充行数据
        for (CountryDTO country : countryInfos) {
            EasyExcelTable easyExcelTable = new EasyExcelTable();
            easyExcelTable.setCountry(country.getCountryName());
            easyExcelTable.setArea(country.getArea());

            //5.设置每个岗位的总人数和已迁移人数
            //5.1 设置默认值为0
            jobList.forEach(job -> setFieldsByReflection(easyExcelTable, job.replace(" ", ""), 0, 0));
            //5.2 设置实际值
            Map<String, int[]> jobDataMap =
                    countryJobDataMap.getOrDefault(country.getCountryName(), Collections.emptyMap());
            for (Map.Entry<String, int[]> entry : jobDataMap.entrySet()) {
                setFieldsByReflection(easyExcelTable, entry.getKey(), entry.getValue()[0], entry.getValue()[1]);
            }
            excelData.add(easyExcelTable);
        }
        long mid2 = System.currentTimeMillis();
        log.info("query migratedata time:{}", mid2 - mid);
        // 6.将数据写入excel
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            String sheetName = "sheet1";
            String fileName = "migrate_data" + System.currentTimeMillis() + ".xlsx";
            EasyExcel.write(outputStream, EasyExcelTable.class)
                .registerWriteHandler(new SimpleColumnWidthStyleStrategy(25)).sheet(sheetName).doWrite(excelData);
            // 7.通过fds将excel文件上传
            String fileUrl = fdsService.uploadFile(fileName, new ByteArrayInputStream(outputStream.toByteArray()));
            long end = System.currentTimeMillis();
            log.info("upload file time:{}", end - mid2);
            log.info("total time:{}", end - start);
            return fileUrl;
        } catch (Exception e) {
            log.error("Error writing to Excel", e);
            throw new BizException(ErrorCodes.EXPORT_TO_EXCEL_FAILED);
        }
    }

    @Override
    public IntlRmsUserDTO getByRmsUserId(String rmsUserid) {
        Optional<IntlRmsUser> optional = this.getByUserId(rmsUserid);
        return optional.map(this::convertUser).orElse(null);
    }

    @Override
    public List<IntlRmsUserDTO> getByRmsUserIds(List<String> rmsUserIdList) {
        if (CollectionUtils.isEmpty(rmsUserIdList)) {
            return Lists.newArrayList();
        }
        List<IntlRmsUser> users = intlRmsUserMapper.getByRmsUserIds(rmsUserIdList);
        return users.stream().map(this::convertUser).collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateUser(IntlRmsUserDTO intlRmsUser) {
        Assert.notNull(intlRmsUser.getId(), "Id is null");
        IntlRmsUser dbUser = this.getById(intlRmsUser.getId());
        if (Objects.isNull(dbUser)) {
            return false;
        }
        ComponentLocator.getConverter().convert(intlRmsUser, dbUser);
        return updateUser(dbUser);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean saveUser(IntlRmsUserDTO intlRmsUser) {
        IntlRmsUser newUser = ComponentLocator.getConverter().convert(intlRmsUser, IntlRmsUser.class);
        newUser.setCreatedAt(System.currentTimeMillis());
        newUser.setUpdatedAt(System.currentTimeMillis());
        newUser.setCreatedOn(DateUtils.getNowDate());
        newUser.setModifiedOn(DateUtils.getNowDate());
        int effectRows = baseMapper.insert(newUser);
        return effectRows > 0;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateUser(IntlRmsUser user) {
        user.setUpdatedAt(System.currentTimeMillis());
        user.setModifiedOn(DateUtils.getNowDate());
        boolean updated = this.updateById(user);
        // 删除用户缓存
        removeUserCache(user.getRmsUserid(), user.getDomainName(), user.getMiId());
        return updated;
    }

    @Override
    public List<BusinessDataResponse> getUserPositionsWithStoreFilter(BusinessDataInputRequest userInfoRequestDTO) {
        return userInfoManager.getUserPositionsWithStoreFilter(userInfoRequestDTO);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchUpdateUsers(List<IntlRmsUserDTO> users) {
        if (CollectionUtils.isEmpty(users)) {
            return;
        }
        List<IntlRmsUser> intlRmsUsers = ComponentLocator.getConverter().convertList(users, IntlRmsUser.class);
        long current = System.currentTimeMillis();
        Date date = DateUtils.getNowDate();
        intlRmsUsers.forEach(user -> {
            user.setUpdatedAt(current);
            user.setModifiedOn(date);
        });
        intlRmsUserMapper.batchUpdateUsers(intlRmsUsers);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchSaveUsers(List<IntlRmsUserDTO> users) {
        if (CollectionUtils.isEmpty(users)) {
            return;
        }
        List<IntlRmsUser> intlRmsUsers = ComponentLocator.getConverter().convertList(users, IntlRmsUser.class);
        long current = System.currentTimeMillis();
        Date date = DateUtils.getNowDate();
        intlRmsUsers.forEach(user -> {
            user.setCreatedAt(current);
            user.setUpdatedAt(current);
            user.setCreatedOn(date);
            user.setModifiedOn(date);
        });
        intlRmsUserMapper.batchSaveUsers(intlRmsUsers);
    }


    @Override
    public void removeUserCache(String rmsUserid, String domainName, Long miId) {
        try {
            ThreadUtil.execAsync(() -> {
                RedisKey redisKeyForName = RedisKeyEnum.RMS_USER_CACHE.get(domainName);
                RedisKey redisKeyForId = RedisKeyEnum.INTL_USER_INFO_CACHE.get(rmsUserid);
                RedisKey redisKey = RedisKeyEnum.RMS_USER_BY_MI_ID_CACHE.get(miId);
                redisClient.del(redisKeyForName, redisKeyForId, redisKey);
            });
        } catch (Exception e) {
            // 只输出异常日志，不抛出异常
            log.error("Remove user cache error, rmsUserId:{}", rmsUserid, e);
        }
    }
}
