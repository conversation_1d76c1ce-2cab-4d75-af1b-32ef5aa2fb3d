package com.mi.info.intl.retail.user.infra.mapper.read;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.info.intl.retail.constant.DBTypeRoutingKey;
import com.mi.info.intl.retail.user.infra.entity.IntlRmsUser;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【intl_rms_user(用户表)】的数据库操作Mapper
* @createDate 2025-06-04 16:49:44
* @Entity generator.domain.IntlRmsUser
*/
@DS(DBTypeRoutingKey.XMSTORE_BE_READ)
@Mapper
public interface IntlRmsUserReadMapper extends BaseMapper<IntlRmsUser> {

}




