package com.mi.info.intl.retail.user.infra.mapper.read;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.BusinessDataInputRequest;
import com.mi.info.intl.retail.user.infra.entity.UserPositionStore;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface UserPositionStoreReadMapper extends BaseMapper<UserPositionStore> {

    List<UserPositionStore> getUserPositionStore(@Param("country") String country,
            @Param("titles") List<Integer> titles, @Param("storeGrades") List<String> storeGrades, @Param("midList") List<Long> midList);

    List<UserPositionStore> getUserNewProductTarget();
    /**
     * 根据条件查询用户阵地门店信息（支持门店类型和门店编码筛选,阵地编码）
     */
    List<UserPositionStore> getUserPositionStoreWithStoreFilter(@Param("r") BusinessDataInputRequest userInfoRequestDTO,
           @Param("midList") List<Long> midList);
}
