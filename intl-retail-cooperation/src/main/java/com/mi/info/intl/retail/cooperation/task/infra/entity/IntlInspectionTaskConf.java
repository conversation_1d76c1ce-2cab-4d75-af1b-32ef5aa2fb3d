package com.mi.info.intl.retail.cooperation.task.infra.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.info.intl.retail.cooperation.task.config.ListInspectionActionHandler;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 巡检任务表
 *
 * @TableName intl_inspection_task_conf
 */
@TableName(value = "intl_inspection_task_conf", autoResultMap = true)
@Data
public class IntlInspectionTaskConf extends BaseEntity implements Serializable {
    
    private static final long serialVersionUID = -8947015408990054983L;
    
    /**
     * 区域
     */
    @TableField(value = "region")
    private String region;
    
    /**
     * 区域编码
     */
    @TableField(value = "region_code")
    private String regionCode;
    
    /**
     * 国家
     */
    @TableField(value = "country")
    private String country;

    /**
     * 用户职位
     */
    @TableField(value = "user_title")
    private String userTitle;

    /**
     * 用户职位code
     */
    @TableField(value = "user_title_codes")
    private String userTitleCodes;
    
    /**
     * 国家/地区编码 eg：香港 HK
     */
    @TableField(value = "country_code")
    private String countryCode;
    
    /**
     * s级门店巡检频次,格式 x月x次
     */
    @TableField(value = "s_store_inspection_frequency")
    private String sStoreInspectionFrequency;
    
    /**
     * a级门店巡检频次,格式 x月x次
     */
    @TableField(value = "a_store_inspection_frequency")
    private String aStoreInspectionFrequency;
    
    /**
     * b级门店巡检频次,格式 x月x次
     */
    @TableField(value = "b_store_inspection_frequency")
    private String bStoreInspectionFrequency;
    
    /**
     * c级门店巡检频次,格式 x月x次
     */
    @TableField(value = "c_store_inspection_frequency")
    private String cStoreInspectionFrequency;
    
    /**
     * d级门店巡检频次,格式 x月x次
     */
    @TableField(value = "d_store_inspection_frequency")
    private String dStoreInspectionFrequency;
    
    /**
     * 有促阵地巡检动作
     */
    @TableField(value = "has_promoter_front_inspection_action", typeHandler = ListInspectionActionHandler.class)
    private List<InspectionAction> hasPromoterFrontInspectionAction;
    
    /**
     * 无促阵地巡检动作
     */
    @TableField(value = "no_promoter_front_inspection_action", typeHandler = ListInspectionActionHandler.class)
    private List<InspectionAction> noPromoterFrontInspectionAction;
    
    /**
     * 有促售点巡检动作
     */
    @TableField(value = "has_promoter_pos_inspection_action", typeHandler = ListInspectionActionHandler.class)
    private List<InspectionAction> hasPromoterPosInspectionAction;
    
    /**
     * 无售点地巡检动作
     */
    @TableField(value = "no_promoter_pos_inspection_action", typeHandler = ListInspectionActionHandler.class)
    private List<InspectionAction> noPromoterPosInspectionAction;
    
    /**
     * 阵地巡检时长分钟
     */
    @TableField(value = "front_inspection_time")
    private Integer frontInspectionTime;
    
    /**
     * POS巡检时长分钟
     */
    @TableField(value = "pos_inspection_time")
    private Integer posInspectionTime;

    /**
     * 是否停用
     */
    @TableField(value = "is_disabled")
    private Boolean isDisabled;
    
}
