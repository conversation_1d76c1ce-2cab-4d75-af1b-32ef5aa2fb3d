package com.mi.info.intl.retail.cooperation.task.infra.mapper.read;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.info.intl.retail.cooperation.task.infra.entity.IntlRmsUser;

import java.util.List;

public interface RmsUserReadMapper extends BaseMapper<IntlRmsUser> {

    IntlRmsUser selectByDomainName(String domainName);

    /**
     * 批量根据domainName查询用户
     */
    List<IntlRmsUser> selectByDomainNames(List<String> domainNames);
}
