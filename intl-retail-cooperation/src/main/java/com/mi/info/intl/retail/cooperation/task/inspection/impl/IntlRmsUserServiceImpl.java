package com.mi.info.intl.retail.cooperation.task.inspection.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.info.intl.retail.api.fieldforce.user.IntlRmsUserApiService;
import com.mi.info.intl.retail.cooperation.task.dto.dto.IntlRmsUserDto;
import com.mi.info.intl.retail.cooperation.task.infra.entity.IntlRmsUser;
import com.mi.info.intl.retail.cooperation.task.infra.mapper.read.RmsUserReadMapper;
import com.mi.info.intl.retail.cooperation.task.inspection.IntlRmsUserService;
import com.mi.info.intl.retail.core.utils.CollUtils;
import com.mi.info.intl.retail.dto.IntlRmsUserDTO;
import com.mi.info.intl.retail.utils.redis.RedisKey;
import com.mi.info.intl.retail.utils.redis.RedisKeyEnum;
import lombok.extern.slf4j.Slf4j;
import com.mi.info.intl.retail.utils.redis.RedisClient;

import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
public class IntlRmsUserServiceImpl
        extends ServiceImpl<RmsUserReadMapper, IntlRmsUser>
        implements IntlRmsUserService, IntlRmsUserApiService {

    @Autowired
    private RmsUserReadMapper rmsUserReadMapper;

    @Autowired
    private RedisClient redisClient;

    @Override
    public IntlRmsUserDto getIntlRmsUserByDomainName(String domainName) {
        if (StringUtils.isEmpty(domainName)) {
            return null;
        }
        try {
            IntlRmsUser intlRmsUser = rmsUserReadMapper.selectByDomainName(domainName);
            if (intlRmsUser == null) {
                return null;
            }
            return convert2Domain(intlRmsUser);
        } catch (Exception e) {
            log.error("getIntlRmsUserByDomainName error: ", e);
        }
        return null;
    }

    private static IntlRmsUserDto convert2Domain(IntlRmsUser intlRmsUser) {
        return IntlRmsUserDto.builder()
                .rmsUserid(intlRmsUser.getRmsUserid())
                .code(intlRmsUser.getCode())
                .domainName(intlRmsUser.getDomainName())
                .englishName(intlRmsUser.getEnglishName())
                .countryId(intlRmsUser.getCountryId())
                .countryName(intlRmsUser.getCountryName())
                .jobId(intlRmsUser.getJobId())
                .jobName(intlRmsUser.getJobName())
                .email(intlRmsUser.getEmail())
                .mobile(intlRmsUser.getMobile())
                .miId(intlRmsUser.getMiId())
                .managerId(intlRmsUser.getManagerId())
                .managerName(intlRmsUser.getManagerName())
                .virtualMiId(intlRmsUser.getVirtualMiId())
                .languageId(intlRmsUser.getLanguageId())
                .languageName(intlRmsUser.getLanguageName())
                .isDisabled(intlRmsUser.getIsDisabled())
                .build();
    }

    @Override
    public Optional<IntlRmsUserDto> getIntlRmsUserByMiId(Long miId) {
        if (miId == null) {
            return Optional.empty();
        }
        LambdaQueryWrapper<IntlRmsUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(IntlRmsUser::getMiId, miId);

        return rmsUserReadMapper.selectPage(new Page<>(1, 1, false), wrapper).getRecords().stream().findFirst()
                .map(IntlRmsUserServiceImpl::convert2Domain);
    }

    @Override
    public List<IntlRmsUserDto> getIntlRmsUserByMiIds(List<Long> miIds) {
        List<Long> finalMiId =
                Optional.ofNullable(miIds).orElse(Collections.emptyList()).stream().filter(Objects::nonNull)
                        .filter(u -> u != 0L).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(finalMiId)) {
            return Collections.emptyList();
        }
        List<IntlRmsUser> intlRmsUsers = rmsUserReadMapper.selectList(
                Wrappers.lambdaQuery(IntlRmsUser.class).in(IntlRmsUser::getMiId, finalMiId));
        return CollUtils.mapping(intlRmsUsers, IntlRmsUserServiceImpl::convert2Domain);
    }

    @Override
    public List<IntlRmsUserDto> getIntlRmsUserByDomainNames(List<String> domainNames) {
        if (domainNames == null || domainNames.isEmpty()) {
            return Collections.emptyList();
        }
        try {
            List<IntlRmsUser> users = rmsUserReadMapper.selectByDomainNames(domainNames);
            if (users == null || users.isEmpty()) {
                return Collections.emptyList();
            }
            return users.stream().map(IntlRmsUserServiceImpl::convert2Domain).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("getIntlRmsUserByDomainNames error: ", e);
        }
        return Collections.emptyList();
    }

    @Override
    public List<IntlRmsUserDTO> getRmsUserByMiIds(List<Long> miIdList) {
        if (miIdList == null || miIdList.isEmpty()) {
            return null;
        }

        List<IntlRmsUserDTO> intlRmsUserDTOList = new java.util.ArrayList<>();
        List<IntlRmsUser> rmsUserList = rmsUserReadMapper
                .selectList(Wrappers.<IntlRmsUser>lambdaQuery().in(IntlRmsUser::getMiId, miIdList));

        if (rmsUserList.isEmpty()) {
            return intlRmsUserDTOList;
        }

        for (IntlRmsUser intlRmsUser : rmsUserList) {
            IntlRmsUserDTO rmsUserDTO = convertUser(intlRmsUser);
            intlRmsUserDTOList.add(rmsUserDTO);
        }
        return intlRmsUserDTOList;

    }

    @Override
    public IntlRmsUserDTO getRmsUserByUniqueName(String uniqueName) {
        RedisKey redisKey = RedisKeyEnum.RMS_USER_CACHE.get(uniqueName);
        IntlRmsUserDTO userCache = redisClient.getObj(redisKey, IntlRmsUserDTO.class);
        if (Objects.nonNull(userCache)) {
            return userCache;
        }
        IntlRmsUser intlRmsUser = rmsUserReadMapper.selectByDomainName(uniqueName);
        if (Objects.isNull(intlRmsUser)) {
            return null;
        }
        IntlRmsUserDTO rmsUser = convertUser(intlRmsUser);
        redisClient.set(redisKey, JSON.toJSONString(rmsUser));
        return rmsUser;
    }

    private IntlRmsUserDTO convertUser(IntlRmsUser intlRmsUser) {
        return IntlRmsUserDTO.builder()
                .rmsUserid(intlRmsUser.getRmsUserid())
                .code(intlRmsUser.getCode())
                .domainName(intlRmsUser.getDomainName())
                .englishName(intlRmsUser.getEnglishName())
                .countryId(intlRmsUser.getCountryId())
                .countryName(intlRmsUser.getCountryName())
                .jobId(intlRmsUser.getJobId())
                .jobName(intlRmsUser.getJobName())
                .email(intlRmsUser.getEmail())
                .mobile(intlRmsUser.getMobile())
                .miId(intlRmsUser.getMiId())
                .managerId(intlRmsUser.getManagerId())
                .managerName(intlRmsUser.getManagerName())
                .virtualMiId(intlRmsUser.getVirtualMiId())
                .languageId(intlRmsUser.getLanguageId())
                .languageName(intlRmsUser.getLanguageName())
                .languageCode(intlRmsUser.getLanguageCode())
                .isDisabled(intlRmsUser.getIsDisabled())
                .build();
    }
}
