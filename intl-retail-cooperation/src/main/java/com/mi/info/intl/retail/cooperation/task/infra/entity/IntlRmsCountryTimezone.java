package com.mi.info.intl.retail.cooperation.task.infra.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 国家时区表
 *
 * @TableName intl_rms_country_timezone
 */
@TableName(value = "intl_rms_country_timezone")
@Data
public class IntlRmsCountryTimezone implements Serializable {
    
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    
    /**
     * 唯一标识
     */
    @TableField(value = "country_timezone_id")
    private String countryTimezoneId;
    
    /**
     * 名称
     */
    @TableField(value = "name")
    private String name;
    
    /**
     * 国家唯一标识
     */
    @TableField(value = "country_id")
    private String countryId;
    
    /**
     * 国家标签
     */
    @TableField(value = "country_name")
    private String countryName;
    
    /**
     * 国家短代码
     */
    @TableField(value = "country_code")
    private String countryCode;
    
    /**
     * 时区名称
     */
    @TableField(value = "timezone_name")
    private String timezoneName;
    
    /**
     * 时区代码
     */
    @TableField(value = "timezone_code")
    private String timezoneCode;
    
    /**
     * 偏移量
     */
    @TableField(value = "bias")
    private String bias;
    
    /**
     * 是否可用
     */
    @TableField(value = "state_code")
    private Integer stateCode;
    
    /**
     * 区域
     */
    @TableField(value = "area")
    private String area;
    
    /**
     * 区域短代码
     */
    @TableField(value = "area_code")
    private String areaCode;
    
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}