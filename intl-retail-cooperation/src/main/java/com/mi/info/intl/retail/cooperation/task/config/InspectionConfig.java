package com.mi.info.intl.retail.cooperation.task.config;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.alibaba.nacos.spring.context.annotation.config.NacosPropertySource;
import com.mi.info.intl.retail.api.task.enums.TaskActionEnum;
import com.mi.info.intl.retail.utils.intl.IntlTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 巡检任务配置类
 * 负责管理巡检任务相关的各种配置参数，包括国家白名单、事件类型配置等
 *
 * <AUTHOR>
 * @date 2025/1/27
 */
@Slf4j
@Configuration
@NacosPropertySource(dataId = "supervisor-inspection-config", autoRefreshed = true)
public class InspectionConfig {
    
    // ==================== 常量定义 ====================
    
    /**
     * 默认需要checkin的事件类型列表
     */
    private static final List<String> DEFAULT_CHECKIN_EVENT_TYPES = Arrays.asList(
            "InspectionDisplay", "Promotions", "MaterielUpload", "StoreSales",
            "SalesUploadIMEI", "SalesUploadQty", "Marketing research", "LDU",
            "StoreTraining", "StockUpload", "StoreInspection", "Material Management",
            "Receiving", "SalesUploadIMEINew"
    );
    
    /**
     * 配置分隔符
     */
    private static final String CONFIG_SEPARATOR = ",";
    private static final String KEY_VALUE_SEPARATOR = ";";
    private static final String KEY_EVENT_SEPARATOR = ":";
    
    // ==================== 配置字段 ====================
    
    /**
     * 国家白名单配置
     */
    @NacosValue(value = "${inspection.task.countryWhiteList:SG}", autoRefreshed = true)
    private String countryWhiteListConfig;
    
    /**
     * 主管职位ID配置
     */
    @NacosValue(value = "${inspection.task.supervisorJobIds:500900002,100000051,100000024}", autoRefreshed = true)
    private String supervisorJobIds;
    
    /**
     * 必须执行的事件定义ID配置
     */
    @NacosValue(value = "${inspection.task.mustDoEventDefinitionIds:1096,1097}", autoRefreshed = true)
    private String mustDoEventDefinitionIds;
    
    /**
     * 任选一个的事件定义ID配置
     */
    @NacosValue(value = "${inspection.task.anyOneEventDefinitionIds:1090,1091,1094,1095}", autoRefreshed = true)
    private String anyOneEventDefinitionIds;

    /**
     * MID灰度名单配置
     */
    @NacosValue(value = "${inspection.task.midGrayList:}", autoRefreshed = true)
    private String midGrayList;
    
    @NacosValue(value = "${inspection.task.materialMidGrayList:}", autoRefreshed = true)
    private String materialMidGrayList;

    @NacosValue(value = "${inspection.task.countryWhiteSalesUploadQty:SA}", autoRefreshed = true)
    private String countryWhiteSalesUploadQty;
    
    /**
     * 默认需要checkin的事件类型配置
     * 格式：事件类型1,事件类型2,事件类型3
     * 例如：InspectionDisplay,Promotions,MaterielUpload,StoreSales,SalesUploadIMEI,SalesUploadQty,
     * Marketing research,LDU,StoreTraining,StockUpload,StoreInspection,Material Management,Receiving,SalesUploadIMEINew
     */
    @NacosValue(value = "${checkin.event.type.default.config:InspectionDisplay,Promotions,MaterielUpload,StoreSales,"
            + "SalesUploadIMEI,SalesUploadQty,Marketing research,LDU,StoreTraining,StockUpload,StoreInspection,"
            + "Material Management,Receiving,SalesUploadIMEINew}", autoRefreshed = true)
    private String defaultEventTypeConfig;
    
    /**
     * 各国无需checkin的事件类型配置
     * 格式：国家代码:事件类型1,事件类型2;国家代码2:事件类型3,事件类型4
     * 例如：ID:SalesUploadIMEI,SalesUploadIMEINew;SG:StoreTraining
     */
    @NacosValue(value = "${checkin.event.type.exclude.config:}", autoRefreshed = true)
    private String excludeEventTypeConfig;
    
    /**
     * 事件类型列表配置（新增）
     * 格式：事件类型1,事件类型2,事件类型3
     * 默认：CHECK_OUT,STORE_CHECK
     */
    @NacosValue(value = "${checkin.event.type.list.config:CHECK_OUT,STORE_CHECK}", autoRefreshed = true)
    private String eventTypeListConfig;
    
    /**
     * 按类型和国家的无需checkin事件类型配置
     * 格式：类型_国家代码:事件类型1,事件类型2;类型_国家代码2:事件类型3,事件类型4
     * 例如：1_ID:SalesUploadIMEI,SalesUploadIMEINew;2_SG:StoreTraining
     * 其中1表示金刚位，2表示任务中心列表
     */
    @NacosValue(value = "${checkin.event.type.exclude.by.type.config:}", autoRefreshed = true)
    private String excludeEventTypeByTypeConfig;
    
    /**
     * 特殊工作时间配置
     * 格式: AE:06:00;EG:06:00;NG:07:00;KE:08:00;ZA:09:00;MA:10:00
     * 例如：AE:06:00;EG:06:00
     */
    @NacosValue(value = "${special.task.worktime.areaId:AE:06:00;EG:06:00}", autoRefreshed = true)
    private String workTimeConfig;
    
    // ==================== 默认配置 ====================
    
    /**
     * 默认必须执行的事件列表
     */
    private static final List<Long> DEFAULT_MUST_DO_EVENTS = Arrays.asList(
            TaskActionEnum.SALES_UPLOAD_QTY.getActionCode(),
            TaskActionEnum.STOCK_UPLOAD.getActionCode()
    );
    
    /**
     * 默认任选一个的事件列表
     */
    private static final List<Long> DEFAULT_ANY_ONE_EVENTS = Arrays.asList(
            TaskActionEnum.SAMPLE_DEVICE_REPORTING_SR.getActionCode(),
            TaskActionEnum.DISPLAY_INSPECTION.getActionCode(),
            TaskActionEnum.IN_STORE_TRAINING.getActionCode(),
            TaskActionEnum.STORE_CHECK_SR.getActionCode()
    );
    
    // ==================== 公共方法 ====================
    
    /**
     * 获取国家白名单
     * @return 国家代码列表
     */
    public List<String> getCountryWhiteList() {
        return parseStringList(countryWhiteListConfig);
    }
    
    /**
     * 获取主管职位ID列表
     * @return 职位ID列表
     */
    public List<String> getSupervisorJobIdsList() {
        return parseStringList(supervisorJobIds);
    }
    
    /**
     * 获取必须执行的事件定义ID列表
     * @return 事件定义ID列表
     */
    public List<Long> getMustDoEventDefinitionIds() {
        return parseLongList(mustDoEventDefinitionIds);
    }
    
    /**
     * 获取任选一个的事件定义ID列表
     * @return 事件定义ID列表
     */
    public List<Long> getAnyOneEventDefinitionIds() {
        return parseLongList(anyOneEventDefinitionIds);
    }

    /**
     * 获取MID灰度名单
     * @return MID列表
     */
    public List<Long> getMidGrayList() {
        return parseLongList(midGrayList);
    }
    
    /**
     * 获取默认必须执行的事件列表
     * @return 默认必须执行的事件列表
     */
    public List<Long> getDefaultMustDoEvents() {
        return new ArrayList<>(DEFAULT_MUST_DO_EVENTS);
    }
    
    /**
     * 获取默认任选一个的事件列表
     * @return 默认任选一个的事件列表
     */
    public List<Long> getDefaultAnyOneEvents() {
        return new ArrayList<>(DEFAULT_ANY_ONE_EVENTS);
    }

    public Set<String> getCountryWhiteSalesUploadQtySet() {
        return Optional.ofNullable(countryWhiteSalesUploadQty)
                .map(s -> s.split(","))
                .map(Arrays::stream)
                .orElseGet(Stream::empty)
                .filter(StringUtils::isNotBlank)
                .map(String::trim)
                .collect(Collectors.toSet());
    }

    public Set<Long> getMaterialMiIdGraySet() {
        return Optional.ofNullable(materialMidGrayList)
                .map(s -> s.split(","))
                .map(Arrays::stream)
                .orElseGet(Stream::empty)
                .filter(StringUtils::isNotBlank)
                .map(String::trim)
                .map(Long::valueOf)
                .collect(Collectors.toSet());
    }
    
    /**
     * 获取默认需要checkin的事件类型列表
     * @return 默认事件类型列表
     */
    public List<String> getDefaultCheckInEventTypes() {
        if (defaultEventTypeConfig == null || defaultEventTypeConfig.trim().isEmpty()) {
            log.debug("使用默认checkin事件类型配置");
            return new ArrayList<>(DEFAULT_CHECKIN_EVENT_TYPES);
        }
        
        try {
            List<String> eventTypes = parseStringList(defaultEventTypeConfig);
            if (eventTypes.isEmpty()) {
                log.warn("解析checkin事件类型配置为空，使用默认配置");
                return new ArrayList<>(DEFAULT_CHECKIN_EVENT_TYPES);
            }
            log.debug("成功解析checkin事件类型配置，共{}个事件类型", eventTypes.size());
            return eventTypes;
        } catch (Exception e) {
            log.error("解析checkin事件类型默认配置失败: {}", defaultEventTypeConfig, e);
            return new ArrayList<>(DEFAULT_CHECKIN_EVENT_TYPES);
        }
    }
    
    /**
     * 根据国家代码获取无需checkin的事件类型列表
     * @param country 国家代码
     * @return 无需checkin的事件类型列表
     */
    public List<String> getExcludeEventTypesByCountry(String country) {
        if (country == null || country.trim().isEmpty()) {
            log.warn("国家代码为空，返回空排除列表");
            return Collections.emptyList();
        }
        
        if (excludeEventTypeConfig == null || excludeEventTypeConfig.trim().isEmpty()) {
            log.debug("排除配置为空，国家{}无需排除任何事件类型", country);
            return Collections.emptyList();
        }
        
        try {
            String normalizedCountry = country.trim().toUpperCase();
            List<String> excludeList = parseCountryEventConfig(excludeEventTypeConfig, normalizedCountry);
            log.debug("国家{}的排除事件类型: {}", normalizedCountry, excludeList);
            return excludeList;
        } catch (Exception e) {
            log.error("解析checkin事件类型排除配置失败: {}", excludeEventTypeConfig, e);
            return Collections.emptyList();
        }
    }
    
    /**
     * 获取事件类型列表（新增）
     * @return 事件类型列表，默认CHECK_OUT,STORE_CHECK
     */
    public List<String> getEventTypeList() {
        if (eventTypeListConfig == null || eventTypeListConfig.trim().isEmpty()) {
            log.debug("事件类型列表配置为空，使用默认配置");
            return Arrays.asList("CHECK_OUT", "STORE_CHECK");
        }
        
        try {
            List<String> eventTypes = parseStringList(eventTypeListConfig);
            if (eventTypes.isEmpty()) {
                log.warn("解析事件类型列表配置为空，使用默认配置");
                return Arrays.asList("CHECK_OUT", "STORE_CHECK");
            }
            log.debug("成功解析事件类型列表配置，共{}个事件类型", eventTypes.size());
            return eventTypes;
        } catch (Exception e) {
            log.error("解析事件类型列表配置失败: {}", eventTypeListConfig, e);
            return Arrays.asList("CHECK_OUT", "STORE_CHECK");
        }
    }
    
    /**
     * 根据类型和国家代码获取无需checkin的事件类型列表
     * @param type 类型：1. 金刚位 2. 任务中心列表
     * @param country 国家代码
     * @return 无需checkin的事件类型列表
     */
    public List<String> getExcludeEventTypesByTypeAndCountry(Integer type, String country) {
        if (type == null || country == null || country.trim().isEmpty()) {
            log.warn("类型或国家代码为空，返回空排除列表");
            return Collections.emptyList();
        }
        
        if (excludeEventTypeByTypeConfig == null || excludeEventTypeByTypeConfig.trim().isEmpty()) {
            log.debug("按类型排除配置为空，类型{}国家{}无需排除任何事件类型", type, country);
            return Collections.emptyList();
        }
        
        try {
            String normalizedCountry = country.trim().toUpperCase();
            // 解析格式：type_country:event1,event2;type_country:event3,event4
            // 例如：1_SG:StoreTraining,LDU;2_SG:Marketing research
            String typeCountryKey = type + "_" + normalizedCountry;
            List<String> excludeList = parseTypeCountryEventConfig(excludeEventTypeByTypeConfig, typeCountryKey);
            log.debug("类型{}国家{}的排除事件类型: {}", type, normalizedCountry, excludeList);
            return excludeList;
        } catch (Exception e) {
            log.error("解析按类型checkin事件类型排除配置失败: {}", excludeEventTypeByTypeConfig, e);
            return Collections.emptyList();
        }
    }
    
    /**
     * 获取工作时间信息
     * @param country 国家代码
     * @return 工作时间信息
     */
    public WorkTimeInfo getWorkTime(String country) {
        if (country == null || country.trim().isEmpty()) {
            log.warn("国家代码为空，返回默认工作时间");
            return getDefaultWorkTime();
        }
        
        if (workTimeConfig == null || workTimeConfig.trim().isEmpty()) {
            log.debug("工作时间配置为空，国家{}使用默认工作时间", country);
            return getDefaultWorkTime();
        }
        
        try {
            String normalizedCountry = country.trim().toUpperCase();
            // 解析格式：AE:06:00;EG:06:00;NG:07:00;KE:08:00;ZA:09:00;MA:10:00
            String workTime = parseWorkTimeConfig(workTimeConfig, normalizedCountry);
            if (workTime != null && !workTime.trim().isEmpty()) {
                return parseWorkTime(workTime);
            }
            
            log.debug("国家{}未找到特殊工作时间配置，使用默认工作时间", normalizedCountry);
            return getDefaultWorkTime();
        } catch (Exception e) {
            log.error("解析工作时间配置失败: {}", workTimeConfig, e);
            return getDefaultWorkTime();
        }
    }
    
    // ==================== 私有辅助方法 ====================
    
    /**
     * 解析字符串列表配置
     * @param config 配置字符串
     * @return 解析后的字符串列表
     */
    private List<String> parseStringList(String config) {
        if (config == null || config.trim().isEmpty()) {
            return Collections.emptyList();
        }
        
        return Stream.of(config.split(CONFIG_SEPARATOR))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .collect(Collectors.toList());
    }
    
    /**
     * 解析长整型列表配置
     * @param config 配置字符串
     * @return 解析后的长整型列表
     */
    private List<Long> parseLongList(String config) {
        if (config == null || config.trim().isEmpty()) {
            return Collections.emptyList();
        }
        
        try {
            return Stream.of(config.split(CONFIG_SEPARATOR))
                    .map(String::trim)
                    .filter(s -> !s.isEmpty())
                    .map(Long::valueOf)
                    .collect(Collectors.toList());
        } catch (NumberFormatException e) {
            log.error("解析长整型配置失败: {}", config, e);
            return Collections.emptyList();
        }
    }
    
    /**
     * 解析事件配置（支持国家或类型+国家格式）
     * @param config 配置字符串
     * @param key 键值（国家代码或类型_国家代码）
     * @return 该键对应的事件类型列表
     */
    private List<String> parseCountryEventConfig(String config, String key) {
        return Stream.of(config.split(KEY_VALUE_SEPARATOR))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .map(this::parseCountryEventPair)
                .filter(pair -> pair != null && key.equals(pair.getKey()))
                .findFirst()
                .map(pair -> parseStringList(pair.getValue()))
                .orElse(Collections.emptyList());
    }
    
    /**
     * 解析类型+国家事件配置
     * @param config 配置字符串，格式：type_country:event1,event2;type_country:event3,event4
     * @param typeCountryKey 类型_国家键值
     * @return 该键对应的事件类型列表
     */
    private List<String> parseTypeCountryEventConfig(String config, String typeCountryKey) {
        return Stream.of(config.split(KEY_VALUE_SEPARATOR))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .map(this::parseCountryEventPair)
                .filter(pair -> pair != null && typeCountryKey.equals(pair.getKey()))
                .findFirst()
                .map(pair -> parseStringList(pair.getValue()))
                .orElse(Collections.emptyList());
    }
    
    /**
     * 解析键值对（支持国家或类型+国家格式）
     * @param keyValuePair 键:值配置，如 "SG:event1,event2" 或 "1_SG:event1,event2"
     * @return 键和值的键值对
     */
    private java.util.AbstractMap.SimpleEntry<String, String> parseCountryEventPair(String keyValuePair) {
        String[] parts = keyValuePair.split(KEY_EVENT_SEPARATOR);
        if (parts.length == 2) {
            return new java.util.AbstractMap.SimpleEntry<>(parts[0].trim().toUpperCase(), parts[1].trim());
        }
        return null;
    }
    
    /**
     * 解析工作时间配置
     * @param config 配置字符串，格式：AE:06:00;EG:06:00;NG:07:00
     * @param country 国家代码
     * @return 工作时间字符串，如 "06:00"
     */
    private String parseWorkTimeConfig(String config, String country) {
        return Stream.of(config.split(KEY_VALUE_SEPARATOR))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .map(this::parseWorkTimePair)
                .filter(pair -> pair != null && country.equals(pair.getKey()))
                .findFirst()
                .map(java.util.AbstractMap.SimpleEntry::getValue)
                .orElse(null);
    }
    
    /**
     * 解析工作时间键值对
     * @param keyValuePair 键:值配置，如 "AE:06:00"
     * @return 键和值的键值对
     */
    private java.util.AbstractMap.SimpleEntry<String, String> parseWorkTimePair(String keyValuePair) {
        String[] parts = keyValuePair.split(KEY_EVENT_SEPARATOR);
        if (parts.length == 2) {
            return new java.util.AbstractMap.SimpleEntry<>(parts[0].trim().toUpperCase(), parts[1].trim());
        }
        return null;
    }
    
    /**
     * 解析工作时间字符串
     * @param workTimeStr 时间字符串，格式如 "06:00"
     * @return 工作时间信息
     */
    private WorkTimeInfo parseWorkTime(String workTimeStr) {
        try {
            String[] parts = workTimeStr.split(":");
            int hour = Integer.parseInt(parts[0]);
            int minute = parts.length > 1 ? Integer.parseInt(parts[1]) : 0;
            
            return new WorkTimeInfo(hour, minute);
        } catch (Exception e) {
            log.error("解析工作时间字符串失败: {}", workTimeStr, e);
            return getDefaultWorkTime();
        }
    }
    
    /**
     * 获取默认工作时间（0点）
     * @return 默认工作时间信息
     */
    private WorkTimeInfo getDefaultWorkTime() {
        return new WorkTimeInfo(0, 0);
    }
    
    /**
     * 工作时间信息内部类
     * 基于IntlTimeUtil实现，支持各国时区
     */
    public static class WorkTimeInfo {
        private final int hour;
        private final int minute;
        
        public WorkTimeInfo(int hour, int minute) {
            this.hour = hour;
            this.minute = minute;
        }
        
        public int getHour() {
            return hour;
        }
        
        public int getMinute() {
            return minute;
        }
        
        /**
         * 获取当天工作时间的开始时间戳（毫秒）
         * @param country 国家代码
         * @return 开始时间戳
         */
        public long getStartTimeStamp(String country) {
            try {
                // 使用IntlTimeUtil获取工作开始时间戳
                Long workStartTime = IntlTimeUtil.getWorkStartTimeMillis(country, hour, minute);
                if (workStartTime != null) {
                    return workStartTime;
                }
                
                // 如果获取失败，使用默认逻辑
                log.warn("无法获取国家{}的工作开始时间，使用默认工作时间", country);
                return getDefaultStartTimeStamp();
            } catch (Exception e) {
                log.error("获取开始时间戳失败，使用默认值", e);
                return getDefaultStartTimeStamp();
            }
        }
        
        /**
         * 获取当天工作时间的结束时间戳（毫秒）
         * @param country 国家代码
         * @return 结束时间戳
         */
        public long getEndTimeStamp(String country) {
            try {
                // 使用IntlTimeUtil获取工作结束时间戳
                Long workEndTime = IntlTimeUtil.getWorkEndTimeMillis(country, hour, minute);
                if (workEndTime != null) {
                    return workEndTime;
                }
                
                // 如果获取失败，使用默认逻辑
                log.warn("无法获取国家{}的工作结束时间，使用默认工作时间", country);
                return getDefaultEndTimeStamp();
            } catch (Exception e) {
                log.error("获取结束时间戳失败，使用默认值", e);
                return getDefaultEndTimeStamp();
            }
        }
        
        /**
         * 获取默认开始时间戳（当天0点）
         * @return 默认开始时间戳
         */
        private long getDefaultStartTimeStamp() {
            java.time.ZoneId beijingZone = java.time.ZoneId.of("Asia/Shanghai");
            java.time.LocalDate today = java.time.LocalDate.now(beijingZone);
            java.time.ZonedDateTime startOfDay = today.atStartOfDay(beijingZone);
            return startOfDay.toInstant().toEpochMilli();
        }
        
        /**
         * 获取默认结束时间戳（第二天0点）
         * @return 默认结束时间戳
         */
        private long getDefaultEndTimeStamp() {
            java.time.ZoneId beijingZone = java.time.ZoneId.of("Asia/Shanghai");
            java.time.LocalDate today = java.time.LocalDate.now(beijingZone);
            java.time.LocalDate tomorrow = today.plusDays(1);
            java.time.ZonedDateTime endOfDay = tomorrow.atStartOfDay(beijingZone);
            return endOfDay.toInstant().toEpochMilli();
        }
    }
}
