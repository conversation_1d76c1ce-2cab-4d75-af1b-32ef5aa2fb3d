package com.mi.info.intl.retail.cooperation.task.inspection;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.info.intl.retail.cooperation.task.dto.dto.IntlRmsUserDto;
import com.mi.info.intl.retail.cooperation.task.infra.entity.IntlRmsUser;

import java.util.List;
import java.util.Optional;

public interface IntlRmsUserService extends IService<IntlRmsUser> {

    IntlRmsUserDto getIntlRmsUserByDomainName(String domainName);

    Optional<IntlRmsUserDto> getIntlRmsUserByMiId(Long miId);

    List<IntlRmsUserDto> getIntlRmsUserByMiIds(List<Long> miIds);

    /**
     * 根据多个domainName批量查询用户信息
     * @param domainNames 域账号列表
     * @return 用户信息列表
     */
    List<IntlRmsUserDto> getIntlRmsUserByDomainNames(List<String> domainNames);
}
