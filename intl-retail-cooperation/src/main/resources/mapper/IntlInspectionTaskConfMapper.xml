<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.info.intl.retail.cooperation.task.infra.mapper.IntlInspectionTaskConfMapper">

    <resultMap id="BaseResultMap" type="com.mi.info.intl.retail.cooperation.task.infra.entity.IntlInspectionTaskConf">
            <id property="id" column="id" />
            <result property="region" column="region" />
            <result property="regionCode" column="region_code" />
            <result property="country" column="country" />
            <result property="countryCode" column="country_code" />
            <result property="sStoreInspectionFrequency" column="s_store_inspection_frequency" />
            <result property="aStoreInspectionFrequency" column="a_store_inspection_frequency" />
            <result property="bStoreInspectionFrequency" column="b_store_inspection_frequency" />
            <result property="cStoreInspectionFrequency" column="c_store_inspection_frequency" />
            <result property="dStoreInspectionFrequency" column="d_store_inspection_frequency" />
            <result property="hasPromoterFrontInspectionAction" column="has_promoter_front_inspection_action" />
            <result property="noPromoterFrontInspectionAction" column="no_promoter_front_inspection_action" />
            <result property="hasPromoterPosInspectionAction" column="has_promoter_pos_inspection_action" />
            <result property="noPromoterPosInspectionAction" column="no_promoter_pos_inspection_action" />
            <result property="frontInspectionTime" column="front_inspection_time" />
            <result property="posInspectionTime" column="pos_inspection_time" />
            <result property="createdAt" column="created_at" />
            <result property="updatedAt" column="updated_at" />
            <result property="createdBy" column="created_by" />
            <result property="updatedBy" column="updated_by" />
    </resultMap>

    <sql id="Base_Column_List">
        id,region,region_code,country,country_code,s_store_inspection_frequency,a_store_inspection_frequency,
        b_store_inspection_frequency,c_store_inspection_frequency,d_store_inspection_frequency,
        has_promoter_front_inspection_action,no_promoter_front_inspection_action,
        has_promoter_pos_inspection_action,no_promoter_pos_inspection_action,
        front_inspection_time,pos_inspection_time,created_at,updated_at,created_by,updated_by
    </sql>
</mapper>
