package com.mi.info.intl.retail.cooperation.downloadcenter.service.impl;

import com.mi.info.intl.retail.cooperation.downloadcenter.config.LduConfig;
import com.mi.info.intl.retail.cooperation.downloadcenter.dto.JobTriggerRequest;
import com.mi.info.intl.retail.cooperation.downloadcenter.dto.JobTriggerResponse;
import com.xiaomi.nr.job.admin.dto.TriggerJobRequestDTO;
import com.xiaomi.nr.job.admin.service.NrJobService;
import com.xiaomi.youpin.infra.rpc.Result;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * JobTriggerServiceImpl 单元测试类
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("任务触发服务实现类测试")
class JobTriggerServiceImplTest {

    @InjectMocks
    private JobTriggerServiceImpl jobTriggerService;

    @Mock
    private NrJobService nrJobService;

    @Mock
    private LduConfig lduConfig;

    private JobTriggerRequest testRequest;
    private TriggerJobRequestDTO testTriggerRequest;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        testRequest = JobTriggerRequest.builder()
                .jobKey("testJobKey")
                .owner("testOwner")
                .taskParam("testParam")
                .taskDesc("测试任务描述")
                .taskName("测试任务")
                .projectId(30L)
                .projectName("测试项目")
                .businessModule("ldu")
                .build();

        testTriggerRequest = new TriggerJobRequestDTO();
        testTriggerRequest.setJobKey("testJobKey");
        testTriggerRequest.setOwner("testOwner");
        testTriggerRequest.setTaskParam("testParam");
        testTriggerRequest.setTaskDesc("测试任务描述");
        testTriggerRequest.setTaskName("测试任务");
        testTriggerRequest.setProjectId(30L);
        testTriggerRequest.setProjectName("测试项目");
    }

    @Test
    @DisplayName("触发任务 - 成功场景")
    void testTriggerJob_Success() {
        // Given
        Result<String> successResult = Result.success("job_triggered_successfully");

        when(nrJobService.triggerJob(any(TriggerJobRequestDTO.class)))
                .thenReturn(successResult);

        // When
        JobTriggerResponse result = jobTriggerService.triggerJob(testRequest);

        // Then
        assertNotNull(result);
        assertEquals("0", result.getCode());
        assertEquals("success", result.getMessage());
        verify(nrJobService, times(1)).triggerJob(any(TriggerJobRequestDTO.class));
    }

    @Test
    @DisplayName("触发任务 - 业务异常")
    void testTriggerJob_BusinessError() {
        // Given
        when(nrJobService.triggerJob(any(TriggerJobRequestDTO.class)))
                .thenThrow(new RuntimeException("业务错误"));

        // When
        JobTriggerResponse result = jobTriggerService.triggerJob(testRequest);

        // Then
        assertNotNull(result);
        assertEquals("500", result.getCode());
        assertTrue(result.getMessage().contains("任务触发失败"));
        verify(nrJobService, times(1)).triggerJob(any(TriggerJobRequestDTO.class));
    }

    @Test
    @DisplayName("触发任务 - 系统异常")
    void testTriggerJob_SystemError() {
        // Given
        when(nrJobService.triggerJob(any(TriggerJobRequestDTO.class)))
                .thenThrow(new RuntimeException("系统错误"));

        // When
        JobTriggerResponse result = jobTriggerService.triggerJob(testRequest);

        // Then
        assertNotNull(result);
        assertEquals("500", result.getCode());
        assertTrue(result.getMessage().contains("任务触发失败"));
        verify(nrJobService, times(1)).triggerJob(any(TriggerJobRequestDTO.class));
    }

    @Test
    @DisplayName("触发任务 - 服务异常")
    void testTriggerJob_ServiceException() {
        // Given
        when(nrJobService.triggerJob(any(TriggerJobRequestDTO.class)))
                .thenThrow(new RuntimeException("服务调用失败"));

        // When
        JobTriggerResponse result = jobTriggerService.triggerJob(testRequest);

        // Then
        assertNotNull(result);
        assertEquals("500", result.getCode());
        assertTrue(result.getMessage().contains("任务触发失败"));
        assertTrue(result.getMessage().contains("服务调用失败"));
        verify(nrJobService, times(1)).triggerJob(any(TriggerJobRequestDTO.class));
    }

    @Test
    @DisplayName("触发任务 - 空请求")
    void testTriggerJob_NullRequest() {
        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                () -> jobTriggerService.triggerJob(null));
        assertEquals("请求参数不能为空", exception.getMessage());
        verify(nrJobService, never()).triggerJob(any());
    }

    @Test
    @DisplayName("触发任务 - 空jobKey")
    void testTriggerJob_EmptyJobKey() {
        // Given
        JobTriggerRequest request = JobTriggerRequest.builder()
                .jobKey("")
                .owner("testOwner")
                .taskParam("testParam")
                .build();

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                () -> jobTriggerService.triggerJob(request));
        assertEquals("任务Key不能为空", exception.getMessage());
        verify(nrJobService, never()).triggerJob(any());
    }

    @Test
    @DisplayName("触发任务 - 空owner")
    void testTriggerJob_EmptyOwner() {
        // Given
        JobTriggerRequest request = JobTriggerRequest.builder()
                .jobKey("testJobKey")
                .owner("")
                .taskParam("testParam")
                .build();

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                () -> jobTriggerService.triggerJob(request));
        assertEquals("负责人不能为空", exception.getMessage());
        verify(nrJobService, never()).triggerJob(any());
    }

    @Test
    @DisplayName("触发任务 - 空taskParam")
    void testTriggerJob_EmptyTaskParam() {
        // Given
        JobTriggerRequest request = JobTriggerRequest.builder()
                .jobKey("testJobKey")
                .owner("testOwner")
                .taskParam("")
                .build();

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                () -> jobTriggerService.triggerJob(request));
        assertEquals("任务参数不能为空", exception.getMessage());
        verify(nrJobService, never()).triggerJob(any());
    }

    @Test
    @DisplayName("异步触发任务 - 成功场景")
    void testTriggerJobAsync_Success() {
        // Given
        Result<String> successResult = Result.success("job_triggered_successfully");

        when(nrJobService.triggerJob(any(TriggerJobRequestDTO.class)))
                .thenReturn(successResult);

        // When
        JobTriggerResponse result = jobTriggerService.triggerJobAsync(testRequest);

        // Then
        assertNotNull(result);
        assertEquals("0", result.getCode());
        assertEquals("success", result.getMessage());
        verify(nrJobService, times(1)).triggerJob(any(TriggerJobRequestDTO.class));
    }

    @Test
    @DisplayName("异步触发任务 - 异常场景")
    void testTriggerJobAsync_Exception() {
        // Given
        when(nrJobService.triggerJob(any(TriggerJobRequestDTO.class)))
                .thenThrow(new RuntimeException("异步任务触发失败"));

        // When
        JobTriggerResponse result = jobTriggerService.triggerJobAsync(testRequest);

        // Then
        assertNotNull(result);
        assertEquals("500", result.getCode());
        assertTrue(result.getMessage().contains("任务触发失败"));
        verify(nrJobService, times(1)).triggerJob(any(TriggerJobRequestDTO.class));
    }

    @Test
    @DisplayName("触发任务 - 不同业务模块")
    void testTriggerJob_DifferentBusinessModules() {
        // Given
        String[] businessModules = {"ldu", "org", "sales", "fieldforce"};
        Result<String> successResult = Result.success("job_triggered_successfully");

        when(nrJobService.triggerJob(any(TriggerJobRequestDTO.class)))
                .thenReturn(successResult);

        // When & Then
        for (String module : businessModules) {
            JobTriggerRequest request = JobTriggerRequest.builder()
                    .jobKey("testJobKey")
                    .owner("testOwner")
                    .taskParam("testParam")
                    .businessModule(module)
                    .build();

            JobTriggerResponse result = jobTriggerService.triggerJob(request);
            assertNotNull(result);
            assertEquals("200", result.getCode());
        }

        verify(nrJobService, times(businessModules.length)).triggerJob(any(TriggerJobRequestDTO.class));
    }

    @Test
    @DisplayName("触发任务 - 不同任务类型")
    void testTriggerJob_DifferentJobTypes() {
        // Given
        String[] jobKeys = {"lduExportHandleXxlJob", "orgSyncHandleXxlJob", "salesReportHandleXxlJob"};
        Result<String> successResult = Result.success("job_triggered_successfully");

        when(nrJobService.triggerJob(any(TriggerJobRequestDTO.class)))
                .thenReturn(successResult);

        // When & Then
        for (String jobKey : jobKeys) {
            JobTriggerRequest request = JobTriggerRequest.builder()
                    .jobKey(jobKey)
                    .owner("testOwner")
                    .taskParam("testParam")
                    .build();

            JobTriggerResponse result = jobTriggerService.triggerJob(request);
            assertNotNull(result);
            assertEquals("200", result.getCode());
        }

        verify(nrJobService, times(jobKeys.length)).triggerJob(any(TriggerJobRequestDTO.class));
    }

    @Test
    @DisplayName("触发任务 - 特殊字符参数")
    void testTriggerJob_SpecialCharacters() {
        // Given
        JobTriggerRequest request = JobTriggerRequest.builder()
                .jobKey("testJobKey")
                .owner("<EMAIL>")
                .taskParam("{\"param\":\"test@#$%^&*()_+-=[]{}|;':\",./<>?`~\"}")
                .taskDesc("测试任务描述@#$%")
                .taskName("测试任务@#$%")
                .build();

        Result<String> successResult = Result.success("job_triggered_successfully");

        when(nrJobService.triggerJob(any(TriggerJobRequestDTO.class)))
                .thenReturn(successResult);

        // When
        JobTriggerResponse result = jobTriggerService.triggerJob(request);

        // Then
        assertNotNull(result);
        assertEquals("0", result.getCode());
        verify(nrJobService, times(1)).triggerJob(any(TriggerJobRequestDTO.class));
    }

    @Test
    @DisplayName("触发任务 - 中文参数")
    void testTriggerJob_ChineseCharacters() {
        // Given
        JobTriggerRequest request = JobTriggerRequest.builder()
                .jobKey("testJobKey")
                .owner("测试负责人")
                .taskParam("{\"param\":\"测试参数\"}")
                .taskDesc("测试任务描述")
                .taskName("测试任务名称")
                .projectName("测试项目名称")
                .build();

        Result<String> successResult = Result.success("job_triggered_successfully");

        when(nrJobService.triggerJob(any(TriggerJobRequestDTO.class)))
                .thenReturn(successResult);

        // When
        JobTriggerResponse result = jobTriggerService.triggerJob(request);

        // Then
        assertNotNull(result);
        assertEquals("0", result.getCode());
        verify(nrJobService, times(1)).triggerJob(any(TriggerJobRequestDTO.class));
    }

    @Test
    @DisplayName("触发任务 - 长参数")
    void testTriggerJob_LongParameters() {
        // Given
        String longParam = String.join("", Collections.nCopies(1000, "a"));
        JobTriggerRequest request = JobTriggerRequest.builder()
                .jobKey("testJobKey")
                .owner("testOwner")
                .taskParam(longParam)
                .taskDesc(String.join("", Collections.nCopies(500, "a")))
                .taskName(String.join("", Collections.nCopies(200, "a")))
                .build();

        Result<String> successResult = Result.success("job_triggered_successfully");

        when(nrJobService.triggerJob(any(TriggerJobRequestDTO.class)))
                .thenReturn(successResult);

        // When
        JobTriggerResponse result = jobTriggerService.triggerJob(request);

        // Then
        assertNotNull(result);
        assertEquals("0", result.getCode());
        verify(nrJobService, times(1)).triggerJob(any(TriggerJobRequestDTO.class));
    }

    @Test
    @DisplayName("触发任务 - 边界值测试")
    void testTriggerJob_BoundaryValues() {
        // Given
        JobTriggerRequest request = JobTriggerRequest.builder()
                .jobKey("a")
                .owner("b")
                .taskParam("c")
                .projectId(0L)
                .build();

        Result<String> successResult = Result.success("job_triggered_successfully");

        when(nrJobService.triggerJob(any(TriggerJobRequestDTO.class)))
                .thenReturn(successResult);

        // When
        JobTriggerResponse result = jobTriggerService.triggerJob(request);

        // Then
        assertNotNull(result);
        assertEquals("0", result.getCode());
        verify(nrJobService, times(1)).triggerJob(any(TriggerJobRequestDTO.class));
    }

    @Test
    @DisplayName("触发任务 - 空结果处理")
    void testTriggerJob_NullResult() {
        // Given
        Result<String> nullResult = Result.success(null);

        when(nrJobService.triggerJob(any(TriggerJobRequestDTO.class)))
                .thenReturn(nullResult);

        // When
        JobTriggerResponse result = jobTriggerService.triggerJob(testRequest);

        // Then
        assertNotNull(result);
        assertEquals("0", result.getCode());
        assertEquals("success", result.getMessage());
        verify(nrJobService, times(1)).triggerJob(any(TriggerJobRequestDTO.class));
    }
}
