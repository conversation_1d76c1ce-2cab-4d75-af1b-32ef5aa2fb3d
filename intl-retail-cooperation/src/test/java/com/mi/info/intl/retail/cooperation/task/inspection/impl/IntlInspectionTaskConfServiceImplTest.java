package com.mi.info.intl.retail.cooperation.task.inspection.impl;

import com.mi.info.intl.retail.cooperation.task.config.InspectionConfig;
import com.mi.info.intl.retail.cooperation.task.inspection.PositionService;
import com.mi.info.intl.retail.cooperation.task.infra.entity.IntlInspectionTaskConf;
import com.mi.info.intl.retail.cooperation.task.infra.mapper.read.IntlInspectionTaskConfReadMapper;
import com.mi.info.intl.retail.intlretail.service.api.inspection.dto.NeedCheckInEventTypeResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * IntlInspectionTaskConfServiceImpl新增功能测试类
 * <AUTHOR>
 * @date 2025/1/27
 */
@ExtendWith(MockitoExtension.class)
class IntlInspectionTaskConfServiceImplTest {
    
    @Mock
    private IntlInspectionTaskConfReadMapper readMapper;
    
    @Mock
    private InspectionConfig inspectionConfig;
    
    @Mock
    private PositionService positionService;
    
    @InjectMocks
    private IntlInspectionTaskConfServiceImpl service;
    
    private List<String> defaultEventTypes;
    private IntlInspectionTaskConf taskConf;
    
    @BeforeEach
    void setUp() {
        // 准备测试数据
        defaultEventTypes = Arrays.asList(
                "InspectionDisplay", "Promotions", "MaterielUpload", "StoreSales",
                "SalesUploadIMEI", "SalesUploadQty", "Marketing research", "LDU",
                "StoreTraining", "StockUpload", "StoreInspection", "Material Management",
                "Receiving", "SalesUploadIMEINew"
        );
        
        taskConf = new IntlInspectionTaskConf();
        taskConf.setCountryCode("SG");
        taskConf.setPosInspectionTime(30);
        taskConf.setFrontInspectionTime(45);
    }
    
    @Test
    void testGetNeedCheckInEventTypeList_WithZeroInspectionTime_ShouldReturnDefaultList() {
        // 准备测试数据 - 测试阵地巡检时长为0的情况
        String country = "SG";
        String positionCode = "POS001";
        Integer type = 1;
        
        // Mock配置
        when(inspectionConfig.getDefaultCheckInEventTypes()).thenReturn(defaultEventTypes);
        when(positionService.getPositionTypeName(positionCode)).thenReturn("pos");
        when(readMapper.selectList(any())).thenReturn(Collections.singletonList(taskConf));
        
        // 设置巡检时长为0
        taskConf.setPosInspectionTime(0);
        
        // 执行测试
        List<String> result = service.getNeedCheckInEventTypeList(country, positionCode, type);
        
        // 验证结果 - 应该返回默认事件列表的内容（但不一定是同一个对象）
        assertEquals(defaultEventTypes.size(), result.size());
        assertTrue(result.containsAll(defaultEventTypes));
        verify(positionService).getPositionTypeName(positionCode);
        verify(inspectionConfig).getDefaultCheckInEventTypes();
    }
    
    @Test
    void testGetNeedCheckInEventTypeList_WithNormalInspectionTime_ShouldReturnFilteredList() {
        // 准备测试数据 - 测试正常巡检时长的情况
        String country = "SG";
        String positionCode = "POS001";
        Integer type = 1;
        List<String> excludeTypes = Arrays.asList("StoreTraining", "LDU");
        
        // Mock配置
        when(inspectionConfig.getDefaultCheckInEventTypes()).thenReturn(defaultEventTypes);
        when(positionService.getPositionTypeName(positionCode)).thenReturn("pos");
        when(readMapper.selectList(any())).thenReturn(Collections.singletonList(taskConf));
        when(inspectionConfig.getExcludeEventTypesByTypeAndCountry(type, country))
                .thenReturn(excludeTypes);
        
        // 执行测试
        List<String> result = service.getNeedCheckInEventTypeList(country, positionCode, type);
        
        // 验证结果
        assertFalse(result.contains("StoreTraining"));
        assertFalse(result.contains("LDU"));
        assertTrue(result.contains("InspectionDisplay"));
        assertTrue(result.contains("Promotions"));
        verify(positionService).getPositionTypeName(positionCode);
        verify(inspectionConfig).getExcludeEventTypesByTypeAndCountry(type, country);
    }
    
    
    @Test
    void testGetNeedCheckInEventTypeList_WithNullType_ShouldUseOriginalConfig() {
        // 准备测试数据
        String country = "SG";
        String positionCode = "POS001";
        Integer type = null;
        List<String> excludeTypes = Arrays.asList("StoreTraining");
        
        // Mock配置
        when(inspectionConfig.getDefaultCheckInEventTypes()).thenReturn(defaultEventTypes);
        when(positionService.getPositionTypeName(positionCode)).thenReturn("pos");
        when(readMapper.selectList(any())).thenReturn(Collections.singletonList(taskConf));
        when(inspectionConfig.getExcludeEventTypesByTypeAndCountry(type, country))
                .thenReturn(excludeTypes);
        
        // 执行测试
        List<String> result = service.getNeedCheckInEventTypeList(country, positionCode, type);
        
        // 验证结果
        assertFalse(result.contains("StoreTraining"));
        verify(inspectionConfig).getExcludeEventTypesByTypeAndCountry(type, country);
    }
    
    @Test
    void testGetNeedCheckInEventTypeList_WithNonPosPositionType_ShouldUseFrontInspectionTime() {
        // 准备测试数据
        String country = "SG";
        String positionCode = "FRONT001";
        Integer type = 1;
        
        // Mock配置
        when(inspectionConfig.getDefaultCheckInEventTypes()).thenReturn(defaultEventTypes);
        when(positionService.getPositionTypeName(positionCode)).thenReturn("front");
        when(readMapper.selectList(any())).thenReturn(Collections.singletonList(taskConf));
        
        // 设置front巡检时长为0
        taskConf.setFrontInspectionTime(0);
        
        // 执行测试
        List<String> result = service.getNeedCheckInEventTypeList(country, positionCode, type);
        
        // 验证结果 - 应该返回默认事件列表的内容
        assertEquals(defaultEventTypes.size(), result.size());
        assertTrue(result.containsAll(defaultEventTypes));
    }
    
    @Test
    void testGetNeedCheckInEventTypeList_WithException_ShouldReturnDefaultList() {
        // 准备测试数据
        String country = "SG";
        String positionCode = "POS001";
        Integer type = 1;
        
        // Mock配置
        when(inspectionConfig.getDefaultCheckInEventTypes()).thenReturn(defaultEventTypes);
        when(positionService.getPositionTypeName(positionCode))
                .thenThrow(new RuntimeException("Position service error"));
        when(inspectionConfig.getExcludeEventTypesByTypeAndCountry(type, country))
                .thenReturn(Collections.emptyList());
        
        // 执行测试
        List<String> result = service.getNeedCheckInEventTypeList(country, positionCode, type);
        
        // 验证结果 - 异常时应该返回默认列表的内容
        assertEquals(defaultEventTypes.size(), result.size());
        assertTrue(result.containsAll(defaultEventTypes));
        verify(positionService).getPositionTypeName(positionCode);
        verify(inspectionConfig).getExcludeEventTypesByTypeAndCountry(type, country);
    }
    
    @Test
    void testGetNeedCheckInEventTypeListWithEventTypeList_ShouldReturnBothLists() {
        // 准备测试数据
        String country = "SG";
        String positionCode = "POS001";
        Integer type = 1;
        List<String> excludeTypes = Arrays.asList("StoreTraining", "LDU");
        List<String> eventTypeList = Arrays.asList("CHECK_OUT", "STORE_CHECK");
        
        // Mock配置
        when(inspectionConfig.getDefaultCheckInEventTypes()).thenReturn(defaultEventTypes);
        when(positionService.getPositionTypeName(positionCode)).thenReturn("pos");
        when(readMapper.selectList(any())).thenReturn(Collections.singletonList(taskConf));
        when(inspectionConfig.getExcludeEventTypesByTypeAndCountry(type, country))
                .thenReturn(excludeTypes);
        when(inspectionConfig.getEventTypeList()).thenReturn(eventTypeList);
        
        // 执行测试
        NeedCheckInEventTypeResponse result = service.getNeedCheckInEventTypeListWithEventTypeList(country, positionCode, type);
        
        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getMenuEventTypeList());
        assertNotNull(result.getEventTypeList());
        
        // 验证menuEventTypeList（原逻辑）
        assertFalse(result.getMenuEventTypeList().contains("StoreTraining"));
        assertFalse(result.getMenuEventTypeList().contains("LDU"));
        assertTrue(result.getMenuEventTypeList().contains("InspectionDisplay"));
        assertTrue(result.getMenuEventTypeList().contains("Promotions"));
        
        // 验证eventTypeList（新增）
        assertEquals(eventTypeList, result.getEventTypeList());
        assertTrue(result.getEventTypeList().contains("CHECK_OUT"));
        assertTrue(result.getEventTypeList().contains("STORE_CHECK"));
        
        // 验证方法调用
        verify(inspectionConfig).getDefaultCheckInEventTypes();
        verify(positionService).getPositionTypeName(positionCode);
        verify(inspectionConfig).getExcludeEventTypesByTypeAndCountry(type, country);
        verify(inspectionConfig).getEventTypeList();
    }
    
    @Test
    void testGetNeedCheckInEventTypeListWithEventTypeList_WithEmptyMenuList_ShouldStillReturnEventTypeList() {
        // 准备测试数据
        String country = "SG";
        String positionCode = "POS001";
        Integer type = 1;
        List<String> eventTypeList = Arrays.asList("CHECK_OUT", "STORE_CHECK");
        
        // Mock配置 - 模拟menuEventTypeList为空的情况
        when(inspectionConfig.getDefaultCheckInEventTypes()).thenReturn(Collections.emptyList());
        when(positionService.getPositionTypeName(positionCode)).thenReturn("pos");
        when(readMapper.selectList(any())).thenReturn(Collections.singletonList(taskConf));
        when(inspectionConfig.getExcludeEventTypesByTypeAndCountry(type, country))
                .thenReturn(Collections.emptyList());
        when(inspectionConfig.getEventTypeList()).thenReturn(eventTypeList);
        
        // 执行测试
        NeedCheckInEventTypeResponse result = service.getNeedCheckInEventTypeListWithEventTypeList(country, positionCode, type);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.getMenuEventTypeList().isEmpty());
        assertFalse(result.getEventTypeList().isEmpty());
        assertEquals(eventTypeList, result.getEventTypeList());
        
        // 验证方法调用
        verify(inspectionConfig).getEventTypeList();
    }
}
