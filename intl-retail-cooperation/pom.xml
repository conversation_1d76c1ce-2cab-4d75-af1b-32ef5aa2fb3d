<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.mi.info.intl.retail</groupId>
        <artifactId>intl-retail</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>
    
    <artifactId>intl-retail-cooperation</artifactId>
    
    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <!-- 内部模块依赖 -->
        <dependency>
            <groupId>com.mi.info.intl.retail</groupId>
            <artifactId>intl-retail-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.mi.info.intl.retail</groupId>
            <artifactId>intl-retail-common</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- 任务调度相关依赖 -->
        <dependency>
            <groupId>com.xiaomi.nr</groupId>
            <artifactId>nr-job-api</artifactId>
            <version>2.6.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.xiaomi.nr</groupId>
            <artifactId>nr-job-core</artifactId>
            <version>2.6.0-SNAPSHOT</version>
        </dependency>

        <!-- RPC相关依赖 -->
        <dependency>
            <groupId>com.xiaomi.youpin</groupId>
            <artifactId>youpin-infra-rpc</artifactId>
        </dependency>

        <!-- 工具类依赖 -->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.8.22</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
        </dependency>

        <!-- Spring相关依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
        </dependency>

        <!-- Dubbo相关依赖 -->
        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo-spring-boot-starter</artifactId>
        </dependency>

        <!-- 数据库相关依赖 -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-annotation</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>

        <!-- 消息队列相关依赖 -->
        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-spring-boot-starter</artifactId>
            <version>2.2.0-mdh2.2.7-RELEASE</version>
        </dependency>

        <!-- 其他工具依赖 -->
        <dependency>
            <groupId>ma.glasnost.orika</groupId>
            <artifactId>orika-core</artifactId>
            <version>1.5.2</version>
        </dependency>
        <dependency>
            <groupId>com.xiaomi.cnzone</groupId>
            <artifactId>brain-platform-api</artifactId>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.xiaomi.cnzone.commons</groupId>-->
<!--            <artifactId>commons-util</artifactId>-->
<!--            <version>3.7-SP6</version>-->
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <groupId>org.apache.logging.log4j</groupId>-->
<!--                    <artifactId>log4j-slf4j-impl</artifactId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
<!--        </dependency>-->

        <!-- 日志相关 -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>

        <!-- 测试依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.mi.info.intl.retail</groupId>
            <artifactId>intl-retail-inner-api</artifactId>
            <version>${project.version}</version>
        </dependency>

    </dependencies>

</project>