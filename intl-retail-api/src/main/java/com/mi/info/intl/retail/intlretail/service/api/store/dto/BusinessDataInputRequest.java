package com.mi.info.intl.retail.intlretail.service.api.store.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.models.auth.In;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

@Setter
@Getter
public class BusinessDataInputRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("Region")
    private String region;

    @JsonProperty("ChannelTypeList")
    private List<Integer> channelTypeList;

    /**
     * 阵地类型
     */
    @JsonProperty("PositionTypeList")
    private List<Integer> positionTypeList;

    @JsonProperty("RetailerCodeList")
    private List<String> retailerCodeList;
    /**
     * 阵地编码列表
     */
    @JsonProperty("PositionCodeList")
    private List<String> positionCodeList;

    @JsonProperty("TitleCodeList")
    private List<Integer> titleCodeList;

    @JsonProperty("MidList")
    private List<String> midList;

    @JsonProperty("StoreGradeList")
    private List<String> storeGradeList;

    @JsonProperty("IsPromotion")
    private Integer isPromotion;
    /**
     * 类型 1: 促销员 2: 督导 3: 阵地+人
     */
    @JsonProperty("Type")
    private Integer type;

    /**
     * 门店类型列表
     */
    @JsonProperty("StoreType")
    private List<String> storeType;

    /**
     * 门店编码列表
     */
    @JsonProperty("StoreCodes")
    private List<String> storeCodes;

    /**
     * 门店编码列表
     */
    @JsonProperty("Project")
    private List<String> project;

    /**
     * 区域编码-短码
     */
    @JsonProperty("AreaList")
    private List<String> areaList;
    /**
     * 国家编码-短码
     */
    @JsonProperty("CountryCodeList")
    private List<String> countryCodeList;
}
