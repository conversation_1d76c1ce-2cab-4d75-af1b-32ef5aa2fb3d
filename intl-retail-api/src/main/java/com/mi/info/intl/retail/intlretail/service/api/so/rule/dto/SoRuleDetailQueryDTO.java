package com.mi.info.intl.retail.intlretail.service.api.so.rule.dto;

import java.util.List;

import com.mi.info.intl.retail.bean.BasePage;
import com.mi.info.intl.retail.model.TimeRange;
import com.xiaomi.mone.docs.annotations.dubbo.ApiDocClassDefine;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025/7/24 19:33
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SoRuleDetailQueryDTO extends BasePage {

    private static final long serialVersionUID = -8989871589811712350L;

    /**
     * id，可模糊查询
     */
    @ApiDocClassDefine("id")
    private String id;

    /**
     * 国家编码（多选）
     */
    @ApiDocClassDefine("国家编码")
    private List<String> countryCodeList;

    /**
     * 生效时间戳 - 范围
     */
    @ApiDocClassDefine("生效时间戳，起止时间")
    private TimeRange effectiveTime;

    /**
     * 状态 has pending approval ( yes , no) <br/>
     * - 审批单状态为Pending, Rejected，Has pending approval为YES <br/>
     * - 审批单状态为Approved，Recalled，Has pending approval为NO <br/>
     *
     */
    @ApiDocClassDefine("状态")
    private Integer status;

    /**
     * 创建时间，起始时间范围
     */
    @ApiDocClassDefine("创建时间")
    private TimeRange createTime;

    /**
     * 创建人
     */
    @ApiDocClassDefine("创建人")
    private String createUser;

    /**
     * 创建人miId列表
     */
    @ApiDocClassDefine("创建人miId列表，多选")
    private List<String> createUsers;
}
