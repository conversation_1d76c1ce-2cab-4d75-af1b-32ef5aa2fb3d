package com.mi.info.intl.retail.intlretail.service.api.so.upload;

import com.mi.info.intl.retail.intlretail.service.api.so.dto.GetImportLogListRequest;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.GetImportLogListResponse;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.GetImportTemplateRequest;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.GetImportTemplateResponse;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.ImportDataRequest;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.ImportDataResponse;
import com.mi.info.intl.retail.model.CommonApiResponse;

public interface SoImportService {

    /**
     * 导入模板下载
     *
     * @param request 下载请求
     * @return 模板文件链接
     */
    CommonApiResponse<GetImportTemplateResponse> getImportTemplate(GetImportTemplateRequest request);

    /**
     * 导入数据提交
     *
     * @param request 导入请求
     * @return 导入结果
     */
    CommonApiResponse<ImportDataResponse> importData(ImportDataRequest request);

    /**
     * 导入结果查询
     *
     * @param request 查询请求
     * @return 导入日志列表
     */
    CommonApiResponse<GetImportLogListResponse> getImportLogList(GetImportLogListRequest request);
}
