package com.mi.info.intl.retail.intlretail.service.api.ldu.dto;

import lombok.Data;

import java.util.List;

/**
 * 产品搜索请求对象
 * 
 * <AUTHOR>
 * @date 2025/1/27
 */
@Data
public class SearchProductReq {
    
    /**
     * 搜索关键词（单个查询时使用）
     */
    private String keyword;
    
    /**
     * 搜索关键词列表（批量查询时使用）
     */
    private List<String> keywords;

    /**
     * 查询类型：FUZZY-模糊查询，EXACT-精确查询
     * 默认为模糊查询
     */
    private String searchType = "FUZZY";
}
