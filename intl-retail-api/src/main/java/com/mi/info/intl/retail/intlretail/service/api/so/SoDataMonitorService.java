package com.mi.info.intl.retail.intlretail.service.api.so;

import com.mi.info.intl.retail.intlretail.service.api.so.dto.ImeiMonitorDto;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.QtyMonitorDto;

import java.util.List;

public interface SoDataMonitorService {


    /**
     * 通过RmsId批量获取Imei数据
     * @param rmsIdList rms系统imei表的Id
     * @return
     */
    List<ImeiMonitorDto> getImeiList(List<String> rmsIdList);

    List<QtyMonitorDto> getQtyList(List<String> rmsIdList);

}
