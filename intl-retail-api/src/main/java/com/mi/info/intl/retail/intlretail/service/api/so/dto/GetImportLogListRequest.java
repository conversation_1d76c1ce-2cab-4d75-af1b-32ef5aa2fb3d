package com.mi.info.intl.retail.intlretail.service.api.so.dto;

import com.mi.info.intl.retail.model.BasePageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 导入结果查询请求DTO
 *
 * <AUTHOR> Assistant
 * @date 2025/09/29
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GetImportLogListRequest extends BasePageRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 国家短代码
     */
    private String countryCode;

    /**
     * 用户GUID
     */
    private String userId;

    /**
     * 用户mid
     */
    private Long miId;

    /**
     * 职位code
     */
    private Long userTitle;

    /**
     * 模糊查询任务名称
     */
    private String taskName;

    /**
     * 子应用名称
     */
    private List<String> dataSource;

    /**
     * 导入日志id
     */
    private Long logId;

    /**
     * 状态：0-处理中、1-成功、2-失败
     */
    private List<Integer> status;

    /**
     * 导入耗时：0-30s(0), 30s-1min(1), 1min-5min(2), 5min-10min(3), >10min(4)
     */
    private List<Integer> importDuration;

    /**
     * 操作开始时间（时间戳）
     */
    private Long operationStartTime;

    /**
     * 操作结束时间（时间戳）
     */
    private Long operationEndTime;

    /**
     * 导入类型：QTY(1), IMEI(2)
     */
    private List<Integer> importTypes;
}
