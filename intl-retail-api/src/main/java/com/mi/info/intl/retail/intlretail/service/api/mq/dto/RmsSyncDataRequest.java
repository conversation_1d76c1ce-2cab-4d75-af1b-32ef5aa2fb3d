package com.mi.info.intl.retail.intlretail.service.api.mq.dto;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

@Accessors(chain = true)
@Data
public class RmsSyncDataRequest implements Serializable {

    private static final long serialVersionUID = -4235088978698575427L;

    private List<JSONObject> dataList;

    /**
     * 取值：imei\qty
     */
    private String type;

    /**
     * 取值：
     * create
     * report verification
     * activate verification
     */
    private String operateType;

    private List<String> fields;
}
