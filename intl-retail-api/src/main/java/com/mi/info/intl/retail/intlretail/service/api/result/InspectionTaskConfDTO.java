package com.mi.info.intl.retail.intlretail.service.api.result;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.mi.info.intl.retail.advice.excel.convert.BooleanStringConverter;
import com.mi.info.intl.retail.advice.excel.convert.ListStringConverter;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/12
 **/
@Data
@ExcelIgnoreUnannotated
public class InspectionTaskConfDTO {
    /**
     * 巡检任务ID
     */
    @ExcelProperty("Inspection Task ID")
    private Integer id;

    /**
     * 是否新品大促期间
     */
    @ExcelProperty(value = "Is New Product Promotion", converter = BooleanStringConverter.class)
    private Boolean isNewProductPromotion;

    /**
     * 区域
     */
    @ExcelProperty("Region")
    private String region;

    /**
     * 国家
     */
    @ExcelProperty("Country/Region")
    private String country;

    /**
     * S级巡检频次
     */
    @ExcelProperty("S-grade Store Inspection Frequency")
    private String sStoreInspectionFrequency;

    /**
     * a级门店巡检频次,格式 x月x次
     */
    @ExcelProperty("A-grade Store Inspection Frequency")
    private String aStoreInspectionFrequency;

    /**
     * b级门店巡检频次,格式 x月x次
     */
    @ExcelProperty("B-grade Store Inspection Frequency")
    private String bStoreInspectionFrequency;

    /**
     * c级门店巡检频次,格式 x月x次
     */
    @ExcelProperty("C-grade Store Inspection Frequency")
    private String cStoreInspectionFrequency;

    /**
     * d级门店巡检频次,格式 x月x次
     */
    @ExcelProperty("D-grade Store Inspection Frequency")
    private String dStoreInspectionFrequency;

    /**
     * 有促阵地巡检动作
     */
    @ExcelProperty(value = "PC POS Inspection Actions",  converter = ListStringConverter.class)
    private List<String> hasPromoterFrontInspectionAction;

    /**
     * 无促阵地巡检动作
     */
    @ExcelProperty(value = "NPC POS Inspection Actions",  converter = ListStringConverter.class)
    private List<String> noPromoterFrontInspectionAction;

    /**
     * 有促售点巡检动作
     */
    @ExcelProperty(value = "PC Position Inspection Actions",  converter = ListStringConverter.class)
    private List<String> hasPromoterPosInspectionAction;

    /**
     * 无售点地巡检动作
     */
    @ExcelProperty(value = "NPC Position Inspection Actions",  converter = ListStringConverter.class)
    private List<String> noPromoterPosInspectionAction;

    /**
     * 阵地巡检时长分钟
     */
    @ExcelProperty("Position Inspection Duration (min)")
    private Integer frontInspectionTime;

    /**
     * POS巡检时长分钟
     */
    @ExcelProperty("POS Inspection Duration (min)")
    private Integer posInspectionTime;

    /**
     * 职位
     */
    @ExcelProperty("User Title")
    private String userTitle;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty("Created Time")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty("Last modified time")
    private Date updateTime;

    /**
     * 新品任务
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty("New Product Start Time")
    private Date promotionStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty("New Product End Time")
    private Date promotionEndTime;

    /**
     * 是否禁用
     */
    private Boolean isDisabled;

    /**
     * 新品任务完成动作标准
     */
    @ExcelProperty("New Product Task Completion Action Standard")
    private String newProductTaskCompletionActionStandard;


    /**
     * 新品任务完成时间
     */
    @ExcelProperty("Duration of new products in store")
    private Integer durationOfNewProductsInStore;
}
