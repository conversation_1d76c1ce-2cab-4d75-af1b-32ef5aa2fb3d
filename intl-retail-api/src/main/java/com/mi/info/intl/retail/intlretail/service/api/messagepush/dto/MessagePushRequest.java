package com.mi.info.intl.retail.intlretail.service.api.messagepush.dto;

import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

@Setter
@Getter
public class MessagePushRequest implements Serializable {
    private List<String> userAccountList;
    private String title;
    private String content;
    private String pageUrl;
}
