package com.mi.info.intl.retail.intlretail.service.api.proxy;

import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.PopWindowDTO;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.taskcenter.ConfirmPopWindowsReq;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.taskcenter.HeadCalendarReq;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.taskcenter.TaskCentterPushReq;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.taskcenter.QueryCentralBrainCardsRequest;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.taskcenter.QueryCentralBrainListRequest;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.taskcenter.SupervisorTaskHttpReq;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.taskcenter.TaskCenterCalendarReq;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.taskcenter.TaskCenterCommonReq;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.taskcenter.TaskCenterDetailReq;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.taskcenter.TaskCenterFinishTaskReq;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.taskcenter.TaskCenterInspectionConfReq;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.taskcenter.TaskCenterTaskNumReq;

public interface TaskCenterProxyService {

    Object queryCentralBrainList(QueryCentralBrainListRequest request);

    Object queryCentralBrainCards(QueryCentralBrainCardsRequest request);

    Object querySupervisorTaskCards(SupervisorTaskHttpReq request);

    Object queryAchRateCards(SupervisorTaskHttpReq request);

    Object queryTaskStoreList(SupervisorTaskHttpReq request);

    Object queryPositionTaskList(SupervisorTaskHttpReq request);

    Object buildAchRateTaskList(SupervisorTaskHttpReq request);

    /**
     * 获取视图列表(日/周/月)
     */
    Object getCalendarByType(TaskCenterCalendarReq req);

    /**
     * 无需完成
     */
    void noNeedCompleteTask(TaskCenterFinishTaskReq req);

    /**
     * 完成非只读任务
     */
    String finishOuterTask(TaskCenterFinishTaskReq req);
    /**
     * 任务数量统计
     */
    Object queryTaskNum(TaskCenterTaskNumReq req);
    
    /**
     * 任务数量统计
     */
    Object queryNewProductTaskNum(TaskCenterTaskNumReq req);

    /**
     * 任务详情
     */
    Object getDetailTaskInfo(TaskCenterDetailReq req);

    /**
     * 事件详情
     */
    Object getDetailTaskEventInfo(TaskCenterDetailReq req);

    /**
     * 弹窗任务列表
     */
    PopWindowDTO getPopWindowContent(TaskCenterCommonReq req);

    /**
     * 确认弹窗
     */
    void confirmPopWindow(ConfirmPopWindowsReq req);

    /**
     * 获取总部角色视图列表
     */
    Object getCalendarForHead(HeadCalendarReq req);

    Object getEventCalendarByType(TaskCenterCalendarReq req);
    
    Object queryEventTaskNum(TaskCenterTaskNumReq req);

    Object queryNewProductEventTaskNum(TaskCenterTaskNumReq req);


    /***
     * 获取用户配置
     * @param taskCenterInspectionConfReq
     * @return
     */
    Object getInspectionConfTask(TaskCenterInspectionConfReq taskCenterInspectionConfReq);
}
