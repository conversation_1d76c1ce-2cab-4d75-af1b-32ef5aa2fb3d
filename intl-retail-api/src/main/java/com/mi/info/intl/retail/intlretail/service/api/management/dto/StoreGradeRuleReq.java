package com.mi.info.intl.retail.intlretail.service.api.management.dto;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 门店等级规则请求实体
 * 用于接收前端传递的门店等级配置数据
 */
@Data
public class StoreGradeRuleReq {
    
    /**
     * 主键ID，修改时才传入
     */
    private Long id;

    /**
     * 规则主表id列表
     */
    private List<Long> ids;

    /**
     * 类型 提交1  保存2
     */
    private Integer submitType;

    /**
     * 审批业务Key
     */
    private String flowInstId;

    /**
     * 规则日志ID
     */
    private Integer ruleLogId;

    private Integer modificationMethod;
    
    /**
     * 渠道类型
     */
    private Integer channelType;

    /**
     * 渠道类型列表
     */
    private List<Integer> channelTypeList;

    /**
     * 是否是批量修改
     */
    private Boolean hasBatchEdit = false;
    
    /**
     * 国家编码
     */
    private String countryCode;

    /**
     * 国家名称
     */
    private String countryName;

    /**
     * 零售商
     */
    private Retailer retailer;

    /**
     * 零售商列表
     */
    private List<Retailer> retailers;

    /**
     * 零售商代码
     */
    private String retailerCode;

    /**
     * 零售商代码列表
     */
    private List<String> retailerCodes;

    /**
     * 零售商Map
     */
    private Map<String, Long> retailerMap;

    /**
     * 零售商名称
     */
    private String retailerName;
    
    /**
     * S级最小数量
     */
    private Integer sMinCount;
    
    /**
     * A级最小数量
     */
    private Integer aMinCount;
    
    /**
     * B级最小数量
     */
    private Integer bMinCount;
    
    /**
     * C级最小数量
     */
    private Integer cMinCount;

    /**
     * D级最小数量
     */
    private Integer dMinCount;

    /**
     * 跳转申请的页面链接
     */
    private String applicationURL;

    /**
     *全国零售经理
     */
    private String nationalRetailManager;

    /**
     * 文件链接
     */
    private String fileLink;
    
    /**
     * 文件数据(手动上传时文件信息数据)
     */
    private FileData fileData;

    /**
     * 文件内部类
     */
    @Data
    public static class FileData {
        /**
         * 文件名称
         */
        private String fileName;

        /**
         * 文件地址
         */
        private String url;

        /**
         * 文件id
         */
        private Long id;
    }

    /**
     * 零售商内部类
     */
    @Data
    public static class Retailer {
        /**
         * 零售商名称
         */
        private String name;
        
        /**
         * 零售商代码
         */
        private String code;
    }
}
