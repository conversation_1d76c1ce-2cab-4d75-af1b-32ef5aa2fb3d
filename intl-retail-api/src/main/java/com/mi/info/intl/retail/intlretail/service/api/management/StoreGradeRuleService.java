package com.mi.info.intl.retail.intlretail.service.api.management;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.CommonApproveHistoryReq;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.CommonApproveHistoryResp;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.FileTemplateReq;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.RetailerQueryReq;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.RetailerQueryResp;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.RuleModificationLog;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.RuleModifyVersionResp;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.RuleQueryReq;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.StoreGradeExportReq;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.StoreGradeReq;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.StoreGradeResp;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.StoreGradeRuleDetailResp;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.StoreGradeRulePageQueryReq;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.StoreGradeRulePageResp;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.StoreGradeRuleRecallReq;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.StoreGradeRuleReq;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.SubmitResp;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.UploadManuallyRuleDetailsResp;
import com.mi.info.intl.retail.model.CommonApiResponse;

import java.util.List;

/**
 * 门店等级规则服务接口
 */
public interface StoreGradeRuleService {

    /**
     * 保存门店等级规则
     *
     * @param storeGradeRuleReq 门店等级规则请求对象
     * @return 保存结果，成功返回true，失败返回false
     */
    CommonApiResponse<Long> saveStoreGradeRule(StoreGradeRuleReq storeGradeRuleReq);

    /**
     * 批量修改门店等级规则
     *
     * @param storeGradeRuleReq 批量修改门店等级规则请求对象
     * @return 修改结果，成功返回true，失败返回false
     */
    CommonApiResponse<Long> batchEditStoreGradeRule(StoreGradeRuleReq storeGradeRuleReq);

    /**
     * 检测门店等级规则
     *
     * @param storeGradeRuleReq 门店等级规则请求对象
     * @return 检测结果，成功返回true，失败返回false
     */
    CommonApiResponse<Integer> check(StoreGradeRuleReq storeGradeRuleReq);

    /**
     * 查询门店等级规则提交记录详情
     *
     * @param request
     * @return
     */
    CommonApiResponse<StoreGradeRuleDetailResp.RuleDetails> query(RuleQueryReq request);

    /**
     * 查询门店等级规则详情
     *
     * @param request
     * @return
     */
    CommonApiResponse<StoreGradeRuleDetailResp.RuleDetails> queryDetail(RuleQueryReq request);

    /**
     * 获取门店等级规则草稿详情
     *
     * @param request
     * @return
     */
    CommonApiResponse<StoreGradeRuleDetailResp.RuleDetails> queryDraft(RuleQueryReq request);

    /**
     * 查询门店等级规则审批列表
     *
     * @param request
     * @return
     */
    CommonApiResponse<Page<RuleModificationLog>> queryApprovalList(RuleQueryReq request);

    /**
     * 分页查询门店等级规则
     *
     * @param request 分页查询请求
     * @return 分页查询结果
     */
    CommonApiResponse<StoreGradeRulePageResp> pageQuery(StoreGradeRulePageQueryReq request);

    /**
     * 根据ID删除门店等级规则
     *
     * @param id 规则ID
     * @return 删除结果，成功返回true，失败返回false
     */
    CommonApiResponse<Boolean> deleteStoreGradeRule(Long id);

    /**
     * 流程提交
     */
    SubmitResp submit(StoreGradeRuleReq  req);

    /**
     * 重新发起流程
     */
    SubmitResp anewSubmit(StoreGradeRuleReq req);

    /**
     * 流程撤回
     */
    CommonApiResponse<Boolean> recall(StoreGradeRuleRecallReq req);

    /**
     * 查看审批记录
     */
    CommonApiResponse<List<CommonApproveHistoryResp>> listProcessLog(CommonApproveHistoryReq req);
    
    /**
     * 根据retailer和kapa查询Grade信息
     */
    CommonApiResponse<StoreGradeResp> queryStoreGrade(StoreGradeReq req);
    
    /**
     * Store Grade 的直接批量修改和审批
     */
    SubmitResp batchUpdateStoreGrade(StoreGradeRuleReq req);
    
    /**
     * 获取文件模板
     */
    CommonApiResponse<String> getFileTemplate(FileTemplateReq  req);
    
    /**
     *  根据国家编码和渠道查询所有零售商信息
     */
    CommonApiResponse<List<RetailerQueryResp>> queryRetailerList(RetailerQueryReq request);
    
    /**
     * 获取手动上传规则详情
     *
     */
    CommonApiResponse<UploadManuallyRuleDetailsResp> getUploadManuallyRuleDetails(Long ruleId);
    
    /**
     * 导出门店等级规则
     *
     */
    CommonApiResponse<String> exportStoreGradeRule(StoreGradeExportReq request);

}