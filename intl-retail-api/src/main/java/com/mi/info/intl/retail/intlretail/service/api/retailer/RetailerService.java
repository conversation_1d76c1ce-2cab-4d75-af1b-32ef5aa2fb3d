package com.mi.info.intl.retail.intlretail.service.api.retailer;

import com.mi.info.intl.retail.intlretail.service.api.retailer.dto.RetailerAreaResponse;
import com.mi.info.intl.retail.intlretail.service.api.retailer.dto.RetailerExcelValidationContent;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.BusinessDataInputRequest;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.BusinessDataResponse;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.ExcelValidationRequest;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.RetailerInfoRequest;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.RetailerInfoResponse;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.RetailerListRequest;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.RetailerListResponse;

import java.util.List;

public interface RetailerService {

    List<BusinessDataResponse> getOrgPerson(BusinessDataInputRequest request);

   // List<RetailerExcelValidationContent> uploadExcel(MultipartFile file, String region);

    List<RetailerExcelValidationContent> uploadFds(ExcelValidationRequest request);

    RetailerAreaResponse getArea();

    List<RetailerInfoResponse> getRetailerInfo(RetailerInfoRequest request);

    List<RetailerListResponse> getRetailerList(RetailerListRequest request);

}
