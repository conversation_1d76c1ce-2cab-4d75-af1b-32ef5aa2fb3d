package com.mi.info.intl.retail.intlretail.service.api.so.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 导入数据提交请求DTO
 *
 * <AUTHOR>
 * @date 2025/7/31
 */
@Data
public class ImportDataRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 导入源文件id
     */
    private Long fileId;

    /**
     * 导入源文件链接
     */
    private String fileUrl;

    /**
     * 导入类型 1：IMEI 2：QTY
     */
    private Integer type;

    /**
     * 导入动作类型 1：create 2：update
     */
    private Integer actionType;

    /**
     * 用户真实mid
     */
    private Long miId;

    /**
     * 用户真实mid
     */
    private String fileName;

    /**
     * 导入数据类型 1：IMEI 2：QTY
     */
    private Integer dataSource ;
} 