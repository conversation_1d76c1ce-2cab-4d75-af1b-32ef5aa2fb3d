package com.mi.info.intl.retail.intlretail.service.api.ldu.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025-07-04
 */
@Data
public class IntlLduTargetDto implements Serializable {

    private static final long serialVersionUID = -723132109871143210L;

    private Long id;

    /**
     * 区域
     */
    private String region;

    /**
     * 区域编码
     */
    private String regionCode;

    /**
     * 国家
     */
    private String country;

    /**
     * 国家编码
     */
    private String countryCode;


    /**
     * 产品线
     */
    private String productLine;


    private String productLineName;


    private String productLineNameEn;


    /**
     * 项目代码
     */
    private String projectCode;

    /**
     * 项目名称
     */
    private String projectName;



    private String projectNameEn;



    /**
     * 目标创建日期
     */
    private long targetCreateDate;

    /**
     * 目标创建日期(时区转换后)
     */
    private String targetCreateTime;

    /**
     * 目标修改日期
     */
    private long targetUpdateDate;

    /**
     * 目标修改日期(时区转换后)
     */
    private String targetUpdateTime;


    /**
     * 创建人ID
     */
    private String createUserId;

    /**
     * 创建人姓名
     */
    private String createUserName;

    /**
     * 修改人ID
     */
    private String updateUserId;

    /**
     * 修改人ID
     */
    private String updateUserName;

    /**
     * 目标覆盖门店数
     */
    private int targetCoveredStores;

    /**
     * 实际覆盖门店数
     */
    private int actualCoveredStores;

    /**
     * 目标销出样品数
     */
    private int targetSampleOut;

    /**
     * 实际销出样品数
     */
    private int actualSampleOut;
}
