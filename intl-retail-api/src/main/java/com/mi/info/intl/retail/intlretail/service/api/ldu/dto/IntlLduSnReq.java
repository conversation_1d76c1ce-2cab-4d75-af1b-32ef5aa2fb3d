package com.mi.info.intl.retail.intlretail.service.api.ldu.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.mi.info.intl.retail.model.BasePageRequest;
import com.xiaomi.mone.docs.annotations.dubbo.ApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-07-03
 */
@Data
public class IntlLduSnReq extends BasePageRequest implements Serializable {

    private static final long serialVersionUID = 853257008547568237L;

    @JsonProperty("countryCode")
    private List<String> countryCode;

    @JsonProperty("retailerCode")
    private String retailerCode;

    @JsonProperty("productLine")
    private String productLine;

    @JsonProperty("projectCode")
    private String projectCode;

    @JsonProperty("goodsId")
    private String goodsId;

    @JsonProperty("goodsName")
    private String goodsName;

    @JsonProperty("lduType")
    private String lduType;

    @JsonProperty("snImei")
    private String snImei;

    @JsonProperty("sn")
    private String sn;

    @JsonProperty("isReport")
    private Integer isReport;

    @JsonProperty("status")
    private Integer status;


    /**
     * 产品ID
     */
    @JsonProperty("productId")
    @ApiDocClassDefine(value = "产品ID")
    private String productId;

    /**
     * 产品名称
     */
    @JsonProperty("productName")
    @ApiDocClassDefine(value = "产品名称")
    private String productName;
}
