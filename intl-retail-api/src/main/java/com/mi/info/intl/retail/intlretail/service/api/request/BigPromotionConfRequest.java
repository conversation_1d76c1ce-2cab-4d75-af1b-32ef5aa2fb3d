package com.mi.info.intl.retail.intlretail.service.api.request;

import lombok.Data;

import java.util.List;

@Data
public class BigPromotionConfRequest {
    /**
     * id
     */
    private Integer id;
    /**
     * 区域
     */
    private String region;
    /**
     * 区域列表
     */
    private List<String> regionList;
    /**
     * 国家/地区
     */
    private String country;
    /**
     * 国家/地区列表
     */
    private List<String> countryList;
    /**
     * 开始时间
     */
    private String startTime;
    /**
     * 结束时间
     */
    private String endTime;

    private Integer pageNum = 1;

    private Integer pageSize = 10;

}
