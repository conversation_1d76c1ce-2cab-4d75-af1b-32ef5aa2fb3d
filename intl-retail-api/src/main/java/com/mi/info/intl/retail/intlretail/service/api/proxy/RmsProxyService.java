package com.mi.info.intl.retail.intlretail.service.api.proxy;

import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.ResponseEntity;

/**
 * 代理跳转的对外提供服务的service
 * 提供外部依赖跳转的能力
 */
public interface RmsProxyService {

    String requestByUserToken(String path, String data, String userToken, String httpMethod);

    String requestByUserToken(String path, String type, String data, String userToken, String httpMethod);

    ResponseEntity<ByteArrayResource> requestByUserToken(String path, String userToken, String httpMethod);

    String soRetailSyncToRms(String path, String data,  String httpMethod);
}
