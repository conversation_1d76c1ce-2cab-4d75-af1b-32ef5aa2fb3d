package com.mi.info.intl.retail.intlretail.service.api.request;

import com.mi.info.intl.retail.model.BasePageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/12
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class IntlInspectionTaskRequest extends BasePageRequest {

    private List<String> regionList;

    private List<String> countryList;
}
