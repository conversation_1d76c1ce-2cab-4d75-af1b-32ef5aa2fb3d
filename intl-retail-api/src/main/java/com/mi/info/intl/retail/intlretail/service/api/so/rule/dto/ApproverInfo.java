package com.mi.info.intl.retail.intlretail.service.api.so.rule.dto;

import java.io.Serializable;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.mi.info.intl.retail.utils.AESGCMUtil;
import com.xiaomi.mone.docs.annotations.dubbo.ApiDocClassDefine;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * 审批人信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/28 16:27
 */
@Setter
@Getter
@EqualsAndHashCode
public class ApproverInfo implements Serializable {
    /**
     * 用户id
     */
    @ApiDocClassDefine(value = "用户id")
    private String userId;

    /**
     * 用户名称
     */
    @ApiDocClassDefine(value = "用户名称")
    private String userName;

    /**
     * 用户头像
     */
    @ApiDocClassDefine(value = "用户头像")
    private String pictureUrl;

    @JsonIgnore
    @JSONField(serialize = false)
    public String getDisPlayName() {
        return AESGCMUtil.decryptGCM(userName) + "(" + userId + ")";
    }
}
