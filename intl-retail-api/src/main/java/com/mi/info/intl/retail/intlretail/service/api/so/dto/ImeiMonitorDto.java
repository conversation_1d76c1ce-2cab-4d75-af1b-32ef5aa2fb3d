package com.mi.info.intl.retail.intlretail.service.api.so.dto;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

@Builder
@Data
public class ImeiMonitorDto implements Serializable {

    private Long retailId;

    private String rmsId;

    /**
     * 验证结果（0：验证中；1：验证通过；2：验证失败）
     */
    private Integer verificationResult;


    private Integer statusCode;


    /**
     * SI校验结果
     */
    private Integer siVerifyResult;

}
