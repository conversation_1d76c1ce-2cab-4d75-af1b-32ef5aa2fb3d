package com.mi.info.intl.retail.intlretail.service.api.so.rule.dto;

import java.io.Serializable;
import java.util.List;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xiaomi.mone.docs.annotations.dubbo.ApiDocClassDefine;

import lombok.*;

/**
 * 批准者DTO
 *
 * <AUTHOR>
 * @date 2025/08/05
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class ApproverDTO implements Serializable {

    /**
     * 节点名称
     */
    @ApiDocClassDefine(value = "节点名称")
    private String nodeName;

    /**
     * 节点排序
     */
    @ApiDocClassDefine(value = "节点排序")
    private Integer sort;

    /**
     * 节点状态 ，参考 SoRuleDetailApproveStatus
     */
    @ApiDocClassDefine(value = "节点状态")
    private Integer status;

    /**
     * 节点状态标签
     */
    @ApiDocClassDefine(value = "节点状态标签")
    private String statusLabel;

    /**
     * 审批备注信息
     */
    @ApiDocClassDefine(value = "审批备注信息，如驳回原因")
    private String comment;

    /**
     * 审批时间
     */
    @ApiDocClassDefine(value = "审批时间")
    private Long approveTime;

    /**
     * 审批时间，格式化用于展示
     */
    @ApiDocClassDefine(value = "审批时间，格式化用于展示")
    private String approveTimeStr;

    /**
     * 审批人信息
     */
    @ApiDocClassDefine(value = "审批人信息")
    private List<ApproverInfo> approverList;

    /**
     * 审批人邮箱前缀，后端用，不参与序列化。
     */
    @JsonIgnore
    @JSONField(serialize = false)
    @ApiDocClassDefine(value = "审批人邮箱前缀")
    private String approverEmailPrefix;

}
