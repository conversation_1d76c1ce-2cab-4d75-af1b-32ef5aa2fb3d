package com.mi.info.intl.retail.intlretail.service.api.so.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 导入结果查询响应DTO
 *
 * <AUTHOR> Assistant
 * @date 2025/09/29
 */
@Data
public class GetImportLogListResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 总数
     */
    private Integer total;

    /**
     * 当前页码
     */
    private Integer currentPage;

    /**
     * 每页数量
     */
    private Integer pageSize;

    /**
     * 日志数据列表
     */
    private List<ImportLogRecord> records;

    /**
     * 导入日志记录
     */
    @Data
    public static class ImportLogRecord implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 任务名称
         */
        private String taskName;

        /**
         * 来源
         */
        private String dataSource;

        /**
         * 导入类型：QTY(1), IMEI(2)
         */
        private Integer importType;

        /**
         * 文件名称
         */
        private String fileName;

        /**
         * 状态：0-处理中、1-成功、2-失败
         */
        private Integer status;

        /**
         * 进度
         */
        private Integer importProgress;

        /**
         * 预估时间：0-30s(0), 30s-1min(1), 1min-5min(2), 5min-10min(3), >10min(4)
         */
        private Integer importDuration;

        /**
         * 操作人名称
         */
        private String operator;

        /**
         * 操作时间
         */
        private Long operationTime;

        /**
         * 操作类型
         */
        private Integer action;

        /**
         * 文件链接
         */
        private String fileUrl;
    }
}
