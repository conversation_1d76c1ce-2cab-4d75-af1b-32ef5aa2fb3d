<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>intl-retail</artifactId>
        <groupId>com.mi.info.intl.retail</groupId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>
    <artifactId>intl-retail-api</artifactId>
    <modelVersion>4.0.0</modelVersion>
    <version>0.0.1-SNAPSHOT</version>
    <packaging>jar</packaging>
    <properties>
        <java.version>1.8</java.version>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <neptune_env>test</neptune_env>
        <nr.job.api>2.6.0-SNAPSHOT</nr.job.api>
        <nr.job.core>2.6.0-SNAPSHOT</nr.job.core>
        <miapi-doc-annos.version>2.7.12-mone-v20-SNAPSHOT</miapi-doc-annos.version>
    </properties>

    <distributionManagement>
        <repository>
            <id>central</id>
            <name>maven-release-virtual</name>
            <url>https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <name>maven-snapshot-virtual</name>
            <url>https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual</url>
        </snapshotRepository>
    </distributionManagement>

    <dependencies>
        <dependency>
            <artifactId>market-api</artifactId>
            <groupId>market</groupId>
            <version>1.1.0</version>
        </dependency>
        <dependency>
            <groupId>com.mi.info.intl.retail</groupId>
            <artifactId>intl-retail-common</artifactId>
            <version>${project.version}</version>
        </dependency>
        <!-- Removed duplicate dubbo-docs-core dependency -->

        <dependency>
            <groupId>com.xiaomi.mit</groupId>
            <artifactId>mit-starter</artifactId>
            <version>1.2.9</version>
        </dependency>

        <dependency>
            <groupId>com.xiaomi.cnzone</groupId>
            <artifactId>storems-api</artifactId>
            <version>1.2-RELEASE</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <artifactId>dubbo-docs-annotations</artifactId>
                    <groupId>com.xiaomi.mone</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.xiaomi.cnzone</groupId>
            <artifactId>storems-common</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.xiaomi.nr</groupId>
            <artifactId>copilot-gl-api</artifactId>
            <version>0.0.1-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.xiaomi.youpin</groupId>
                    <artifactId>youpin-infra-rpc</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.xiaomi.youpin</groupId>
                    <artifactId>api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.xiaomi.cnzone</groupId>
            <artifactId>store-api</artifactId>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.xiaomi.cnzone</groupId>
            <artifactId>maindata-api</artifactId>
            <version>1.9.1-SNAPSHOT</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <artifactId>dubbo-docs-annotations</artifactId>
                    <groupId>com.xiaomi.mone</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 任务中心 任务创建相关-->
        <dependency>
            <groupId>com.xiaomi.nr</groupId>
            <artifactId>nr-job-api</artifactId>
            <version>${nr.job.api}</version>
        </dependency>
        <!-- 任务中心 任务实现相关-->
        <dependency>
            <groupId>com.xiaomi.nr</groupId>
            <artifactId>nr-job-core</artifactId>
            <version>${nr.job.core}</version>
        </dependency>

        <!-- miapi 相关 -->
        <dependency>
            <groupId>com.xiaomi.mone</groupId>
            <artifactId>miapi-doc-annos</artifactId>
            <version>${miapi-doc-annos.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>jakarta.validation</groupId>
            <artifactId>jakarta.validation-api</artifactId>
            <version>2.0.2</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.mi.info.intl.retail</groupId>
            <artifactId>intl-retail-core</artifactId>
        </dependency>


        <dependency>
            <groupId>com.xiaomi.cnzone</groupId>
            <artifactId>proretail-training-api</artifactId>
            <version>1.6-RELEASE</version>
            <exclusions>
                <exclusion>
                    <artifactId>i18n-area-java-sdk</artifactId>
                    <groupId>com.xiaomi.nr</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.mi.xms</groupId>
                    <artifactId>neptune-client-starter</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.larksuite.oapi</groupId>
            <artifactId>oapi-sdk</artifactId>
            <version>2.3.6</version>
            <optional>true</optional>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.6.1</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <testSource>${java.version}</testSource>
                    <testTarget>${java.version}</testTarget>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>2.10.4</version>
                <configuration>
                    <encoding>${project.build.sourceEncoding}</encoding>
                    <aggregate>true</aggregate>
                    <charset>${project.build.sourceEncoding}</charset>
                    <docencoding>${project.build.sourceEncoding}</docencoding>
                </configuration>
                <executions>
                    <execution>
                        <id>attach-javadocs</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                        <configuration>
                            <additionalparam>-Xdoclint:none</additionalparam>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.0.1</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>