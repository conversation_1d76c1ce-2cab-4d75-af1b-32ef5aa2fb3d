<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.mi.info.intl.retail</groupId>
        <artifactId>intl-retail</artifactId>
        <version>0.0.1-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>intl-retail-proxy</artifactId>
    <packaging>pom</packaging>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <revision>0.0.1-SNAPSHOT</revision>
    </properties>

    <modules>
        <module>intl-retail-domain</module>
        <module>intl-retail-infra</module>
        <module>intl-retail-app</module>
    </modules>

    <dependencies>
        <dependency>
            <groupId>com.mi.info.intl.retail</groupId>
            <artifactId>intl-retail-inner-api</artifactId>
            <version>0.0.1-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>
</project>