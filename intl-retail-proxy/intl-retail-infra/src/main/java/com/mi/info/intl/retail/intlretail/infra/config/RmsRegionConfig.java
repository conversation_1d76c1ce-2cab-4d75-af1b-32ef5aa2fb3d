package com.mi.info.intl.retail.intlretail.infra.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.stream.JsonReader;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.InputStream;
import java.util.Map;

@Component
public class RmsRegionConfig {
    public static final String FILE_NAME = "rms-env-country.json";

    @Autowired
    private ObjectMapper objectMapper;

    @PostConstruct
    public void init() {
        try (InputStream inputStream = JsonReader.class.getClassLoader().getResourceAsStream(FILE_NAME)) {
            if (inputStream != null) {
                regions = objectMapper.readValue(inputStream, Map.class);
            }
        } catch (Exception e) {
            throw new RuntimeException("Error reading rms-env-country.json: " + e.getMessage(), e);
        }
    }

    @Setter
    @Getter
    private Map<String, String> regions;
}