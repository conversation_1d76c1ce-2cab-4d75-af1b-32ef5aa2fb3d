package com.mi.info.intl.retail.intlretail.infra.rpc;

import com.xiaomi.cnzone.brain.platform.api.model.req.PushTaskReq;
import com.xiaomi.cnzone.brain.platform.api.model.req.admin.CurrentTaskInstanceReq;
import com.xiaomi.cnzone.brain.platform.api.model.req.admin.NewTaskCheckReq;
import com.xiaomi.cnzone.brain.platform.api.model.req.admin.ProretailOuterEventReq;
import com.xiaomi.cnzone.brain.platform.api.model.req.app.*;
import com.xiaomi.cnzone.brain.platform.api.model.resp.admin.NewTaskCheckResp;
import com.xiaomi.cnzone.brain.platform.api.model.resp.app.*;
import com.xiaomi.cnzone.brain.platform.api.provider.BrainPlatformAppProvider;
import com.xiaomi.cnzone.brain.platform.api.provider.BrainPlatformOuterProvider;
import com.xiaomi.cnzone.brain.platform.api.provider.TaskCheckServiceProvider;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.rpc.RpcException;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class TaskCenterServiceAdapter {

    @DubboReference(group = "${center.dubbo.group:}", check = false, timeout = 20000, retries = 0)
    private BrainPlatformAppProvider brainPlatformAppProvider;

    @DubboReference(group = "${center.dubbo.group:}", check = false, timeout = 20000, retries = 0)
    private BrainPlatformOuterProvider brainPlatformOuterProvider;

    @DubboReference(group = "${center.dubbo.group:}", check = false, timeout = 20000, retries = 0)
    private TaskCheckServiceProvider taskCheckServiceProvider;

    /**
     * 获取日视图数据
     */
    public DayCalendarResp getDayCalendar(TaskCalendarReq taskCalendarReq) {
        final Result<DayCalendarResp> dayCalendarRespResult = brainPlatformAppProvider.dayCalendar(taskCalendarReq);
        this.checkResult(dayCalendarRespResult);
        return dayCalendarRespResult.getData();
    }

    /**
     * 无需完成任务接口
     */
    public void noNeedCompleteTask(NoNeedCompleteTaskReq noNeedCompleteTaskReq) {
        Result<Void> noNeedCompleteResult = brainPlatformAppProvider.noNeedCompleteTask(noNeedCompleteTaskReq);
        this.checkResult(noNeedCompleteResult);
    }

    /**
     * 完成非只读任务
     */
    public String finishOuterTask(ProretailOuterEventReq proretailOuterEventReq) {
        final Result<String> outerTaskResult = brainPlatformOuterProvider.outerTaskFinish(proretailOuterEventReq);
        this.checkResult(outerTaskResult);
        return outerTaskResult.getData();
    }

    /**
     * 任务数量统计
     */
    public TaskNumResp queryTaskNum(TaskNumReq taskNumReq) {
        final Result<TaskNumResp> taskNumRespResult = brainPlatformAppProvider.queryTaskNum(taskNumReq);
        this.checkResult(taskNumRespResult);
        return taskNumRespResult.getData();
    }

    /**
     * 任务详情
     */
    public TaskCenterTaskDetailInfoResp getDetailTaskInfo(TaskCenterTaskDetailInfoReq taskDetailInfoReq) {
        final Result<TaskCenterTaskDetailInfoResp> detailTaskInfo = brainPlatformAppProvider.getDetailTaskInfo(taskDetailInfoReq);
        this.checkResult(detailTaskInfo);
        return detailTaskInfo.getData();
    }
    
    /**
     * 事件详情
     */
    public TaskCenterTaskDetailInfoResp getDetailTaskEventInfo(TaskCenterTaskDetailInfoReq taskDetailInfoReq) {
        final Result<TaskCenterTaskDetailInfoResp> detailTaskInfo = brainPlatformAppProvider.getDetailTaskEventInfo(taskDetailInfoReq);
        this.checkResult(detailTaskInfo);
        return detailTaskInfo.getData();
    }

    /**
     * 弹窗任务列表
     */
    public PopWindowResp getPopWindowContent(PopWindowReq popWindowReq) {
        try {
            log.info("getPopWindowContent dubbo:{}", popWindowReq);
            final Result<PopWindowResp> popWindowContent = brainPlatformAppProvider.getPopWindowContent(popWindowReq);
            this.checkResult(popWindowContent);
            return popWindowContent.getData();
        } catch (Exception e) {
            log.error("getPopWindowContent error", e);
            return null;
        }
    }

    /**
     * 确认弹窗记录sult
     */
    public void confirmPopWindow(ConfirmPopWindowReq comfirmPopWindowReq) {
        Result<Void> result = brainPlatformAppProvider.confirmPopWindow(comfirmPopWindowReq);
        this.handlerResult(result, "消息已读确认异常");
    }

    /**
     * 获取追踪任务列表数据
     */
    public TaskTrackingResp taskTracking(TaskTrackingReq taskTrackingReq) {
        Result<TaskTrackingResp> taskTrackingRespResult = brainPlatformAppProvider.taskTracking(taskTrackingReq);
        this.checkResult(taskTrackingRespResult);
        return taskTrackingRespResult.getData();
    }

    public TaskInstanceByTypeResp queryMiIdByType(TaskInstanceReq taskInstanceReq) {
        try {
            log.info("queryMiIdByType taskInstanceReq:{}", taskInstanceReq);
            return brainPlatformOuterProvider.queryMidByType(taskInstanceReq);
        } catch (Exception e) {
            log.error("Error processing result data", e);
            return null;
        }
    }
    
    public String finishUserCurrentTaskInstance(CurrentTaskInstanceReq req) {
        Result<String> taskTrackingRespResult = brainPlatformOuterProvider.finishUserCurrentTaskInstance(req);
        this.checkResult(taskTrackingRespResult);
        return taskTrackingRespResult.getData();
    }

    private void checkResult(Result<?> result) {
        if (result == null) {
            throw new RuntimeException("Task center response is null");
        }
        if (result.getCode() != 0) {
            throw new RuntimeException(result.getMessage());
        }
    }


    private void handlerResult(Result<?> result, String customMessage) {
        if (result.getCode() != 0) {
            // 如果有自定义message就用自定义，否则用result.getMessage()
            String errorMsg = customMessage != null ? customMessage : result.getMessage();
            log.error("Error processing result data:{}", errorMsg);
            throw new RpcException(errorMsg);
        }
    }

    // 兼容老用法
    private void handlerResult(Result<?> result) {
        handlerResult(result, null);
    }
    
    public DayCalendarResp getDayEventCalendar(TaskCalendarReq taskCalendarReq) {
        Result<DayCalendarResp> dayCalendarRespResult = brainPlatformAppProvider.dayCalendarEvent(taskCalendarReq);
        checkResult(dayCalendarRespResult);
        return dayCalendarRespResult.getData();
    }
    
    public TaskNumResp queryEventTaskNum(TaskNumReq taskNumReq) {
        Result<TaskNumResp> taskNumRespResult = brainPlatformAppProvider.queryEventTaskNum(taskNumReq);
        checkResult(taskNumRespResult);
       return taskNumRespResult.getData();
    }

    public List<NewTaskCheckResp> checkNewTaskWithoutRule(NewTaskCheckReq newTaskCheckReq) {
        Result<List<NewTaskCheckResp>> newTaskCheckRespResult = taskCheckServiceProvider.checkNewTaskWithoutRule(
                newTaskCheckReq);
        checkResult(newTaskCheckRespResult);
        return newTaskCheckRespResult.getData();
    }
    
    public  BusinessTypeStatusResp queryBusinessTypeTaskNum(TaskNumReq req) {
        Result<BusinessTypeStatusResp> result = brainPlatformAppProvider.queryBusinessTypeTaskNum(req);
        this.checkResult(result);
        return result.getData();
    }
    
    public BusinessTypeStatusResp queryBusinessTypeEventTaskNum(TaskNumReq req) {
        Result<BusinessTypeStatusResp> businessTypeStatusRespResult = brainPlatformAppProvider.queryBusinessTypeEventTaskNum(
                req);
        this.checkResult(businessTypeStatusRespResult);
        return businessTypeStatusRespResult.getData();
    }
}
