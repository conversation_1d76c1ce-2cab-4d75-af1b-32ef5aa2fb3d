package com.mi.info.intl.retail.intlretail.infra.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
@ConfigurationProperties(prefix = "appli-user-token")
public class AppliUserTokenConfig {
    @Setter
    @Getter
    private String url;

    @Setter
    @Getter
    private Map<String, String> resources;

    @Setter
    @Getter
    private List<ClientInfo> clients;

    @Setter
    @Getter
    public static class ClientInfo {
        // Getters and Setters
        private String clientId;
        private String clientSecret;

    }

}
