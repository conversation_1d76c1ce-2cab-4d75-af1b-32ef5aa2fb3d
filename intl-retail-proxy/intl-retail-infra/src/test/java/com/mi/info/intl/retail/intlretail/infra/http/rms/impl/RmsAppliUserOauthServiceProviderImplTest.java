package com.mi.info.intl.retail.intlretail.infra.http.rms.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.mi.info.intl.retail.intlretail.domain.common.utils.JsonUtil;
import com.mi.info.intl.retail.intlretail.domain.http.rms.RmsAppliUserOauthServiceProvider;

import org.assertj.core.api.Assertions;
import org.json.JSONObject;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;

// @SpringBootTest
public class RmsAppliUserOauthServiceProviderImplTest {

    @Resource
    private RmsAppliUserOauthServiceProvider rmsAppliUserOauthServiceProvider;


    // @Test
    public void testHttpForList() throws JsonProcessingException {
        String path = "/api/data/v9.2/new_QueryChannelInfo";
        String jsonString = "{\"name\":\"John\",\"age\":30}";
        ObjectMapper objectMapper = new ObjectMapper();
        String httpMethod = "POST";
        //生成ChannelInfoRmsRequest对应的json对象
        ObjectNode requestBody = JsonUtil.json2bean("{\"Input\":\"{\\\"type\\\":20,\\\"search\\\":\\\"RMSUAT20230427127428\\\",\\\"codes\\\":[]}\"}", ObjectNode.class);

        List<JSONObject> result = rmsAppliUserOauthServiceProvider.httpForList("ID", path, requestBody, httpMethod, JSONObject.class);
        System.out.println(result);
        Assertions.assertThat(result).isNotNull();
    }

    // @Test
    public void testHttpForObject() throws JsonProcessingException {
        String path = "/api/data/v9.2/new_savestoreforchannelretail";
        String httpMethod = "POST";
        ObjectNode requestBody = JsonUtil.json2bean("{\"Input\":\"{\\\"orgId\\\":\\\"IDJM70479\\\",\\\"changeModule\\\":[\\\"orgBase\\\",\\\"orgBuild\\\",\\\"orgCategory\\\",\\\"orgContact\\\",\\\"orgCost\\\",\\\"orgExtension\\\",\\\"orgFinance\\\",\\\"orgInvoice\\\",\\\"orgService\\\",\\\"orgTime\\\"],\\\"opType\\\":2,\\\"areaId\\\":\\\"ID\\\",\\\"businessType\\\":\\\"phone\\\"}\"}", ObjectNode.class);
        String result = rmsAppliUserOauthServiceProvider.httpForObject("ID", path, requestBody, httpMethod, String.class);
        System.out.println(result);
        Assertions.assertThat(result).isNotNull();
    }
}
