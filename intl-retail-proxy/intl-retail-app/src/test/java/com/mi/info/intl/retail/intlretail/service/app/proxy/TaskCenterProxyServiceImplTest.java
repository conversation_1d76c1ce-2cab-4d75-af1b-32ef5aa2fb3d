package com.mi.info.intl.retail.intlretail.service.app.proxy;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mi.info.intl.retail.cooperation.task.dto.dto.InspectionTaskConfDTO;
import com.mi.info.intl.retail.cooperation.task.inspection.IntlInspectionTaskConfService;
import com.mi.info.intl.retail.exception.RetailRunTimeException;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.taskcenter.TaskCenterInspectionConfReq;
import com.mi.info.intl.retail.intlretail.service.api.result.TaskCenterInspectionConfResponse;
import com.mi.info.intl.retail.org.domain.repository.IntlRmsStoreRepository;
import com.mi.info.intl.retail.org.infra.entity.IntlRmsStore;
import com.xiaomi.cnzone.proretail.newcommon.util.RetailJsonUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import static org.mockito.Mockito.mockStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class TaskCenterProxyServiceImplTest {

    @InjectMocks
    private TaskCenterProxyServiceImpl service;

    @Mock
    private IntlInspectionTaskConfService intlInspectionTaskConfService;

    @Mock
    private IntlRmsStoreRepository intlRmsStoreRepository;

    private TaskCenterInspectionConfReq baseReq;

    @BeforeEach
    void setUp() {
        baseReq = new TaskCenterInspectionConfReq();
        baseReq.setOrgId("CRSS-001");
        baseReq.setCountry("SG");
        baseReq.setJobValue("JOB-1001");
    }

    private IntlRmsStore buildStore(String gradeName) {
        IntlRmsStore store = new IntlRmsStore();
        store.setGradeName(gradeName);
        store.setCode("CODE-001");
        store.setCrssCode("CRSS-001");
        store.setStoreId("STORE-001");
        store.setName("StoreName");
        store.setAddress("StoreAddr");
        return store;
    }

    private void assertStoreFields(TaskCenterInspectionConfResponse resp) {
        assertEquals("S", resp.getStoreGrade());
        assertEquals("CODE-001", resp.getCode());
        assertEquals("CRSS-001", resp.getCrssCode());
        assertEquals("STORE-001", resp.getStoreId());
        assertEquals("StoreName", resp.getName());
        assertEquals("StoreAddr", resp.getAddress());
    }

    @Test
    void getInspectionConfTask_storeNotFound_shouldThrow() {
        when(intlRmsStoreRepository.findByCrssCode("CRSS-001")).thenReturn(Collections.emptyList());
        assertThrows(RetailRunTimeException.class, () -> service.getInspectionConfTask(baseReq));
    }

    @Test
    void getInspectionConfTask_noConfig_shouldReturnEmptyDefaults() {
        when(intlRmsStoreRepository.findByCrssCode("CRSS-001")).thenReturn(Collections.singletonList(buildStore("S")));
        when(intlInspectionTaskConfService.pageList(any())).thenReturn(null);

        Object respObj = service.getInspectionConfTask(baseReq);
        assertTrue(respObj instanceof TaskCenterInspectionConfResponse);
        TaskCenterInspectionConfResponse resp = (TaskCenterInspectionConfResponse) respObj;

        assertStoreFields(resp);
        assertEquals(0, resp.getFrequencyTimes());
        assertEquals(0, resp.getFrequencyMonth());
        assertEquals("0 times every 0 month", resp.getFrequency());
    }

    @Test
    void getInspectionConfTask_configNotMatchUserTitle_shouldReturnEmptyDefaults() {
        when(intlRmsStoreRepository.findByCrssCode("CRSS-001")).thenReturn(Collections.singletonList(buildStore("S")));
        @SuppressWarnings("unchecked")
        IPage<InspectionTaskConfDTO> page = Mockito.mock(IPage.class);
        InspectionTaskConfDTO dto = new InspectionTaskConfDTO();
        dto.setUserTitleCodes("OTHER-CODE");
        when(page.getRecords()).thenReturn(Collections.singletonList(dto));
        when(intlInspectionTaskConfService.pageList(any())).thenReturn(page);

        Object respObj = service.getInspectionConfTask(baseReq);
        TaskCenterInspectionConfResponse resp = (TaskCenterInspectionConfResponse) respObj;

        assertStoreFields(resp);
        assertEquals(0, resp.getFrequencyTimes());
        assertEquals(0, resp.getFrequencyMonth());
        assertEquals("0 times every 0 month", resp.getFrequency());
    }

    @Test
    void getInspectionConfTask_shouldPickFirstEnabledAndParseFrequency_S() {
        when(intlRmsStoreRepository.findByCrssCode("CRSS-001")).thenReturn(Collections.singletonList(buildStore("S")));
        @SuppressWarnings("unchecked")
        IPage<InspectionTaskConfDTO> page = Mockito.mock(IPage.class);
        InspectionTaskConfDTO disabled = new InspectionTaskConfDTO();
        disabled.setUserTitleCodes("JOB-1001,JOB-2002");
        disabled.setIsDisabled(true);
        disabled.setSStoreInspectionFrequency("4 times every 1 month");

        InspectionTaskConfDTO enabled = new InspectionTaskConfDTO();
        enabled.setUserTitleCodes("JOB-1001");
        enabled.setIsDisabled(false);
        enabled.setSStoreInspectionFrequency("4 times every 1 month");

        List<InspectionTaskConfDTO> records = Arrays.asList(disabled, enabled);
        when(page.getRecords()).thenReturn(records);
        when(intlInspectionTaskConfService.pageList(any())).thenReturn(page);

        Object respObj = service.getInspectionConfTask(baseReq);
        TaskCenterInspectionConfResponse resp = (TaskCenterInspectionConfResponse) respObj;

        assertStoreFields(resp);
        assertEquals(Integer.valueOf(4), resp.getFrequencyTimes());
        assertEquals(Integer.valueOf(1), resp.getFrequencyMonth());
        assertEquals("4 times every 1 month", resp.getFrequency());
    }

    @Test
    void getInspectionConfTask_allMatchedDisabled_shouldFallbackFirst_andParse_B() {
        when(intlRmsStoreRepository.findByCrssCode("CRSS-001")).thenReturn(Collections.singletonList(buildStore("B")));
        @SuppressWarnings("unchecked")
        IPage<InspectionTaskConfDTO> page = Mockito.mock(IPage.class);
        InspectionTaskConfDTO onlyDisabled = new InspectionTaskConfDTO();
        onlyDisabled.setUserTitleCodes("JOB-1001,JOB-2002");
        onlyDisabled.setIsDisabled(true);
        onlyDisabled.setBStoreInspectionFrequency("2 times every 3 month");
        when(page.getRecords()).thenReturn(Collections.singletonList(onlyDisabled));
        when(intlInspectionTaskConfService.pageList(any())).thenReturn(page);

        Object respObj = service.getInspectionConfTask(baseReq);
        TaskCenterInspectionConfResponse resp = (TaskCenterInspectionConfResponse) respObj;

        assertEquals("B", resp.getStoreGrade());
        assertEquals(Integer.valueOf(2), resp.getFrequencyTimes());
        assertEquals(Integer.valueOf(3), resp.getFrequencyMonth());
        assertEquals("2 times every 3 month", resp.getFrequency());
    }

    @Test
    void getInspectionConfTask_unknownStoreGrade_shouldUseDefaultFrequency() {
        when(intlRmsStoreRepository.findByCrssCode("CRSS-001")).thenReturn(Collections.singletonList(buildStore("Z")));
        @SuppressWarnings("unchecked")
        IPage<InspectionTaskConfDTO> page = Mockito.mock(IPage.class);
        InspectionTaskConfDTO enabled = new InspectionTaskConfDTO();
        enabled.setUserTitleCodes("JOB-1001");
        enabled.setIsDisabled(false);
        // 填写任意频率字段不会被使用，因为门店等级未知将走默认
        enabled.setSStoreInspectionFrequency("9 times every 9 month");
        when(page.getRecords()).thenReturn(Collections.singletonList(enabled));
        when(intlInspectionTaskConfService.pageList(any())).thenReturn(page);

        Object respObj = service.getInspectionConfTask(baseReq);
        TaskCenterInspectionConfResponse resp = (TaskCenterInspectionConfResponse) respObj;

        assertEquals("Z", resp.getStoreGrade());
        assertEquals(Integer.valueOf(0), resp.getFrequencyTimes());
        assertEquals(Integer.valueOf(0), resp.getFrequencyMonth());
        assertEquals("0 times every 0 month", resp.getFrequency());
    }

    @Test
    void getInspectionConfTask_exceptionWhileParsing_shouldReturnEmptyDefaults() {
        try (MockedStatic<RetailJsonUtil> mockedStatic = mockStatic(RetailJsonUtil.class)) {
            mockedStatic.when(() -> RetailJsonUtil.toJson(any())).thenReturn("");
            when(intlRmsStoreRepository.findByCrssCode("CRSS-001")).thenReturn(
                    Collections.singletonList(buildStore("C")));
            @SuppressWarnings("unchecked")
            IPage<InspectionTaskConfDTO> page = Mockito.mock(IPage.class);
            InspectionTaskConfDTO spyDto = Mockito.spy(new InspectionTaskConfDTO());
            spyDto.setUserTitleCodes("JOB-1001");
            spyDto.setIsDisabled(false);
            Mockito.doThrow(new RuntimeException("boom")).when(spyDto).getCStoreInspectionFrequency();
            when(page.getRecords()).thenReturn(Collections.singletonList(spyDto));
            when(intlInspectionTaskConfService.pageList(any())).thenReturn(page);

            Object respObj = service.getInspectionConfTask(baseReq);
            TaskCenterInspectionConfResponse resp = (TaskCenterInspectionConfResponse) respObj;

            assertEquals("C", resp.getStoreGrade());
            assertEquals(0, resp.getFrequencyTimes());
            assertEquals(0, resp.getFrequencyMonth());
            assertEquals("0 times every 0 month", resp.getFrequency());
        }
    }
} 