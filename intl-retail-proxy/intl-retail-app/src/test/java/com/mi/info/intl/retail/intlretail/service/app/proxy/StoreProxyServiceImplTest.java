package com.mi.info.intl.retail.intlretail.service.app.proxy;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.mi.info.intl.retail.exception.RetailRunTimeException;
import com.mi.info.intl.retail.intlretail.service.api.proxy.RmsProxyService;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.RmsUserBaseDataResponse;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.UpdateDefaultStoreDto;
import com.mi.info.intl.retail.intlretail.service.app.rpc.StoreBuildRpc;
import com.mi.info.intl.retail.intlretail.service.app.rpc.StoreMainDataRpc;
import com.xiaomi.cnzone.storeapi.model.channelbuild.common.req.AppPageRequest;
import com.xiaomi.cnzone.storeapi.model.channelbuild.common.req.RmsCallBackReq;
import com.xiaomi.cnzone.storeapi.model.v2.storenode.req.NodeQueryReq;
import com.xiaomi.cnzone.storeapi.model.v2.storenode.req.NodeSubmitReq;
import com.xiaomi.cnzone.storems.api.model.req.store.GetPositionInfoRequest;
import com.xiaomi.cnzone.storems.api.model.req.store.GetStoreLogListRequest;
import com.xiaomi.cnzone.storems.api.model.req.store.GetStorePositionLogListRequest;
import com.xiaomi.cnzone.storems.api.model.req.store.PositionListReq;
import com.xiaomi.cnzone.storems.api.model.req.store.international.GetInternationalChannelMergeListRequest;
import com.xiaomi.cnzone.storems.api.model.req.store.international.GetInternationalChannelMergeListResponse;
import com.xiaomi.cnzone.storems.api.model.req.store.international.GetInternationalChannelStoreDetailRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * StoreProxyServiceImpl 单元测试类
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("门店代理服务实现类测试")
class StoreProxyServiceImplTest {

    @InjectMocks
    private StoreProxyServiceImpl storeProxyService;

    @Mock
    private RmsProxyService rmsProxyService;

    @Mock
    private StoreBuildRpc storeBuildRpc;

    @Mock
    private StoreMainDataRpc storeMainDataRpc;

    private UpdateDefaultStoreDto testUpdateDefaultStoreDto;
    private RmsUserBaseDataResponse testUserBaseInfo;
    private GetInternationalChannelMergeListRequest testListStoreRequest;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        testUpdateDefaultStoreDto = new UpdateDefaultStoreDto();

        testUserBaseInfo = new RmsUserBaseDataResponse();
        testUserBaseInfo.setUserId("USER001");
        testUserBaseInfo.setEnglishName("Test User");
        testUserBaseInfo.setUserCountryShortCode("CN");

        testListStoreRequest = new GetInternationalChannelMergeListRequest();
        testListStoreRequest.setPageSize(10);
    }


//     @Test
//     @DisplayName("获取门店列表 - 成功场景")
    void testListStore_Success() {
        // Given
        String userId = "USER001";
        String userAccount = "test_user";
        String userToken = "test_token";
        List<String> orgIds = Arrays.asList("ORG001", "ORG002");
        
        when(rmsProxyService.requestByUserToken(anyString(), anyString(), eq(userToken), eq("POST")))
                .thenReturn(JSON.toJSONString(orgIds));

        // When
        Object result = storeProxyService.listStore(testListStoreRequest, userId, userAccount, userToken);

        // Then
        assertNotNull(result);
        verify(rmsProxyService, times(1)).requestByUserToken(anyString(), anyString(), eq(userToken), eq("POST"));
        verify(storeMainDataRpc, times(1)).listStores(any(GetInternationalChannelMergeListRequest.class));
    }

//     @Test
//     @DisplayName("获取门店列表 - 用户无关联门店")
    void testListStore_NoAssociatedStores() {
        // Given
        String userId = "USER001";
        String userAccount = "test_user";
        String userToken = "test_token";
        
        when(rmsProxyService.requestByUserToken(anyString(), anyString(), eq(userToken), eq("POST")))
                .thenReturn(JSON.toJSONString(Collections.emptyList()));

        // When & Then
        RetailRunTimeException exception = assertThrows(RetailRunTimeException.class, 
                () -> storeProxyService.listStore(testListStoreRequest, userId, userAccount, userToken));
        assertEquals("This user is not associating to any Stores.Please contact your leader", exception.getMessage());
        verify(storeMainDataRpc, never()).listStores(any(GetInternationalChannelMergeListRequest.class));
    }

//     @Test
//     @DisplayName("获取所有门店列表 - 成功场景")
    void testListAllStore_Success() {
        // Given
        String userId = "USER001";
        String userAccount = "test_user";
        String userToken = "test_token";
        List<String> orgIds = Arrays.asList("ORG001", "ORG002");

        when(rmsProxyService.requestByUserToken(anyString(), anyString(), eq(userToken), eq("POST")))
                .thenReturn(JSON.toJSONString(orgIds));
        // When
        Object result = storeProxyService.listAllStore(testListStoreRequest, userId, userAccount, userToken);

        // Then
        assertNotNull(result);
        verify(rmsProxyService, times(1)).requestByUserToken(anyString(), anyString(), eq(userToken), eq("POST"));
        verify(storeMainDataRpc, times(1)).listStores(any(GetInternationalChannelMergeListRequest.class));
    }

    @Test
    @DisplayName("获取门店详情 - 成功场景")
    void testGetStore_Success() {
        // Given
        GetInternationalChannelStoreDetailRequest request = new GetInternationalChannelStoreDetailRequest();
        
        when(storeMainDataRpc.getStore(any(GetInternationalChannelStoreDetailRequest.class)))
                .thenReturn(new Object());

        // When
        Object result = storeProxyService.getStore(request);

        // Then
        assertNotNull(result);
        verify(storeMainDataRpc, times(1)).getStore(request);
    }

//     @Test
//     @DisplayName("获取职位列表 - 成功场景")
    void testListPosition_Success() {
        // Given
        String userId = "USER001";
        String userAccount = "test_user";
        String userToken = "test_token";
        PositionListReq request = new PositionListReq();
        List<String> positionCodes = Arrays.asList("POS001", "POS002");
        
        when(rmsProxyService.requestByUserToken(anyString(), anyString(), eq(userToken), eq("POST")))
                .thenReturn(JSON.toJSONString(positionCodes));
        when(storeMainDataRpc.listPositions(any(PositionListReq.class)))
                .thenReturn(new Object());

        // When
        Object result = storeProxyService.listPosition(request, userId, userAccount, userToken);

        // Then
        assertNotNull(result);
        verify(rmsProxyService, times(1)).requestByUserToken(anyString(), anyString(), eq(userToken), eq("POST"));
        verify(storeMainDataRpc, times(1)).listPositions(any(PositionListReq.class));
    }

//     @Test
//     @DisplayName("获取职位列表 - 用户无关联职位")
    void testListPosition_NoAssociatedPositions() {
        // Given
        String userId = "USER001";
        String userAccount = "test_user";
        String userToken = "test_token";
        PositionListReq request = new PositionListReq();
        
        when(rmsProxyService.requestByUserToken(anyString(), anyString(), eq(userToken), eq("POST")))
                .thenReturn(JSON.toJSONString(Collections.emptyList()));

        // When & Then
        RetailRunTimeException exception = assertThrows(RetailRunTimeException.class, 
                () -> storeProxyService.listPosition(request, userId, userAccount, userToken));
        assertEquals("This user is not associating to any Positions.Please contact your leader", exception.getMessage());
        verify(storeMainDataRpc, never()).listPositions(any(PositionListReq.class));
    }

    @Test
    @DisplayName("获取职位详情 - 成功场景")
    void testGetPosition_Success() {
        // Given
        GetPositionInfoRequest request = new GetPositionInfoRequest();
        
        when(storeMainDataRpc.getPosition(any(GetPositionInfoRequest.class)))
                .thenReturn(new Object());

        // When
        Object result = storeProxyService.getPosition(request);

        // Then
        assertNotNull(result);
        verify(storeMainDataRpc, times(1)).getPosition(request);
    }

    @Test
    @DisplayName("获取已提交门店列表 - 成功场景")
    void testListSubmitStore_Success() {
        // Given
        AppPageRequest request = new AppPageRequest();
        PageInfo expectedPageInfo = new PageInfo();
        
        when(storeBuildRpc.listSubmitStore(any(AppPageRequest.class)))
                .thenReturn(expectedPageInfo);

        // When
        PageInfo result = storeProxyService.listSubmitStore(request);

        // Then
        assertNotNull(result);
        assertEquals(expectedPageInfo, result);
        verify(storeBuildRpc, times(1)).listSubmitStore(request);
    }

    @Test
    @DisplayName("查询节点 - 成功场景")
    void testQuery_Success() {
        // Given
        NodeQueryReq request = new NodeQueryReq();
        when(storeBuildRpc.query(any(NodeQueryReq.class)))
                .thenReturn(new Object());

        // When
        Object result = storeProxyService.query(request);

        // Then
        assertNotNull(result);
        verify(storeBuildRpc, times(1)).query(request);
    }

    @Test
    @DisplayName("审核 - 成功场景")
    void testAudit_Success() {
        // Given
        RmsCallBackReq request = new RmsCallBackReq();
        when(storeBuildRpc.audit(any(RmsCallBackReq.class)))
                .thenReturn(new Object());

        // When
        Object result = storeProxyService.audit(request);

        // Then
        assertNotNull(result);
        verify(storeBuildRpc, times(1)).audit(request);
    }

    @Test
    @DisplayName("获取选择器全部数据 - 成功场景")
    void testGetSelectorAll_Success() {
        // Given
        when(storeBuildRpc.getSelectorAll())
                .thenReturn(new Object());

        // When
        Object result = storeProxyService.getSelectorAll();

        // Then
        assertNotNull(result);
        verify(storeBuildRpc, times(1)).getSelectorAll();
    }

//     @Test
//     @DisplayName("提交节点 - 成功场景")
    void testSubmit_Success() {
        // Given
        NodeSubmitReq request = new NodeSubmitReq();
        request.setData("{\"baseInfo\":{\"storeName\":\"测试门店\"}}");
        String userToken = "test_token";
        
        Map<String, Object> rmsResponse = new HashMap<>();
        rmsResponse.put("IsAppTitle", true);
        
        when(rmsProxyService.requestByUserToken(anyString(), anyString(), anyString(), eq(userToken), eq("POST")))
                .thenReturn(JSON.toJSONString(rmsResponse));
        when(storeBuildRpc.submit(any(NodeSubmitReq.class)))
                .thenReturn(new Object());

        // When
        Object result = storeProxyService.submit(request, userToken, testUserBaseInfo);

        // Then
        assertNotNull(result);
        verify(rmsProxyService, times(1)).requestByUserToken(anyString(), anyString(), anyString(), eq(userToken), eq("POST"));
        verify(storeBuildRpc, times(1)).submit(any(NodeSubmitReq.class));
    }

    @Test
    @DisplayName("撤回节点 - 成功场景")
    void testRecall_Success() {
        // Given
        NodeSubmitReq request = new NodeSubmitReq();
        String userToken = "test_token";
        String userId = "USER001";
        
        when(storeBuildRpc.auditCancel(any(NodeSubmitReq.class)))
                .thenReturn(new Object());
        when(storeBuildRpc.getSelectorAll())
                .thenReturn(new Object());

        // When
        Object result = storeProxyService.recall(request, userToken, userId);

        // Then
        assertNotNull(result);
        verify(storeBuildRpc, times(1)).auditCancel(request);
        verify(storeBuildRpc, times(1)).getSelectorAll();
    }

    @Test
    @DisplayName("获取门店日志列表 - 成功场景")
    void testListStoreLogs_Success() {
        // Given
        GetStoreLogListRequest request = new GetStoreLogListRequest();
        
        when(storeMainDataRpc.listStoreLogs(any(GetStoreLogListRequest.class)))
                .thenReturn(new Object());

        // When
        Object result = storeProxyService.listStoreLogs(request);

        // Then
        assertNotNull(result);
        verify(storeMainDataRpc, times(1)).listStoreLogs(request);
    }

    @Test
    @DisplayName("获取职位日志列表 - 成功场景")
    void testListPositionLogs_Success() {
        // Given
        GetStorePositionLogListRequest request = new GetStorePositionLogListRequest();
        when(storeMainDataRpc.listPositionLogs(any(GetStorePositionLogListRequest.class)))
                .thenReturn(new Object());

        // When
        Object result = storeProxyService.listPositionLogs(request);

        // Then
        assertNotNull(result);
        verify(storeMainDataRpc, times(1)).listPositionLogs(request);
    }

//     @Test
//     @DisplayName("检查用户是否在RMS中存在 - 存在")
    void testIsExistInRms_True() {
        // Given
        String userToken = "test_token";
        String userId = "USER001";
        
        Map<String, Object> rmsResponse = new HashMap<>();
        rmsResponse.put("IsAppTitle", true);
        
        when(rmsProxyService.requestByUserToken(anyString(), anyString(), anyString(), eq(userToken), eq("POST")))
                .thenReturn(JSON.toJSONString(rmsResponse));

        // When
        boolean result = storeProxyService.isExistInRms(userToken, userId);

        // Then
        assertTrue(result);
        verify(rmsProxyService, times(1)).requestByUserToken(anyString(), anyString(), anyString(), eq(userToken), eq("POST"));
    }


    // 辅助方法
    private Object createStoreItem(String orgId, int status) {
        // 这里需要根据实际的StoreItem类来创建对象
        // 由于没有看到具体的类定义，这里返回一个模拟对象
        Map<String, Object> item = new HashMap<>();
        item.put("orgId", orgId);
        item.put("status", status);
        return item;
    }
}
