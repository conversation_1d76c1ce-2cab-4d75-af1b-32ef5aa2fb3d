package com.mi.info.intl.retail.intlretail.service.app.inspection.impl;

import com.mi.info.intl.retail.cooperation.task.infra.entity.IntlInspectionTaskConf;
import com.mi.info.intl.retail.cooperation.task.inspection.IntlInspectionTaskConfService;
import com.mi.info.intl.retail.fieldforce.infra.database.dataobject.retailer.IntlRmsRetailer;
import com.mi.info.intl.retail.fieldforce.infra.database.mapper.retailer.IntlRmsRetailerMapper;
import com.mi.info.intl.retail.intlretail.service.api.request.IntlInspectionInfoRequest;
import com.mi.info.intl.retail.intlretail.service.api.result.IntlInspectionInfoResponse;
import com.mi.info.intl.retail.intlretail.service.api.retailer.dto.CommonResponse;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import static org.mockito.ArgumentMatchers.anyList;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

@ExtendWith(MockitoExtension.class)
public class InspectionServiceImplTest {

    // 被测试的类
    @InjectMocks
    private InspectionServiceImpl inspectionService;

    // 需要mock的依赖
    @Mock
    private IntlInspectionTaskConfService intlInspectionTaskConfService;

    @Mock
    private IntlRmsRetailerMapper intlRmsRetailerMapper;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * 测试用例：request为null
     * 预期结果：返回空数据的CommonResponse
     */
    @Test
    void testGetInspectionInfo_RequestIsNull() {
        // 执行测试
        CommonResponse<IntlInspectionInfoResponse> result = inspectionService.getInspectionInfo(null);

        // 验证结果
        assertNotNull(result);
        assertNull(result.getData());
    }

    /**
     * 测试用例：request.id为null
     * 预期结果：返回空数据的CommonResponse
     */
    @Test
    void testGetInspectionInfo_RequestIdIsNull() {
        // 准备测试数据
        IntlInspectionInfoRequest request = new IntlInspectionInfoRequest();
        request.setId(null);

        // 执行测试
        CommonResponse<IntlInspectionInfoResponse> result = inspectionService.getInspectionInfo(request);

        // 验证结果
        assertNotNull(result);
        assertNull(result.getData());
    }

    /**
     * 测试用例：根据ID查询不到任务配置
     * 预期结果：返回空数据的CommonResponse
     */
    @Test
    void testGetInspectionInfo_TaskConfNotFound() {
        // 准备测试数据
        IntlInspectionInfoRequest request = new IntlInspectionInfoRequest();
        request.setId(1L);
        when(intlInspectionTaskConfService.getById(1L)).thenReturn(null);

        // 执行测试
        CommonResponse<IntlInspectionInfoResponse> result = inspectionService.getInspectionInfo(request);

        // 验证结果
        assertNotNull(result);
        assertNull(result.getData());
        // 验证mock方法被调用
        verify(intlInspectionTaskConfService).getById(1L);
    }

    /**
     * 测试用例：正常查询到任务配置，且零售商列表为空
     * 预期结果：返回完整信息但零售商列表为空
     */
    @Test
    void testGetInspectionInfo_TaskConfFoundWithEmptyRetailerList() {
        // 准备测试数据
        IntlInspectionInfoRequest request = new IntlInspectionInfoRequest();
        request.setId(1L);

        IntlInspectionTaskConf taskConf = new IntlInspectionTaskConf();
        taskConf.setId(1);
        taskConf.setRegion("region");
        taskConf.setCountry("country");
        taskConf.setUserTitle("userTitle");
        taskConf.setAllRetailer(1);
        taskConf.setRetailerList(new String[]{});

        when(intlInspectionTaskConfService.getById(1L)).thenReturn(taskConf);

        // 执行测试
        CommonResponse<IntlInspectionInfoResponse> result = inspectionService.getInspectionInfo(request);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getData());
        assertEquals(1L, result.getData().getId());
        assertEquals("region", result.getData().getRegion());
        assertEquals("country", result.getData().getCountry());
        assertEquals("userTitle", result.getData().getUserTitle());
        assertEquals(1, result.getData().getAllRetailer());
        assertNotNull(result.getData().getRetailerList());
        assertTrue(result.getData().getRetailerList().isEmpty());

        verify(intlRmsRetailerMapper, times(0)).selectByRetailerCode(anyList());
    }

    @Test
    void testGetInspectionInfo_TaskConfFoundWithRetailerList_emptySelectByRetailerCode() {
        // 准备测试数据
        IntlInspectionInfoRequest request = new IntlInspectionInfoRequest();
        request.setId(1L);

        IntlInspectionTaskConf taskConf = new IntlInspectionTaskConf();
        taskConf.setId(1);
        taskConf.setRegion("region");
        taskConf.setCountry("country");
        taskConf.setUserTitle("userTitle");
        taskConf.setAllRetailer(1);
        taskConf.setRetailerList(new String[]{"retailer1"});

        when(intlInspectionTaskConfService.getById(1L)).thenReturn(taskConf);
        when(intlRmsRetailerMapper.selectByRetailerCode(anyList())).thenReturn(new ArrayList<>());

        // 执行测试
        CommonResponse<IntlInspectionInfoResponse> result = inspectionService.getInspectionInfo(request);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getData());
        assertEquals(1L, result.getData().getId());
        assertEquals("region", result.getData().getRegion());
        assertEquals("country", result.getData().getCountry());
        assertEquals("userTitle", result.getData().getUserTitle());
        assertEquals(1, result.getData().getAllRetailer());
        assertNotNull(result.getData().getRetailerList());
        assertTrue(result.getData().getRetailerList().isEmpty());

        verify(intlRmsRetailerMapper).selectByRetailerCode(anyList());
    }

    /**
     * 测试用例：正常查询到任务配置，且有零售商列表
     * 预期结果：返回完整信息包括零售商列表
     */
    @Test
    void testGetInspectionInfo_TaskConfFoundWithRetailerList() {
        // 准备测试数据
        IntlInspectionInfoRequest request = new IntlInspectionInfoRequest();
        request.setId(1L);

        IntlInspectionTaskConf taskConf = new IntlInspectionTaskConf();
        taskConf.setId(1);
        taskConf.setRegion("region");
        taskConf.setCountry("country");
        taskConf.setUserTitle("userTitle");
        taskConf.setAllRetailer(1);
        taskConf.setRetailerList(new String[]{"retailer1", "retailer2"});

        List<IntlRmsRetailer> retailers = new ArrayList<>();
        IntlRmsRetailer retailer1 = new IntlRmsRetailer();
        retailer1.setName("retailerCode1");
        retailer1.setRetailerName("retailerName1");
        retailers.add(retailer1);

        IntlRmsRetailer retailer2 = new IntlRmsRetailer();
        retailer2.setName("retailerCode2");
        retailer2.setRetailerName("retailerName2");
        retailers.add(retailer2);

        when(intlInspectionTaskConfService.getById(1L)).thenReturn(taskConf);
        when(intlRmsRetailerMapper.selectByRetailerCode(anyList())).thenReturn(retailers);

        // 执行测试
        CommonResponse<IntlInspectionInfoResponse> result = inspectionService.getInspectionInfo(request);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getData());
        assertEquals(1L, result.getData().getId());
        assertEquals("region", result.getData().getRegion());
        assertEquals("country", result.getData().getCountry());
        assertEquals("userTitle", result.getData().getUserTitle());
        assertEquals(1, result.getData().getAllRetailer());
        assertNotNull(result.getData().getRetailerList());
        assertEquals(2, result.getData().getRetailerList().size());
        assertEquals("retailerCode1", result.getData().getRetailerList().get(0).getRetailerCode());
        assertEquals("retailerName1", result.getData().getRetailerList().get(0).getRetailerName());
        assertEquals("retailerCode2", result.getData().getRetailerList().get(1).getRetailerCode());
        assertEquals("retailerName2", result.getData().getRetailerList().get(1).getRetailerName());

        // 验证mock方法被调用
        verify(intlInspectionTaskConfService).getById(1L);
        verify(intlRmsRetailerMapper).selectByRetailerCode(anyList());
    }
}
