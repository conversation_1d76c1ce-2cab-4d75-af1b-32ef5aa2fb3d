
package com.mi.info.intl.retail.intlretail.service.app.proxy;

import com.mi.info.intl.retail.cooperation.task.dto.IntlInspectionTaskQuery;
import com.mi.info.intl.retail.cooperation.task.dto.dto.InspectionTaskConfDTO;
import com.mi.info.intl.retail.cooperation.task.inspection.BigPromotionConfigService;
import com.mi.info.intl.retail.cooperation.task.inspection.IntlInspectionMessageInstanceService;
import com.mi.info.intl.retail.cooperation.task.inspection.IntlInspectionTaskConfService;
import com.mi.info.intl.retail.intlretail.infra.rpc.TaskCenterServiceAdapter;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.PopWindowDTO;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.taskcenter.*;
import com.mi.info.intl.retail.org.domain.repository.IntlRmsStoreRepository;
import com.mi.info.intl.retail.org.infra.entity.IntlRmsStore;
import com.mi.info.intl.retail.user.constant.StoreGradeEnum;
import com.xiaomi.cnzone.brain.platform.api.model.resp.app.DayCalendarResp;
import com.xiaomi.cnzone.brain.platform.api.model.resp.app.PopWindowResp;
import com.xiaomi.cnzone.brain.platform.api.model.resp.app.TaskNumResp;
import com.xiaomi.cnzone.proretail.bi.api.model.resp.common.MetricsResp;
import com.xiaomi.cnzone.proretail.bi.api.model.resp.common.TableResp;
import com.xiaomi.cnzone.proretail.bi.api.provider.GlobalCentralBrainProvider;
import com.xiaomi.youpin.infra.rpc.Result;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;
import org.springframework.test.util.ReflectionTestUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

class TaskCenterProxyServiceImplV2Test {
    
    @InjectMocks
    private TaskCenterProxyServiceImpl taskCenterProxyService;
    
    @Mock
    private TaskCenterServiceAdapter taskCenterServiceAdapter;
    
    @Mock
    private IntlInspectionMessageInstanceService inspectionMessageInstanceService;
    
    @Mock
    private BigPromotionConfigService bigPromotionConfigService;
    
    @Mock
    private GlobalCentralBrainProvider globalCentralBrainProvider;
    
    @Mock
    private IntlInspectionTaskConfService intlInspectionTaskConfService;
    
    @Mock
    private IntlRmsStoreRepository intlRmsStoreRepository;
    
    @Mock
    private HttpServletRequest httpServletRequest;
    
    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        // 设置私有字段
        ReflectionTestUtils.setField(taskCenterProxyService, "notifyJobtitle", Arrays.asList("500900002", "100000051", "100000024"));
        ReflectionTestUtils.setField(taskCenterProxyService, "mandatoryItemList", Arrays.asList("SALES_REPORTING", "INVENTORY_REPORTING"));
        ReflectionTestUtils.setField(taskCenterProxyService, "selectOneItemList", Arrays.asList("SAMPLE_DEVICE_REPORTING", "TRAINING_REPORTING", "DISPLAY_REPORTING", "STORE_CHECK"));
        ReflectionTestUtils.setField(taskCenterProxyService, "supervisorBusinessTypeIds", Arrays.asList(201L, 202L));
        ReflectionTestUtils.setField(taskCenterProxyService, "newProductMaterialBusinessTypeIds", Arrays.asList(401L, 402L, 403L));
    }
    
    @Test
    void getCalendarByType_NormalCase_ReturnsCalendar() {
        // Given
        TaskCenterCalendarReq req = new TaskCenterCalendarReq();
        req.setCountryShortCode("CN");
        
        DayCalendarResp mockResp = new DayCalendarResp();
        mockResp.setTimeList(new ArrayList<>());
        mockResp.setTypeList(new ArrayList<>());
        
        when(taskCenterServiceAdapter.getDayCalendar(any())).thenReturn(mockResp);
        
        // When
        Object result = taskCenterProxyService.getCalendarByType(req);
        
        // Then
        assertNotNull(result);
        assertTrue(result instanceof DayCalendarResp);
        verify(taskCenterServiceAdapter).getDayCalendar(any());
    }
    
    @Test
    void getCalendarByType_NullResponse_ReturnsNull() {
        // Given
        TaskCenterCalendarReq req = new TaskCenterCalendarReq();
        
        when(taskCenterServiceAdapter.getDayCalendar(any())).thenReturn(null);
        
        // When
        Object result = taskCenterProxyService.getCalendarByType(req);
        
        // Then
        assertNull(result);
        verify(taskCenterServiceAdapter).getDayCalendar(any());
    }
    
    @Test
    void queryTaskNum_NormalCase_ReturnsTaskNum() {
        // Given
        TaskCenterTaskNumReq req = new TaskCenterTaskNumReq();
        req.setArea("CN");
        req.setMiId(1L);
        req.setOrgId("ORG001");
        
        TaskNumResp mockResp = new TaskNumResp();
        mockResp.setAllTaskNum(10);
        mockResp.setCompletedNum(5);
        mockResp.setUnCompletedNum(5);
        
        when(taskCenterServiceAdapter.queryTaskNum(any())).thenReturn(mockResp);
        
        // When
        Object result = taskCenterProxyService.queryTaskNum(req);
        
        // Then
        assertNotNull(result);
        assertTrue(result instanceof TaskNumResp);
        verify(taskCenterServiceAdapter).queryTaskNum(any());
    }
    
    @Test
    void getPopWindowContent_NormalCase_ReturnsPopWindowDTO() {
        // Given
        TaskCenterCommonReq req = new TaskCenterCommonReq();
        req.setMiId(1L);
        req.setJobValue("500900002");
        
        PopWindowResp mockResp = new PopWindowResp();
        List<PopWindowResp.PopWindow> list = new ArrayList<>();
        PopWindowResp.PopWindow detail = new PopWindowResp.PopWindow();
        detail.setTaskInstanceIds(Arrays.asList(1L, 2L));
        detail.setTitle("Test Title");
        detail.setContent("Test Content");
        detail.setRedFlag(true);
        detail.setType("testType");
        list.add(detail);
        mockResp.setList(list);
        
        when(taskCenterServiceAdapter.getPopWindowContent(any())).thenReturn(mockResp);
        
        // When
        PopWindowDTO result = taskCenterProxyService.getPopWindowContent(req);
        
        // Then
        assertNotNull(result);
        assertNotNull(result.getList());
        assertFalse(result.getList().isEmpty());
        verify(taskCenterServiceAdapter).getPopWindowContent(any());
    }
    
    @Test
    void queryEventTaskNum_NormalCase_ReturnsTaskNumResp() {
        // Given
        TaskCenterTaskNumReq req = new TaskCenterTaskNumReq();
        req.setArea("CN");
        req.setMiId(1L);
        req.setOrgId("ORG001");
        
        TaskNumResp mockResp = new TaskNumResp();
        mockResp.setInspectionTime(100);
        mockResp.setInStoreTime(200);
        
        List<TaskNumResp.EventBusinessType> eventBusinessTypes = new ArrayList<>();
        TaskNumResp.EventBusinessType type1 = new TaskNumResp.EventBusinessType();
        type1.setBusinessTypeName("SALES_REPORTING");
        type1.setStatus(0); // UN_DO
        eventBusinessTypes.add(type1);
        
        TaskNumResp.EventBusinessType type2 = new TaskNumResp.EventBusinessType();
        type2.setBusinessTypeName("SAMPLE_DEVICE_REPORTING");
        type2.setStatus(1); // DOING
        eventBusinessTypes.add(type2);
        
        mockResp.setEventBusinessTypeList(eventBusinessTypes);
        
        when(taskCenterServiceAdapter.queryEventTaskNum(any())).thenReturn(mockResp);
        when(bigPromotionConfigService.hasBigPromotionTaskConfig(anyList())).thenReturn(Collections.singletonMap("CN", true));
        
        // When
        Object result = taskCenterProxyService.queryEventTaskNum(req);
        
        // Then
        assertNotNull(result);
        assertTrue(result instanceof TaskNumResp);
        verify(taskCenterServiceAdapter).queryEventTaskNum(any());
        verify(bigPromotionConfigService).hasBigPromotionTaskConfig(anyList());
    }
    
    @Test
    void queryEventTaskNum_NonPromotion_ReturnsFilteredResp() {
        // Given
        TaskCenterTaskNumReq req = new TaskCenterTaskNumReq();
        req.setArea("CN");
        req.setMiId(1L);
        req.setOrgId("ORG001");
        
        TaskNumResp mockResp = new TaskNumResp();
        mockResp.setInspectionTime(100);
        mockResp.setInStoreTime(200);
        mockResp.setEventBusinessTypeList(new ArrayList<>());
        
        when(taskCenterServiceAdapter.queryEventTaskNum(any())).thenReturn(mockResp);
        when(bigPromotionConfigService.hasBigPromotionTaskConfig(anyList())).thenReturn(Collections.singletonMap("CN", false));
        
        // When
        Object result = taskCenterProxyService.queryEventTaskNum(req);
        
        // Then
        assertNotNull(result);
        assertTrue(result instanceof TaskNumResp);
        TaskNumResp resp = (TaskNumResp) result;
        assertEquals(100, resp.getInspectionTime());
        assertEquals(200, resp.getInStoreTime());
        assertTrue(resp.getEventBusinessTypeList().isEmpty());
    }
    
    @Test
    void getInspectionConfTask_NormalCase_ReturnsResponse() {
        // Given
        TaskCenterInspectionConfReq req = new TaskCenterInspectionConfReq();
        req.setOrgId("ORG001");
        req.setCountry("CN");
        req.setJobValue("JOB001");
        
        // Mock store
        IntlRmsStore store = new IntlRmsStore();
        store.setGradeName(StoreGradeEnum.A.getName());
        store.setCode("STORE001");
        store.setCrssCode("CRSS001");
        store.setStoreId("1");
        store.setName("Test Store");
        store.setAddress("Test Address");
        
        when(intlRmsStoreRepository.findByCrssCode("ORG001")).thenReturn(Arrays.asList(store));
        
        // Mock task config
        InspectionTaskConfDTO taskConf = new InspectionTaskConfDTO();
        taskConf.setUserTitleCodes("JOB001");
        taskConf.setAStoreInspectionFrequency("2 times every 1 month");
        taskConf.setIsDisabled(false);
        
        com.baomidou.mybatisplus.core.metadata.IPage<InspectionTaskConfDTO> page =
                new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>();
        page.setRecords(Arrays.asList(taskConf));
        
        when(intlInspectionTaskConfService.pageList(any(IntlInspectionTaskQuery.class))).thenReturn(page);
        
        // When
        Object result = taskCenterProxyService.getInspectionConfTask(req);
        
        // Then
        assertNotNull(result);
        verify(intlRmsStoreRepository).findByCrssCode("ORG001");
        verify(intlInspectionTaskConfService).pageList(any(IntlInspectionTaskQuery.class));
    }
    
    @Test
    void getInspectionConfTask_NoStore_ThrowsException() {
        // Given
        TaskCenterInspectionConfReq req = new TaskCenterInspectionConfReq();
        req.setOrgId("ORG001");
        
        when(intlRmsStoreRepository.findByCrssCode("ORG001")).thenReturn(new ArrayList<>());
        
        // When & Then
        assertThrows(RuntimeException.class, () -> taskCenterProxyService.getInspectionConfTask(req));
        verify(intlRmsStoreRepository).findByCrssCode("ORG001");
    }
    
    @Test
    void queryCentralBrainList_NormalCase_ReturnsData() {
        // Given
        QueryCentralBrainListRequest request = new QueryCentralBrainListRequest();
        request.setTaskBatchId("1");
        request.setPageNo(1);
        request.setPageSize(10);
        
        TableResp mockData = new TableResp();
        Result<TableResp> mockResult = Result.success(mockData);
        
        when(globalCentralBrainProvider.queryCentralBrainList(any())).thenReturn(mockResult);
        
        // When
        Object result = taskCenterProxyService.queryCentralBrainList(request);
        
        // Then
        assertNotNull(result);
        assertEquals(mockData, result);
        verify(globalCentralBrainProvider).queryCentralBrainList(any());
    }
    
    @Test
    void queryCentralBrainCards_NormalCase_ReturnsData() {
        // Given
        QueryCentralBrainCardsRequest request = new QueryCentralBrainCardsRequest();
        request.setTaskBatchId("1");
        request.setStartTime(System.currentTimeMillis() +"");
        request.setEndTime(String.valueOf(System.currentTimeMillis() + 86400000));
        
        MetricsResp mockData = new MetricsResp();
        Result<MetricsResp> mockResult = Result.success(mockData);
        
        when(globalCentralBrainProvider.queryCentralBrainCards(any())).thenReturn(mockResult);
        
        // When
        Object result = taskCenterProxyService.queryCentralBrainCards(request);
        
        // Then
        assertNotNull(result);
        assertEquals(mockData, result);
        verify(globalCentralBrainProvider).queryCentralBrainCards(any());
    }
}