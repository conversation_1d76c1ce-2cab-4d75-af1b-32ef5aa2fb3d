package com.mi.info.intl.retail.intlretail.service.app.oapi.impl;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import com.lark.oapi.Client;
import com.lark.oapi.core.response.BaseResponse;
import com.lark.oapi.core.utils.Jsons;
import com.lark.oapi.service.im.v1.enums.MsgTypeEnum;
import com.lark.oapi.service.im.v1.enums.ReceiveIdTypeEnum;
import com.lark.oapi.service.im.v1.model.CreateMessageReq;
import com.lark.oapi.service.im.v1.model.CreateMessageReqBody;
import com.lark.oapi.service.im.v1.model.CreateMessageRespBody;
import com.lark.oapi.service.im.v1.model.ListChatReq;
import com.lark.oapi.service.im.v1.model.ListChatResp;
import com.mi.info.intl.retail.intlretail.service.app.config.OapiNacosConfig;
import com.mi.info.intl.retail.intlretail.service.api.oapi.OapiMessageService;
import com.mi.info.intl.retail.intlretail.service.api.oapi.dto.MissingRuleAlertDTO;
import com.xiaomi.mone.docs.annotations.dubbo.ApiDoc;
import com.xiaomi.mone.docs.annotations.dubbo.ApiModule;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.nio.charset.StandardCharsets;
import java.util.Map;
import javax.annotation.Resource;

@Slf4j
@Service
@DubboService(group = "${oapi.dubbo.group:}", interfaceClass = OapiMessageService.class)
@ApiModule(value = "飞书消息", apiInterface = OapiMessageService.class)
public class OapiMessageServiceImpl implements OapiMessageService {

    @Resource
    private Client client;
    @Resource
    private OapiNacosConfig oapiNacosConfig;

    /**
     * 调用该接口向指定用户或者群聊发送消息
     *
     * @param receiveId 接收人ID
     * @param template 消息模板，使用 {key} 作为占位符
     * @param map 模板参数映射，key 对应模板中的占位符
     * @return 创建成功返回 true，否则返回 false
     */
    @Override
    @ApiDoc(value = "调用该接口向指定用户或者群聊发送消息-卡片消息")
    public boolean sendInteractiveMessage(String receiveId, String template, Map<String, Object> map) {
        BaseResponse<CreateMessageRespBody> resp = null;
        Assert.hasText(receiveId, "receiveId不能为空");
        Assert.hasText(template, "template不能为空");
        try {
            String format = StrUtil.format(template, map);
            CreateMessageReq req = CreateMessageReq.newBuilder()
                    .receiveIdType(ReceiveIdTypeEnum.CHAT_ID.getValue())
                    .createMessageReqBody(CreateMessageReqBody.newBuilder()
                            .receiveId(receiveId)
                            .msgType(MsgTypeEnum.MSG_TYPE_INTERACTIVE.getValue())
                            .content(format)
                            .build())
                    .build();
            resp = client.im().v1().message().create(req);
            return resp.success();
        } catch (Exception e) {
            log.error("发送飞书群机器人消息异常。{}", e.getMessage(), e);
        } finally {
            if (resp != null) {
                log.info("发送飞书群机器人消息结果：{}", Jsons.createGSON(true, false)
                        .toJson(new String(resp.getRawResponse().getBody(), StandardCharsets.UTF_8)));
            }
        }
        return false;
    }

    @Override
    @ApiDoc(value = "搜索群列表")
    public ListChatResp searchChat(String userIdType, String pageToke) {
        ListChatReq.Builder builder = ListChatReq.newBuilder();
        // 发起请求
        if (CharSequenceUtil.isNotBlank(userIdType)) {
            builder.userIdType(userIdType);
        }
        if (CharSequenceUtil.isNotBlank(pageToke)) {
            builder.pageToken(pageToke);
        }
        ListChatReq req = builder.pageSize(100).build();
        try {
            // 发起请求
            return client.im().v1().chat().list(req);
        } catch (Exception e) {
            log.error("搜索群列表异常。{}", e.getMessage(), e);
        }
        return new ListChatResp();
    }

    /**
     * 发送缺失规则提醒消息
     *
     * @param receiveId 接收人ID
     * @param missingRuleAlertDTO 缺失规则提醒参数
     * @return 发送成功返回 true，否则返回 false
     */
    @Override
    @ApiDoc(value = "发送缺失规则提醒消息", description = "定制")
    public boolean sendMissingRuleAlertMessage(String receiveId, MissingRuleAlertDTO missingRuleAlertDTO) {
        if (missingRuleAlertDTO == null) {
            return false;
        }
        return sendInteractiveMessage(receiveId, oapiNacosConfig.getMissingRuleAlertTemplate(),
                missingRuleAlertDTO.getContent());
    }
}
