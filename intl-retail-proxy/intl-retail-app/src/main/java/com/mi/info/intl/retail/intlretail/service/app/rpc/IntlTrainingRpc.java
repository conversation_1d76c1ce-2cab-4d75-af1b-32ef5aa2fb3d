package com.mi.info.intl.retail.intlretail.service.app.rpc;

import com.alibaba.fastjson.JSON;
import com.mi.info.intl.retail.exception.RetailRunTimeException;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.xiaomi.proretail.training.api.rpc.service.intl.IntlQuizExamineeAppProvider;
import com.xiaomi.proretail.training.api.rpc.service.intl.IntlStudyTaskAppProvider;
import com.xiaomi.proretail.training.api.rpc.service.intl.IntlTrainingWebService;

import com.xiaomi.proretail.training.api.rpc.service.intl.IntlUserCheckProvider;
import com.xiaomi.proretail.training.api.vo.trainingcheck.intl.IntlExamDetailResponse;
import com.xiaomi.proretail.training.api.vo.trainingcheck.intl.IntlExamRequest;
import com.xiaomi.proretail.training.api.vo.trainingcheck.intl.IntlExamResponse;
import com.xiaomi.proretail.training.api.vo.trainingcheck.intl.IntlExamSubmitRequest;
import com.xiaomi.proretail.training.api.vo.trainingcheck.intl.IntlExamSubmitResponse;
import com.xiaomi.proretail.training.api.vo.trainingcheck.intl.IntlInstruction;
import com.xiaomi.proretail.training.api.vo.trainingcheck.intl.IntlProblem;
import com.xiaomi.proretail.training.api.vo.trainingstudy.intl.IntlAddOperateParam;
import com.xiaomi.proretail.training.api.vo.trainingstudy.intl.IntlBaseRequest;
import com.xiaomi.proretail.training.api.vo.trainingstudy.intl.IntlCourseListVo;
import com.xiaomi.proretail.training.api.vo.trainingstudy.intl.IntlExamineeListParam;
import com.xiaomi.proretail.training.api.vo.trainingstudy.intl.IntlExamineeListResp;
import com.xiaomi.proretail.training.api.vo.trainingstudy.intl.IntlExamineeLogListParam;
import com.xiaomi.proretail.training.api.vo.trainingstudy.intl.IntlExamineeLogListResp;
import com.xiaomi.proretail.training.api.vo.trainingstudy.intl.IntlProjectListVo;
import com.xiaomi.proretail.training.api.vo.trainingstudy.intl.IntlSearchHistoryListParam;
import com.xiaomi.proretail.training.api.vo.trainingstudy.intl.IntlSearchHistoryListResp;
import com.xiaomi.proretail.training.api.vo.trainingstudy.intl.IntlSearchHistoryParam;
import com.xiaomi.proretail.training.api.vo.trainingstudy.intl.IntlStudyProgressParam;
import com.xiaomi.proretail.training.api.vo.trainingstudy.intl.IntlStudyProgressUploadParam;
import com.xiaomi.proretail.training.api.vo.trainingstudy.intl.IntlStudyProgressVo;
import com.xiaomi.proretail.training.api.vo.trainingstudy.intl.IntlStudyTaskDetailVo;
import com.xiaomi.proretail.training.api.vo.trainingstudy.intl.IntlStudyTaskDownLineParam;
import com.xiaomi.proretail.training.api.vo.trainingstudy.intl.IntlUserCheckParam;
import com.xiaomi.proretail.training.api.vo.trainingstudy.intl.IntlUserCheckResp;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class IntlTrainingRpc {
    @Reference(group = "${training.dubbo.group:}", check = false, interfaceClass = IntlTrainingWebService.class)
    private IntlTrainingWebService intlTrainingWebService;
    Integer success = 0;
    private static final Integer ERROR_CODE_USER_CHECK_FAIL = 400080006;

    @Reference(group = "${training.dubbo.group:}", check = false, interfaceClass = IntlStudyTaskAppProvider.class)
    private IntlStudyTaskAppProvider intlStudyTaskAppProvider;

    @Reference(group = "${training.dubbo.group:}", check = false, interfaceClass = IntlQuizExamineeAppProvider.class)
    private IntlQuizExamineeAppProvider intlQuizExamineeAppProvider;

    @Reference(group = "${training.dubbo.group:}", check = false, interfaceClass = IntlUserCheckProvider.class)
    private IntlUserCheckProvider intlUserCheckProvider;

    public IntlExamResponse<IntlProblem> getExamProblems(IntlExamRequest examRequest) {
        Result<IntlExamResponse<IntlProblem>> result = intlTrainingWebService.getExamProblems(examRequest);
        if (result.getCode() == success) {
            return result.getData();
        }
        throw new RetailRunTimeException(result.getMessage());
    }

    public CommonApiResponse<IntlInstruction> getExamInstruction(IntlExamRequest examRequest) {
        Result<IntlInstruction> result = intlTrainingWebService.getExamInstruction(examRequest);
        if (result.getCode() == success) {
            return new CommonApiResponse<>(result.getData());
        }
        if (result.getCode() == ERROR_CODE_USER_CHECK_FAIL) {
            return CommonApiResponse.failure(ERROR_CODE_USER_CHECK_FAIL, result.getMessage());
        }
        throw new RetailRunTimeException(result.getMessage());
    }

    public IntlExamDetailResponse getExamDetail(IntlExamRequest examRequest) {
        Result<IntlExamDetailResponse> result = intlTrainingWebService.getExamDetail(examRequest);
        if (result.getCode() == success) {
            return result.getData();
        }
        throw new RetailRunTimeException(result.getMessage());
    }

    public IntlExamSubmitResponse submit(IntlExamSubmitRequest examSubmitRequest) {
        Result<IntlExamSubmitResponse> result = intlTrainingWebService.submit(examSubmitRequest);
        log.info("IntlTrainingRpc#submit result:{}", JSON.toJSON(result));
        if (result.getCode() == success) {
            return result.getData();
        }
        throw new RetailRunTimeException(result.getMessage());
    }

    public Object projectList(IntlBaseRequest intlBaseRequest) {
        Result<IntlProjectListVo> result = intlStudyTaskAppProvider.projectList(intlBaseRequest);
        if (result.getCode() == success) {
            return result.getData();
        }
        throw new RetailRunTimeException(result.getMessage());
    }

    public Object courseList(IntlBaseRequest intlBaseRequest) {
        Result<IntlCourseListVo> result = intlStudyTaskAppProvider.courseList(intlBaseRequest);
        if (result.getCode() == success) {
            return result.getData();
        }
        throw new RetailRunTimeException(result.getMessage());
    }

    public Object getStudyTaskDetail(IntlStudyTaskDownLineParam intlStudyTaskDownLineParam) {
        Result<IntlStudyTaskDetailVo> result = intlStudyTaskAppProvider.getStudyTaskDetail(intlStudyTaskDownLineParam);
        if (result.getCode() == success) {
            return result.getData();
        }
        throw new RetailRunTimeException(result.getMessage());
    }

    public Object getDetailProgress(IntlStudyProgressParam intlStudyProgressParam) {
        Result<IntlStudyProgressVo> result = intlStudyTaskAppProvider.getDetailProgress(intlStudyProgressParam);
        if (result.getCode() == success) {
            return result.getData();
        }
        throw new RetailRunTimeException(result.getMessage());
    }

    public Object uploadStudyProgress(IntlStudyProgressUploadParam intlStudyProgressUploadParam) {
        Result<Integer> result = intlStudyTaskAppProvider.uploadStudyProgress(intlStudyProgressUploadParam);
        if (result.getCode() == success) {
            return result.getData();
        }
        throw new RetailRunTimeException(result.getMessage());
    }

    public IntlExamineeListResp getExamList(IntlExamineeListParam param) {
        Result<IntlExamineeListResp> result = intlQuizExamineeAppProvider.list(param);
        if (result.getCode() == success) {
            return result.getData();
        }
        throw new RetailRunTimeException(result.getMessage());
    }

    public IntlExamineeLogListResp getHistoryList(IntlExamineeLogListParam param) {
        Result<IntlExamineeLogListResp> result = intlQuizExamineeAppProvider.historylist(param);
        if (result.getCode() == success) {
            return result.getData();
        }
        throw new RetailRunTimeException(result.getMessage());
    }

    public String addSearchHistory(IntlSearchHistoryParam param) {
        Result<String> result = intlQuizExamineeAppProvider.addSearchHistory(param);
        if (result.getCode() == success) {
            return result.getData();
        }
        throw new RetailRunTimeException(result.getMessage());
    }

    public String deleteSearchHistory(IntlSearchHistoryParam param) {
        Result<String> result = intlQuizExamineeAppProvider.deleteSearchHistory(param);
        if (result.getCode() == success) {
            return result.getData();
        }
        throw new RetailRunTimeException(result.getMessage());
    }

    public String deleteAllSearchHistory(IntlBaseRequest param) {
        Result<String> result = intlQuizExamineeAppProvider.deleteAllSearchHistory(param);
        if (result.getCode() == success) {
            return result.getData();
        }
        throw new RetailRunTimeException(result.getMessage());
    }

    public List<IntlSearchHistoryListResp> searchHistoryList(IntlSearchHistoryListParam param) {
        Result<List<IntlSearchHistoryListResp>> result = intlQuizExamineeAppProvider.searchHistoryList(param);
        if (result.getCode() == success) {
            return result.getData();
        }
        throw new RetailRunTimeException(result.getMessage());
    }
    public String addOperate(IntlAddOperateParam param) {
        Result<String> result = intlUserCheckProvider.addOperate(param);
        if (result.getCode() == success) {
            return result.getData();
        }
        throw new RetailRunTimeException(result.getMessage());
    }

    public List<IntlUserCheckResp> userCheck(IntlUserCheckParam param) {
        Result<List<IntlUserCheckResp>> result = intlUserCheckProvider.userCheck(param);
        if (result.getCode() == success) {
            return result.getData();
        }
        throw new RetailRunTimeException(result.getMessage());
    }
}
