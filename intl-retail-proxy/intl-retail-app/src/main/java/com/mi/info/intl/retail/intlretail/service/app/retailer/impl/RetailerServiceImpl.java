package com.mi.info.intl.retail.intlretail.service.app.retailer.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mi.info.intl.retail.api.fieldforce.retailer.RetailerApiService;
import com.mi.info.intl.retail.api.fieldforce.retailer.dto.IntlRetailerDTO;
import com.mi.info.intl.retail.api.front.position.dto.IntlPositionDTO;
import com.mi.info.intl.retail.component.ComponentLocator;
import com.mi.info.intl.retail.intlretail.domain.common.utils.JsonUtil;
import com.mi.info.intl.retail.intlretail.service.api.ldu.IntlLduReportLogService;
import com.mi.info.intl.retail.intlretail.service.api.retailer.RetailerService;
import com.mi.info.intl.retail.intlretail.service.api.retailer.dto.ChannelDto;
import com.mi.info.intl.retail.intlretail.service.api.retailer.dto.PositionDto;
import com.mi.info.intl.retail.intlretail.service.api.retailer.dto.RetailerAreaResponse;
import com.mi.info.intl.retail.intlretail.service.api.retailer.dto.RetailerExcelValidationContent;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.*;
import com.mi.info.intl.retail.intlretail.service.api.store.gateway.IGateWayChannelInfoService;
import com.mi.info.intl.retail.intlretail.service.api.store.gateway.dto.GateWayChannelInfoResponse;
import com.mi.info.intl.retail.intlretail.service.app.retailer.constant.*;
import com.mi.info.intl.retail.retailer.entity.IntlRmsRetailer;
import com.mi.info.intl.retail.retailer.mapper.IntlRmsRetailerMapper;
import com.mi.info.intl.retail.user.app.UserService;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@DubboService(group = "${center.dubbo.group:}", interfaceClass = RetailerService.class)
public class RetailerServiceImpl implements RetailerService, RetailerApiService {

    @Resource
    private IGateWayChannelInfoService gateWayChannelInfoService;
    @Resource
    private UserService userService; // 可能是为了兼容旧版本或其他用途

    @Resource
    private IntlRmsRetailerMapper intlRmsRetailerMapper;

    @Resource
    private IntlLduReportLogService intlLduReportLogService;


    @Override
    public RetailerAreaResponse getArea() {

        RetailerAreaResponse retailerAreaResponse = new RetailerAreaResponse();
        List<ChannelDto> channelList = new ArrayList<>();
        // 阵地
        List<PositionDto> positionCodeList = new ArrayList<>();
        // 岗位
        List<PositionDto> positionTypeList = new ArrayList<>();
        // 用户职位
        List<PositionDto> userTitleList = new ArrayList<>();

        for (RetailerChannelTypeEnum channelTypeEnum : RetailerChannelTypeEnum.values()) {
            ChannelDto channelDto = new ChannelDto();
            channelDto.setChannelId(channelTypeEnum.getId());
            channelDto.setChannelName(channelTypeEnum.getName());
            channelList.add(channelDto);
        }

        for (RetailerPositionCodeEnum item : RetailerPositionCodeEnum.values()) {
            PositionDto positionDto = new PositionDto();
            positionDto.setPositionId(item.getId());
            positionDto.setPositionName(item.getName());
            positionCodeList.add(positionDto);
        }

        for (RetailerPositionTypeEnum item : RetailerPositionTypeEnum.values()) {
            PositionDto positionDto = new PositionDto();
            positionDto.setPositionId(item.getOrgId());
            positionDto.setPositionName(item.getName());
            positionTypeList.add(positionDto);
        }

        for (RmsUserTitleEnum item : RmsUserTitleEnum.values()) {
            PositionDto positionDto = new PositionDto();
            positionDto.setPositionId(item.getOrgId());
            positionDto.setPositionName(item.getName());
            userTitleList.add(positionDto);
        }

        retailerAreaResponse.setChannelList(channelList);
        retailerAreaResponse.setPositionCodeList(positionCodeList);
        retailerAreaResponse.setPositionTypeList(positionTypeList);
        retailerAreaResponse.setUserTitleList(userTitleList);
        return retailerAreaResponse;
    }

    @Override
    public List<RetailerInfoResponse> getRetailerInfo(RetailerInfoRequest request) {
        if (request == null) {
            return Collections.emptyList();
        }

        ChannelInfoRequest channelInfoRequest = new ChannelInfoRequest();
        channelInfoRequest.setType(StoreTypeEnum.RETAILER.getId());
        channelInfoRequest.setAreaId(request.getRegion());
        channelInfoRequest.setCountryCode(request.getRegion());
        if (request.getCodes() != null && !request.getCodes().isEmpty()) {
            if (request.getCodes().size() == 1) {
                channelInfoRequest.setSearch(request.getCodes().get(0));
            } else {
                channelInfoRequest.setCodes(request.getCodes());
            }
        }

        if (gateWayChannelInfoService == null) {
            return Collections.emptyList();
        }

        GateWayChannelInfoResponse result = gateWayChannelInfoService.queryChannelInfo(channelInfoRequest);
        if (result == null || result.getData() == null) {
            return Collections.emptyList();
        }

        List<ChannelInfoResponse> data = result.getData();
        if (data.isEmpty()) {
            return Collections.emptyList();
        }

        List<RetailerInfoResponse> resultList = new ArrayList<>();
        for (ChannelInfoResponse datum : data) {
            if (datum == null || datum.getBasic() == null) {
                continue;
            }
            RetailerInfoResponse item = new RetailerInfoResponse();
            item.setCode(datum.getBasic().getCode());
            item.setName(datum.getBasic().getName());
            resultList.add(item);
        }
        return resultList;
    }

    @Override
    public List<RetailerListResponse> getRetailerList(RetailerListRequest request) {
        log.info("getRetailerList_request:{}", JsonUtil.bean2json(request));
        if (request == null) {
            return Collections.emptyList();
        }

        if (gateWayChannelInfoService == null) {
            return Collections.emptyList();
        }

        try {
            List<RetailerListResponse> result = gateWayChannelInfoService.queryRetailerList(request);
            log.info("getRetailerList_responses:{}", JsonUtil.bean2json(result));
            return result != null ? result : Collections.emptyList();
        } catch (Exception e) {
            log.error("Error calling gateWayChannelInfoService.queryRetailerList", e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<BusinessDataResponse> getOrgPerson(BusinessDataInputRequest request) {

        log.info("getOrgPerson_request:{}", JsonUtil.bean2json(request));
        List<BusinessDataResponse> responses = new ArrayList<>();
        // type不为空且等于2执行督导查询逻辑，type等于3执行阵地+人查询逻辑，否则执行促销员查询逻辑
        if (request.getType() != null && request.getType().equals(2)) {
            // 执行督导查询逻辑（MySQL数据库）
            responses = userService.getUserPositions(request);
        } else if (request.getType() != null && request.getType().equals(3)) {
            // 执行阵地+人查询逻辑（MySQL数据库，支持门店类型和门店编码筛选）
            responses = userService.getUserPositionsWithStoreFilter(request);
        } else if (request.getType() != null && request.getType().equals(4)) {
            // 执行LDU上报的 阵地+人查询逻辑（MySQL数据库，根据项目编码 + 国家筛选）
            List<String> projectList = request.getProject(); //parseProjectList(request.getProject());
            responses = intlLduReportLogService.getUserPositionsByProjectCountry(projectList, request.getRegion());
        } else {
            // 执行促销员查询逻辑(RMS系统)
            responses = gateWayChannelInfoService.queryBusinessData(request);
        }
        log.info("getOrgPerson_responses:{}", JsonUtil.bean2json(responses));
        return responses;
    }

    /*
     * @Override public List<RetailerExcelValidationContent>
     * uploadExcel(MultipartFile file, String region) {
     *
     * if (file == null || file.isEmpty()) { throw new RmsApiException(400,
     * "file is not be empty"); }
     *
     * try {
     *
     * InputStream inputStream = file.getInputStream();
     * List<RetailerExcelValidationContent> result =
     * this.streamProcessing(inputStream, region); return result;
     *
     * } catch (IOException e) { throw new RmsApiException(500,
     * "Error processing Excel file", e); }
     *
     * }
     */

    @Override
    public List<RetailerExcelValidationContent> uploadFds(ExcelValidationRequest excelValidationRequest) {


        // 创建 OkHttpClient 实例
        OkHttpClient client = new OkHttpClient();
        // 创建 HTTP 请求
        Request request = new Request.Builder().url(excelValidationRequest.getUrl()).build();
        try (Response response = client.newCall(request).execute()) {

            List<RetailerExcelValidationContent> result = new ArrayList<>();
            if (response.isSuccessful()) {
                // 获取响应的输入流
                InputStream inputStream = Objects.requireNonNull(response.body()).byteStream();
                result = this.streamProcessing(inputStream, excelValidationRequest.getRegion());
            }

            return result;
        } catch (Exception e) {
            log.error("Error processing Excel file", e);
        }

        return Collections.emptyList();
    }

    private List<RetailerExcelValidationContent> streamProcessing(InputStream inputStream, String region)
            throws IOException {

        List<RetailerExcelValidationContent> result = new ArrayList<>();
        Workbook workbook = new XSSFWorkbook(inputStream);
        List<String> codeList = new ArrayList<>();

        Sheet sheet = workbook.getSheetAt(0);
        for (Row row : sheet) {
            if (row.getRowNum() == 0) {
                continue;
            }

            String code = this.getCellValue(row.getCell(0));
            RetailerExcelValidationContent res = new RetailerExcelValidationContent();
            res.setCode(code);
            res.setLine(row.getRowNum() + 1);
            if (codeList.contains(code)) {
                res.setValid(StatusEnum.INVALID.getId());
                res.setMsg("code is duplicate");
            } else {
                codeList.add(code);
            }
            result.add(res);

        }

        Map<String, String> map = checkRms(result, region);

        for (RetailerExcelValidationContent res : result) {

            if (res.getValid() == 2) {
                continue;
            }

            String code = res.getCode();
            if (StringUtils.isBlank(code)) {
                res.setMsg("code is empty");
                continue;
            }
            if (map.containsKey(code)) {
                res.setValid(StatusEnum.VALID.getId());
                res.setName(map.get(code));
            } else {
                res.setValid(StatusEnum.INVALID.getId());
                res.setMsg("There is no corresponding retailer in this country");
            }
        }
        return result;
    }

    private Map<String, String> checkRms(List<RetailerExcelValidationContent> retailers, String region) {

        List<String> codes = retailers.stream().map(RetailerExcelValidationContent::getCode)
                .collect(Collectors.toList());
        ChannelInfoRequest channelInfoRequest = new ChannelInfoRequest();
        channelInfoRequest.setType(StoreTypeEnum.RETAILER.getId());
        channelInfoRequest.setCodes(codes);
        channelInfoRequest.setAreaId(region);
        channelInfoRequest.setCountryCode(region);
        GateWayChannelInfoResponse result = gateWayChannelInfoService.queryChannelInfo(channelInfoRequest);

        List<ChannelInfoResponse> data = result.getData();
        if (data.isEmpty()) {
            return new HashMap<>();
        }

        Map<String, String> map = new HashMap<>();
        for (ChannelInfoResponse item : data) {
            map.put(item.getBasic().getCode(), item.getBasic().getName());
        }

        return map;
    }

    private String getCellValue(Cell cell) {
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    return String.valueOf(cell.getNumericCellValue());
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }

    @Override
    public Optional<IntlRetailerDTO> getRetailerByRetailerCode(IntlPositionDTO dto) {
        String retailerCode = dto.getRetailerCode();
        if (StringUtils.isEmpty(retailerCode)) {
            return Optional.empty();
        }
        LambdaQueryWrapper<IntlRmsRetailer> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(IntlRmsRetailer::getName, retailerCode)
                .select(IntlRmsRetailer::getId);
        IntlRmsRetailer intlRmsRetailer = intlRmsRetailerMapper.selectOne(wrapper);
        IntlRetailerDTO intlRetailerDTO = new IntlRetailerDTO();
        ComponentLocator.getConverter().convert(intlRmsRetailer, intlRetailerDTO);
        return Optional.of(intlRetailerDTO);
    }

    /**
     * 解析项目代码列表
     * 支持单个项目代码字符串或JSON数组格式的项目代码列表
     * 例如: "PSID0001862" 或 ["PSID0001862", "PSID0001824"]
     *
     * @param project 项目代码字符串
     * @return 项目代码列表
     */
    private List<String> parseProjectList(String project) {
        if (StringUtils.isBlank(project)) {
            return new ArrayList<>();
        }

        try {
            // 尝试解析为JSON数组
            if (project.startsWith("[") && project.endsWith("]")) {
                // 使用简单的字符串处理来解析JSON数组
                String content = project.substring(1, project.length() - 1);
                if (StringUtils.isBlank(content)) {
                    return new ArrayList<>();
                }

                // 分割字符串，处理引号和逗号
                String[] items = content.split(",");
                List<String> result = new ArrayList<>();
                for (String item : items) {
                    String trimmed = item.trim();
                    // 移除引号
                    if (trimmed.startsWith("\"") && trimmed.endsWith("\"")) {
                        trimmed = trimmed.substring(1, trimmed.length() - 1);
                    }
                    if (StringUtils.isNotBlank(trimmed)) {
                        result.add(trimmed);
                    }
                }
                return result;
            } else {
                // 单个项目代码，包装成列表
                return Collections.singletonList(project);
            }
        } catch (Exception e) {
            log.warn("解析项目代码列表失败，project: {}, error: {}", project, e.getMessage());
            // 解析失败时，作为单个项目代码处理
            return Collections.singletonList(project);
        }
    }

}
