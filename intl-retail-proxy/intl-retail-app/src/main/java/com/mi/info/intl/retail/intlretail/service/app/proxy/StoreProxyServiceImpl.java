package com.mi.info.intl.retail.intlretail.service.app.proxy;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.mi.info.intl.retail.core.utils.CacheUtils;
import com.mi.info.intl.retail.exception.RetailRunTimeException;
import com.mi.info.intl.retail.intlretail.domain.common.utils.JsonUtil;
import com.mi.info.intl.retail.intlretail.service.api.proxy.RmsProxyService;
import com.mi.info.intl.retail.intlretail.service.api.proxy.StoreProxyService;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.RmsUserBaseDataResponse;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.UpdateDefaultStoreDto;
import com.mi.info.intl.retail.intlretail.service.app.proxy.converter.UpdateDefaultStoreConverter;
import com.mi.info.intl.retail.intlretail.service.app.proxy.dto.UpdateDefaultStoreApiRequest;
import com.mi.info.intl.retail.intlretail.service.app.rpc.StoreBuildRpc;
import com.mi.info.intl.retail.intlretail.service.app.rpc.StoreMainDataRpc;
import com.xiaomi.cnzone.commons.utils.JacksonUtil;
import com.xiaomi.cnzone.storeapi.model.channelbuild.common.req.AppPageRequest;
import com.xiaomi.cnzone.storeapi.model.channelbuild.common.req.RmsCallBackReq;
import com.xiaomi.cnzone.storeapi.model.v2.storenode.req.NodeQueryReq;
import com.xiaomi.cnzone.storeapi.model.v2.storenode.req.NodeSubmitReq;
import com.xiaomi.cnzone.storems.api.model.req.store.GetPositionInfoRequest;
import com.xiaomi.cnzone.storems.api.model.req.store.GetStoreLogListRequest;
import com.xiaomi.cnzone.storems.api.model.req.store.GetStorePositionLogListRequest;
import com.xiaomi.cnzone.storems.api.model.req.store.PositionListReq;
import com.xiaomi.cnzone.storems.api.model.req.store.international.GetInternationalChannelMergeListRequest;
import com.xiaomi.cnzone.storems.api.model.req.store.international.GetInternationalChannelMergeListResponse;
import com.xiaomi.cnzone.storems.api.model.req.store.international.GetInternationalChannelStoreDetailRequest;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;


@Service
public class StoreProxyServiceImpl implements StoreProxyService {
    @Autowired
    private RmsProxyService rmsProxyService;

    @Autowired
    StoreBuildRpc storeBuildRpc;

    @Autowired
    StoreMainDataRpc storeMainDataRpc;

    static final String UPDATE_STORE_PATH = "/api/data/v9.2/new_GetUserBaseData";

    static final String EDIT_STORE_PATH = "/api/data/v9.2/new_QueryBusinessData";

    static final String MENU_STATUS_HASH_KEY_PREFIX = "MenuStatus";
    static final String USER_HASH_KEY_PREFIX = "UserBaseData";
    static final String MENU_HASH_KEY_PREFIX = "Menu";

    @Override
    public void updateDefaultStore(String key, String userAccount, UpdateDefaultStoreDto dto, String userToken) {
        UpdateDefaultStoreApiRequest storeApi = UpdateDefaultStoreConverter.INSTANCE.toStoreApi(dto);
        rmsProxyService.requestByUserToken(UPDATE_STORE_PATH, JsonUtil.bean2json(storeApi), userToken, "POST");
        // 删除缓存
        CacheUtils.hdel(key, USER_HASH_KEY_PREFIX + ":" + userAccount);
        CacheUtils.hdel(key, MENU_STATUS_HASH_KEY_PREFIX + ":" + userAccount);
        CacheUtils.hdel(key, MENU_HASH_KEY_PREFIX + ":" + userAccount);
    }

    /**
     * 获取门店列表
     *
     * @param
     * @return
     */
    @Override
    public Object listStore(GetInternationalChannelMergeListRequest request, String userId, String userAccount, String userToken) {
        List<String> orgIds = listRMSIds(userToken, userId, "Store");
        if (CollectionUtils.isEmpty(orgIds)) {
            throw new RetailRunTimeException("This user is not associating to any Stores.Please contact your leader");
        }
        request.setOrgIdList(orgIds);
        return storeMainDataRpc.listStores(request);
    }

    @Override
    public Object listAllStore(GetInternationalChannelMergeListRequest request, String userId, String userAccount, String userToken) {
        List<String> orgIds = listRMSIds(userToken, userId, "Store");
        GetInternationalChannelMergeListResponse getInternationalChannelMergeListResponse = storeMainDataRpc.listStores(request);
        updateItemStatus(getInternationalChannelMergeListResponse, orgIds);
        return getInternationalChannelMergeListResponse;
    }

    public void updateItemStatus(GetInternationalChannelMergeListResponse getInternationalChannelMergeListResponse, List<String> orgIds) {
        // 使用 Optional 来避免空指针异常
        Optional.ofNullable(getInternationalChannelMergeListResponse)
                .map(GetInternationalChannelMergeListResponse::getList)
                .ifPresent(list -> {
                    list.forEach(item -> {
                        if (Objects.nonNull(item) && !orgIds.contains(item.getOrgId())) {
                            item.setStatus(10000);
                        }
                    });
                });
    }

    @Override
    public Object getStore(GetInternationalChannelStoreDetailRequest request) {
        return storeMainDataRpc.getStore(request);
    }

    /**
     * 获取门店列表
     *
     * @param
     * @return
     */
    @Override
    public Object listPosition(PositionListReq request, String userId, String userAccount, String userToken) {
        List<String> positionCodes = listRMSIds(userToken, userId, "Position");
        if (CollectionUtils.isEmpty(positionCodes)) {
            throw new RetailRunTimeException("This user is not associating to any Positions.Please contact your leader");
        }
        request.setPositionCodeList(positionCodes);
        return storeMainDataRpc.listPositions(request);
    }

    @Override
    public Object getPosition(GetPositionInfoRequest var1) {
        return storeMainDataRpc.getPosition(var1);
    }


    /**
     * 获取已提交门店列表
     *
     * @param
     * @return
     */
    @Override
    public PageInfo listSubmitStore(AppPageRequest var1) {
        return storeBuildRpc.listSubmitStore(var1);
    }

    @Override
    public Object query(NodeQueryReq request) {
        return storeBuildRpc.query(request);
    }

    @Override
    public Object audit(RmsCallBackReq var1) {
        return storeBuildRpc.audit(var1);
    }

    @Override
    public Object getSelectorAll() {
        return storeBuildRpc.getSelectorAll();
    }

    @Override
    public Object submit(NodeSubmitReq request, String userToken, RmsUserBaseDataResponse userBaseInfo) {
        //查询直属上级是否在RMS
        JSONObject baseModel = JacksonUtil.parseObj(request.getData(), JSONObject.class);
        boolean isExist = isExistInRms(userToken, userBaseInfo.getUserId());
        baseModel.put("rmsManagerFlag", isExist ? 1 : 0);
        baseModel.put("rmsUser", userBaseInfo.getUserId());
        baseModel.put("rmsUserName", userBaseInfo.getEnglishName());
        baseModel.put("areaId", userBaseInfo.getUserCountryShortCode());
        if (baseModel.containsKey("baseInfo")) {
            baseModel.getJSONObject("baseInfo").put("rmsManagerFlag", isExist ? 1 : 0);
            baseModel.getJSONObject("baseInfo").put("rmsUser", userBaseInfo.getUserId());
            baseModel.getJSONObject("baseInfo").put("rmsUserName", userBaseInfo.getEnglishName());
            baseModel.getJSONObject("baseInfo").put("areaId", userBaseInfo.getUserCountryShortCode());
        }
        request.setData(baseModel.toJSONString());
        Object result = storeBuildRpc.submit(request);
        return result;
    }

    @Override
    public Object recall(NodeSubmitReq request, String userToken, String userId) {
        storeBuildRpc.auditCancel(request);
        return storeBuildRpc.getSelectorAll();
    }

    @Override
    public Object listStoreLogs(GetStoreLogListRequest var1) {
        return storeMainDataRpc.listStoreLogs(var1);
    }

    @Override
    public Object listPositionLogs(GetStorePositionLogListRequest var1) {
        return storeMainDataRpc.listPositionLogs(var1);
    }

    boolean isExistInRms(String userToken, String userId) {
        Map map = new HashMap();
        map.put("UserId", userId);
        String resultJson = rmsProxyService.requestByUserToken(EDIT_STORE_PATH, "QueryManagerTitle", JsonUtil.bean2json(map), userToken, "POST");
        // 将 Result 字符串转换为 JSONArray
        Map<String, Object> result = JSON.parseObject(resultJson);
        // 将 JSONArray 转换为 Java List
        return (boolean) result.get("IsAppTitle");
    }

    @Override
    public List<String> listRMSIds(String userToken, String userId, String type) {
        Map map = new HashMap();
        map.put("UserId", userId);
        map.put("TypeName", type);
        String jsonResponse = rmsProxyService.requestByUserToken(EDIT_STORE_PATH, "QueryUserStoreCode", JsonUtil.bean2json(map), userToken, "POST");
        // 将 Result 字符串转换为 JSONArray
        JSONArray resultArray = JSON.parseArray(jsonResponse);
        // 将 JSONArray 转换为 Java List
        List<String> resultList = resultArray.toJavaList(String.class);
        return resultList;
    }
}
