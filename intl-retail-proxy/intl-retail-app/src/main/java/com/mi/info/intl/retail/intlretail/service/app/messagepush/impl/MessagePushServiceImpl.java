package com.mi.info.intl.retail.intlretail.service.app.messagepush.impl;

import com.mi.info.intl.retail.intlretail.domain.common.utils.JsonUtil;
import com.mi.info.intl.retail.intlretail.domain.push.PushService;
import com.mi.info.intl.retail.intlretail.domain.push.constant.MessageChannelEnum;
import com.mi.info.intl.retail.intlretail.domain.push.constant.MessageTypeEnum;
import com.mi.info.intl.retail.intlretail.domain.push.constant.PushChannelEnum;
import com.mi.info.intl.retail.intlretail.domain.push.dto.*;
import com.mi.info.intl.retail.intlretail.domain.userdevice.entity.UserDevice;
import com.mi.info.intl.retail.intlretail.domain.userdevice.repository.UserDeviceRepository;
import com.mi.info.intl.retail.intlretail.service.api.messagepush.MessagePushService;
import com.mi.info.intl.retail.intlretail.service.api.messagepush.dto.MessagePushRequest;
import com.mi.info.intl.retail.intlretail.service.api.messagepush.dto.MessagePushResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@DubboService(group = "${push.dubbo.group:}", interfaceClass = MessagePushService.class)
@Service
@Slf4j
public class MessagePushServiceImpl implements MessagePushService {
    public static final String ACTION_URL = "smartmijia://YouPinMainTabActivity";
    public static final String TYPE = "scene";
    @Autowired
    private UserDeviceRepository userDeviceRepository;
    @Autowired
    private PushService pushService;

    @Override
    public MessagePushResponse sendPushMessage(MessagePushRequest request) {
        log.info("requestBody:{}", JsonUtil.bean2json(request));
        MessagePushResponse response = new MessagePushResponse();
        try {
            // user account list不能超过500条
            if (request.getUserAccountList() == null || request.getUserAccountList().size() > 500) {
                String errorMessage = "userAccountList size is too large";
                if (request.getUserAccountList() == null) {
                    errorMessage = "userAccountList is null";
                }
                response = new MessagePushResponse(500, "sendPushMessage error", errorMessage);
                return response;
            }

            List<UserDevice> userDevices = userDeviceRepository.queryByUserAccounts(request.getUserAccountList());

            if (userDevices != null && !userDevices.isEmpty()) {
                //自定义消息类
                MessageDto messageDto = new MessageDto();
                messageDto.setPageUrl(request.getPageUrl());

                PushMessageDto pushMessageDto = new PushMessageDto();
                pushMessageDto.setMessageType(MessageTypeEnum.STANDARD_STYLE.getValue());
                //重新构造Content
                TransparentKeyValue transparentKeyValueType = new TransparentKeyValue("type", TYPE);
                TransparentKeyValue transparentKeyValueMessage = new TransparentKeyValue("message", JsonUtil.bean2json(request));
                TransparentKeyValue transparentKeyValueMessageChannel = new TransparentKeyValue("messageChannel", "2");
                List<TransparentKeyValue> transparentKeyValue = new ArrayList<>();
                transparentKeyValue.add(transparentKeyValueType);
                transparentKeyValue.add(transparentKeyValueMessage);
                transparentKeyValue.add(transparentKeyValueMessageChannel);
                PushContentDto pushContentDto = new PushContentDto(request.getTitle(), request.getContent(), transparentKeyValue);
                pushMessageDto.setContent(JsonUtil.bean2json(pushContentDto));
                pushMessageDto.setActionUrl(ACTION_URL);
                pushMessageDto.setMessageChannel(MessageChannelEnum.ORDINARY_MESSAGES.getValue());
                pushMessageDto.setType(TYPE);
                pushMessageDto.setMessage(JsonUtil.bean2json(messageDto));
                //获取第一个参数title
                pushMessageDto.setTitle(request.getTitle());
                //获取第二个参数summary
                pushMessageDto.setSummary(pushContentDto.getSummary());
                //获取第四个参数Config
                PushConfigDto pushConfigDto = new PushConfigDto();
                pushMessageDto.setConfig(JsonUtil.bean2json(pushConfigDto));

                // 分组
                Map<String, List<UserDevice>> deviceTypeMap = userDevices.stream()
                        .collect(Collectors.groupingBy(UserDevice::getDeviceType));

                // 处理 Android 设备
                List<UserDevice> androidDevices = deviceTypeMap.get("Android");
                if (androidDevices != null) {
                    List<String> androidRegIdList = androidDevices.stream()
                            .map(UserDevice::getGaId)
                            .collect(Collectors.toList());
                    pushMessageDto.setGaIds(androidRegIdList);
                    pushService.push(PushChannelEnum.IIB, pushMessageDto, androidRegIdList);
                }

                // 处理 IOS 设备
                List<UserDevice> iosDevices = deviceTypeMap.get("IOS");
                if (iosDevices != null) {
                    List<String> iosRegIdList = iosDevices.stream()
                            .map(UserDevice::getGaId)
                            .collect(Collectors.toList());
                    pushService.push(PushChannelEnum.FIRE_BASE, pushMessageDto, iosRegIdList);
                    /*List<String> failedGaIdList = firebaseApiService.sendMessageToIOS(iosRegIdList, content, title, pageUrl);
                    if (failedGaIdList != null && !failedGaIdList.isEmpty()) {
                        // 获取发送失败的设备对应的user account
                        List<String> failedUserAccounts = iosDevices.stream()
                                .filter(device -> failedGaIdList.contains(device.getGaId()))
                                .map(UserDevice::getUserAccount)
                                .collect(Collectors.toList());
                        log.info("failedUserAccounts, " + failedUserAccounts);
                        response = new MessagePushResponse(500, "sendPushMessage error", "ios send failed, " + failedUserAccounts);
                    }*/
                }
                log.info("Push success");
            }
        } catch (Exception e) {
            log.error("sendPushMessage error, ", e);
            response = new MessagePushResponse(500, "sendPushMessage error", e.getMessage());
        }
        return response;

    }
}
