package com.mi.info.intl.retail.intlretail.service.app.proxy;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mi.info.intl.retail.intlretail.domain.common.utils.JsonUtil;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.RmsUserBaseDataRequest;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.RmsUserBaseDataResponse;
import com.mi.info.intl.retail.intlretail.service.api.proxy.RetailerAppConfigService;
import com.mi.info.intl.retail.intlretail.service.api.proxy.RmsProxyService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;


@Slf4j
@Service
public class RetailerAppConfigServiceImpl implements RetailerAppConfigService {

    public static final String GET_USER_BASE_DATA_URL = "/api/data/v9.2/new_GetUserBaseData";

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    public static final String TYPE_MENU = "Menu"; // 常量，用於表示菜單類型
    private static final String TYPE_DEFAULT = "User"; // 常量，默认为User，用于获取用户信息

    @Resource
    private RmsProxyService rmsProxyService;

    @Override
    public RmsUserBaseDataResponse requestRmsGetUserInfo(String account, String token) {
        return requestInfo(account, token, TYPE_DEFAULT);
    }

    @Override
    public RmsUserBaseDataResponse requestRmsGetUserMenuInfo(String account, String token) {
        return requestInfo(account, token, TYPE_MENU);
    }

    private RmsUserBaseDataResponse requestInfo(String account, String token, String type) {
        RmsUserBaseDataResponse userInfo;
        try {
            RmsUserBaseDataRequest body = RmsUserBaseDataRequest.builder()
                    .userAccount(account)
                    .type(type).build();
            String resultJson = rmsProxyService.requestByUserToken(GET_USER_BASE_DATA_URL, JsonUtil.bean2json(body), token, HttpMethod.POST.name());
            userInfo = OBJECT_MAPPER.readValue(resultJson, RmsUserBaseDataResponse.class);
            if (StringUtils.isNotBlank(userInfo.getMenuJson())) {
                List<RmsUserBaseDataResponse.MenuInfo> menuInfos = JsonUtil.jsonArr2beanList(userInfo.getMenuJson(), RmsUserBaseDataResponse.MenuInfo.class);
                userInfo.setMenu(menuInfos);
            }
        } catch (Exception e) {
            throw new RuntimeException("requestInfo error, account is " + account + ", type is " + type, e);
        }
        return userInfo;
    }
}
