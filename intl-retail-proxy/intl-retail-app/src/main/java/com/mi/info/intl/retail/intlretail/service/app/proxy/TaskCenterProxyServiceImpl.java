package com.mi.info.intl.retail.intlretail.service.app.proxy;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.mi.info.intl.retail.cooperation.task.dto.IntlInspectionTaskQuery;
import com.mi.info.intl.retail.cooperation.task.dto.dto.InspectionTaskConfDTO;
import com.mi.info.intl.retail.cooperation.task.infra.entity.IntlInspectionMessageInstance;
import com.mi.info.intl.retail.cooperation.task.inspection.BigPromotionConfigService;
import com.mi.info.intl.retail.cooperation.task.inspection.IntlInspectionMessageInstanceService;
import com.mi.info.intl.retail.cooperation.task.inspection.IntlInspectionTaskConfService;
import com.mi.info.intl.retail.core.utils.IntlRetailAssert;
import com.mi.info.intl.retail.intlretail.domain.common.utils.JsonUtil;
import com.mi.info.intl.retail.intlretail.infra.rpc.TaskCenterServiceAdapter;
import com.mi.info.intl.retail.intlretail.infra.utils.CommonUtils;
import com.mi.info.intl.retail.intlretail.service.api.proxy.TaskCenterProxyService;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.PopWindowDTO;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.taskcenter.ConfirmPopWindowsReq;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.taskcenter.HeadCalendarReq;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.taskcenter.TaskCentterPushReq;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.taskcenter.QueryCentralBrainCardsRequest;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.taskcenter.QueryCentralBrainListRequest;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.taskcenter.SupervisorTaskHttpReq;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.taskcenter.TaskCenterCalendarReq;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.taskcenter.TaskCenterCommonReq;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.taskcenter.TaskCenterDetailReq;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.taskcenter.TaskCenterFinishTaskReq;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.taskcenter.TaskCenterInspectionConfReq;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.taskcenter.TaskCenterTaskNumReq;
import com.mi.info.intl.retail.intlretail.service.api.result.TaskCenterInspectionConfResponse;
import com.mi.info.intl.retail.intlretail.service.app.proxy.converter.CalenderPropertyInfoConvert;
import com.mi.info.intl.retail.intlretail.service.app.proxy.converter.FinishTaskPropertyInfoConvert;
import com.mi.info.intl.retail.intlretail.service.app.proxy.converter.NoNeedCompleteTaskConvert;
import com.mi.info.intl.retail.intlretail.service.app.proxy.converter.PopWindowPropertyInfoConvert;
import com.mi.info.intl.retail.intlretail.service.app.proxy.converter.TaskDetailPropertyInfoConvert;
import com.mi.info.intl.retail.intlretail.service.app.proxy.converter.TaskNumPropertyInfoConvert;
import com.mi.info.intl.retail.intlretail.service.app.proxy.converter.TaskTrackingPropertyInfoConvert;
import com.mi.info.intl.retail.org.domain.repository.IntlRmsStoreRepository;
import com.mi.info.intl.retail.org.infra.entity.IntlRmsStore;
import com.mi.info.intl.retail.user.constant.StoreGradeEnum;
import com.mi.xms.sdk.T;
import com.xiaomi.cnzone.brain.platform.api.model.req.PushTaskReq;
import com.xiaomi.cnzone.brain.platform.api.model.req.admin.ProretailOuterEventReq;
import com.xiaomi.cnzone.brain.platform.api.model.req.app.ConfirmPopWindowReq;
import com.xiaomi.cnzone.brain.platform.api.model.req.app.NoNeedCompleteTaskReq;
import com.xiaomi.cnzone.brain.platform.api.model.req.app.PopWindowReq;
import com.xiaomi.cnzone.brain.platform.api.model.req.app.TaskCalendarReq;
import com.xiaomi.cnzone.brain.platform.api.model.req.app.TaskCenterTaskDetailInfoReq;
import com.xiaomi.cnzone.brain.platform.api.model.req.app.TaskNumReq;
import com.xiaomi.cnzone.brain.platform.api.model.req.app.TaskTrackingReq;
import com.xiaomi.cnzone.brain.platform.api.model.resp.app.BusinessTypeStatusResp;
import com.xiaomi.cnzone.brain.platform.api.model.resp.app.DayCalendarResp;
import com.xiaomi.cnzone.brain.platform.api.model.resp.app.DetailTask;
import com.xiaomi.cnzone.brain.platform.api.model.resp.app.PopWindowResp;
import com.xiaomi.cnzone.brain.platform.api.model.resp.app.TaskNumResp;
import com.xiaomi.cnzone.proretail.bi.api.model.req.brain.CentralBrainReq;
import com.xiaomi.cnzone.proretail.bi.api.model.req.brain.SupervisorTaskReq;
import com.xiaomi.cnzone.proretail.bi.api.model.resp.common.MetricsResp;
import com.xiaomi.cnzone.proretail.bi.api.model.resp.common.TableResp;
import com.xiaomi.cnzone.proretail.bi.api.provider.GlobalCentralBrainProvider;
import com.xiaomi.cnzone.proretail.newcommon.account.ProretailRoleEnum;
import com.xiaomi.cnzone.proretail.newcommon.util.RetailJsonUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

@Service
@Slf4j
public class TaskCenterProxyServiceImpl implements TaskCenterProxyService {

    @Autowired
    private TaskCenterServiceAdapter taskCenterServiceAdapter;

    @Resource
    private IntlInspectionMessageInstanceService inspectionMessageInstanceService;
    @Resource
    private BigPromotionConfigService bigPromotionConfigService;

    @DubboReference(group = "${proretailbi.dubbo.group:}", check = false, timeout = 20000, retries = 0)
    private GlobalCentralBrainProvider globalCentralBrainProvider;

    @Resource
    private IntlInspectionTaskConfService intlInspectionTaskConfService;

    @Resource
    private IntlRmsStoreRepository intlRmsStoreRepository;

    private static final String CHANNEL_RETAIL_TAG = "CHANNEL_RETAIL";

    private static final String RETAIL_TENANT_ID = "2";

    @Value("${promotion.notify:500900002,100000051,100000024}")
    private List<String> notifyJobtitle;
    //
    @Value("${bigPromotion.businessTypeName.mandatory.name:SALES_REPORTING,INVENTORY_REPORTING}")
    private List<String> mandatoryItemList;
    //四选一：SAMPLE_DEVICE_REPORTING 、 TRAINING_REPORTING、DISPLAY_REPORTING、 STORE_CHECK
    @Value("${bigPromotion.businessTypeName.selectOne.name:SAMPLE_DEVICE_REPORTING,TRAINING_REPORTING,DISPLAY_REPORTING,STORE_CHECK}")
    private List<String> selectOneItemList;

    @Value("${supervisor.businessType.ids:201,202}")
    private List<Long> supervisorBusinessTypeIds;

    //中文 新品物料巡检任务   英文  New product material task 401,402,403
    @Value("${new.product.material.businessType.ids:401,402,403}")
    private List<Long> newProductMaterialBusinessTypeIds;


    private static final Integer UN_DO = 0;
    private static final Integer DOING = 1;
    private static final String WINDOW_TYPE = "supervisor-new-product";
    private static final String PROMOTER = "promoter";
    private static final String SUPERVISOR_NEW_TASK = "supervisor-new-task";
    private static final String MESSAGE_KEY1 = "message.templateContent1";
    private static final String MESSAGE_KEY2 = "message.templateContent2";

    //1 大促中间 0 非大大促
    private static final int BIG_PROMOTION_TYPE_MIDDLE = 1;
    private static final int BIG_PROMOTION_TYPE_NORMAL = 0;
    //status 为3 是全量查询
    private static final Integer FULL_QUERY_STATUS = 3;
    private static final Integer UNDO_QUERY_STATUS = 0;
    private static final Integer MANAGE_CHECKOUT_FORCE = 1;

    //New product material task key
    private static final String NEW_PRODUCT_MATERIAL_TASK_KEY = "new.product.material.task";
    private static final String NEW_PRODUCT_MATERIAL_TASK_CAPITAL = "NEW_PRODUCT_MATERIAL_TASK";
    private static final String NEW_PRODUCT_MATERIAL_TASK_EN = "New product material task";
    private static final List<String> NEW_PRODUCT_INSPECTION_TOUR_LIST = Arrays.asList(
            "NEW_PRODUCT_INSPECTION_TOUR_PRICE_TAG", "NEW_PRODUCT_INSPECTION_TOUR_POSM",
            "NEW_PRODUCT_INSPECTION_TOUR_DUMMY");

    @Override
    public Object queryCentralBrainList(QueryCentralBrainListRequest request) {

        CentralBrainReq req = new CentralBrainReq();
        req.setTaskBatchId(request.getTaskBatchId());
        req.setPageNo(request.getPageNo());
        req.setPageSize(request.getPageSize());
        req.setOrderKey(request.getOrderKey());
        req.setOrderType(request.getOrderType());
        req.setStartTime(request.getStartTime());
        req.setEndTime(request.getEndTime());
        Result<TableResp> tableRespResult = globalCentralBrainProvider.queryCentralBrainList(req);
        return tableRespResult.getData();
    }

    @Override
    public Object queryCentralBrainCards(QueryCentralBrainCardsRequest request) {

        CentralBrainReq req = new CentralBrainReq();
        req.setTaskBatchId(request.getTaskBatchId());
        req.setStartTime(request.getStartTime());
        req.setEndTime(request.getEndTime());
        Result<MetricsResp> metricsRespResult = globalCentralBrainProvider.queryCentralBrainCards(req);
        log.info("LogCentralBrainCards org_req:{} request:{},result:{}", JsonUtil.bean2json(request), req,
                metricsRespResult);
        return metricsRespResult.getData();
    }

    @Override
    public Object getCalendarByType(TaskCenterCalendarReq req) {

        req.setLanguageKey(this.getLanguageKeyFromHeader());
        TaskCalendarReq taskCalendarReq = CalenderPropertyInfoConvert.INSTANCE.toTaskCenterPropertyInfo(req);
        taskCalendarReq.setRetailAppSign(CHANNEL_RETAIL_TAG);
        taskCalendarReq.setRetailTenantId(RETAIL_TENANT_ID);
        taskCalendarReq.setRoleEnum(ProretailRoleEnum.SHOP_CLERK);
        log.info("getCalendarByType req:{}", JsonUtil.bean2json(taskCalendarReq));
        DayCalendarResp dayCalendar = taskCenterServiceAdapter.getDayCalendar(taskCalendarReq);
        // 校验获取到的数据是否为空
        if (dayCalendar == null) {
            log.warn("getCalendarByType returned null for the request: {}", JsonUtil.bean2json(taskCalendarReq));
            return null;  // 或者根据实际情况返回一个默认值
        }
        //过滤之前
        log.info("getCalendarByType initial dayCalendar: {}", JsonUtil.bean2json(dayCalendar));
        filterSupervisorBusinessTypeIds(dayCalendar, req.getLanguageKey(), true);
        //过滤之后
        log.info("getCalendarByType filtered dayCalendar: {}", JsonUtil.bean2json(dayCalendar));
        return dayCalendar;
    }

    /**
     * 如果之前是督导的角色，现在是促销员的角色，会做两个动作
     * 1.禁用未完成未过期的，但是有一个问题，如果已完成的任务（不能禁止），促销员还是可以查到督导已完成的任务。
     * 2.为了解决问题1，添加一个过滤器，过滤掉 属于201，202的督导的任务
     */
    private void filterSupervisorBusinessTypeIds(DayCalendarResp dayCalendar, String countryShortCode, boolean isSupervisor) {
        if (dayCalendar == null) {
            return;
        }
        // 过滤时间列表
        List<DetailTask> timeList = dayCalendar.getTimeList();
        if (CollectionUtils.isNotEmpty(timeList)) {
            timeList = filterByBusinessTypeId(timeList, countryShortCode, isSupervisor);
            dayCalendar.setTimeList(timeList);
        }
        // 过滤任务类型列表
        List<DayCalendarResp.TaskType> typeList = dayCalendar.getTypeList();
        if (CollectionUtils.isNotEmpty(typeList)) {
            for (DayCalendarResp.TaskType taskType : typeList) {
                List<DetailTask> value = taskType.getValue();
                if (CollectionUtils.isNotEmpty(value)) {
                    value = filterByBusinessTypeId(value, countryShortCode, isSupervisor);
                    taskType.setValue(value);
                }
            }
            dayCalendar.setTypeList(typeList);
        }
    }

    // 提取过滤逻辑为一个通用方法，避免重复代码
    private List<DetailTask> filterByBusinessTypeId(List<DetailTask> tasks, String countryShortCode, boolean isSupervisor) {
        List<DetailTask> taskList = new ArrayList<>();
        List<DetailTask> newProductMaterials = tasks.stream()
                .filter(task -> task.getBusinessTypeId() != null && newProductMaterialBusinessTypeIds.contains(
                        task.getBusinessTypeId())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(newProductMaterials)) {
            //如果 其中一个任务 taskStatus = 0 就取这一个任务
            DetailTask detailTask = getNewProductMaterial(newProductMaterials, countryShortCode);
            if (detailTask != null) {
                taskList.add(detailTask);
            }
        }
        if (!isSupervisor) {
            List<DetailTask> collect = tasks.stream()
                    .filter(task -> task.getBusinessTypeId() != null && !supervisorBusinessTypeIds.contains(
                            task.getBusinessTypeId())).collect(Collectors.toList());
            taskList.addAll(collect);
        } else {
            List<DetailTask> collect = tasks.stream()
                    .filter(task -> task.getBusinessTypeId() != null && !newProductMaterialBusinessTypeIds.contains(
                            task.getBusinessTypeId())).collect(Collectors.toList());
            taskList.addAll(collect);
        }
        return taskList;
    }

    private DetailTask getNewProductMaterial(List<DetailTask> newProductMaterials, String countryShortCode) {
        DetailTask detailTask = newProductMaterials.stream().filter(task -> task.getTaskStatus() == 0).findFirst()
                .orElse(newProductMaterials.get(0));
        if (null != detailTask) {
            //NEW_PRODUCT_MATERIAL_TASK_KEY
            detailTask.setBusinessTypeName(NEW_PRODUCT_MATERIAL_TASK_CAPITAL);
            detailTask.setTitle(NEW_PRODUCT_MATERIAL_TASK_EN);
            detailTask.setEventTitle(T.tr(NEW_PRODUCT_MATERIAL_TASK_KEY, countryShortCode));
            return detailTask;
        }
        return null;
    }

    @Override
    public void noNeedCompleteTask(TaskCenterFinishTaskReq req) {
        NoNeedCompleteTaskReq noNeedCompleteTaskReq = NoNeedCompleteTaskConvert.INSTANCE.toNoNeedCompleteTaskReq(req);
        noNeedCompleteTaskReq.setRetailAppSign(CHANNEL_RETAIL_TAG);
        noNeedCompleteTaskReq.setRetailTenantId(RETAIL_TENANT_ID);
        taskCenterServiceAdapter.noNeedCompleteTask(noNeedCompleteTaskReq);
    }

    @Override
    public String finishOuterTask(TaskCenterFinishTaskReq req) {
        ProretailOuterEventReq proretailOuterEventReq = FinishTaskPropertyInfoConvert.INSTANCE.toProretailOuterEventReq(
                req);
        proretailOuterEventReq.setRetailAppSign(CHANNEL_RETAIL_TAG);
        proretailOuterEventReq.setRetailTenantId(RETAIL_TENANT_ID);
        return taskCenterServiceAdapter.finishOuterTask(proretailOuterEventReq);
    }


    /**
     * 任务数量统计
     */
    @Override
    public Object queryTaskNum(TaskCenterTaskNumReq req) {
        //TaskNumReq
        TaskNumReq taskNumReq = TaskNumPropertyInfoConvert.INSTANCE.toTaskNumReq(req);
        return taskCenterServiceAdapter.queryTaskNum(taskNumReq);
    }

    /**
     * 任务数量统计
     */
    @Override
    public Object queryNewProductTaskNum(TaskCenterTaskNumReq req) {
        //TaskNumReq
        TaskNumReq taskNumReq = TaskNumPropertyInfoConvert.INSTANCE.toTaskNumReq(req);
        BusinessTypeStatusResp businessTypeStatusResp = taskCenterServiceAdapter.queryBusinessTypeTaskNum(taskNumReq);
        log.info("queryTaskNum taskNumReq:{}", JsonUtil.bean2json(businessTypeStatusResp));
        return buildTaskNumResponse(businessTypeStatusResp, true);
    }

    /**
     * 任务详情
     */
    @Override
    public Object getDetailTaskInfo(TaskCenterDetailReq req) {
        req.setLanguageKey(this.getLanguageKeyFromHeader());
        TaskCenterTaskDetailInfoReq taskDetailInfoReq = TaskDetailPropertyInfoConvert.INSTANCE.toTaskDetailInfoReq(req);
        taskDetailInfoReq.setRetailAppSign(CHANNEL_RETAIL_TAG);
        taskDetailInfoReq.setRetailTenantId(RETAIL_TENANT_ID);
        log.info("getDetailTaskInfo req:{}", JsonUtil.bean2json(taskDetailInfoReq));
        return taskCenterServiceAdapter.getDetailTaskInfo(taskDetailInfoReq);
    }

    /**
     * 事件详情
     */
    @Override
    public Object getDetailTaskEventInfo(TaskCenterDetailReq req) {
        req.setLanguageKey(this.getLanguageKeyFromHeader());
        TaskCenterTaskDetailInfoReq taskDetailInfoReq = TaskDetailPropertyInfoConvert.INSTANCE.toTaskDetailInfoReq(req);
        taskDetailInfoReq.setRetailAppSign(CHANNEL_RETAIL_TAG);
        taskDetailInfoReq.setRetailTenantId(RETAIL_TENANT_ID);
        log.info("getDetailTaskEventInfo req:{}", JsonUtil.bean2json(taskDetailInfoReq));
        return taskCenterServiceAdapter.getDetailTaskEventInfo(taskDetailInfoReq);

    }

    /**
     * 弹窗任务列表
     */
    @Override
    public PopWindowDTO getPopWindowContent(TaskCenterCommonReq req) {

        // 获取语言
        String languageKey = this.getLanguageKeyFromHeader();

        //先获取大脑任务
        //先判断是否新品任务
        Long miId = req.getMiId();
        boolean hasMatchingJobTitle = notifyJobtitle.stream()
                .anyMatch(item -> item.equals(req.getJobValue()));

        PopWindowDTO popWindowDTO = new PopWindowDTO();

        if (hasMatchingJobTitle) {
            log.info("getPopWindowContent hasMatchingJobTitle is new product task {}", req);

            PopWindowDTO.PopWindowDetailDTO popWindowDetailDTO = new PopWindowDTO.PopWindowDetailDTO();
            //查询消息推送表
            IntlInspectionMessageInstance messageInstance = inspectionMessageInstanceService
                    .getOne(Wrappers.<IntlInspectionMessageInstance>lambdaQuery()
                            .eq(IntlInspectionMessageInstance::getNotify, Boolean.FALSE)
                            .eq(IntlInspectionMessageInstance::getMid, miId)
                            .eq(IntlInspectionMessageInstance::getIsDeleted, 0));
            if (null != messageInstance) {
                log.info("查询到消息推送表数据,{} ", messageInstance);
                String params = messageInstance.getParams();
                Map<String, String> paramMap = parseParamString(params);
                // 从Map中获取各字段值
                String promotionName = paramMap.getOrDefault("promotionName", "");
                String startTime = paramMap.getOrDefault("startTime", "");
                String endTime = paramMap.getOrDefault("endTime", "");


                //根据语言类型 拼接消息推送内容
                String languageCode = messageInstance.getLanguageCode();


                    String content1 = T.tr(MESSAGE_KEY1, languageKey);

                    String content2 = T.tr(MESSAGE_KEY2, languageKey);

                    //兜底方案
                    if (MESSAGE_KEY1.equals(content1)) {
                        content1 = T.tr(MESSAGE_KEY1, languageCode);
                        content2 = T.tr(MESSAGE_KEY2, languageCode);
                    }

                    // 内容加粗换行
                    String nameAndTime = String.format("<strong>%s, %s-%s</strong><br/>",
                            promotionName,
                            startTime,
                            endTime);

                   String content = String.format("%s%s<br/>%s",
                             nameAndTime,
                             content1,
                             content2);



                popWindowDTO.setMiId(miId);
                popWindowDetailDTO.setTaskInstanceId(String.valueOf(messageInstance.getId()));
                popWindowDetailDTO.setTitle(messageInstance.getTitle());
                popWindowDetailDTO.setContent(content);
                popWindowDetailDTO.setTaskType(WINDOW_TYPE);
                popWindowDTO.setCustomList(Collections.singletonList(popWindowDetailDTO));
            }
        }

        String taskType = hasMatchingJobTitle ? SUPERVISOR_NEW_TASK : PROMOTER;
        PopWindowReq popWindowReq = PopWindowPropertyInfoConvert.INSTANCE.toPopWindowReq(req);
        popWindowReq.setRetailAppSign(CHANNEL_RETAIL_TAG);
        popWindowReq.setRetailTenantId(RETAIL_TENANT_ID);
        popWindowReq.setTaskType(taskType);
        popWindowReq.setLanguageKey(languageKey);
        PopWindowResp popWindowContent = taskCenterServiceAdapter.getPopWindowContent(popWindowReq);

        if (Objects.nonNull(popWindowContent) && CollUtil.isNotEmpty(popWindowContent.getList())) {
            popWindowDTO.setList(convert2PopWindowDTO(popWindowContent));
        }

        return popWindowDTO;
    }

    private Map<String, String> parseParamString(String params) {
        Map<String, String> resultMap = new HashMap<>();
        if (params == null || params.isEmpty()) {
            return resultMap;
        }

        // 使用中文顿号分割不同参数
        String[] paramPairs = params.split(",");
        for (String pair : paramPairs) {
            // 使用冒号分割参数名和参数值
            String[] keyValue = pair.split(":", 2);
            if (keyValue.length == 2) {
                resultMap.put(keyValue[0].trim(), keyValue[1].trim());
            }
        }
        return resultMap;
    }

    private List<PopWindowDTO.PopWindowDetailDTO> convert2PopWindowDTO(PopWindowResp popWindowContent) {

        if (CollUtil.isEmpty(popWindowContent.getList())) {

            return null;
        }

        return popWindowContent.getList().stream().map(popWindowDetail -> {
            PopWindowDTO.PopWindowDetailDTO popWindowDetailDTO = new PopWindowDTO.PopWindowDetailDTO();

            String taskInstanceIds = popWindowDetail.getTaskInstanceIds()
                    .stream()
                    .filter(Objects::nonNull)  // 过滤 null 值
                    .map(Object::toString)     // 将 Long 转为 String
                    .collect(Collectors.joining(","));

            popWindowDetailDTO.setTaskInstanceId(taskInstanceIds);
            popWindowDetailDTO.setTitle(popWindowDetail.getTitle());
            popWindowDetailDTO.setContent(popWindowDetail.getContent());
            popWindowDetailDTO.setRedFlag(popWindowDetail.getRedFlag());
            popWindowDetailDTO.setTaskType(popWindowDetail.getType());
            return popWindowDetailDTO;
        }).collect(Collectors.toList());

    }

    /**
     * 确认弹窗
     */
    @Override
    public void confirmPopWindow(ConfirmPopWindowsReq req) {
        log.info("确认弹窗请求参数:{}", req);
        for (ConfirmPopWindowsReq.ConfirmPopWindowDetail detail : req.getList()) {
            //大促弹窗已读
            if (StringUtils.isNotBlank(detail.getTaskType()) && detail.getTaskType().equals(WINDOW_TYPE)) {
                List<String> taskIdList = req.getList().stream().filter(Objects::nonNull)
                        .map(ConfirmPopWindowsReq.ConfirmPopWindowDetail::getTaskInstanceId)
                        .collect(Collectors.toList());

                //标识消息实例为已读
                inspectionMessageInstanceService.update(Wrappers.<IntlInspectionMessageInstance>lambdaUpdate()
                        .set(IntlInspectionMessageInstance::getNotify, Boolean.TRUE)
                        .set(IntlInspectionMessageInstance::getUpdatedBy, req.getMiId())
                        .set(IntlInspectionMessageInstance::getUpdatedAt, System.currentTimeMillis() / 1000)
                        .in(IntlInspectionMessageInstance::getId, taskIdList)
                        .eq(IntlInspectionMessageInstance::getMid, req.getMiId()));

            }

            //大脑侧返回的TaskInstanceId 可能包含多个逗号分隔的TaskInstanceId，需要拆分处理
            //如:"123,321,456,678"
            List<ConfirmPopWindowReq.ConfirmPopWindow> confirmPopWindowList =
                    Arrays.stream(detail.getTaskInstanceId().split(","))
                            .map(String::trim)
                            .filter(StringUtils::isNotBlank)
                            .map(id -> {
                                ConfirmPopWindowReq.ConfirmPopWindow window =
                                        new ConfirmPopWindowReq.ConfirmPopWindow();
                                window.setTaskInstanceId(Long.valueOf(id));
                                window.setRedFlag(detail.getRedFlag());
                                return window;
                            })
                    .collect(Collectors.toList());

            ConfirmPopWindowReq confirmPopWindowReq = new ConfirmPopWindowReq();
            confirmPopWindowReq.setRetailAppSign(CHANNEL_RETAIL_TAG);
            confirmPopWindowReq.setRetailTenantId(RETAIL_TENANT_ID);
            confirmPopWindowReq.setList(confirmPopWindowList);
            log.info("taskCenterServiceAdapter confirmPopWindow:{}", confirmPopWindowReq);
            taskCenterServiceAdapter.confirmPopWindow(confirmPopWindowReq);
        }

    }

    /**
     * 获取总部角色视图列表
     */
    @Override
    public Object getCalendarForHead(HeadCalendarReq req) {
        TaskTrackingReq taskTrackingReq = TaskTrackingPropertyInfoConvert.INSTANCE.toTaskTrackingReq(req);
        return taskCenterServiceAdapter.taskTracking(taskTrackingReq);
    }

    @Override
    public Object getEventCalendarByType(TaskCenterCalendarReq req) {
        String countryShortCode = req.getCountryShortCode();
        // 查询新品大促任务配置
        long startTime = System.currentTimeMillis();
        Map<String, Boolean> newBigPromotionMap = bigPromotionConfigService.hasBigPromotionTaskConfig(
                Arrays.asList(countryShortCode));
        log.info("hasBigPromotionTaskConfig countryShortCode:{} cost:{}", req.getCountryShortCode(),
                System.currentTimeMillis() - startTime);
        boolean isNewBigPromotion = Boolean.TRUE.equals(newBigPromotionMap.get(countryShortCode));
        // 转换请求
        TaskCalendarReq taskCalendarReq = CalenderPropertyInfoConvert.INSTANCE.toTaskCenterPropertyInfo(req);
        taskCalendarReq.setRetailAppSign(CHANNEL_RETAIL_TAG);
        taskCalendarReq.setRetailTenantId(RETAIL_TENANT_ID);
        taskCalendarReq.setRoleEnum(ProretailRoleEnum.SHOP_CLERK);
        taskCalendarReq.setLanguageKey(this.getLanguageKeyFromHeader());
        //如果是大促，并且
        boolean bigPromotionManage = checkNewBigPromotionManageCheckout(isNewBigPromotion, req.getStatus(), req.getManageCheckout());
        if (bigPromotionManage) {
            taskCalendarReq.setStatus(FULL_QUERY_STATUS);
        }
        log.info("getEventCalendarByType req:{}", JsonUtil.bean2json(taskCalendarReq));

        // 获取日历任务
        DayCalendarResp dayEventCalendar = taskCenterServiceAdapter.getDayEventCalendar(taskCalendarReq);
        // 更新每个任务的新品大促类型
        updateNewBigPromotionTypeForTasks(dayEventCalendar, isNewBigPromotion, req.getManageCheckout(),
                bigPromotionManage);
        //过滤之前
        log.info("getEventCalendarByType before filter:{}", JsonUtil.bean2json(dayEventCalendar));
        filterSupervisorBusinessTypeIds(dayEventCalendar, this.getLanguageKeyFromHeader(), true);
        log.info("getEventCalendarByType after filter:{}", JsonUtil.bean2json(dayEventCalendar));
        return dayEventCalendar;
    }
    
    @Override
    public Object queryEventTaskNum(TaskCenterTaskNumReq req) {
        // 创建请求对象并打印日志
        TaskNumReq taskNumReq = new TaskNumReq();
        taskNumReq.setArea(req.getArea());
        taskNumReq.setMid(req.getMiId());
        taskNumReq.setOrgId(req.getOrgId());
        taskNumReq.setStartTimeStamp(req.getStartTimeStamp());
        taskNumReq.setEndTimeStamp(req.getEndTimeStamp());
        log.info("queryEventTaskNum req:{}", JsonUtil.bean2json(taskNumReq));

        // 查询任务数量
        TaskNumResp taskNumResp = taskCenterServiceAdapter.queryEventTaskNum(taskNumReq);
        taskNumResp.setInspectionTime(Optional.ofNullable(taskNumResp.getInspectionTime()).orElse(0));

        // 判断是否为新品大促
        boolean isNewBigPromotion =
                Optional.ofNullable(bigPromotionConfigService.hasBigPromotionTaskConfig(Arrays.asList(req.getArea())))
                        .map(map -> map.get(req.getArea()))
                        .orElse(false);

        if (!isNewBigPromotion) {
            // 如果不是新品大促，清空业务类型列表并返回
            taskNumResp.setInStoreTime(Optional.ofNullable(taskNumResp.getInStoreTime()).orElse(0));
            taskNumResp.setEventBusinessTypeList(Collections.emptyList());
            taskNumResp.setUndoBusinessTypeNameList(Collections.emptyList());
            return taskNumResp;
        }
        //大促期间，不管控 巡检时长  inspectionTime == 0
        taskNumResp.setInspectionTime(0);
        //大促期间，不管控 入店时长  inStoreTime == 0
        taskNumResp.setInStoreTime(0);
        // 初始化计数
        int allTaskNum = 0;
        int undoTaskNum = 0;
        List<TaskNumResp.EventBusinessType> eventBusinessTypeList = taskNumResp.getEventBusinessTypeList();

        // 如果事件业务类型列表为空，返回默认值
        if (CollectionUtils.isEmpty(eventBusinessTypeList)) {
            log.info("eventBusinessTypeList is empty");
            return setTaskNumRespDefaults(taskNumResp);
        }

        // 计算强制任务数量
        long mandatoryCount = eventBusinessTypeList.stream()
                .filter(e -> mandatoryItemList.contains(e.getBusinessTypeName()))
                .count();
        allTaskNum = (int) mandatoryCount;

        // 计算强制未完成任务数量
        long mandatoryUndoCount = eventBusinessTypeList.stream()
                .filter(e -> mandatoryItemList.contains(e.getBusinessTypeName()) &&
                        Objects.equals(UN_DO, e.getStatus()))
                .count();
        undoTaskNum = (int) mandatoryUndoCount;

        // 检查是否有selectOne任务并调整计数
        boolean hasSelectOneItem = eventBusinessTypeList.stream()
                .anyMatch(e -> selectOneItemList.contains(e.getBusinessTypeName()));

        if (hasSelectOneItem) {
            allTaskNum++;
            undoTaskNum += eventBusinessTypeList.stream()
                    .anyMatch(e -> selectOneItemList.contains(e.getBusinessTypeName()) &&
                            Objects.equals(DOING, e.getStatus())) ? 0 : 1;
        }
        // 设置任务状态
        taskNumResp.setAllTaskNum(allTaskNum);
        taskNumResp.setCompletedNum(allTaskNum - undoTaskNum);
        taskNumResp.setUnCompletedNum(undoTaskNum);
        return taskNumResp;
    }
    

    /**
     * 如果是在大促期间， 并且manageCheckout 设置为1 ，表示用户签出处理，那边可以从用户传入为3（查询全量）再过滤已完成的。做取巧处理。
     */
    private boolean checkNewBigPromotionManageCheckout(boolean isNewBigPromotion, Integer status,
            Integer manageCheckout) {
        //isNewBigPromotion 为 true，status 为 0 (全量)， manageCheckout = 1 （强制） 的时候
        return isNewBigPromotion && Objects.equals(status, UNDO_QUERY_STATUS) && Objects.equals(manageCheckout,
                MANAGE_CHECKOUT_FORCE);
    }

    // 提取的更新新品大促类型的方法
    private void updateNewBigPromotionTypeForTasks(DayCalendarResp dayEventCalendar, boolean isNewBigPromotion,
            Integer manageCheckout, boolean bigPromotionManage) {
        if (dayEventCalendar == null) {
            return;
        }
        // 更新单个任务和任务类型下的所有任务
        updateTaskPromotionType(dayEventCalendar.getTimeList(), isNewBigPromotion, manageCheckout, bigPromotionManage);
        updateTaskPromotionTypeInTypes(dayEventCalendar.getTypeList(), isNewBigPromotion, bigPromotionManage);
    }

    // 更新任务的新品大促的签出
    private void updateTaskPromotionType(List<DetailTask> timeList, boolean isNewBigPromotion, Integer manageCheckout, boolean bigPromotionManage) {
        if (CollectionUtils.isEmpty(timeList)) {
            return;
        }
        // 非大促：标记所有为普通，然后结束
        if (!isNewBigPromotion) {
            timeList.forEach(task -> updateTask(task, false));
            return;
        }
        // 大促但无管控：标记所有为大促，然后结束
        if (!Objects.equals(manageCheckout, DOING)) {
            timeList.forEach(task -> updateTask(task, true));
            return;
        }
        // 大促且有管控：先过滤出要保留的业务类型
        List<String> allItemList = new ArrayList<>();
        allItemList.addAll(selectOneItemList);
        allItemList.addAll(mandatoryItemList);
        List<DetailTask> promotionTasks = timeList.stream()
                .filter(task -> allItemList.contains(task.getBusinessTypeName()))
                .collect(Collectors.toList());
        // 用过滤后的列表替换原 timeList
        timeList.clear();
        timeList.addAll(promotionTasks);
        if (timeList.isEmpty()) {
            return;
        }
        // 再次按 “已完成一个 selectOne” 规则做二次过滤
        boolean isCompletedOne = checkCompletedOne(timeList);
        List<DetailTask> finalTasks = timeList.stream()
                .filter(task -> !(isCompletedOne
                        && StringUtils.isNotBlank(task.getBusinessTypeName())
                        && selectOneItemList.contains(task.getBusinessTypeName())))
                .collect(Collectors.toList());
        // 用最终列表再替换一次
        timeList.clear();
        timeList.addAll(finalTasks);
        if (bigPromotionManage) {
            timeList.removeIf(task -> Objects.equals(task.getStatus(), DOING));
        }
        // 最后标记它们为大促
        timeList.forEach(task -> updateTask(task, true));
    }

    // 更新任务类型下的所有任务
    private void updateTaskPromotionTypeInTypes(List<DayCalendarResp.TaskType> typeList, boolean isNewBigPromotion, boolean bigPromotionManage) {
        // 如果列表为空，直接返回
        if (CollectionUtils.isEmpty(typeList)) {
            return;
        }
        // 同时完成：1）如需管理大促，删掉任务状态为 DOING（原逻辑）
        //           2）删掉空的 TaskType
        typeList.removeIf(taskType -> {
            List<DetailTask> tasks = taskType.getValue();
            if (CollectionUtils.isEmpty(tasks)) {
                // 没有任务，整个类型删掉
                return true;
            }
            if (bigPromotionManage) {
                // 删除符合条件的明细
                tasks.removeIf(task -> Objects.equals(task.getStatus(), DOING));
            }
            // 如果删完后没有任务，也删掉这个类型
            return tasks.isEmpty();
        });

        // 对剩下的每个任务执行更新
        for (DayCalendarResp.TaskType taskType : typeList) {
            for (DetailTask detail : taskType.getValue()) {
                updateTask(detail, isNewBigPromotion);
            }
        }
    }

    // 更新单个任务的新品大促类型
    private void updateTask(DetailTask detailTask, boolean isNewBigPromotion) {
        detailTask.setNewBigPromotionType(
                isNewBigPromotion ? BIG_PROMOTION_TYPE_MIDDLE : BIG_PROMOTION_TYPE_NORMAL);

        if (isNewBigPromotion) {
            detailTask.setMandatoryItems(mandatoryItemList);
        } else {
            detailTask.setMandatoryItems(new ArrayList<>());
        }
    }

    private boolean checkCompletedOne(List<DetailTask> timeList) {
        return timeList.stream().anyMatch(
                task -> StringUtils.isNotBlank(task.getBusinessTypeName()) &&
                        selectOneItemList.contains(task.getBusinessTypeName()) &&
                        Objects.equals(task.getStatus(), DOING));
    }
    
    @Override
    public Object queryNewProductEventTaskNum(TaskCenterTaskNumReq req) {
        // 创建请求对象并记录日志
        TaskNumReq taskNumReq = buildTaskNumReq(req);
        log.info("queryNewProductEventTaskNum req:{}", JsonUtil.bean2json(taskNumReq));
        // 查询业务数据
        BusinessTypeStatusResp businessTypeStatusResp = taskCenterServiceAdapter.queryBusinessTypeEventTaskNum(
                taskNumReq);
        log.info("queryNewProductEventTaskNum resp:{}", JsonUtil.bean2json(businessTypeStatusResp));
        TaskNumResp taskNumResp = buildTaskNumResponse(businessTypeStatusResp, false);
        // 判断是否为新品大促
        boolean isNewBigPromotion = isNewBigPromotion(req.getArea());
        if (!isNewBigPromotion) {
            return handleNonPromotionResponse(taskNumResp);
        }
        return handlePromotionResponse(taskNumResp);
    }

    // 辅助方法：检查是否为大促
    private boolean isNewBigPromotion(String area) {
        return Optional.ofNullable(bigPromotionConfigService.hasBigPromotionTaskConfig(Arrays.asList(area)))
                .map(map -> map.get(area))
                .orElse(false);
    }

    // 辅助方法：处理大促情况
    private TaskNumResp handlePromotionResponse(TaskNumResp taskNumResp) {
        // 大促期间不管控时长
        taskNumResp.setInspectionTime(0);
        taskNumResp.setInStoreTime(0);
        List<TaskNumResp.EventBusinessType> eventBusinessTypeList = taskNumResp.getEventBusinessTypeList();
        if (CollectionUtils.isEmpty(eventBusinessTypeList)) {
            log.info("eventBusinessTypeList is empty");
            return setTaskNumRespDefaults(taskNumResp);
        }
        // 计算强制任务数量
        long mandatoryCount = countMandatoryTasks(eventBusinessTypeList);
        int allTaskNum = (int) mandatoryCount;

        // 计算强制未完成任务数量
        long mandatoryUndoCount = countMandatoryUndoTasks(eventBusinessTypeList);
        int undoTaskNum = (int) mandatoryUndoCount;

        // 检查并处理selectOne任务
        if (hasSelectOneItem(eventBusinessTypeList)) {
            allTaskNum++;
            if (!hasDoingSelectOneItem(eventBusinessTypeList)) {
                undoTaskNum++;
            }
        }
        // 设置最终任务数量
        taskNumResp.setAllTaskNum(allTaskNum);
        taskNumResp.setCompletedNum(allTaskNum - undoTaskNum);
        taskNumResp.setUnCompletedNum(undoTaskNum);
        return taskNumResp;
    }

    // 辅助方法：计算强制任务数量
    private long countMandatoryTasks(List<TaskNumResp.EventBusinessType> eventBusinessTypeList) {
        return eventBusinessTypeList.stream()
                .filter(e -> mandatoryItemList.contains(e.getBusinessTypeName()))
                .count();
    }

    // 辅助方法：计算强制未完成任务数量
    private long countMandatoryUndoTasks(List<TaskNumResp.EventBusinessType> eventBusinessTypeList) {
        return eventBusinessTypeList.stream()
                .filter(e -> mandatoryItemList.contains(e.getBusinessTypeName()) &&
                        Objects.equals(UN_DO, e.getStatus()))
                .count();
    }

    // 辅助方法：检查是否有selectOne任务
    private boolean hasSelectOneItem(List<TaskNumResp.EventBusinessType> eventBusinessTypeList) {
        return eventBusinessTypeList.stream()
                .anyMatch(e -> selectOneItemList.contains(e.getBusinessTypeName()));
    }

    // 辅助方法：检查是否有进行中的selectOne任务
    private boolean hasDoingSelectOneItem(List<TaskNumResp.EventBusinessType> eventBusinessTypeList) {
        return eventBusinessTypeList.stream()
                .anyMatch(e -> selectOneItemList.contains(e.getBusinessTypeName()) &&
                        Objects.equals(DOING, e.getStatus()));
    }

    // 辅助方法：处理非大促情况
    private TaskNumResp handleNonPromotionResponse(TaskNumResp taskNumResp) {
        taskNumResp.setEventBusinessTypeList(Collections.emptyList());
        taskNumResp.setUndoBusinessTypeNameList(Collections.emptyList());
        return taskNumResp;
    }

    // 辅助方法：构建任务数量请求
    private TaskNumReq buildTaskNumReq(TaskCenterTaskNumReq req) {
        TaskNumReq taskNumReq = new TaskNumReq();
        taskNumReq.setArea(req.getArea());
        taskNumReq.setMid(req.getMiId());
        taskNumReq.setOrgId(req.getOrgId());
        taskNumReq.setStartTimeStamp(req.getStartTimeStamp());
        taskNumReq.setEndTimeStamp(req.getEndTimeStamp());
        return taskNumReq;
    }

    // 辅助方法：构建基础响应
    private TaskNumResp buildTaskNumResponse(BusinessTypeStatusResp businessTypeStatusResp, boolean isPromoter) {
        TaskNumResp taskNumResp = new TaskNumResp();
        taskNumResp.setInspectionTime(Optional.ofNullable(businessTypeStatusResp.getInspectionTime()).orElse(0));
        // 检查新产品物料状态
        Integer newProductMaterial = checkNewProductMaterial(businessTypeStatusResp.getUnCompletedNum(),
                businessTypeStatusResp.getCompletedNum());
        // 设置响应字段
        taskNumResp.setCompletedNum(
                conversionCompletedBusinessType(businessTypeStatusResp.getCompletedNum(), newProductMaterial,
                        isPromoter));
        taskNumResp.setUnCompletedNum(
                conversionUnCompletedBusinessType(businessTypeStatusResp.getUnCompletedNum(), newProductMaterial,
                        isPromoter));
        taskNumResp.setAllTaskNum(conversionAllBusinessType(businessTypeStatusResp.getAllTaskNum(), isPromoter));
        taskNumResp.setNoNeedCompletedNum(
                conversionNoNeedCompletedBusinessType(businessTypeStatusResp.getNoNeedCompletedNum(), isPromoter));
        taskNumResp.setStartTime(businessTypeStatusResp.getStartTime());
        taskNumResp.setEndTime(businessTypeStatusResp.getEndTime());
        taskNumResp.setAllTaskCount(businessTypeStatusResp.getAllTaskCount());
        taskNumResp.setNoSignaler(businessTypeStatusResp.getNoSignaler());
        taskNumResp.setInStoreTime(businessTypeStatusResp.getInStoreTime());
        taskNumResp.setEventBusinessTypeList(
                convertEventBusinessTypeList(businessTypeStatusResp.getEventBusinessTypeList()));
        taskNumResp.setCompletedTaskCount(businessTypeStatusResp.getCompletedTaskCount());
        return taskNumResp;
    }

    private Integer conversionNoNeedCompletedBusinessType(List<Long> noNeedCompletedNum, boolean isPromoter) {
        if (CollectionUtils.isEmpty(noNeedCompletedNum)) {
            return 0;
        }
        if (isPromoter) {
            //排除
            noNeedCompletedNum = noNeedCompletedNum.stream().filter(o -> !supervisorBusinessTypeIds.contains(o))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(noNeedCompletedNum)) {
                return 0;
            }
        }
        return noNeedCompletedNum.size();
    }

    private List<TaskNumResp.EventBusinessType> convertEventBusinessTypeList(List<BusinessTypeStatusResp.EventBusinessType> eventBusinessTypeList) {
        if (CollectionUtils.isEmpty(eventBusinessTypeList)) {
            return Collections.emptyList();
        }
        // 分离新产品和非新产品列表
        Map<Boolean, List<BusinessTypeStatusResp.EventBusinessType>> partitionedList = eventBusinessTypeList.stream()
                .collect(Collectors.partitioningBy(
                        e -> NEW_PRODUCT_INSPECTION_TOUR_LIST.contains(e.getBusinessTypeName())
                ));
        List<BusinessTypeStatusResp.EventBusinessType> newProductList = partitionedList.get(true);
        List<BusinessTypeStatusResp.EventBusinessType> notNewProductList = partitionedList.get(false);
        // 如果没有新产品，直接转换所有项
        if (CollectionUtils.isEmpty(newProductList)) {
            return convertToTargetType(eventBusinessTypeList);
        }
        // 检查新产品列表中是否有未完成项
        Optional<BusinessTypeStatusResp.EventBusinessType> firstUncompleted = newProductList.stream()
                .filter(e -> Objects.equals(UN_DO, e.getStatus()))
                .findFirst();
        if (firstUncompleted.isPresent()) {
            // 有未完成项，添加第一个未完成项到非新产品列表
            return convertWithAdditionalItem(notNewProductList, firstUncompleted.get());
        } else {
            // 所有新产品都已完成，添加第一个已完成项到非新产品列表
            Optional<BusinessTypeStatusResp.EventBusinessType> firstCompleted = newProductList.stream()
                    .filter(e -> Objects.equals(DOING, e.getStatus()))
                    .findFirst();
            return firstCompleted.map(completedItem ->
                            convertWithAdditionalItem(notNewProductList, completedItem))
                    .orElseGet(ArrayList::new);
        }
    }


    // 辅助方法：转换列表类型
    private List<TaskNumResp.EventBusinessType> convertToTargetType(List<BusinessTypeStatusResp.EventBusinessType> sourceList) {
        return sourceList.stream()
                .map(this::convertItem)
                .collect(Collectors.toList());
    }

    // 辅助方法：转换列表并添加额外项
    private List<TaskNumResp.EventBusinessType> convertWithAdditionalItem(
            List<BusinessTypeStatusResp.EventBusinessType> baseList,
            BusinessTypeStatusResp.EventBusinessType additionalItem) {

        List<BusinessTypeStatusResp.EventBusinessType> combinedList = new ArrayList<>(baseList);
        combinedList.add(additionalItem);
        return convertToTargetType(combinedList);
    }

    // 辅助方法：转换单个项
    private TaskNumResp.EventBusinessType convertItem(BusinessTypeStatusResp.EventBusinessType source) {
        TaskNumResp.EventBusinessType target = new TaskNumResp.EventBusinessType();
        target.setBusinessTypeName(source.getBusinessTypeName());
        target.setStatus(source.getStatus());
        return target;
    }

    private Integer conversionAllBusinessType(List<Long> allTaskNum, boolean isPromoter) {
        if (CollectionUtils.isEmpty(allTaskNum)) {
            return 0;
        }
        if (isPromoter) {
            //排除
            allTaskNum = allTaskNum.stream().filter(o -> !supervisorBusinessTypeIds.contains(o))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(allTaskNum)) {
                return 0;
            }
        }
        List<Long> newProductMaterialCompletedNum = allTaskNum.stream()
                .filter(newProductMaterialBusinessTypeIds::contains).collect(Collectors.toList());
        List<Long> notNewProductMaterialCompletedNum = allTaskNum.stream()
                .filter(o -> !newProductMaterialBusinessTypeIds.contains(o)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(newProductMaterialCompletedNum) && CollectionUtils.isNotEmpty(
                notNewProductMaterialCompletedNum)) {
            return 1 + notNewProductMaterialCompletedNum.size();
        }
        if (CollectionUtils.isNotEmpty(newProductMaterialCompletedNum) && CollectionUtils.isEmpty(
                notNewProductMaterialCompletedNum)) {
            return 1;
        }
        return allTaskNum.size();
    }

    private int checkNewProductMaterial(List<Long> unCompletedNum, List<Long> completedNum) {
        if (CollectionUtils.isEmpty(unCompletedNum) && CollectionUtils.isEmpty(completedNum)) {
            return 0;
        }
        boolean hasUncompleted = unCompletedNum.stream()
                .anyMatch(newProductMaterialBusinessTypeIds::contains);
        if (hasUncompleted) {
            return 2;
        }
        boolean hasCompleted = completedNum.stream()
                .anyMatch(newProductMaterialBusinessTypeIds::contains);
        if (hasCompleted) {
            return 1;
        }
        return 0;
    }

    private Integer conversionUnCompletedBusinessType(List<Long> unCompletedNum, Integer newProductMaterial, boolean isPromoter) {
        if (CollectionUtils.isEmpty(unCompletedNum)) {
            return 0;
        }
        if (isPromoter) {
            //排除
            unCompletedNum = unCompletedNum.stream().filter(o -> !supervisorBusinessTypeIds.contains(o))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(unCompletedNum)) {
                return 0;
            }
        }
        boolean hasUncompleted = unCompletedNum.stream().anyMatch(newProductMaterialBusinessTypeIds::contains);
        if (!hasUncompleted) {
            return unCompletedNum.size();
        }
        List<Long> newProductMaterialCompletedNum = unCompletedNum.stream()
                .filter(newProductMaterialBusinessTypeIds::contains).collect(Collectors.toList());
        List<Long> notNewProductMaterialCompletedNum = unCompletedNum.stream()
                .filter(o -> !newProductMaterialBusinessTypeIds.contains(o)).collect(Collectors.toList());
        if (newProductMaterial == 2 && CollectionUtils.isNotEmpty(newProductMaterialCompletedNum)) {
            //取一个
            return 1 + notNewProductMaterialCompletedNum.size();
        }
        return notNewProductMaterialCompletedNum.size();
    }

    private Integer conversionCompletedBusinessType(List<Long> completedNum, Integer newProductMaterial, boolean isPromoter) {
        if (CollectionUtils.isEmpty(completedNum)) {
            return 0;
        }
        if (isPromoter) {
            //排除
            completedNum = completedNum.stream().filter(o -> !supervisorBusinessTypeIds.contains(o))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(completedNum)) {
                return 0;
            }
        }
        boolean hasUncompleted = completedNum.stream().anyMatch(newProductMaterialBusinessTypeIds::contains);
        if (!hasUncompleted) {
            return completedNum.size();
        }
        List<Long> newProductMaterialCompletedNum = completedNum.stream()
                .filter(newProductMaterialBusinessTypeIds::contains).collect(Collectors.toList());
        List<Long> notNewProductMaterialCompletedNum = completedNum.stream()
                .filter(o -> !newProductMaterialBusinessTypeIds.contains(o)).collect(Collectors.toList());
        if (newProductMaterial == 1 && CollectionUtils.isNotEmpty(newProductMaterialCompletedNum)) {
            //取一个
            return 1 + notNewProductMaterialCompletedNum.size();
        }
        return notNewProductMaterialCompletedNum.size();
    }

    private TaskNumResp setTaskNumRespDefaults(TaskNumResp taskNumResp) {
        // 设置默认值
        taskNumResp.setAllTaskNum(0);
        taskNumResp.setCompletedNum(0);
        taskNumResp.setUnCompletedNum(0);
        taskNumResp.setEventBusinessTypeList(Collections.emptyList());
        taskNumResp.setUndoBusinessTypeNameList(Collections.emptyList());
        return taskNumResp;
    }

    private String getLanguageKeyFromHeader() {
        // 从 request 中获取 headers 整个 map
        final HttpServletRequest request = CommonUtils.getCurrentRequest();
        if (request == null) {
            return StringUtils.EMPTY;
        }
        return StringUtils.isEmpty(request.getHeader("X-Retail-Language")) ? StringUtils.EMPTY
                : request.getHeader("X-Retail-Language");
    }

    @Override
    public Object querySupervisorTaskCards(SupervisorTaskHttpReq request) {
        SupervisorTaskReq req = convertRequest(request);
        Result<MetricsResp> result = globalCentralBrainProvider.querySupervisorTaskCards(req);
        return result.getData();
    }

    @Override
    public Object queryAchRateCards(SupervisorTaskHttpReq request) {
        SupervisorTaskReq req = convertRequest(request);
        Result<MetricsResp> result = globalCentralBrainProvider.queryAchRateCards(req);
        return result.getData();
    }

    @Override
    public Object queryTaskStoreList(SupervisorTaskHttpReq request) {
        SupervisorTaskReq req = convertRequest(request);
        Result<TableResp> result = globalCentralBrainProvider.queryTaskStoreList(req);
        return result.getData();
    }

    @Override
    public Object queryPositionTaskList(SupervisorTaskHttpReq request) {
        SupervisorTaskReq req = convertRequest(request);
        Result<TableResp> result = globalCentralBrainProvider.queryPositionTaskList(req);
        return result.getData();
    }

    @Override
    public Object buildAchRateTaskList(SupervisorTaskHttpReq request) {
        SupervisorTaskReq req = convertRequest(request);
        Result<TableResp> result = globalCentralBrainProvider.buildAchRateTaskList(req);
        return result.getData();
    }

    private SupervisorTaskReq convertRequest(SupervisorTaskHttpReq request) {
        SupervisorTaskReq req = new SupervisorTaskReq();
        req.setStartDay(request.getStartDay());
        req.setEndDay(request.getEndDay());
        req.setAreaIds(request.getAreaIds());
        req.setRegionIds(request.getRegionIds());
        req.setUserTitles(request.getUserTitles());
        req.setViewType(request.getViewType());
        req.setStoreLevel(request.getStoreLevel());
        req.setStoreLevelId(request.getStoreLevelId());
        req.setPageNo(request.getPageNo());
        req.setPageSize(request.getPageSize());
        req.setStaffId(request.getStaffId());
        req.setOrderKey(request.getOrderKey());
        req.setOrderType(request.getOrderType());
        req.setOrgId(request.getOrgId());
        req.setAchType(request.getAchType());
        return req;
    }


    @Override
    public Object getInspectionConfTask(TaskCenterInspectionConfReq taskCenterInspectionConfReq) {
        // 查询门店信息
        List<IntlRmsStore> intlRmsStoreList = intlRmsStoreRepository.findByCrssCode(taskCenterInspectionConfReq.getOrgId());
        IntlRetailAssert.notEmpty(intlRmsStoreList, taskCenterInspectionConfReq.getOrgId() + " store not found");
        IntlRmsStore intlRmsStore = intlRmsStoreList.get(0);
        TaskCenterInspectionConfResponse taskCenterInspectionConfResponse = new TaskCenterInspectionConfResponse();
        taskCenterInspectionConfResponse.setStoreGrade(intlRmsStore.getGradeName());
        taskCenterInspectionConfResponse.setCode(intlRmsStore.getCode());
        taskCenterInspectionConfResponse.setCrssCode(intlRmsStore.getCrssCode());
        taskCenterInspectionConfResponse.setStoreId(intlRmsStore.getStoreId());
        taskCenterInspectionConfResponse.setName(intlRmsStore.getName());
        taskCenterInspectionConfResponse.setAddress(intlRmsStore.getAddress());
        /***
         * 按国家查询
         */
        IntlInspectionTaskQuery intlInspectionTaskQuery = new IntlInspectionTaskQuery();
        intlInspectionTaskQuery.setPageSize(1000L);
        intlInspectionTaskQuery.setCountryList(Lists.newArrayList(taskCenterInspectionConfReq.getCountry()));
        IPage<InspectionTaskConfDTO> inspectionTaskConfDTOIPage = intlInspectionTaskConfService.pageList(intlInspectionTaskQuery);
        if (Objects.isNull(inspectionTaskConfDTOIPage) || CollectionUtils.isEmpty(inspectionTaskConfDTOIPage.getRecords())) {
            return emptyInspectionConfTask(taskCenterInspectionConfResponse);
        }
        List<InspectionTaskConfDTO> inspectionTaskConfDTOList = inspectionTaskConfDTOIPage.getRecords().stream().filter(
                item -> StringUtils.isNotBlank(item.getUserTitleCodes())
                        && item.getUserTitleCodes().contains(taskCenterInspectionConfReq.getJobValue())
        ).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(inspectionTaskConfDTOList)) {
            InspectionTaskConfDTO find = inspectionTaskConfDTOList.stream()
                    .filter(item -> !item.getIsDisabled())
                    .findFirst()
                    .orElse(inspectionTaskConfDTOList.get(0));
            log.info("getInspectionConfTask data :{}", RetailJsonUtil.toJson(find));
            return convertTimes(taskCenterInspectionConfResponse, find);
        } else {
            return emptyInspectionConfTask(taskCenterInspectionConfResponse);
        }
    }


    private TaskCenterInspectionConfResponse emptyInspectionConfTask(TaskCenterInspectionConfResponse taskCenterInspectionConfResponse) {
        taskCenterInspectionConfResponse.setFrequencyTimes(0);
        taskCenterInspectionConfResponse.setFrequencyMonth(0);
        taskCenterInspectionConfResponse.setFrequency("0 times every 0 month");
        return taskCenterInspectionConfResponse;
    }


    private TaskCenterInspectionConfResponse convertTimes(TaskCenterInspectionConfResponse taskCenterInspectionConfResponse,
                                                          InspectionTaskConfDTO taskConfDTO) {
        try {
            if (Objects.isNull(taskConfDTO) || StringUtils.isBlank(taskCenterInspectionConfResponse.getStoreGrade())) {
                return emptyInspectionConfTask(taskCenterInspectionConfResponse);
            }
            String frequency = null;
            if (StringUtils.equals(taskCenterInspectionConfResponse.getStoreGrade(), StoreGradeEnum.S.getName())) {
                frequency = taskConfDTO.getSStoreInspectionFrequency();
            } else if (StringUtils.equals(taskCenterInspectionConfResponse.getStoreGrade(), StoreGradeEnum.A.getName())) {
                frequency = taskConfDTO.getAStoreInspectionFrequency();
            } else if (StringUtils.equals(taskCenterInspectionConfResponse.getStoreGrade(), StoreGradeEnum.B.getName())) {
                frequency = taskConfDTO.getBStoreInspectionFrequency();
            } else if (StringUtils.equals(taskCenterInspectionConfResponse.getStoreGrade(), StoreGradeEnum.C.getName())) {
                frequency = taskConfDTO.getCStoreInspectionFrequency();
            } else if (StringUtils.equals(taskCenterInspectionConfResponse.getStoreGrade(), StoreGradeEnum.D.getName())) {
                frequency = taskConfDTO.getDStoreInspectionFrequency();
            } else {
                frequency = "0 times every 0 month";
            }
            // 去掉固定字符串"times every"和"month",只保留数字
            Pattern pattern = Pattern.compile("\\d+");
            Matcher matcher = pattern.matcher(frequency);
            List<Integer> numbers = new ArrayList<>();
            while (matcher.find() && numbers.size() < 2) {
                numbers.add(Integer.parseInt(matcher.group()));
            }
            // 初始化次数和月份变量
            int times = 0;
            int months = 0;
            if (numbers.size() == 2) {
                times = numbers.get(0);
                months = numbers.get(1);
            }
            taskCenterInspectionConfResponse.setFrequencyTimes(times);
            taskCenterInspectionConfResponse.setFrequencyMonth(months);
            taskCenterInspectionConfResponse.setFrequency(frequency);
            return taskCenterInspectionConfResponse;
        } catch (Exception exception) {
            log.error("convertTimes error:{}", exception.getMessage(), exception);
            return emptyInspectionConfTask(taskCenterInspectionConfResponse);
        }
    }
}
