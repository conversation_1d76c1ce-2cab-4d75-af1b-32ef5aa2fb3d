package com.mi.info.intl.retail.intlretail.service.app.store.impl;

import com.mi.info.intl.retail.core.utils.CacheUtils;
import com.mi.info.intl.retail.intlretail.domain.common.utils.JsonUtil;
import com.mi.info.intl.retail.intlretail.domain.http.rms.RmsAppliUserOauthServiceProvider;
import com.mi.info.intl.retail.intlretail.service.api.store.IChannelInfoService;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.ChannelInfoRequest;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.ChannelInfoResponse;

import com.mi.info.intl.retail.intlretail.service.api.store.dto.ChannelInfoRmsRequest;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.RmsRequest;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

@DubboService(group = "${store.dubbo.group:}", interfaceClass = IChannelInfoService.class)
@Service
@Slf4j
public class ChannelInfoServiceImpl implements IChannelInfoService {

    static final String QUERY_CHANNELINFO_PATH = "/api/data/v9.2/new_QueryChannelInfo";
    @Value("${rms-region-default}")
    private String defaultAreaId;
    @Autowired
    private RmsAppliUserOauthServiceProvider rmsAppliUserOauthServiceProvider;

    /*
     * 调用方：数字门店后端
     */
    @Override
    public List<ChannelInfoResponse> queryChannelInfo(ChannelInfoRequest requestBody) {
        try {
            List<ChannelInfoResponse> response = new ArrayList<>();
            // 整理多调用方的入参
            Map<String, List<String>> groupedCodes = new HashMap<>();
            if (requestBody.getCodeWithArea() != null && !requestBody.getCodeWithArea().isEmpty()) {
                groupedCodes = requestBody.getCodeWithArea().stream()
                        .collect(Collectors.groupingBy(
                                ChannelInfoRequest.CodeWithArea::getAreaId,
                                Collectors.mapping(ChannelInfoRequest.CodeWithArea::getCode, Collectors.toList())
                        ));
            } else {
                String areaId = Optional.ofNullable(requestBody.getAreaId())
                        .filter(id -> !id.isEmpty())
                        .orElse(defaultAreaId);
                groupedCodes.put(areaId, requestBody.getCodes());
            }

            // 从缓存获取数据
            List<ChannelInfoResponse> cacheResult = new ArrayList<>();
            for (Map.Entry<String, List<String>> entry : groupedCodes.entrySet()) {
                String areaId = entry.getKey();
                List<String> codes = entry.getValue();
                List<ChannelInfoResponse> itemResult = CacheUtils.hMultiGet(areaId + ":" + requestBody.getType(), codes, ChannelInfoResponse.class);
                if (itemResult != null && !itemResult.isEmpty()) {
                    itemResult.removeIf(Objects::isNull);
                    cacheResult.addAll(itemResult);
                }
            }

            // 发送请求从db获取数据
            Set<String> cachedCodes = cacheResult.stream()
                    .map(r -> r.getBasic().getCode())
                    .collect(Collectors.toSet());
            Map<String, List<String>> withoutCodes = groupedCodes.entrySet().stream()
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            entry -> entry.getValue().stream()
                                    .filter(code -> !cachedCodes.contains(code))
                                    .collect(Collectors.toList())
                    ));
            withoutCodes.entrySet().removeIf(entry -> entry.getValue().isEmpty());
            List<ChannelInfoResponse> dbResult = new ArrayList<>();
            if (!withoutCodes.isEmpty()) {
                List<CompletableFuture<List<ChannelInfoResponse>>> futures = new ArrayList<>();
                // 遍历分组后的数据，并行处理每个 areaId 的 HTTP 请求
                for (Map.Entry<String, List<String>> entry : withoutCodes.entrySet()) {
                    CompletableFuture<List<ChannelInfoResponse>> future = CompletableFuture.supplyAsync(() ->
                            processAreaId(entry.getKey(), entry.getValue(), requestBody));
                    futures.add(future);
                }

                // 等待所有 CompletableFuture 完成，并收集结果
                CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
                CompletableFuture<List<ChannelInfoResponse>> combinedFuture = allFutures.thenApply(v ->
                        futures.stream()
                                .map(CompletableFuture::join)
                                .flatMap(List::stream)
                                .collect(Collectors.toList()));

                // 获取最终结果
                dbResult = combinedFuture.get();
                // 更新缓存
                for (Map.Entry<String, List<String>> entry : withoutCodes.entrySet()) {
                    Map<String, Object> cacheMap = dbResult.stream()
                            .filter(item -> entry.getValue().contains(item.getBasic().getCode()))
                            .collect(Collectors.toMap(
                                    m -> m.getBasic().getCode(),
                                    m -> m
                            ));
                    CacheUtils.hputAll(entry.getKey() + ":" + requestBody.getType(), cacheMap);
                }
            }

            response.addAll(cacheResult);
            response.addAll(dbResult);
            double hitRate = 100.0 * cacheResult.size() / Math.max(1, cacheResult.size() + dbResult.size());
            log.info("channel info cache hit rate: {}", String.format("%.2f%%", hitRate));

            return response;
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt(); // 恢复中断标志
            log.error("ChannelInfoServiceImpl.queryChannelInfo Thread was interrupted", e);
            throw new RuntimeException("Thread was interrupted", e);
        } catch (ExecutionException e) {
            log.error(" ChannelInfoServiceImpl.queryChannelInfo Execution exception occurred", e);
            throw new RuntimeException("Execution error", e);
        } catch (Exception e) {
            log.error("ChannelInfoServiceImpl.queryChannelInfo error", e);
            throw new RuntimeException("ChannelInfoServiceImpl.queryChannelInfo error", e);
        }
    }

    private List<ChannelInfoResponse> processAreaId(String areaId,
                                                    List<String> codes,
                                                    ChannelInfoRequest requestBody) {
        ChannelInfoRmsRequest requestBodyByAreaId = new ChannelInfoRmsRequest();
        requestBodyByAreaId.setType(requestBody.getType());
        requestBodyByAreaId.setCodes(codes);

        RmsRequest rmsRequest = new RmsRequest();
        rmsRequest.setInput(JsonUtil.bean2json(requestBodyByAreaId));

        return rmsAppliUserOauthServiceProvider.httpForList(areaId, QUERY_CHANNELINFO_PATH, rmsRequest, "POST", ChannelInfoResponse.class);
    }
}
