package com.mi.info.intl.retail.intlretail.service.app.proxy.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

public class RmsApiRequest {
    @JsonProperty("SourceType")
    public String sourceType;

    @JsonProperty("Input")
    public String input;

    //构造函数，且sourceType默认是lingshoutong
    public RmsApiRequest(String input) {
        this.sourceType = "lingshoutong";
        if (input == null) {
            input = "";
        }
        this.input = input;
    }
}
