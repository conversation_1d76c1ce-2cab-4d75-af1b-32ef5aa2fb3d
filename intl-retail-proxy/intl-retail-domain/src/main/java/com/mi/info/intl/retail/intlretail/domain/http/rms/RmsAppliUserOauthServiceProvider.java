package com.mi.info.intl.retail.intlretail.domain.http.rms;

import com.mi.info.intl.retail.intlretail.domain.http.rms.dto.RmsResponseBody;

import java.util.List;

public interface RmsAppliUserOauthServiceProvider {
    <T> List<T> httpForList(String areaId, String path, Object requestBody, String httpMethod, Class<T> responseType);

    <T> T httpForObject(String areaId, String path, Object requestBody, String httpMethod, Class<T> responseType);

    RmsResponseBody httpForRmsResponseBody(String areaId, String path, Object requestBody, String httpMethod);

    RmsResponseBody httpForRmsResponseBodyByJwtToken(String areaId, String path, Object requestBody, String httpMethod, String token);

}
