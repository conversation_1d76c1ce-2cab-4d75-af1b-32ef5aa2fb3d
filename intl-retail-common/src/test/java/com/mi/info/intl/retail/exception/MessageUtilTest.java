package com.mi.info.intl.retail.exception;

import com.mi.info.intl.retail.enums.AreaEnum;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import static org.junit.jupiter.api.Assertions.*;

class MessageUtilTest {

    @Test
    void testGetAreaEnumByLanguage_normal() {
        assertEquals(AreaEnum.US, MessageUtil.getAreaEnumByLanguage("en"));
        assertEquals(AreaEnum.US, MessageUtil.getAreaEnumByLanguage("en-US"));
    }

    @Test
    void testGetAreaEnumByLanguage_blankOrNull() {
        assertEquals(AreaEnum.US, MessageUtil.getAreaEnumByLanguage(""));
        assertEquals(AreaEnum.US, MessageUtil.getAreaEnumByLanguage(null));
    }

    @Test
    void testGetMessageForLanguage_noArgs() {
        try (MockedStatic<MessageUtil> util = Mockito.mockStatic(MessageUtil.class, Mockito.CALLS_REAL_METHODS)) {
            util.when(() -> MessageUtil.getMessageStr(1001)).thenReturn("错误信息");
            String msg = MessageUtil.getMessageForLanguage(1001);
            assertEquals("错误信息", msg);
        }
    }

    @Test
    void testGetMessageForLanguage_withArgs() {
        try (MockedStatic<MessageUtil> util = Mockito.mockStatic(MessageUtil.class, Mockito.CALLS_REAL_METHODS)) {
            util.when(() -> MessageUtil.getMessageStr(1002)).thenReturn("参数错误：{%s}，请检查{%s}");
            String msg = MessageUtil.getMessageForLanguage(1002, "用户名", 123);
            // 只替换前两个{%s}，其余的%s会被去掉
            assertEquals("参数错误：用户名，请检查123", msg);
        }
    }

    @Test
    void testGetMessageForLanguage_withNullArgs() {
        try (MockedStatic<MessageUtil> util = Mockito.mockStatic(MessageUtil.class, Mockito.CALLS_REAL_METHODS)) {
            util.when(() -> MessageUtil.getMessageStr(1003)).thenReturn("发生异常：{%s}");
            String msg = MessageUtil.getMessageForLanguage(1003, (Object) null);
            // null参数会被跳过
            assertEquals("发生异常：{}", msg);
        }
    }

    @Test
    void testGetMessageForLanguage_withIntegerArg() {
        try (MockedStatic<MessageUtil> util = Mockito.mockStatic(MessageUtil.class, Mockito.CALLS_REAL_METHODS)) {
            util.when(() -> MessageUtil.getMessageStr(1004)).thenReturn("错误码：{%s}");
            String msg = MessageUtil.getMessageForLanguage(1004, 404);
            assertEquals("错误码：404", msg);
        }
    }
}