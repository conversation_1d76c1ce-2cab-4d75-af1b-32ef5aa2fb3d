package com.mi.info.intl.retail.utils;

import okhttp3.mockwebserver.MockResponse;
import okhttp3.mockwebserver.MockWebServer;
import org.junit.jupiter.api.*;
import java.io.File;
import java.io.FileWriter;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import static org.junit.jupiter.api.Assertions.*;
import java.io.IOException;

class OkHttpUtilTest {
    private static MockWebServer mockWebServer;
    private OkHttpUtil okHttpUtil;

    @BeforeAll
    static void setUpServer() throws Exception {
        mockWebServer = new MockWebServer();
        mockWebServer.start();
    }

    @AfterAll
    static void tearDownServer() throws Exception {
        mockWebServer.shutdown();
    }

    @BeforeEach
    void setUp() {
        okHttpUtil = OkHttpUtil.getInstance();
    }

    @Test
    void testGet() throws Exception {
        mockWebServer.enqueue(new MockResponse().setBody("hello world"));
        String url = mockWebServer.url("/get").toString();
        String result = okHttpUtil.get(url, Collections.emptyMap());
        assertEquals("hello world", result);
    }

    @Test
    void testPostJson() throws Exception {
        mockWebServer.enqueue(new MockResponse().setBody("post ok"));
        String url = mockWebServer.url("/post").toString();
        Map<String, Object> data = new HashMap<>();
        data.put("key", "value");
        String result = okHttpUtil.postJson(url, data, Collections.emptyMap());
        assertEquals("post ok", result);
    }

    @Test
    void testPutJson() throws Exception {
        mockWebServer.enqueue(new MockResponse().setBody("put ok"));
        String url = mockWebServer.url("/put").toString();
        Map<String, Object> data = new HashMap<>();
        data.put("key", "value");
        String result = okHttpUtil.putJson(url, data, Collections.emptyMap());
        assertEquals("put ok", result);
    }

    @Test
    void testDelete() throws Exception {
        mockWebServer.enqueue(new MockResponse().setBody("delete ok"));
        String url = mockWebServer.url("/delete").toString();
        String result = okHttpUtil.delete(url, Collections.emptyMap());
        assertEquals("delete ok", result);
    }

    @Test
    void testUploadFile() throws Exception {
        mockWebServer.enqueue(new MockResponse().setBody("upload ok"));
        String url = mockWebServer.url("/upload").toString();
        File tempFile = File.createTempFile("test", ".txt");
        try (FileWriter writer = new FileWriter(tempFile)) {
            writer.write("file content");
        }
        String result = okHttpUtil.uploadFile(url, tempFile, "file", null, Collections.emptyMap());
        assertEquals("upload ok", result);
        tempFile.delete();
    }

    @Test
    void testDownloadFile() throws Exception {
        String fileContent = "download content";
        mockWebServer.enqueue(new MockResponse().setBody(fileContent));
        String url = mockWebServer.url("/download").toString();
        File tempFile = File.createTempFile("download", ".txt");
        okHttpUtil.downloadFile(url, tempFile.getAbsolutePath(), Collections.emptyMap());
        String read = new String(java.nio.file.Files.readAllBytes(tempFile.toPath()));
        assertEquals(fileContent, read);
        tempFile.delete();
    }

    @Test
    void testGetWithParams() throws Exception {
        mockWebServer.enqueue(new MockResponse().setBody("get with params"));
        String url = mockWebServer.url("/get").toString();
        Map<String, Object> params = new HashMap<>();
        params.put("a", 1);
        params.put("b", "test");
        String result = okHttpUtil.get(url, Collections.emptyMap(), params);
        assertEquals("get with params", result);
    }

    @Test
    void testPatchJson() throws Exception {
        mockWebServer.enqueue(new MockResponse().setBody("patch ok"));
        String url = mockWebServer.url("/patch").toString();
        Map<String, Object> data = new HashMap<>();
        data.put("key", "value");
        String result = okHttpUtil.patchJson(url, data, Collections.emptyMap());
        assertEquals("patch ok", result);
    }

    @Test
    void testGetAsync() throws Exception {
        mockWebServer.enqueue(new MockResponse().setBody("async get"));
        String url = mockWebServer.url("/getAsync").toString();
        final StringBuilder result = new StringBuilder();
        final Object lock = new Object();
        okHttpUtil.getAsync(url, Collections.emptyMap(), new okhttp3.Callback() {
            @Override
            public void onFailure(okhttp3.Call call, IOException e) {
                fail(e);
            }
            @Override
            public void onResponse(okhttp3.Call call, okhttp3.Response response) throws IOException {
                result.append(response.body().string());
                synchronized (lock) { lock.notify(); }
            }
        });
        synchronized (lock) { lock.wait(2000); }
        assertEquals("async get", result.toString());
    }

    @Test
    void testPostJsonAsync() throws Exception {
        mockWebServer.enqueue(new MockResponse().setBody("async post"));
        String url = mockWebServer.url("/postAsync").toString();
        Map<String, Object> data = new HashMap<>();
        data.put("key", "value");
        final StringBuilder result = new StringBuilder();
        final Object lock = new Object();
        okHttpUtil.postJsonAsync(url, data, Collections.emptyMap(), new okhttp3.Callback() {
            @Override
            public void onFailure(okhttp3.Call call, IOException e) {
                fail(e);
            }
            @Override
            public void onResponse(okhttp3.Call call, okhttp3.Response response) throws IOException {
                result.append(response.body().string());
                synchronized (lock) { lock.notify(); }
            }
        });
        synchronized (lock) { lock.wait(2000); }
        assertEquals("async post", result.toString());
    }

    @Test
    void testPutJsonAsync() throws Exception {
        mockWebServer.enqueue(new MockResponse().setBody("async put"));
        String url = mockWebServer.url("/putAsync").toString();
        Map<String, Object> data = new HashMap<>();
        data.put("key", "value");
        final StringBuilder result = new StringBuilder();
        final Object lock = new Object();
        okHttpUtil.putJsonAsync(url, data, Collections.emptyMap(), new okhttp3.Callback() {
            @Override
            public void onFailure(okhttp3.Call call, IOException e) {
                fail(e);
            }
            @Override
            public void onResponse(okhttp3.Call call, okhttp3.Response response) throws IOException {
                result.append(response.body().string());
                synchronized (lock) { lock.notify(); }
            }
        });
        synchronized (lock) { lock.wait(2000); }
        assertEquals("async put", result.toString());
    }

    @Test
    void testDeleteAsync() throws Exception {
        mockWebServer.enqueue(new MockResponse().setBody("async delete"));
        String url = mockWebServer.url("/deleteAsync").toString();
        final StringBuilder result = new StringBuilder();
        final Object lock = new Object();
        okHttpUtil.deleteAsync(url, Collections.emptyMap(), new okhttp3.Callback() {
            @Override
            public void onFailure(okhttp3.Call call, IOException e) {
                fail(e);
            }
            @Override
            public void onResponse(okhttp3.Call call, okhttp3.Response response) throws IOException {
                result.append(response.body().string());
                synchronized (lock) { lock.notify(); }
            }
        });
        synchronized (lock) { lock.wait(2000); }
        assertEquals("async delete", result.toString());
    }

    @Test
    void testPatchJsonAsync() throws Exception {
        mockWebServer.enqueue(new MockResponse().setBody("async patch"));
        String url = mockWebServer.url("/patchAsync").toString();
        Map<String, Object> data = new HashMap<>();
        data.put("key", "value");
        final StringBuilder result = new StringBuilder();
        final Object lock = new Object();
        okHttpUtil.patchJsonAsync(url, data, Collections.emptyMap(), new okhttp3.Callback() {
            @Override
            public void onFailure(okhttp3.Call call, IOException e) {
                fail(e);
            }
            @Override
            public void onResponse(okhttp3.Call call, okhttp3.Response response) throws IOException {
                result.append(response.body().string());
                synchronized (lock) { lock.notify(); }
            }
        });
        synchronized (lock) { lock.wait(2000); }
        assertEquals("async patch", result.toString());
    }

    @Test
    void testUploadFileAsync() throws Exception {
        mockWebServer.enqueue(new MockResponse().setBody("async upload"));
        String url = mockWebServer.url("/uploadAsync").toString();
        File tempFile = File.createTempFile("test", ".txt");
        try (FileWriter writer = new FileWriter(tempFile)) {
            writer.write("file content");
        }
        final StringBuilder result = new StringBuilder();
        final Object lock = new Object();
        okHttpUtil.uploadFileAsync(url, tempFile, "file", null, Collections.emptyMap(), new okhttp3.Callback() {
            @Override
            public void onFailure(okhttp3.Call call, IOException e) {
                fail(e);
            }
            @Override
            public void onResponse(okhttp3.Call call, okhttp3.Response response) throws IOException {
                result.append(response.body().string());
                synchronized (lock) { lock.notify(); }
            }
        });
        synchronized (lock) { lock.wait(2000); }
        assertEquals("async upload", result.toString());
        tempFile.delete();
    }

    @Test
    void testDownloadFileAsync() throws Exception {
        String fileContent = "async download content";
        mockWebServer.enqueue(new MockResponse().setBody(fileContent));
        String url = mockWebServer.url("/downloadAsync").toString();
        final StringBuilder result = new StringBuilder();
        final Object lock = new Object();
        okHttpUtil.downloadFileAsync(url, Collections.emptyMap(), new okhttp3.Callback() {
            @Override
            public void onFailure(okhttp3.Call call, IOException e) {
                fail(e);
            }
            @Override
            public void onResponse(okhttp3.Call call, okhttp3.Response response) throws IOException {
                result.append(response.body().string());
                synchronized (lock) { lock.notify(); }
            }
        });
        synchronized (lock) { lock.wait(2000); }
        assertEquals("async download content", result.toString());
    }
}
