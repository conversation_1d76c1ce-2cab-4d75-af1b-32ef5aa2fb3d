package com.mi.info.intl.retail.model;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.Set;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

/**
 * BasePageRequest 单元测试类
 */
@DisplayName("BasePageRequest 单元测试")
public class BasePageRequestTest {

    private BasePageRequest basePageRequest;
    private Validator validator;

    @BeforeEach
    void setUp() {
        basePageRequest = new BasePageRequest();
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        validator = factory.getValidator();
    }

    @AfterEach
    void tearDown() {
        basePageRequest = null;
        validator = null;
    }

    @Test
    @DisplayName("测试默认构造函数")
    void testDefaultConstructor() {
        BasePageRequest request = new BasePageRequest();
        
        assertNotNull(request);
        assertEquals(10L, request.getPageSize());
        assertEquals(1L, request.getPageNum());
    }

    @Test
    @DisplayName("测试全参数构造函数")
    void testAllArgsConstructor() {
        Long pageSize = 20L;
        Long pageNum = 2L;
        
        BasePageRequest request = new BasePageRequest(pageSize, pageNum);
        
        assertNotNull(request);
        assertEquals(pageSize, request.getPageSize());
        assertEquals(pageNum, request.getPageNum());
    }

    @Test
    @DisplayName("测试getter和setter方法")
    void testGettersAndSetters() {
        // 测试pageSize
        basePageRequest.setPageSize(50L);
        assertEquals(50L, basePageRequest.getPageSize());
        
        basePageRequest.setPageSize(100L);
        assertEquals(100L, basePageRequest.getPageSize());
        
        basePageRequest.setPageSize(null);
        assertNull(basePageRequest.getPageSize());
        
        // 测试pageNum
        basePageRequest.setPageNum(5L);
        assertEquals(5L, basePageRequest.getPageNum());
        
        basePageRequest.setPageNum(10L);
        assertEquals(10L, basePageRequest.getPageNum());
        
        basePageRequest.setPageNum(null);
        assertNull(basePageRequest.getPageNum());
    }

    @Test
    @DisplayName("测试pageSize验证注解 - 最小值")
    void testPageSizeMinValidation() {
        // 测试小于最小值的情况
        basePageRequest.setPageSize(5L);
        
        Set<ConstraintViolation<BasePageRequest>> violations = validator.validate(basePageRequest);
        
        assertTrue(violations.size() > 0);
        boolean hasPageSizeMinError = violations.stream()
            .anyMatch(v -> "pageSize".equals(v.getPropertyPath().toString()) 
                && v.getMessage().contains("不能小于"));
        assertTrue(hasPageSizeMinError);
    }


    @Test
    @DisplayName("测试pageSize验证注解 - 有效值")
    void testPageSizeValidValues() {
        // 测试最小值
        basePageRequest.setPageSize(10L);
        Set<ConstraintViolation<BasePageRequest>> violations1 = validator.validate(basePageRequest);
        assertTrue(violations1.isEmpty());
        
        // 测试最大值
        basePageRequest.setPageSize(1000L);
        Set<ConstraintViolation<BasePageRequest>> violations2 = validator.validate(basePageRequest);
        assertTrue(violations2.isEmpty());
        
        // 测试中间值
        basePageRequest.setPageSize(100L);
        Set<ConstraintViolation<BasePageRequest>> violations3 = validator.validate(basePageRequest);
        assertTrue(violations3.isEmpty());
    }

    @Test
    @DisplayName("测试pageNum验证注解 - 最小值")
    void testPageNumMinValidation() {
        // 测试小于最小值的情况
        basePageRequest.setPageNum(0L);
        
        Set<ConstraintViolation<BasePageRequest>> violations = validator.validate(basePageRequest);
        
        assertTrue(violations.size() > 0);
        boolean hasPageNumMinError = violations.stream()
            .anyMatch(v -> "pageNum".equals(v.getPropertyPath().toString()) 
                && v.getMessage().contains("不能小于"));
        assertTrue(hasPageNumMinError);
    }

    @Test
    @DisplayName("测试pageNum验证注解 - 有效值")
    void testPageNumValidValues() {
        // 测试最小值
        basePageRequest.setPageNum(1L);
        Set<ConstraintViolation<BasePageRequest>> violations1 = validator.validate(basePageRequest);
        assertTrue(violations1.isEmpty());
        
        // 测试大值
        basePageRequest.setPageNum(100L);
        Set<ConstraintViolation<BasePageRequest>> violations2 = validator.validate(basePageRequest);
        assertTrue(violations2.isEmpty());
    }

    @Test
    @DisplayName("测试边界值")
    void testBoundaryValues() {
        // 测试pageSize边界值
        basePageRequest.setPageSize(Long.MAX_VALUE);
        assertEquals(Long.MAX_VALUE, basePageRequest.getPageSize());
        
        basePageRequest.setPageSize(Long.MIN_VALUE);
        assertEquals(Long.MIN_VALUE, basePageRequest.getPageSize());
        
        // 测试pageNum边界值
        basePageRequest.setPageNum(Long.MAX_VALUE);
        assertEquals(Long.MAX_VALUE, basePageRequest.getPageNum());
        
        basePageRequest.setPageNum(Long.MIN_VALUE);
        assertEquals(Long.MIN_VALUE, basePageRequest.getPageNum());
    }

    @Test
    @DisplayName("测试序列化兼容性")
    void testSerializationCompatibility() {
        // 验证实现了Serializable接口
        assertTrue(BasePageRequest.class.getInterfaces().length > 0);
        assertTrue(java.io.Serializable.class.isAssignableFrom(BasePageRequest.class));
    }

    @Test
    @DisplayName("测试Lombok注解")
    void testLombokAnnotations() {
        // 验证@Getter注解
        assertNotNull(basePageRequest.getPageSize());
        assertNotNull(basePageRequest.getPageNum());
        
        // 验证@Setter注解
        basePageRequest.setPageSize(50L);
        basePageRequest.setPageNum(5L);
        assertEquals(50L, basePageRequest.getPageSize());
        assertEquals(5L, basePageRequest.getPageNum());
        
        // 验证@NoArgsConstructor注解
        BasePageRequest noArgsConstructor = new BasePageRequest();
        assertNotNull(noArgsConstructor);
        
        // 验证@AllArgsConstructor注解
        BasePageRequest allArgsConstructor = new BasePageRequest(100L, 10L);
        assertNotNull(allArgsConstructor);
        assertEquals(100L, allArgsConstructor.getPageSize());
        assertEquals(10L, allArgsConstructor.getPageNum());
    }

    @Test
    @DisplayName("测试@Deprecated注解")
    void testDeprecatedAnnotation() {
        // 验证类被标记为@Deprecated
        assertTrue(BasePageRequest.class.isAnnotationPresent(Deprecated.class));
        
        // 验证@Deprecated注解的存在
        Deprecated deprecatedAnnotation = BasePageRequest.class.getAnnotation(Deprecated.class);
        assertNotNull(deprecatedAnnotation);
    }

    @Test
    @DisplayName("测试默认值")
    void testDefaultValues() {
        BasePageRequest request = new BasePageRequest();
        
        // 验证默认值
        assertEquals(10L, request.getPageSize());
        assertEquals(1L, request.getPageNum());
    }

    @Test
    @DisplayName("测试null值处理")
    void testNullValueHandling() {
        // 测试null pageSize
        basePageRequest.setPageSize(null);
        assertNull(basePageRequest.getPageSize());
        
        // 测试null pageNum
        basePageRequest.setPageNum(null);
        assertNull(basePageRequest.getPageNum());
        
        // 测试全null构造函数
        BasePageRequest nullRequest = new BasePageRequest(null, null);
        assertNull(nullRequest.getPageSize());
        assertNull(nullRequest.getPageNum());
    }

    @Test
    @DisplayName("测试并发安全性")
    void testConcurrencySafety() throws InterruptedException {
        int threadCount = 10;
        Thread[] threads = new Thread[threadCount];
        BasePageRequest[] requests = new BasePageRequest[threadCount];
        
        for (int i = 0; i < threadCount; i++) {
            final int index = i;
            threads[i] = new Thread(() -> {
                requests[index] = new BasePageRequest((long)(index + 1) * 10, (long)(index + 1));
            });
        }
        
        // 启动所有线程
        for (Thread thread : threads) {
            thread.start();
        }
        
        // 等待所有线程完成
        for (Thread thread : threads) {
            thread.join();
        }
        
        // 验证所有请求对象都正确创建
        for (int i = 0; i < threadCount; i++) {
            assertNotNull(requests[i]);
            assertEquals((i + 1) * 10, requests[i].getPageSize());
            assertEquals(i + 1, requests[i].getPageNum());
        }
    }

    @Test
    @DisplayName("测试性能")
    void testPerformance() {
        long startTime = System.currentTimeMillis();
        
        // 创建大量对象
        for (int i = 0; i < 10000; i++) {
            BasePageRequest request = new BasePageRequest((long)(i + 1), (long)(i + 1));
        }
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        // 验证性能（应该在100ms内完成）
        assertTrue(duration < 100, "性能测试失败，耗时：" + duration + "ms");
    }

    @Test
    @DisplayName("测试内存使用")
    void testMemoryUsage() {
        // 创建大量对象
        BasePageRequest[] requests = new BasePageRequest[1000];
        for (int i = 0; i < 1000; i++) {
            requests[i] = new BasePageRequest((long)(i + 1), (long)(i + 1));
        }
        
        // 验证对象创建成功
        for (BasePageRequest request : requests) {
            assertNotNull(request);
            assertTrue(request.getPageSize() > 0);
            assertTrue(request.getPageNum() > 0);
        }
        
        // 清理引用
        for (int i = 0; i < 1000; i++) {
            requests[i] = null;
        }
    }

    @Test
    @DisplayName("测试分页业务逻辑")
    void testPaginationBusinessLogic() {
        // 测试第一页
        BasePageRequest firstPage = new BasePageRequest(10L, 1L);
        assertEquals(10L, firstPage.getPageSize());
        assertEquals(1L, firstPage.getPageNum());
        
        // 测试中间页
        BasePageRequest middlePage = new BasePageRequest(20L, 5L);
        assertEquals(20L, middlePage.getPageSize());
        assertEquals(5L, middlePage.getPageNum());
        
        // 测试大页码
        BasePageRequest largePage = new BasePageRequest(50L, 100L);
        assertEquals(50L, largePage.getPageSize());
        assertEquals(100L, largePage.getPageNum());
    }

    @Test
    @DisplayName("测试验证注解组合")
    void testValidationAnnotationCombination() {
        // 测试pageSize和pageNum都有效
        basePageRequest.setPageSize(50L);
        basePageRequest.setPageNum(5L);
        Set<ConstraintViolation<BasePageRequest>> violations1 = validator.validate(basePageRequest);
        assertTrue(violations1.isEmpty());
        
        // 测试pageSize无效，pageNum有效
        basePageRequest.setPageSize(5L);
        basePageRequest.setPageNum(5L);
        Set<ConstraintViolation<BasePageRequest>> violations2 = validator.validate(basePageRequest);
        assertTrue(violations2.size() > 0);
        
        // 测试pageSize有效，pageNum无效
        basePageRequest.setPageSize(50L);
        basePageRequest.setPageNum(0L);
        Set<ConstraintViolation<BasePageRequest>> violations3 = validator.validate(basePageRequest);
        assertTrue(violations3.size() > 0);
        
        // 测试pageSize和pageNum都无效
        basePageRequest.setPageSize(5L);
        basePageRequest.setPageNum(0L);
        Set<ConstraintViolation<BasePageRequest>> violations4 = validator.validate(basePageRequest);
        assertTrue(violations4.size() > 1);
    }
}
