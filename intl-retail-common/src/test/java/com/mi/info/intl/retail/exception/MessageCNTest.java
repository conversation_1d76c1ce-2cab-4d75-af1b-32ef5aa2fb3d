package com.mi.info.intl.retail.exception;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * MessageCN enum unit test
 * MessageCN枚举单元测试
 */
class MessageCNTest {

    /**
     * Test get method with existing code
     * 测试get方法传入存在的code值
     */
    @Test
    void testGetWithExistingCode() {
        // Test SUCCESS code
        // 测试SUCCESS的code值
        MessageCN result1 = MessageCN.get(200);
        assertEquals(MessageCN.SUCCESS, result1, "Should return SUCCESS enum for code 200");
        assertEquals(200, result1.getCode(), "SUCCESS code should be 200");
        assertEquals("成功", result1.getDesc(), "SUCCESS description should be '成功'");

        // Test SYS_ERROR code
        // 测试SYS_ERROR的code值
        MessageCN result2 = MessageCN.get(1);
        assertEquals(MessageCN.SYS_ERROR, result2, "Should return SYS_ERROR enum for code 1");

        // Test ERR_BIZ_ERROR code
        // 测试ERR_BIZ_ERROR的code值
        MessageCN result3 = MessageCN.get(2);
        assertEquals(MessageCN.ERR_BIZ_ERROR, result3, "Should return ERR_BIZ_ERROR enum for code 2");

        // Test last enum code
        // 测试最后一个枚举的code值
        MessageCN result4 = MessageCN.get(17);
        assertEquals(MessageCN.CAN_NOT_OBTAIN_USERS, result4, "Should return CAN_NOT_OBTAIN_USERS enum for code 17");
    }

    /**
     * Test get method with non-existing code
     * 测试get方法传入不存在的code值
     */
    @Test
    void testGetWithNonExistingCode() {
        // Test with code that doesn't exist
        // 测试不存在的code值
        MessageCN result = MessageCN.get(999);
        assertNull(result, "Should return null for non-existing code 999");

        // Test with negative code
        // 测试负数code值
        MessageCN result2 = MessageCN.get(-1);
        assertNull(result2, "Should return null for negative code -1");

        // Test with zero code
        // 测试code值为0
        MessageCN result3 = MessageCN.get(0);
        assertNull(result3, "Should return null for code 0 as it's not defined");
    }

    /**
     * Test get method with boundary values
     * 测试get方法的边界值
     */
    @Test
    void testGetWithBoundaryValues() {
        // Test minimum code value
        // 测试最小code值
        MessageCN minResult = MessageCN.get(1);
        assertEquals(MessageCN.SYS_ERROR, minResult, "Should return SYS_ERROR for minimum code 1");

        // Test maximum code value
        // 测试最大code值
        MessageCN maxResult = MessageCN.get(17);
        assertEquals(MessageCN.CAN_NOT_OBTAIN_USERS, maxResult, "Should return CAN_NOT_OBTAIN_USERS for maximum code 17");
    }

    /**
     * Test that all defined enum codes can be retrieved
     * 测试所有定义的枚举code值都能被正确获取
     */
    @Test
    void testAllDefinedCodesCanBeRetrieved() {
        // Get all enum values and verify each can be retrieved by its code
        // 获取所有枚举值并验证每个都能通过code获取
        MessageCN[] allEnums = MessageCN.values();
        
        for (MessageCN enumValue : allEnums) {
            MessageCN retrieved = MessageCN.get(enumValue.getCode());
            assertEquals(enumValue, retrieved, 
                "Should be able to retrieve enum " + enumValue + " by its code " + enumValue.getCode());
        }
    }
}
