package com.mi.info.intl.retail.utils;

import static org.junit.jupiter.api.Assertions.*;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;

import org.apache.dubbo.rpc.RpcContext;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import lombok.extern.slf4j.Slf4j;

/**
 * RpcContextExecutor 测试类
 * 
 * <AUTHOR>
 * @date 2024/12/19
 */
@Slf4j
@ExtendWith(MockitoExtension.class)
class RpcContextExecutorTest {

    private ExecutorService originalExecutorService;
    private ScheduledExecutorService originalScheduledExecutorService;

    @BeforeEach
    void setUp() {
        originalExecutorService = Executors.newFixedThreadPool(4);
        originalScheduledExecutorService = Executors.newScheduledThreadPool(2);
    }

    @Test
    void testExecutorWrapper() throws Exception {
        // 设置RpcContext
        RpcContext.getContext().setAttachment("executor_key", "executor_value");

        // 创建RpcContext感知的Executor
        Executor rpcContextExecutor = new RpcContextExecutor(originalExecutorService);

        CountDownLatch latch = new CountDownLatch(1);
        AtomicReference<String> capturedValue = new AtomicReference<>();

        // 使用包装后的Executor执行任务
        rpcContextExecutor.execute(() -> {
            try {
                String value = RpcContext.getContext().getAttachment("executor_key");
                capturedValue.set(value);
                log.info("Executor包装器执行: value={}", value);
                assertEquals("executor_value", value);
            } finally {
                latch.countDown();
            }
        });

        latch.await(5, TimeUnit.SECONDS);
        assertEquals("executor_value", capturedValue.get());

        // 验证主线程的RpcContext仍然存在
        assertEquals("executor_value", RpcContext.getContext().getAttachment("executor_key"));
    }

    @Test
    void testExecutorServiceWrapper() throws Exception {
        // 设置RpcContext
        RpcContext.getContext().setAttachment("executor_service_key", "executor_service_value");

        // 创建RpcContext感知的ExecutorService
        ExecutorService rpcContextExecutorService = RpcContextExecutor.wrap(originalExecutorService);

        // 测试submit方法
        Future<String> future = rpcContextExecutorService.submit(() -> {
            String value = RpcContext.getContext().getAttachment("executor_service_key");
            log.info("ExecutorService包装器执行: value={}", value);
            assertEquals("executor_service_value", value);
            return "ExecutorService执行完成";
        });

        String result = future.get(5, TimeUnit.SECONDS);
        assertEquals("ExecutorService执行完成", result);

        // 验证主线程的RpcContext仍然存在
        assertEquals("executor_service_value", RpcContext.getContext().getAttachment("executor_service_key"));
    }

    @Test
    void testExecutorServiceSubmitWithResult() throws Exception {
        // 设置RpcContext
        RpcContext.getContext().setAttachment("submit_result_key", "submit_result_value");

        ExecutorService rpcContextExecutorService = RpcContextExecutor.wrap(originalExecutorService);

        // 测试submit(Runnable, T)方法
        Future<String> future = rpcContextExecutorService.submit(() -> {
            String value = RpcContext.getContext().getAttachment("submit_result_key");
            log.info("Submit with result执行: value={}", value);
            assertEquals("submit_result_value", value);
        }, "预设结果");

        String result = future.get(5, TimeUnit.SECONDS);
        assertEquals("预设结果", result);

        // 验证主线程的RpcContext仍然存在
        assertEquals("submit_result_value", RpcContext.getContext().getAttachment("submit_result_key"));
    }

    @Test
    void testExecutorServiceInvokeAll() throws Exception {
        // 设置RpcContext
        RpcContext.getContext().setAttachment("invoke_all_key", "invoke_all_value");

        ExecutorService rpcContextExecutorService = RpcContextExecutor.wrap(originalExecutorService);

        // 创建多个Callable任务
        List<Callable<String>> tasks = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            final int taskId = i;
            tasks.add(() -> {
                String value = RpcContext.getContext().getAttachment("invoke_all_key");
                log.info("InvokeAll任务{}执行: value={}", taskId, value);
                assertEquals("invoke_all_value", value);
                return "任务" + taskId + "完成";
            });
        }

        // 执行invokeAll
        List<Future<String>> futures = rpcContextExecutorService.invokeAll(tasks);

        // 验证所有任务都成功完成
        for (int i = 0; i < futures.size(); i++) {
            String result = futures.get(i).get(5, TimeUnit.SECONDS);
            assertEquals("任务" + i + "完成", result);
        }

        // 验证主线程的RpcContext仍然存在
        assertEquals("invoke_all_value", RpcContext.getContext().getAttachment("invoke_all_key"));
    }

    @Test
    void testExecutorServiceInvokeAllWithTimeout() throws Exception {
        // 设置RpcContext
        RpcContext.getContext().setAttachment("invoke_all_timeout_key", "invoke_all_timeout_value");

        ExecutorService rpcContextExecutorService = RpcContextExecutor.wrap(originalExecutorService);

        // 创建任务列表
        List<Callable<String>> tasks = new ArrayList<>();
        for (int i = 0; i < 2; i++) {
            final int taskId = i;
            tasks.add(() -> {
                String value = RpcContext.getContext().getAttachment("invoke_all_timeout_key");
                log.info("InvokeAll with timeout任务{}执行: value={}", taskId, value);
                assertEquals("invoke_all_timeout_value", value);
                return "任务" + taskId + "完成";
            });
        }

        // 执行invokeAll with timeout
        List<Future<String>> futures = rpcContextExecutorService.invokeAll(tasks, 10, TimeUnit.SECONDS);

        // 验证结果
        for (int i = 0; i < futures.size(); i++) {
            String result = futures.get(i).get(5, TimeUnit.SECONDS);
            assertEquals("任务" + i + "完成", result);
        }

        // 验证主线程的RpcContext仍然存在
        assertEquals("invoke_all_timeout_value", RpcContext.getContext().getAttachment("invoke_all_timeout_key"));
    }

    @Test
    void testExecutorServiceInvokeAny() throws Exception {
        // 设置RpcContext
        RpcContext.getContext().setAttachment("invoke_any_key", "invoke_any_value");

        ExecutorService rpcContextExecutorService = RpcContextExecutor.wrap(originalExecutorService);

        // 创建多个Callable任务
        List<Callable<String>> tasks = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            final int taskId = i;
            tasks.add(() -> {
                String value = RpcContext.getContext().getAttachment("invoke_any_key");
                log.info("InvokeAny任务{}执行: value={}", taskId, value);
                assertEquals("invoke_any_value", value);
                return "任务" + taskId + "完成";
            });
        }

        // 执行invokeAny
        String result = rpcContextExecutorService.invokeAny(tasks);
        assertTrue(result.startsWith("任务") && result.endsWith("完成"));

        // 验证主线程的RpcContext仍然存在
        assertEquals("invoke_any_value", RpcContext.getContext().getAttachment("invoke_any_key"));
    }

    @Test
    void testExecutorServiceInvokeAnyWithTimeout() throws Exception {
        // 设置RpcContext
        RpcContext.getContext().setAttachment("invoke_any_timeout_key", "invoke_any_timeout_value");

        ExecutorService rpcContextExecutorService = RpcContextExecutor.wrap(originalExecutorService);

        // 创建任务列表
        List<Callable<String>> tasks = new ArrayList<>();
        for (int i = 0; i < 2; i++) {
            final int taskId = i;
            tasks.add(() -> {
                String value = RpcContext.getContext().getAttachment("invoke_any_timeout_key");
                log.info("InvokeAny with timeout任务{}执行: value={}", taskId, value);
                assertEquals("invoke_any_timeout_value", value);
                return "任务" + taskId + "完成";
            });
        }

        // 执行invokeAny with timeout
        String result = rpcContextExecutorService.invokeAny(tasks, 10, TimeUnit.SECONDS);
        assertTrue(result.startsWith("任务") && result.endsWith("完成"));

        // 验证主线程的RpcContext仍然存在
        assertEquals("invoke_any_timeout_value", RpcContext.getContext().getAttachment("invoke_any_timeout_key"));
    }

    @Test
    void testScheduledExecutorServiceWrapper() throws Exception {
        // 设置RpcContext
        RpcContext.getContext().setAttachment("scheduled_key", "scheduled_value");

        // 创建RpcContext感知的ScheduledExecutorService
        ScheduledExecutorService rpcContextScheduledExecutor =
            RpcContextExecutor.wrap(originalScheduledExecutorService);

        CountDownLatch latch = new CountDownLatch(1);
        AtomicReference<String> capturedValue = new AtomicReference<>();

        // 测试schedule方法
        ScheduledFuture<?> future = rpcContextScheduledExecutor.schedule(() -> {
            try {
                String value = RpcContext.getContext().getAttachment("scheduled_key");
                capturedValue.set(value);
                log.info("ScheduledExecutorService包装器执行: value={}", value);
                assertEquals("scheduled_value", value);
            } finally {
                latch.countDown();
            }
        }, 100, TimeUnit.MILLISECONDS);

        latch.await(5, TimeUnit.SECONDS);
        assertEquals("scheduled_value", capturedValue.get());

        // 验证主线程的RpcContext仍然存在
        assertEquals("scheduled_value", RpcContext.getContext().getAttachment("scheduled_key"));
    }

    @Test
    void testScheduledExecutorServiceScheduleWithResult() throws Exception {
        // 设置RpcContext
        RpcContext.getContext().setAttachment("scheduled_result_key", "scheduled_result_value");

        ScheduledExecutorService rpcContextScheduledExecutor =
            RpcContextExecutor.wrap(originalScheduledExecutorService);

        // 测试schedule(Callable, delay, unit)方法
        ScheduledFuture<String> future = rpcContextScheduledExecutor.schedule(() -> {
            String value = RpcContext.getContext().getAttachment("scheduled_result_key");
            log.info("Scheduled with result执行: value={}", value);
            assertEquals("scheduled_result_value", value);
            return "Scheduled执行完成";
        }, 100, TimeUnit.MILLISECONDS);

        String result = future.get(5, TimeUnit.SECONDS);
        assertEquals("Scheduled执行完成", result);

        // 验证主线程的RpcContext仍然存在
        assertEquals("scheduled_result_value", RpcContext.getContext().getAttachment("scheduled_result_key"));
    }

    @Test
    void testScheduledExecutorServiceScheduleAtFixedRate() throws Exception {
        // 设置RpcContext
        RpcContext.getContext().setAttachment("fixed_rate_key", "fixed_rate_value");

        ScheduledExecutorService rpcContextScheduledExecutor =
            RpcContextExecutor.wrap(originalScheduledExecutorService);

        CountDownLatch latch = new CountDownLatch(2);
        AtomicInteger executionCount = new AtomicInteger(0);

        // 测试scheduleAtFixedRate方法
        ScheduledFuture<?> future = rpcContextScheduledExecutor.scheduleAtFixedRate(() -> {
            try {
                String value = RpcContext.getContext().getAttachment("fixed_rate_key");
                log.info("FixedRate执行{}: value={}", executionCount.incrementAndGet(), value);
                assertEquals("fixed_rate_value", value);
            } finally {
                latch.countDown();
            }
        }, 100, 200, TimeUnit.MILLISECONDS);

        latch.await(5, TimeUnit.SECONDS);
        future.cancel(false);

        assertTrue(executionCount.get() >= 2);

        // 验证主线程的RpcContext仍然存在
        assertEquals("fixed_rate_value", RpcContext.getContext().getAttachment("fixed_rate_key"));
    }

    @Test
    void testScheduledExecutorServiceScheduleWithFixedDelay() throws Exception {
        // 设置RpcContext
        RpcContext.getContext().setAttachment("fixed_delay_key", "fixed_delay_value");

        ScheduledExecutorService rpcContextScheduledExecutor =
            RpcContextExecutor.wrap(originalScheduledExecutorService);

        CountDownLatch latch = new CountDownLatch(2);
        AtomicInteger executionCount = new AtomicInteger(0);

        // 测试scheduleWithFixedDelay方法
        ScheduledFuture<?> future = rpcContextScheduledExecutor.scheduleWithFixedDelay(() -> {
            try {
                String value = RpcContext.getContext().getAttachment("fixed_delay_key");
                log.info("FixedDelay执行{}: value={}", executionCount.incrementAndGet(), value);
                assertEquals("fixed_delay_value", value);
            } finally {
                latch.countDown();
            }
        }, 100, 200, TimeUnit.MILLISECONDS);

        latch.await(5, TimeUnit.SECONDS);
        future.cancel(false);

        assertTrue(executionCount.get() >= 2);

        // 验证主线程的RpcContext仍然存在
        assertEquals("fixed_delay_value", RpcContext.getContext().getAttachment("fixed_delay_key"));
    }

    @Test
    void testExecutorServiceLifecycleMethods() {
        ExecutorService rpcContextExecutorService = RpcContextExecutor.wrap(originalExecutorService);

        // 测试生命周期方法
        assertFalse(rpcContextExecutorService.isShutdown());
        assertFalse(rpcContextExecutorService.isTerminated());

        // 关闭ExecutorService
        rpcContextExecutorService.shutdown();
        assertTrue(rpcContextExecutorService.isShutdown());

        // 等待终止
        try {
            boolean terminated = rpcContextExecutorService.awaitTermination(5, TimeUnit.SECONDS);
            assertTrue(terminated);
            assertTrue(rpcContextExecutorService.isTerminated());
        } catch (InterruptedException e) {
            fail("等待终止时被中断");
        }
    }

    @Test
    void testExecutorServiceShutdownNow() {
        ExecutorService rpcContextExecutorService = RpcContextExecutor.wrap(originalExecutorService);

        // 提交一些任务
        for (int i = 0; i < 5; i++) {
            rpcContextExecutorService.submit(() -> {
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            });
        }

        // 立即关闭
        List<Runnable> pendingTasks = rpcContextExecutorService.shutdownNow();
        assertTrue(rpcContextExecutorService.isShutdown());

        // 等待终止
        try {
            boolean terminated = rpcContextExecutorService.awaitTermination(5, TimeUnit.SECONDS);
            assertTrue(terminated);
        } catch (InterruptedException e) {
            fail("等待终止时被中断");
        }
    }

    @Test
    void testExceptionHandling() {
        // 设置RpcContext
        RpcContext.getContext().setAttachment("exception_key", "exception_value");

        ExecutorService rpcContextExecutorService = RpcContextExecutor.wrap(originalExecutorService);

        // 测试异常情况下的上下文传递
        Future<String> future = rpcContextExecutorService.submit(() -> {
            // 验证上下文传递正常
            String value = RpcContext.getContext().getAttachment("exception_key");
            assertEquals("exception_value", value);

            // 抛出异常
            throw new RuntimeException("测试异常");
        });

        // 验证异常被正确传播
        ExecutionException exception = assertThrows(ExecutionException.class, () -> {
            future.get(5, TimeUnit.SECONDS);
        });

        assertTrue(exception.getCause() instanceof RuntimeException);
        assertEquals("测试异常", exception.getCause().getMessage());

        // 验证主线程的RpcContext仍然存在
        assertEquals("exception_value", RpcContext.getContext().getAttachment("exception_key"));
    }

    @Test
    void testConcurrentExecution() throws Exception {
        // 设置RpcContext
        RpcContext.getContext().setAttachment("concurrent_key", "concurrent_value");

        ExecutorService rpcContextExecutorService = RpcContextExecutor.wrap(originalExecutorService);

        int taskCount = 10;
        CountDownLatch latch = new CountDownLatch(taskCount);
        AtomicInteger successCount = new AtomicInteger(0);

        // 创建多个并发任务
        List<Future<Void>> futures = new ArrayList<>();
        for (int i = 0; i < taskCount; i++) {
            final int taskId = i;
            Future<Void> future = rpcContextExecutorService.submit(() -> {
                try {
                    String value = RpcContext.getContext().getAttachment("concurrent_key");
                    assertEquals("concurrent_value", value);
                    log.info("并发任务{}执行成功", taskId);
                    successCount.incrementAndGet();
                } finally {
                    latch.countDown();
                }
                return null;
            });
            futures.add(future);
        }

        // 等待所有任务完成
        latch.await(10, TimeUnit.SECONDS);

        // 验证所有任务都成功执行
        assertEquals(taskCount, successCount.get());

        // 验证所有Future都完成
        for (Future<Void> future : futures) {
            assertTrue(future.isDone());
        }

        // 验证主线程的RpcContext仍然存在
        assertEquals("concurrent_value", RpcContext.getContext().getAttachment("concurrent_key"));
    }

    @Test
    void testTimeoutHandling() {
        // 设置RpcContext
        RpcContext.getContext().setAttachment("timeout_key", "timeout_value");

        ExecutorService rpcContextExecutorService = RpcContextExecutor.wrap(originalExecutorService);

        // 创建一个长时间运行的任务
        Future<String> future = rpcContextExecutorService.submit(() -> {
            try {
                // 验证上下文传递
                String value = RpcContext.getContext().getAttachment("timeout_key");
                assertEquals("timeout_value", value);

                // 模拟长时间运行
                Thread.sleep(2000);
                return "长时间任务完成";
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException(e);
            }
        });

        // 设置超时时间
        assertThrows(TimeoutException.class, () -> {
            future.get(1, TimeUnit.SECONDS);
        });

        // 验证主线程的RpcContext仍然存在
        assertEquals("timeout_value", RpcContext.getContext().getAttachment("timeout_key"));
    }
}
