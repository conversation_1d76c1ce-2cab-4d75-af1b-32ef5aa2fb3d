package com.mi.info.intl.retail.dto;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertTrue;


@DisplayName("IntlRmsUserDTOTest领域对象测试")
public class IntlRmsUserDTOTest {

    @Nested
    @DisplayName("setTest")
    class SetTest {
        @Test
        @DisplayName("调用approve后应更新modifiedOn")
        void testSeting() throws InterruptedException {
            IntlRmsUserDTO domain = new IntlRmsUserDTO();
            domain.setBrands("");
            domain.setTestAccount(1);
            domain.setKeySellingProducts("");
            assertTrue(domain.getBrands().equals(""), "OK");
            assertTrue(domain.getTestAccount().equals(1));
            assertTrue(domain.getKeySellingProducts().equals(""), "OK");
        }
    }



}