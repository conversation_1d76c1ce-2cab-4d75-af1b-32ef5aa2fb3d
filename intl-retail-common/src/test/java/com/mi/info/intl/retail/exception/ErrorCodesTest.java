package com.mi.info.intl.retail.exception;

import com.xiaomi.youpin.infra.rpc.errors.ErrorCode;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class ErrorCodesTest {
    @Test
    void testAllErrorCodesNotNull() {
        assertNotNull(ErrorCodes.SYS_ERROR);
        assertNotNull(ErrorCodes.BIZ_ERROR);
        assertNotNull(ErrorCodes.PARAM_IS_EMPTY);
        assertNotNull(ErrorCodes.PARAMS_ERROR);
        assertNotNull(ErrorCodes.NOT_EXISTS);
        assertNotNull(ErrorCodes.DATA_NOT_FOUND);
        assertNotNull(ErrorCodes.SEND_MQ_FAILED);
        assertNotNull(ErrorCodes.CONSUME_MQ_FAILED);
        assertNotNull(ErrorCodes.POSITION_NOT_EXIST);
        assertNotNull(ErrorCodes.RETAILER_NOT_EXIST);
        assertNotNull(ErrorCodes.SO_RULE_NOT_EXIST);
        assertNotNull(ErrorCodes.COUNTRY_CODE_NOT_EXIST);
        assertNotNull(ErrorCodes.SO_RULE_LOG_NOT_EXIST);
        assertNotNull(ErrorCodes.CALL_FDS_ERROR);
        assertNotNull(ErrorCodes.RPC_CALL_TIMEOUT);
        assertNotNull(ErrorCodes.RPC_CALL_ERROR);
        assertNotNull(ErrorCodes.CAN_NOT_OBTAIN_USERS);
    }
}