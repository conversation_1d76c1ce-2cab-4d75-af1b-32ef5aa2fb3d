package com.mi.info.intl.retail.model;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/7/30 11:35
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserInfo implements Serializable {

    /**
     * 用户id，对应$upc_account
     */
    private String userId;

    /**
     * 用户名称，对应$upc_userName
     */
    private String userName;

    /**
     * 用户邮箱，对应$upc_email
     */
    private String email;

    /**
     * 用户语言，对应$language
     */
    private String language;

    /**
     * miID，对应$upc_miID
     */
    private Long miID;

    /**
     * 用户区域,对应$area_id
     */
    private String areaId;

}
