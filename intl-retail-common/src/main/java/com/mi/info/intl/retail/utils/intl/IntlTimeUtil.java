package com.mi.info.intl.retail.utils.intl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.mi.info.intl.retail.exception.RetailRunTimeException;
import com.mi.info.intl.retail.model.UserInfo;
import com.mi.info.intl.retail.utils.UserInfoUtil;
import com.xiaomi.com.i18n.area.Area;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 国际化时间工具类
 */
@Slf4j
public class IntlTimeUtil {


    private static final SimpleDateFormat SIMPLE_DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    public static final String DEFAULT_DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern(DEFAULT_DATE_FORMAT);

    public static final String TIME_FORMAT_DAY = "yyyyMMdd";

    public static final String TIME_FORMAT_DAY_BY_LINE = "yyyy-MM-dd";

    public static final DateTimeFormatter FORMATTER_YYYYMMDD = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    // 私有构造方法，防止工具类被实例化

    private IntlTimeUtil() {
        throw new UnsupportedOperationException("Utility class");
    }

    public static String toFormatString(Date date) {
        SimpleDateFormat formatter = new SimpleDateFormat(DEFAULT_DATE_FORMAT);
        return formatter.format(date);
    }

    /**
     * 根据国家代码获取对应时区的当前时间
     *
     * @param countryCode 国家代码，如"CN"、"US"等
     * @return 格式化后的当前时间字符串，格式为"yyyy-MM-dd HH:mm:ss"，如果发生错误则返回null
     */
    public static String getOffsetDateTimeByCountryCode(String countryCode) {
        try {
            // 检查国家代码是否有效
            if (countryCode == null || countryCode.isEmpty()) {
                return null; // 国家代码不能为空
            }
            Area area = Area.of(countryCode);
            if (area == null) {
                return null; // 无效的国家代码
            }
            String timezone = area.getTimezone();
            if (timezone == null || timezone.isEmpty()) {
                return null; // 无效的时区
            }
            // 获取指定时区的当前时间并格式化
            return OffsetDateTime.ofInstant(Instant.now(), ZoneId.of(timezone)).format(FORMATTER);
        } catch (Exception ex) {
            log.error("Error getting timezone for country code: {}", countryCode, ex);
        }
        return null;
    }

    public static Date getCountryLocalDateByCountryCode(String countryCode) {
        try {
            if (countryCode == null || countryCode.isEmpty()) {
                return null; // 国家代码不能为空
            }
            Area area = Area.of(countryCode);
            if (area == null) {
                return null; // 无效的国家代码
            }
            String timezone = area.getTimezone();
            if (timezone == null || timezone.isEmpty()) {
                return null; // 无效的时区
            }
            return SIMPLE_DATE_FORMAT.parse(
                    OffsetDateTime.ofInstant(Instant.now(), ZoneId.of(timezone)).format(FORMATTER));
        } catch (Exception ex) {
            log.error("Error getting timezone for country code: {}", countryCode, ex);
        }
        return null;
    }

    /**
     * 获取某国家对应时区的一天的开始和结束时间戳（毫秒）
     *
     * @param countryCode 国家编码，如 "CN"、"US"
     * @return 长度为 2 的 List：第一个是当天 00:00:00.000 的时间戳，第二个是 23:59:59.999 的时间戳（毫秒）
     */
    public static List<Long> getStartAndEndOfDayMillis(String countryCode) {
        try {
            Area area = Area.of(countryCode);
            String timezone = area.getTimezone();
            ZoneId zoneId = ZoneId.of(timezone);

            // 当前时刻的 ZonedDateTime（带时区信息）
            ZonedDateTime nowZoned = ZonedDateTime.now(zoneId);

            // 获取当天 00:00:00.000 的 ZonedDateTime
            ZonedDateTime startOfDay = nowZoned.toLocalDate().atStartOfDay(zoneId);

            // 获取当天 23:59:59.999 的 ZonedDateTime
            ZonedDateTime endOfDay = startOfDay.plusDays(1).minusNanos(1_000_000); // 减去 1 毫秒

            // 转换为 Instant 再转为时间戳（毫秒）
            long startTimestamp = startOfDay.toInstant().toEpochMilli();
            long endTimestamp = endOfDay.toInstant().toEpochMilli();

            return Arrays.asList(startTimestamp, endTimestamp);
        } catch (Exception ex) {
            log.error("Error getting timezone for country code: {}", countryCode, ex);
        }
        return Lists.newArrayList();
    }

    /**
     * 获取指定国家时区的工作开始时间戳（毫秒）
     * 基于当天0点加上指定的工作偏移时间
     *
     * @param countryCode 国家编码，如 "CN"、"US"
     * @param workHour 工作开始小时（0-23）
     * @param workMinute 工作开始分钟（0-59）
     * @return 工作开始时间戳（毫秒），如果获取失败返回null
     */
    public static Long getWorkStartTimeMillis(String countryCode, int workHour, int workMinute) {
        try {
            Area area = Area.of(countryCode);
            String timezone = area.getTimezone();
            ZoneId zoneId = ZoneId.of(timezone);

            // 获取当天日期
            LocalDate today = LocalDate.now(zoneId);
            
            // 构建工作开始时间
            LocalTime workTime = LocalTime.of(workHour, workMinute);
            ZonedDateTime workStartDateTime = ZonedDateTime.of(today, workTime, zoneId);

            return workStartDateTime.toInstant().toEpochMilli();
        } catch (Exception ex) {
            log.error("Error getting work start time for country code: {}, hour: {}, minute: {}", 
                     countryCode, workHour, workMinute, ex);
            return null;
        }
    }

    /**
     * 获取指定国家时区的工作结束时间戳（毫秒）
     * 基于当天0点加上指定的工作偏移时间，结束时间是第二天的同一时间
     *
     * @param countryCode 国家编码，如 "CN"、"US"
     * @param workHour 工作开始小时（0-23）
     * @param workMinute 工作开始分钟（0-59）
     * @return 工作结束时间戳（毫秒），如果获取失败返回null
     */
    public static Long getWorkEndTimeMillis(String countryCode, int workHour, int workMinute) {
        try {
            Area area = Area.of(countryCode);
            String timezone = area.getTimezone();
            ZoneId zoneId = ZoneId.of(timezone);

            // 获取当天日期
            LocalDate today = LocalDate.now(zoneId);
            
            // 构建工作开始时间
            LocalTime workTime = LocalTime.of(workHour, workMinute);
            ZonedDateTime workStartDateTime = ZonedDateTime.of(today, workTime, zoneId);
            
            // 工作结束时间是第二天的同一时间
            ZonedDateTime workEndDateTime = workStartDateTime.plusDays(1);

            return workEndDateTime.toInstant().toEpochMilli();
        } catch (Exception ex) {
            log.error("Error getting work end time for country code: {}, hour: {}, minute: {}", 
                     countryCode, workHour, workMinute, ex);
            return null;
        }
    }

    /**
     * 根据国家代码列表获取时间
     *
     * @param countryCodeList
     * @return
     */
    public static Map<String, String> getCurrentLocalTimesByCountryCodes(List<String> countryCodeList) {
        Map<String, String> resultMap = new HashMap<>();
        for (String countryCode : countryCodeList) {
            try {
                Area area = Area.of(countryCode);
                if (area == null) {
                    continue;
                }
                // 获取时区 ID，如 "Asia/Shanghai"
                String timezone = area.getTimezone();
                if (null == timezone || timezone.isEmpty()) {
                    continue;
                }
                // 获取当前时区下的 ZonedDateTime
                ZonedDateTime zonedDateTime = ZonedDateTime.now(ZoneId.of(timezone));
                String formattedTime = zonedDateTime.format(FORMATTER);
                resultMap.put(countryCode, formattedTime);
            } catch (Exception e) {
                log.error("Error getting timezone for country code: {}", countryCode, e);
            }
        }
        return resultMap;
    }

    /**
     * 判断变更开始时间是否是指定偏移量时区“明天 0 点”及之后
     *
     * @param startTimeStr 开始时间（格式：yyyy-MM-dd HH:mm:ss）
     * @param countryCode 国家短代码
     * @return true 合法，false 不合法（小于明天 0 点 或 格式错误）
     */
    public static boolean isValidStartTime(String startTimeStr, String countryCode) {
        try {
            if (startTimeStr == null || startTimeStr.isEmpty() || countryCode == null || countryCode.isEmpty()) {
                return false; // 时间不能为空
            }
            Area area = Area.of(countryCode);
            if (area == null) {
                return false; // 无效的国家代码
            }
            // 获取时区
            String timezone = area.getTimezone();
            if (timezone == null || timezone.isEmpty()) {
                return false; // 无效的时区
            }
            // 解析开始时间
            LocalDateTime startTime = parseStartTime(startTimeStr);
            if (startTime == null) {
                return false; // 格式错误
            }
            // 获取当前时区的“明天 0 点”时间
            ZonedDateTime tomorrowMidnight = getTomorrowMidnight(timezone);
            // 比较时间
            return !startTime.isBefore(tomorrowMidnight.toLocalDateTime());
        } catch (Exception e) {
            log.error("Error validating start time with bias: {}", e.getMessage());
            return false; // 发生异常，返回不合法
        }
    }

    /**
     * 解析开始时间字符串为 LocalDateTime 对象
     *
     * @param startTimeStr 开始时间字符串
     * @return LocalDateTime 对象，如果格式错误则返回 null
     */
    private static LocalDateTime parseStartTime(String startTimeStr) {
        try {
            return LocalDateTime.parse(startTimeStr, FORMATTER);
        } catch (DateTimeParseException e) {
            log.error("Error parsing start time: {}", e.getMessage());
            return null; // 如果解析失败，则返回 null
        }
    }

    /**
     * 获取指定时区的“明天 0 点”时间
     *
     * @param timezone 时区ID（例如："Asia/Shanghai"）
     * @return ZonedDateTime 对象，代表“明天 0 点”
     */
    private static ZonedDateTime getTomorrowMidnight(String timezone) {
        // 获取当前时区的当前时间
        ZonedDateTime now = ZonedDateTime.now(ZoneId.of(timezone));
        // 计算明天 0 点的时间
        return now.plusDays(1).toLocalDate().atStartOfDay(now.getZone());
    }

    /**
     * 获取国家代码对应的时区代码
     *
     * @param countryCode 国家代码
     * @return 时区代码
     */
    public static String getTimezoneCodeyCountryCode(String countryCode) {
        return Area.of(countryCode).getTimezone();
    }

    /**
     * 将时间戳转换为对应国家的本地时间，若国家代码无效则使用 UTC 时区
     *
     * @param countryCode 国家代码
     * @param timestamp 时间戳（毫秒）
     * @return 格式化的本地时间字符串，格式为 "yyyy-MM-dd HH:mm:ss"
     */
    public static String parseTimestampToAreaTime(String countryCode, Long timestamp) {
        return parseTimestampToAreaTime(countryCode, timestamp, FORMATTER);
    }

    public static String parseTimestampToAreaTime(String countryCode, Long timestamp, DateTimeFormatter dtf) {

        if (timestamp == null) {
            return "";
        }

        // 默认使用 UTC 时区
        ZoneId zoneId = ZoneId.of("UTC");

        // 尝试获取国家对应的时区
        if (countryCode != null && !countryCode.isEmpty()) {
            Area area = Area.of(countryCode);
            if (area != null) {
                String timezone = area.getTimezone();
                if (timezone != null && !timezone.isEmpty()) {
                    zoneId = ZoneId.of(timezone);
                }
            }
        }

        // 将时间戳转换为指定时区的日期时间并格式化
        Instant instant = Instant.ofEpochMilli(timestamp);
        ZonedDateTime zonedDateTime = ZonedDateTime.ofInstant(instant, zoneId);
        return zonedDateTime.format(dtf == null ? FORMATTER : dtf);
    }

    /**
     * 将时间戳转换为对应国家的本地日期，若国家代码无效则使用 UTC 时区, 只保留日期
     */
    public static Date parseTimestampToAreaDate(String countryCode, Long timestamp) {
        if (timestamp == null) {
            return null;
        }
        try {
            return new SimpleDateFormat("yyyy-MM-dd").parse(parseTimestampToAreaTime(countryCode, timestamp));
        } catch (Exception e) {
            log.error("Error parsing timestamp to area date: countryCode={}, timestamp={}", countryCode, timestamp, e);
            return null;
        }

    }

    /**
     * 获取指定时间戳对应的指定时区的时间
     *
     * @param timestamp
     * @return
     */
    public static String getAreaTimeStr(Long timestamp) {
        if (timestamp == null) {
            return null;
        }
        ZoneId zoneId = null;
        UserInfo userInfo = UserInfoUtil.getUserInfo().orElse(null);
        if (userInfo != null) {
            String areaId = userInfo.getAreaId();
            // 尝试获取国家对应的时区
            Area area = Area.of(areaId);
            if (area != null) {
                String timezone = area.getTimezone();
                if (StringUtils.isNotBlank(timezone)) {
                    zoneId = ZoneId.of(timezone);
                }
            }
        }
        // 将时间戳转换为指定时区的日期时间并格式化
        Instant instant = Instant.ofEpochMilli(timestamp);
        if (zoneId == null) {
            log.warn("zoneId is null, use system default timezone, userInfo={}", JSON.toJSONString(userInfo));
            zoneId = ZoneId.systemDefault();
        }
        ZonedDateTime zonedDateTime = ZonedDateTime.ofInstant(instant, zoneId);
        return zonedDateTime.format(FORMATTER);
    }

    public static Map<String, Long> getCurrentTimeStampByCountryCodes(List<String> countryList) {
        if (CollectionUtils.isEmpty(countryList)) {
            return Collections.emptyMap();
        }
        Map<String, Long> resultMap = new HashMap<>();
        for (String countryCode : countryList) {
            Area area = Area.of(countryCode);
            if (area == null) {
                continue; // 无效的国家代码
            }
            // 获取时区
            String timezone = area.getTimezone();
            if (timezone == null || timezone.isEmpty()) {
                continue; // 无效的时区
            }
            resultMap.put(countryCode, ZonedDateTime.now(ZoneId.of(timezone)).toInstant().toEpochMilli());
        }
        return resultMap;
    }

    /**
     * 将指定格式时间字符串转换为 UTC 时间戳 countryCode 国家简称...i18n 又把这个 看做areaId 比如说ID intl_rms_country_timezone  把这个 ID 当做
     * countryCode 存放...
     */
    public static Long convertToUTCTimestamp(String timeStr, String pattern, String countryCode) {
        Area area = Area.of(countryCode);
        ZoneId zoneId = null;
        if (area == null) {
            // 无效的国家代码 默认使用 UTC 时区
            zoneId = ZoneId.of("UTC");
        } else {
            String timezone = area.getTimezone();
            zoneId = ZoneId.of(timezone);
        }

        // 获取时区
        ZonedDateTime zonedDateTime = ZonedDateTime.parse(timeStr,
                DateTimeFormatter.ofPattern(pattern).withZone(zoneId));
        return zonedDateTime.toInstant().truncatedTo(ChronoUnit.MILLIS).toEpochMilli();
    }

    /**
     * 获取指定日期在目标时区的UTC时间戳范围
     *
     * @param dateStr 日期字符串（如"2025-08-11"）
     * @param pattern 日期格式（如"yyyy-MM-dd"）
     * @param countryCode 国家代码
     * @return 包含[开始时间戳, 结束时间戳]的数组
     */
    public static long[] getDayTimestampRange(String dateStr, String pattern, String countryCode) {
        Area area = Area.of(countryCode);
        ZoneId zoneId = null;
        if (area == null) {
            // 无效的国家代码 默认使用 UTC 时区
            zoneId = ZoneId.of("UTC");
        } else {
            // 获取时区
            String timezone = area.getTimezone();
            zoneId = ZoneId.of(timezone);
        }

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        LocalDate date = LocalDate.parse(dateStr, formatter);
        // 获取时区当天的开始和结束时间
        ZonedDateTime start = date.atStartOfDay(zoneId);
        ZonedDateTime end = date.plusDays(1).atStartOfDay(zoneId).minusNanos(1);
        //        ZonedDateTime endOfDay = startOfDay.plusDays(1).minusNanos(1_000_000)
        // 转换为UTC时间戳
        return new long[]{start.toInstant().truncatedTo(ChronoUnit.MILLIS).toEpochMilli(),
                end.toInstant().truncatedTo(ChronoUnit.MILLIS).toEpochMilli()};
    }

    /**
     * 将时间戳转换为北京时间戳
     */
    public static ZonedDateTime getTargetZonedDateTime(Long timestamp, String sourceCountryCode, ZoneId targetZoneId) {
        if (timestamp == null || sourceCountryCode == null || targetZoneId == null) {
            return null;
        }
        // 获得国家的时区
        ZoneId zoneId = getZoneIdByCountryCode(sourceCountryCode);
        // 将时间戳转换为指定时区的时间
        Instant instant = Instant.ofEpochMilli(timestamp);
        ZonedDateTime zonedDateTime = ZonedDateTime.ofInstant(instant, zoneId);
        //转为北京时间
        LocalDateTime dateTime = LocalDateTime.of(zonedDateTime.getYear(), zonedDateTime.getMonthValue(),
                zonedDateTime.getDayOfMonth(), zonedDateTime.getHour(), zonedDateTime.getMinute(),
                zonedDateTime.getSecond());
        return dateTime.atZone(targetZoneId);
    }

    public static String toBeiJingTimeString(Long timestamp, String sourceCountryCode) {
        ZonedDateTime zonedDateTime = getTargetZonedDateTime(timestamp, sourceCountryCode, ZoneId.of("Asia/Shanghai"));
        if (zonedDateTime == null) {
            return null;
        }
        return zonedDateTime.format(FORMATTER);
    }

    /**
     * 将时间字符串转换为对应国家的本地时间戳
     *
     * @param countryCode 国家代码
     * @param timeStr     时间字符串，格式为 "yyyy-MM-dd HH:mm:ss"
     * @return 时间戳（毫秒），如果转换失败返回null
     */
    public static Long parseAreaTimeToTimestamp(String countryCode, String timeStr) {
        if (timeStr == null || timeStr.isEmpty()) {
            return null;
        }

        try {
            // 默认使用 UTC 时区
            ZoneId zoneId = ZoneId.of("UTC");

            // 尝试获取国家对应的时区
            if (countryCode != null && !countryCode.isEmpty()) {
                Area area = Area.of(countryCode);
                if (area != null) {
                    String timezone = area.getTimezone();
                    if (timezone != null && !timezone.isEmpty()) {
                        zoneId = ZoneId.of(timezone);
                    }
                }
            }

            // 解析时间字符串为LocalDateTime
            LocalDateTime localDateTime = LocalDateTime.parse(timeStr, FORMATTER);

            // 转换为指定时区的ZonedDateTime
            ZonedDateTime zonedDateTime = localDateTime.atZone(zoneId);

            // 转换为时间戳（毫秒）
            return zonedDateTime.toInstant().toEpochMilli();
        } catch (Exception e) {
            log.error("Error parsing area time to timestamp: countryCode={}, timeStr={}", countryCode, timeStr, e);
            return null;
        }
    }

    /**
     * 根据年月获取对应国家时区的月份开始和结束时间戳
     *
     * @param countryCode 国家代码
     * @param year        年份
     * @param month       月份
     * @return 长度为2的数组：[开始时间戳, 结束时间戳]，如果转换失败返回null
     */
    public static Long[] getMonthStartAndEndTimestamp(String countryCode, int year, int month) {
        try {
            // 默认使用 UTC 时区
            ZoneId zoneId = ZoneId.of("UTC");

            // 尝试获取国家对应的时区
            if (countryCode != null && !countryCode.isEmpty()) {
                Area area = Area.of(countryCode);
                if (area != null) {
                    String timezone = area.getTimezone();
                    if (timezone != null && !timezone.isEmpty()) {
                        zoneId = ZoneId.of(timezone);
                    }
                }
            }

            // 构建月份开始时间：年-月-01 00:00:00
            LocalDate startDate = LocalDate.of(year, month, 1);
            ZonedDateTime startDateTime = startDate.atStartOfDay(zoneId);

            // 构建月份结束时间：下个月第一天 00:00:00
            LocalDate endDate = startDate.plusMonths(1);
            ZonedDateTime endDateTime = endDate.atStartOfDay(zoneId);

            return new Long[]{
                    startDateTime.toInstant().toEpochMilli(),
                    endDateTime.toInstant().toEpochMilli()
            };
        } catch (Exception e) {
            log.error("Error getting month start and end timestamp: countryCode={}, year={}, month={}",
                    countryCode, year, month, e);
            return new Long[]{};
        }
    }

    /**
     * 将时间戳转换为对应国家的本地时间，若国家代码无效则使用 UTC 时区
     *
     * @param format      目标格式
     * @param countryCode 国家代码
     * @param timestamp   时间戳（毫秒）
     * @return 格式化的本地时间字符串
     */
    public static String parseTimestampToFormatAreaTime(String format, String countryCode, Long timestamp) {
        if (null == timestamp) {
            return StringUtils.EMPTY;
        }

        // 默认使用 UTC 时区
        ZoneId zoneId = ZoneId.of("UTC");

        // 尝试获取国家对应的时区
        if (countryCode != null && !countryCode.isEmpty()) {
            Area area = Area.of(countryCode);
            if (area != null) {
                String timezone = area.getTimezone();
                if (timezone != null && !timezone.isEmpty()) {
                    zoneId = ZoneId.of(timezone);
                }
            }
        }

        // 将时间戳转换为指定时区的日期时间并格式化
        Instant instant = Instant.ofEpochMilli(timestamp);
        ZonedDateTime zonedDateTime = ZonedDateTime.ofInstant(instant, zoneId);
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(format);
        return zonedDateTime.format(dateTimeFormatter);
    }

    /**
     * 将UTC时间字符串转换为时间戳
     *
     * @param utcTimeStr UTC时间字符串，格式为 "yyyy-MM-dd HH:mm:ss"
     * @return 时间戳（毫秒），如果转换失败返回null
     */
    public static Long parseUtcTimeToTimestamp(String utcTimeStr) {
        if (utcTimeStr == null || utcTimeStr.isEmpty()) {
            return null;
        }

        try {
            // 解析UTC时间字符串为LocalDateTime
            LocalDateTime localDateTime = LocalDateTime.parse(utcTimeStr, FORMATTER);

            // 转换为UTC时区的ZonedDateTime
            ZonedDateTime zonedDateTime = localDateTime.atZone(ZoneId.of("UTC"));

            // 转换为时间戳（毫秒）
            return zonedDateTime.toInstant().toEpochMilli();
        } catch (Exception e) {
            log.error("Error parsing UTC time to timestamp: utcTimeStr={}", utcTimeStr, e);
            return null;
        }
    }

    /**
     * 将本地时间字符串按照指定国家时区转换为时间戳
     *
     * @param countryCode  国家代码
     * @param localTimeStr 本地时间字符串，格式为 "yyyy-MM-dd HH:mm:ss" 或 "yyyy-MM-dd"
     * @return 时间戳（毫秒），如果转换失败返回null
     */
    public static Long parseLocalTimeToTimestamp(String countryCode, String localTimeStr) {
        if (localTimeStr == null || localTimeStr.isEmpty()) {
            return null;
        }

        try {
            // 默认使用 UTC 时区
            ZoneId zoneId = ZoneId.of("UTC");

            // 尝试获取国家对应的时区
            if (countryCode != null && !countryCode.isEmpty()) {
                Area area = Area.of(countryCode);
                if (area != null) {
                    String timezone = area.getTimezone();
                    if (timezone != null && !timezone.isEmpty()) {
                        zoneId = ZoneId.of(timezone);
                    }
                }
            }

            LocalDateTime localDateTime;

            localDateTime = parseFlexibleDateTime(localTimeStr);
            if (localDateTime == null) {
                return null;
            }

            // 转换为指定时区的ZonedDateTime
            ZonedDateTime zonedDateTime = localDateTime.atZone(zoneId);

            // 转换为时间戳（毫秒）
            return zonedDateTime.toInstant().toEpochMilli();
        } catch (Exception e) {
            log.error("Error parsing local time to timestamp: countryCode={}, localTimeStr={}", countryCode, localTimeStr, e);
            return null;
        }
    }

    /**
     * 解析灵活的日期时间格式
     */
    private static LocalDateTime parseFlexibleDateTime(String timeStr) {
        String[] patterns = {
                // 年月日格式 (年-月-日 带时间，支持单/双位时分秒)
                // 基础带时间格式
                "yyyy-M-dd H:mm",
                "yyyy-M-dd HH:mm",
                "yyyy-M-dd H:mm:ss",
                "yyyy-M-dd HH:mm:ss",
                "yyyy-MM-d H:mm",
                "yyyy-MM-d HH:mm",
                "yyyy-MM-d H:mm:ss",
                "yyyy-MM-d HH:mm:ss",
                "yyyy-M-d H:mm",
                "yyyy-M-d HH:mm",
                "yyyy-M-d H:mm:ss",
                "yyyy-M-d HH:mm:ss",
                // 补充双月双日的完整格式
                "yyyy-MM-dd H:mm",
                "yyyy-MM-dd HH:mm",
                "yyyy-MM-dd H:mm:ss",
                "yyyy-MM-dd HH:mm:ss",

                // 年月日格式 (年/月/日 带时间)
                "yyyy/M/dd H:mm",
                "yyyy/M/dd HH:mm",
                "yyyy/M/dd H:mm:ss",
                "yyyy/M/dd HH:mm:ss",
                "yyyy/MM/d H:mm",
                "yyyy/MM/d HH:mm",
                "yyyy/MM/d H:mm:ss",
                "yyyy/MM/d HH:mm:ss",
                "yyyy/M/d H:mm",
                "yyyy/M/d HH:mm",
                "yyyy/M/d H:mm:ss",
                "yyyy/M/d HH:mm:ss",
                // 补充双月双日的完整格式
                "yyyy/MM/dd H:mm",
                "yyyy/MM/dd HH:mm",
                "yyyy/MM/dd H:mm:ss",
                "yyyy/MM/dd HH:mm:ss",

                // 纯日期格式 (年-月-日 或 年/月/日)
                "yyyy-M-dd", "yyyy-MM-d", "yyyy-M-d", "yyyy-MM-dd",
                "yyyy/M/dd", "yyyy/MM/d", "yyyy/M/d", "yyyy/MM/dd",

                // 月日年格式 (月-日-年 或 月/日/年)
                "M-dd-yyyy", "MM-d-yyyy", "M-d-yyyy", "MM-dd-yyyy",
                "M/dd/yyyy", "MM/d/yyyy", "M/d/yyyy", "MM/dd/yyyy"
        };

        for (String pattern : patterns) {
            try {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
                if (pattern.contains(":")) {  // 检查是否包含时间部分
                    return LocalDateTime.parse(timeStr, formatter);
                } else {
                    LocalDate date = LocalDate.parse(timeStr, formatter);
                    return date.atStartOfDay();
                }
            } catch (DateTimeParseException ignored) {
                // 继续尝试下一个格式
            }
        }
        return null;
    }


    /**
     * 将数据库中的UTC时间字符串转换为时间戳
     * 专门处理数据库中存储的UTC时间字符串，如 "2025-06-22 08:50:48"
     *
     * @param utcDateTimeStr UTC时间字符串，格式为 "yyyy-MM-dd HH:mm:ss"
     * @return 时间戳（毫秒），如果转换失败返回null
     */
    public static Long parseUtcDateTimeStringToTimestamp(String utcDateTimeStr) {
        if (utcDateTimeStr == null || utcDateTimeStr.isEmpty()) {
            return null;
        }

        try {
            // 解析时间字符串为LocalDateTime
            LocalDateTime localDateTime = LocalDateTime.parse(utcDateTimeStr, FORMATTER);

            // 明确指定这是UTC时区的时间
            ZonedDateTime utcZonedDateTime = localDateTime.atZone(ZoneId.of("UTC"));

            // 转换为时间戳（毫秒）
            return utcZonedDateTime.toInstant().toEpochMilli();
        } catch (Exception e) {
            log.error("Error parsing UTC datetime string to timestamp: utcDateTimeStr={}", utcDateTimeStr, e);
            return null;
        }
    }

    /**
     * 将年月转换为时间戳
     */
    public static Long convertYearMonthToTimestamp(String year, String month, String countryCode, boolean isStart) {
        try {
            int yearInt = Integer.parseInt(year);
            int monthInt = Integer.parseInt(month);

            // 使用IntlTimeUtil获取月份的开始和结束时间戳
            Long[] timestamps = IntlTimeUtil.getMonthStartAndEndTimestamp(countryCode, yearInt, monthInt);
            if (timestamps.length == 2) {
                return isStart ? timestamps[0] : timestamps[1];
            }

            return null;
        } catch (Exception e) {
            log.error("年月时间转换失败: year={}, month={}, countryCode={}", year, month, countryCode, e);
            return null;
        }
    }


    /**
     * 将时间字符串转换为国家本地时间戳
     */
    public static Long convertToCountryTimestamp(String timeStr, String countryCode) {
        try {
            // 如果timeStr是时间戳格式，直接转换
            if (timeStr.matches("\\d+")) {
                return Long.parseLong(timeStr);
            }

            // 使用IntlTimeUtil进行时区转换
            return IntlTimeUtil.parseAreaTimeToTimestamp(countryCode, timeStr);
        } catch (Exception e) {
            log.error("时间转换失败: timeStr={}, countryCode={}", timeStr, countryCode, e);
            return null;
        }
    }

    /**
     * 获取指定时间戳当天的23:59:59时间戳
     *
     * @param timestamp 时间戳
     * @param countryCode 国家代码
     * @return 当天23:59:59的时间戳
     */
    public static Long getTimeStampDayEnd(Long timestamp, String countryCode) {
        try {
            // 默认使用 UTC 时区
            ZoneId zoneId = ZoneId.of("UTC");

            // 尝试获取国家对应的时区
            if (countryCode != null && !countryCode.isEmpty()) {
                try {
                    Area area = Area.of(countryCode);
                    if (area != null) {
                        String timezone = area.getTimezone();
                        if (timezone != null && !timezone.isEmpty()) {
                            zoneId = ZoneId.of(timezone);
                        }
                    }
                } catch (Exception e) {
                    log.warn("获取国家时区失败，使用UTC时区, countryCode: {}", countryCode, e);
                }
            }

            // 将时间戳转换为指定时区的日期时间
            ZonedDateTime dateTime = Instant.ofEpochMilli(timestamp).atZone(zoneId);

            // 获取当天的23:59:59.999
            ZonedDateTime endOfDay = dateTime.toLocalDate().atTime(23, 59, 59, 999_000_000).atZone(zoneId);

            return endOfDay.toInstant().toEpochMilli();
        } catch (Exception e) {
            log.error("计算时间戳当天结束时间失败, timestamp: {}, countryCode: {}", timestamp, countryCode, e);
            // 如果计算失败，返回原时间戳
            return timestamp;
        }
    }

    /**
     * 将时间戳转换为对应国家的本地时间，若国家代码无效则使用 UTC 时区
     * @param countryCode 国家代码
     * @param timestamp 时间戳（毫秒）
     * @param format 时间格式
     * @return 格式化的本地时间字符串
     */
    public static String parseTimestampToAreaTimeByFormat(String countryCode, Long timestamp, DateTimeFormatter format) {
        if (timestamp == null) {
            return "";
        }

        if (format == null) {
            format = FORMATTER;
        }

        // 默认使用 UTC 时区
        ZoneId zoneId = ZoneId.of("UTC");

        // 尝试获取国家对应的时区
        if (countryCode != null && !countryCode.isEmpty()) {
            Area area = Area.of(countryCode);
            if (area != null) {
                String timezone = area.getTimezone();
                if (timezone != null && !timezone.isEmpty()) {
                    zoneId = ZoneId.of(timezone);
                }
            }
        }

        // 将时间戳转换为指定时区的日期时间并格式化
        Instant instant = Instant.ofEpochMilli(timestamp);
        ZonedDateTime zonedDateTime = ZonedDateTime.ofInstant(instant, zoneId);
        return zonedDateTime.format(format);
    }

    public static ZonedDateTime getTimestampZoneDateTime(String countryCode, Long timestamp) {
        ZoneId zoneId = getZoneIdByCountryCode(countryCode);
        // 将时间戳转换为指定时区的日期时间并格式化
        Instant instant = Instant.ofEpochMilli(timestamp);
        return ZonedDateTime.ofInstant(instant, zoneId);
    }

    public static Long toBeiJingTimestamp(Long timestamp, String sourceCountryCode) {
        ZonedDateTime zonedDateTime = getTargetZonedDateTime(timestamp, sourceCountryCode, ZoneId.of("Asia/Shanghai"));
        if (zonedDateTime == null) {
            return null;
        }
        return zonedDateTime.toInstant().toEpochMilli();
    }

    public static Date toBeiJingTime(Long timestamp, String sourceCountryCode) {
        ZonedDateTime zonedDateTime = getTargetZonedDateTime(timestamp, sourceCountryCode, ZoneId.of("Asia/Shanghai"));
        if (zonedDateTime == null) {
            return null;
        }
        return Date.from(zonedDateTime.toInstant());
    }

    /**
     * 将第一个时间戳的年月日与第二个时间戳的时分秒组合成一个新的时间戳
     *
     * @param dateTimestamp 提供年月日部分的时间戳
     * @param timeTimestamp 提供时分秒部分的时间戳
     * @param countryCode 国家代码，用于确定时区
     * @return 组合后的时间戳（毫秒）
     */
    public static Long combineDateAndTime(Long dateTimestamp, Long timeTimestamp, String countryCode) {
        if (dateTimestamp == null || timeTimestamp == null) {
            return null;
        }
        // 获取指定国家的时区
        ZoneId zoneId = getZoneIdByCountryCode(countryCode);
        // 将时间戳转换为指定时区的ZonedDateTime
        ZonedDateTime dateZonedDateTime = ZonedDateTime.ofInstant(Instant.ofEpochMilli(dateTimestamp), zoneId);
        ZonedDateTime timeZonedDateTime = ZonedDateTime.ofInstant(Instant.ofEpochMilli(timeTimestamp), zoneId);
        // 组合日期和时间：使用第一个时间戳的年月日，第二个时间戳的时分秒
        LocalDateTime combinedDateTime = LocalDateTime.of(
                dateZonedDateTime.getYear(),
                dateZonedDateTime.getMonthValue(),
                dateZonedDateTime.getDayOfMonth(),
                timeZonedDateTime.getHour(),
                timeZonedDateTime.getMinute(),
                timeZonedDateTime.getSecond()
        );
        // 转换回指定时区的ZonedDateTime并获取时间戳
        ZonedDateTime resultZonedDateTime = combinedDateTime.atZone(zoneId);
        return resultZonedDateTime.toInstant().toEpochMilli();

    }

    public static ZoneId getZoneIdByCountryCode(String countryCode) {
        Area area = Area.of(countryCode);
        if (area == null) {
            log.error("Invalid country code: {}, no matching timezone", countryCode);
            throw new RetailRunTimeException(String.format("Country code %s has no matching timezone", countryCode));
        }

        String timezone = area.getTimezone();
        if (timezone == null || timezone.isEmpty()) {
            log.error("Country code: {} has null or empty timezone", countryCode);
            throw new RetailRunTimeException(String.format("Country code %s has null or empty timezone", countryCode));
        }
        log.info("getZoneIdByCountryCode zoneId: {}", timezone);
        return ZoneId.of(timezone);
    }

    public static Long getStartOfDayTimestamp(String countryCode) {
        ZoneId zoneId = getZoneIdByCountryCode(countryCode);
        // 获得当天0点(保留时区时区信息)
        ZonedDateTime startOfDay = ZonedDateTime.now(zoneId).truncatedTo(ChronoUnit.DAYS);
        log.info("getLocalDayTimestamp zoneId: {} offset {}", zoneId, startOfDay.getOffset());
        return startOfDay.toInstant().toEpochMilli();
    }

    /**
     * 将时间戳转换为指定时区的日期时间并格式化 yyyy-MM-dd
     */
    public static String parseTimestampToAreaDateFormat(String countryCode, Long timestamp) {
        ZonedDateTime zonedDateTime = getTimestampZoneDateTime(countryCode, timestamp);
        Area area = Area.of(countryCode);
        Area.Extend extend = area.getExtend();
        DateTimeFormatter dtf;
        if (extend == null || extend.getDateFormat() == null || extend.getDateFormat().trim().isEmpty()) {
            dtf = FORMATTER_YYYYMMDD;
        } else {
            try {
                dtf = DateTimeFormatter.ofPattern(extend.getDateFormat());
            } catch (IllegalArgumentException e) {
                log.warn("Invalid date format pattern for country {}: {}", countryCode, extend.getDateFormat(), e);
                dtf = FORMATTER_YYYYMMDD;
            }
        }
        return zonedDateTime.format(dtf);
    }

    /**
     * 将时间戳转换为指定时区的日期时间并格式化 yyyy-MM-dd HH:mm:ss
     */
    public static String parseTimestampToAreaDateSecFormat(String countryCode, Long timestamp) {
        ZonedDateTime zonedDateTime = getTimestampZoneDateTime(countryCode, timestamp);
        Area area = Area.of(countryCode);
        Area.Extend extend = area.getExtend();
        DateTimeFormatter dtf;
        if (extend == null || extend.getDateTimeSecFormat() == null || extend.getDateTimeSecFormat().trim().isEmpty()) {
            dtf = FORMATTER;
        } else {
            try {
                dtf = DateTimeFormatter.ofPattern(extend.getDateTimeSecFormat());
            } catch (IllegalArgumentException e) {
                log.warn("Invalid date format pattern for country {}: {}", countryCode, extend.getDateTimeSecFormat(),
                        e);
                dtf = FORMATTER;
            }
        }
        return zonedDateTime.format(dtf);
    }

    public static Long getLocalTimestamp(String countryCode) {
        ZoneId zoneId;
        try {
            Area area = Area.of(countryCode);
            zoneId = (area != null) ? ZoneId.of(area.getTimezone()) : ZoneId.of("UTC");
        } catch (Exception e) {
            zoneId = ZoneId.of("UTC"); // Fallback to UTC on any error
        }
        ZonedDateTime now = ZonedDateTime.now(zoneId);
        log.info("getLocalTimestamp zoneId: {} offset {}", zoneId.getId(), now.getOffset().toString());
        return now.toInstant().toEpochMilli();
    }
}
