package com.mi.info.intl.retail.model;

import java.io.Serializable;
import java.util.Map;

import com.mi.info.intl.retail.exception.BizException;
import com.xiaomi.hera.trace.context.TraceIdUtil;
import com.xiaomi.youpin.infra.rpc.errors.ErrorCode;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;

/**
 * 常见API响应
 *
 * <AUTHOR>
 * @date 2025/08/12
 */

public class CommonApiResponse<T> implements Serializable {
    private static final long serialVersionUID = -153195758211395798L;

    private static final String SUCCESS = "ok";

    private int code;

    private String message;

    private T data;

    private String traceId;

    /**
     * 附加字段,方便以后扩展
     */
    private Map<String, String> attachments;

    private Long totalCount;

    private Long pageSize;

    private Long pageIndex;

    private Long totalPages;

    public CommonApiResponse(T body) {
        this.code = 0;
        this.message = SUCCESS;
        this.data = body;
        this.traceId = TraceIdUtil.traceId();
    }

    public CommonApiResponse(int code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
        this.traceId = TraceIdUtil.traceId();
    }

    public static <T> CommonApiResponse<T> failure(int code, String message) {
        return new CommonApiResponse<>(code, message, null);
    }

    public static <T> CommonApiResponse<T> failure(ErrorCode errorCode, String message) {
        return new CommonApiResponse<>(errorCode.getCode(), message, null);
    }

    public static <T> CommonApiResponse<T> failure(int code, String message, T data) {
        return new CommonApiResponse<>(code, message, data);
    }

    public static <T> CommonApiResponse<T> success(T data) {
        return new CommonApiResponse<>(data);
    }

    public static <T> CommonApiResponse<T> success(T data, long totalCount, long pageSize, long pageIndex) {
        CommonApiResponse<T> response = new CommonApiResponse<>(data);
        response.setData(data);
        response.setTotalCount(totalCount);
        response.setPageSize(pageSize);
        response.setPageIndex(pageIndex);
        response.setTotalPages(
            response.getTotalCount() % response.getPageSize() == 0 ? response.getTotalCount() / response.getPageSize()
                : (response.getTotalCount() / response.getPageSize()) + 1);
        return response;
    }

    public static <R> CommonApiResponse<R> success() {
        return success(null);
    }

    public static <T> CommonApiResponse<T> fromException(Throwable e) {
        if (e instanceof BizException) {
            return new CommonApiResponse<>(((BizException) e).getCode(), e.getMessage(), null);
        } else {
            return failure(GeneralCodes.InternalError.getCode(), "系统繁忙，请稍后再试");
        }
    }

    public static <T> CommonApiResponse<T> fromException(Throwable e, String message) {
        if (e instanceof BizException) {
            return new CommonApiResponse<>(((BizException) e).getCode(), message, null);
        } else {
            return failure(GeneralCodes.InternalError.getCode(), message);
        }
    }

    public Long getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(long totalCount) {
        this.totalCount = totalCount;
    }

    public Long getPageSize() {
        return pageSize;
    }

    public void setPageSize(long pageSize) {
        if (pageSize < 1) {
            this.pageSize = 10L;
        } else {
            this.pageSize = Math.min(pageSize, 1000L);
        }
    }

    public Long getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(long pageIndex) {
        if (pageIndex < 1) {
            this.pageIndex = 1L;
        } else {
            this.pageIndex = pageIndex;
        }
    }

    public Long getTotalPages() {
        if (totalPages == null && totalCount != null && pageSize != null) {
            return this.totalCount % this.pageSize == 0 ? this.totalCount / this.pageSize
                : (this.totalCount / this.pageSize) + 1;
        }
        return totalPages;
    }

    public void setTotalPages(Long totalPages) {
        this.totalPages = totalPages;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getTraceId() {
        return traceId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    public Map<String, String> getAttachments() {
        return attachments;
    }

    public void setAttachments(Map<String, String> attachments) {
        this.attachments = attachments;
    }
}
