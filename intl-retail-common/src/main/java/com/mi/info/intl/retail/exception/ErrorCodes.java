package com.mi.info.intl.retail.exception;

import com.xiaomi.youpin.infra.rpc.errors.ErrorCode;

/**
 * 错误码大全
 *
 * <AUTHOR>
 * @date 2025/07/25
 */
@SuppressWarnings("all")
public class ErrorCodes {

    /**
     * 系统异常
     */
    public static ErrorCode SYS_ERROR = ErrorCode.createOnce(DomainScope.SCOPE_SO, 1);

    /**
     * 业务异常
     */
    public static ErrorCode BIZ_ERROR = ErrorCode.createOnce(DomainScope.SCOPE_SO, 2);

    /**
     * 参数:{%s}为空
     */
    public static ErrorCode PARAM_IS_EMPTY = ErrorCode.createOnce(DomainScope.SCOPE_SO, 3);

    /**
     * 参数异常
     */
    public static ErrorCode PARAMS_ERROR = ErrorCode.createOnce(DomainScope.SCOPE_SO, 4);

    /**
     * {%s}不存在
     */
    public static ErrorCode NOT_EXISTS = ErrorCode.createOnce(DomainScope.SCOPE_SO, 5);

    /**
     * {%s}未找到
     */
    public static ErrorCode DATA_NOT_FOUND = ErrorCode.createOnce(DomainScope.SCOPE_SO, 6);

    /**
     * 发送MQ失败:{%s}
     */
    public static ErrorCode SEND_MQ_FAILED = ErrorCode.createOnce(DomainScope.SCOPE_SO, 7);

    /**
     * 消费MQ失败:{%s}
     */
    public static ErrorCode CONSUME_MQ_FAILED = ErrorCode.createOnce(DomainScope.SCOPE_SO, 8);

    /**
     * 阵地信息不存在
     */
    public static ErrorCode POSITION_NOT_EXIST = ErrorCode.createOnce(DomainScope.SCOPE_SO, 9);

    /**
     * 零售商不存在
     */
    public static ErrorCode RETAILER_NOT_EXIST = ErrorCode.createOnce(DomainScope.SCOPE_SO, 10);

    /**
     * 国家so规则不存在
     */
    public static ErrorCode SO_RULE_NOT_EXIST = ErrorCode.createOnce(DomainScope.SCOPE_SO, 11);

    /**
     * 国家不存在
     */
    public static ErrorCode COUNTRY_CODE_NOT_EXIST = ErrorCode.createOnce(DomainScope.SCOPE_SO, 12);

    /**
     * so规则不存在审批中记录
     */
    public static ErrorCode SO_RULE_LOG_NOT_EXIST = ErrorCode.createOnce(DomainScope.SCOPE_SO, 13);

    /**
     * 操作FDS失败
     */
    public static ErrorCode CALL_FDS_ERROR = ErrorCode.createOnce(DomainScope.SCOPE_SO, 14);

    /**
     * RPC调用超时
     */
    public static ErrorCode RPC_CALL_TIMEOUT = ErrorCode.createOnce(DomainScope.SCOPE_SO, 15);

    /**
     * RPC调用异常
     */
    public static ErrorCode RPC_CALL_ERROR = ErrorCode.createOnce(DomainScope.SCOPE_SO, 16);

    /**
     * 无法获取用户信息
     */
    public static ErrorCode CAN_NOT_OBTAIN_USERS = ErrorCode.createOnce(DomainScope.SCOPE_SO, 17);


    /**
     * so规则修改流程审批状态不合法
     */
    public static final ErrorCode SO_RULE_LOG_STATUS_ILLEGAL =  ErrorCode.createOnce(DomainScope.SCOPE_SO, 18) ;

    /**
     * so规则修改流程发起失败
     */
    public static final ErrorCode SO_RULE_LOG_APPROVAL_FAILED = ErrorCode.createOnce(DomainScope.SCOPE_SO, 19);

    /**
     * 规则条数大于最大条数
     */
    public static final ErrorCode RULES_GREATER_THAN_MAX = ErrorCode.createOnce(DomainScope.SCOPE_SO, 20);

    /**
     * imei规则中，存在user title 和 activation product line重复的数据
     */
    public static final ErrorCode IMEI_RULE_REPEAT = ErrorCode.createOnce(DomainScope.SCOPE_SO, 21);

    /**
     * photo规则中，存在 user title 重复的数据
     */
    public static final ErrorCode PHOTO_RULE_REPEAT = ErrorCode.createOnce(DomainScope.SCOPE_SO, 22);

    /**
     * imei规则中，存在 activate time 范围错误的数据
     */
    public static final ErrorCode ACTIVATE_TIME_RANGE_ERROR = ErrorCode.createOnce(DomainScope.SCOPE_SO, 23);

    /**
     * 当前国家已存在规则记录，无法创建；
     */
    public static final ErrorCode COUNTRY_EXISTS_RULE = ErrorCode.createOnce(DomainScope.SCOPE_SO, 24);

    /**
     * 当前国家已存在审批中的规则记录，无法创建；
     */
    public static final ErrorCode COUNTRY_EXISTS_APPROVING_RULE = ErrorCode.createOnce(DomainScope.SCOPE_SO, 25);

    /**
     * 审批节点错误
     */
    public static final ErrorCode SO_RULE_APPROVE_NODE_ERROR = ErrorCode.createOnce(DomainScope.SCOPE_SO, 26);

    /**
     * 导出Excel失败
     */
    public static final ErrorCode EXPORT_TO_EXCEL_FAILED = ErrorCode.createOnce(DomainScope.SCOPE_SO, 27);

    /**
     * 加解密失败
     */
    public static final ErrorCode ENCRYPT_ERROR = ErrorCode.createOnce(DomainScope.SCOPE_SO, 28);

    /**
     * 无法获取用户信息
     */
    public static final ErrorCode CAN_NOT_OBTAIN_USER_INFO = ErrorCode.createOnce(DomainScope.SCOPE_SO, 29);

    /**
     * 规则无变更，不需要发起变更审批
     */
    public static final ErrorCode SO_RULE_NO_CHANGED = ErrorCode.createOnce(DomainScope.SCOPE_SO, 30);

    /**
     * 审批节点的所选审批人邮箱未设置，无法发起审批
     */
    public static final ErrorCode SO_RULE_APPROVE_NO_EMAIL = ErrorCode.createOnce(DomainScope.SCOPE_SO, 31);

    /**
     * 参数信息不合法
     */
    public static final ErrorCode PARAM_ILLEGAL = ErrorCode.createOnce(DomainScope.SCOPE_SO, 32);

}
