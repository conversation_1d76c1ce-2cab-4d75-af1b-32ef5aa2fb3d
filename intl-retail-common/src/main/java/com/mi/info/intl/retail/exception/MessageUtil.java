package com.mi.info.intl.retail.exception;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.lang3.StringUtils;

import com.mi.info.intl.retail.enums.AreaEnum;
import com.mi.info.intl.retail.model.UserInfo;
import com.mi.info.intl.retail.utils.UserInfoUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 消息util
 *
 * <AUTHOR>
 * @date 2025/07/28
 */
public class MessageUtil {

    private MessageUtil() {}


    private static final int ERROR_CODE_PREFIX = 400;

    public static String getMessageForLanguage(Integer code, Object... args) {
        String message = getMessageStr(code);
        if (args != null && args.length > 0) {
            for (Object o : args) {
                if (o == null) {
                    continue;
                }
                if (o.getClass() == Integer.class) {
                    o = o + "";
                }
                message = message.replaceFirst(Pattern.quote("{%s}"), Matcher.quoteReplacement(o.toString()));
            }
        }
        return message.replace("%s", "");
    }

    public static String getMessageStr(Integer code) {
        String message;
        code = code % Integer.parseInt(String.format("%03d%03d", ERROR_CODE_PREFIX, DomainScope.SCOPE_SO.getScopeId()));
        String language = UserInfoUtil.getUserInfo().orElse(new UserInfo()).getLanguage();
        AreaEnum areaEnum = getAreaEnumByLanguage(language);
        switch (areaEnum) {
            case CN:
                MessageCN messageCN = MessageCN.get(code);
                message = messageCN == null ? "" : messageCN.getDesc();
                break;
            case US:
            default:
                MessageUS defaultMssage = MessageUS.get(code);
                message = defaultMssage == null ? "" : defaultMssage.getDesc();
        }
        return message;
    }
    /**
     * 根据语言获取对应的区域枚举值
     *
     * @param language 语言字符串
     * @return 对应的区域枚举值
     */
    public static AreaEnum getAreaEnumByLanguage(String language) {
        if (StringUtils.isNotBlank(language) && language.contains("-")) {
            String[] languageParts = language.split("-");
            language = languageParts[0];
        }
        return AreaEnum.getByLanguage(language);
    }
}
