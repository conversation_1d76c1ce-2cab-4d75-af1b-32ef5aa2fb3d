package com.mi.info.intl.retail.utils.redis;

import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
public enum RedisKeyEnum {

    LOCK_SYNC_DATA_IMEI("LOCK_SYNC_DATA_IMEI:{0}", -1, "同步RMS的IMEI数据锁"),
    LOCK_SYNC_DATA_QTY("LOCK_SYNC_DATA_QTY:{0}", -1, "同步RMS的QTY数据锁"),
    RMS_USER_CACHE("RMS_USER_CACHE", TimeConfig.MINUTE30, "RMS用户缓存（登录用户）"),
    SYNC_IMEI_DATA_LAST_ID("SYNC_IMEI_DATA_LAST_ID", -1, "同步rms-imei存量数据最后同步id"),
    SYNC_QTY_DATA_LAST_ID("SYNC_QTY_DATA_LAST_ID", -1, "同步rms-qty存量数据最后同步id"),
    RMS_USER_MID_CACHE("RMS_USER_MID_CACHE", TimeConfig.HOUR, "rmsId与用户米id对应信息"),
    RMS_POSITION_CACHE("RMS_POSITION_CACHE", TimeConfig.HOUR, "rms阵地、门店数据"),
    RMS_RRP_CACHE("RMS_RRP_CACHE", TimeConfig.HOUR, "rms rrp数据"),
    ORG_INFO_COUNTRY_CODE("ORG_INFO_COUNTRY_CODE", TimeConfig.HOUR, "intl_so_org_info 国家缩写"),
    LOCK_SYNC_DATA_IMEI_TASK("LOCK_SYNC_DATA_IMEI_TASK", -1, "同步存量RMS的IMEI定时任务锁"),
    LOCK_SYNC_DATA_QTY_TASK("LOCK_SYNC_DATA_QTY_TASK", -1, "同步存量RMS的Qty定时任务锁"),
    LOCK_SYNC_DATA_CONSUMER("LOCK_SYNC_DATA_CONSUMER", -1, "同步存量RMS的SO数据消费消息锁"),
    ;

    private String key;
    private int expireTime;
    private String desc;

    RedisKeyEnum(String key, int expireTime, String desc) {
        this.key = key;
        this.expireTime = expireTime;
        this.desc = desc;
    }

    private void setKey(String key) {
        this.key = key;
    }

    private void setExpireTime(int expireTime) {
        this.expireTime = expireTime;
    }

    private void setDesc(String desc) {
        this.desc = desc;
    }

    public RedisKey get(Object... args) {
        return new RedisKey(this.key, this.desc, this.expireTime, args);
    }

    @Override
    public String toString() {
        return "RedisKeyEnum{" +
                "key='" + key + '\'' +
                ", expireTime=" + expireTime +
                ", desc='" + desc + '\'' +
                '}';
    }
}
