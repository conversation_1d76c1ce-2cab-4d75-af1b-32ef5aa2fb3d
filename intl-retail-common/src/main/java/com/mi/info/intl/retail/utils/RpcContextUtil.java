package com.mi.info.intl.retail.utils;

import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.rpc.RpcContext;

import java.util.Map;
import java.util.stream.Collectors;

/**
 * RPC上下文工具类
 * 提供从RPC上下文中获取地区ID、语言等信息的静态方法
 *
 * <AUTHOR>
 * @date 2024/12/19
 */
@Slf4j
public class RpcContextUtil {

    /**
     * 从RpcContext的heracontext中获取http请求Headers中的areaId
     *
     * @param heracontext
     * @return
     */
    public static String getAreaId(String heracontext) {
        return String.valueOf(getValueByKey(heracontext, "mone-retail-area-for-global"));
    }


    /**
     * 从RpcContext的heracontext中获取http请求Headers中的language
     *
     * @param heracontext
     * @return
     */
    public static String getLanguage(String heracontext) {
        return String.valueOf(getValueByKey(heracontext, "mone-retail-language-for-global"));
    }

    /**
     * 从当前RPC上下文中获取地区ID
     * 无参静态方法，复用getAreaId方法
     *
     * @return 地区ID，如果获取失败返回null
     */
    public static String getCurrentAreaId() {
        String heracontext = getCurrentHeracontext();
        return heracontext != null ? getAreaId(heracontext) : null;
    }


    /**
     * 从当前RPC上下文中获取当前账户信息
     *
     * @return 账户信息，如果获取失败返回null
     */
    public static String getCurrentAccount() {
        try {
            RpcContext rpcContext = RpcContext.getContext();
            if (rpcContext == null) {
                log.warn("RpcContext is null");
                return null;
            }
            
            String account = rpcContext.getAttachment("$upc_account");
            String username = rpcContext.getAttachment("$upc_userName");
            String phone = rpcContext.getAttachment("$upc_mobile");
            String miId = rpcContext.getAttachment("$upc_miID");
            String email = rpcContext.getAttachment("$upc_email");

            return account;
        } catch (Exception e) {
            log.error("Failed to get account from RPC context", e);
            return null;
        }
    }

    /**
     * 从当前RPC上下文中获取当前用户名
     *
     * @return 用户名，如果获取失败返回null
     */
    public static String getCurrentUsername() {
        try {
            RpcContext rpcContext = RpcContext.getContext();
            if (rpcContext == null) {
                log.warn("RpcContext is null");
                return null;
            }
            
            return rpcContext.getAttachment("$upc_userName");
        } catch (Exception e) {
            log.error("Failed to get username from RPC context", e);
            return null;
        }
    }

    /**
     * 从当前RPC上下文中获取当前用户手机号
     *
     * @return 手机号，如果获取失败返回null
     */
    public static String getCurrentMobile() {
        try {
            RpcContext rpcContext = RpcContext.getContext();
            if (rpcContext == null) {
                log.warn("RpcContext is null");
                return null;
            }
            
            return rpcContext.getAttachment("$upc_mobile");
        } catch (Exception e) {
            log.error("Failed to get mobile from RPC context", e);
            return null;
        }
    }

    /**
     * 从当前RPC上下文中获取当前用户小米ID
     *
     * @return 小米ID，如果获取失败返回null
     */
    public static String getCurrentMiId() {
        try {
            RpcContext rpcContext = RpcContext.getContext();
            if (rpcContext == null) {
                log.warn("RpcContext is null");
                return null;
            }
            
            return rpcContext.getAttachment("$upc_miID");
        } catch (Exception e) {
            log.error("Failed to get miId from RPC context", e);
            return null;
        }
    }

    /**
     * 从当前RPC上下文中获取当前用户邮箱
     *
     * @return 邮箱，如果获取失败返回null
     */
    public static String getCurrentEmail() {
        try {
            RpcContext rpcContext = RpcContext.getContext();
            if (rpcContext == null) {
                log.warn("RpcContext is null");
                return null;
            }
            
            return rpcContext.getAttachment("$upc_email");
        } catch (Exception e) {
            log.error("Failed to get email from RPC context", e);
            return null;
        }
    }

    /**
     * 从当前RPC上下文中获取语言
     * 无参静态方法，复用getLanguage方法
     *
     * @return 语言代码，如果获取失败返回null
     */
    public static String getCurrentLanguage() {
        String heracontext = getCurrentHeracontext();
        return heracontext != null ? getLanguage(heracontext) : null;
    }

    /**
     * 检查当前RPC上下文是否有效
     *
     * @return true表示有效，false表示无效
     */
    public static boolean isRpcContextValid() {
        return getCurrentHeracontext() != null;
    }

    /**
     * 获取当前RPC上下文中的heracontext
     * 私有辅助方法，统一处理RPC上下文获取逻辑
     *
     * @return heracontext字符串，如果获取失败返回null
     */
    private static String getCurrentHeracontext() {
        try {
            RpcContext rpcContext = RpcContext.getContext();
            if (rpcContext == null) {
                log.warn("RpcContext is null");
                return null;
            }
            
            String heracontext = rpcContext.getAttachment("heracontext");
            if (heracontext == null || heracontext.isEmpty()) {
                log.warn("heracontext is null or empty in RpcContext");
                return null;
            }
            
            return heracontext;
        } catch (Exception e) {
            log.error("Failed to get heracontext from RPC context", e);
            return null;
        }
    }

    private static String getValueByKey(String heracontext, String key) {
        if (heracontext == null || heracontext.isEmpty()) {
            return null;
        }

        Map<String, String> map = java.util.Arrays.stream(heracontext.split(";"))
                .map(part -> part.split(":"))
                .filter(keyValue -> keyValue.length >= 2)
                .collect(Collectors.toMap(keyValue -> keyValue[0], keyValue -> keyValue[1], (v1, v2) -> v1));

        return map.get(key);

    }
} 