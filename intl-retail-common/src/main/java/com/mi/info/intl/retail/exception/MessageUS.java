package com.mi.info.intl.retail.exception;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 英文消息
 *
 * <AUTHOR>
 * @date 2025/07/28
 */
@Getter
@AllArgsConstructor
public enum MessageUS {

    /**
     * US Message
     */
    SUCCESS(200, "success"),
    SYS_ERROR(1, "system error {%s}"),
    ERR_BIZ_ERROR(2, "business exception {%s}"),
    ERR_PARAM_IS_EMPTY(3, "param is null {%s}"),
    ERR_PARAMS_ERROR(4, "param error {%s}"),
    ERR_NOT_EXISTS(5, "not exists {%s}"),
    ERR_DATA_NOT_FOUND(6, "data not found {%s}"),
    ERR_SEND_MQ_FAILED(7, "send mq failed {%s}"),
    ERR_CONSUME_MQ_FAILED(8, "consumer mq failed {%s}"),
    POSITION_NOT_EXIST(9, "position not exist {%s}"),
    RETAILER_NOT_EXIST(10, "retailer not exist {%s}"),
    SO_RULE_NOT_EXIST(11, "so rules not exist {%s}"),
    COUNTRY_CODE_NOT_EXIST(12, "country code not exist {%s}"),
    SO_RULE_LOG_NOT_EXIST(13, "The so rule does not exist in the approval record {%s}"),
    CALL_FDS_ERROR(14, "call fds error {%s}"),
    RPC_CALL_TIMEOUT(15, "interface {%s} timeout"),
    RPC_CALL_ERROR(16, "interface {%s} error"),
    CAN_NOT_OBTAIN_USERS(17, "Job id:{%s} cannot obtain user information"),
    SO_RULE_LOG_STATUS_ILLEGAL(18, "So rule approval status illegal. id={%s}, status={%s}"),
    SO_RULE_LOG_APPROVAL_FAILED(19, "So rule approval process exception. id={%s}, action={%s}"),
    RULES_GREATER_THAN_MAX(20, "The maximum number of rules allowed to be added is %s."),
    IMEI_RULE_REPEAT(21, "IMEI Activation Rules：User Title and Activation Product Line cannot be repeated."),
    PHOTO_RULE_REPEAT(22, "Photo Rules：User Title cannot be repeated."),
    ACTIVATE_TIME_RANGE_ERROR(23, "IMEI Activation Rules：Activate Time From， Activate Time To, range error."),
    COUNTRY_EXISTS_RULE(24, "The country({%s}) already has a rule record, and cannot be created."),
    COUNTRY_EXISTS_APPROVING_RULE(25, "The country({%s}) already has a approving rule, and cannot be modified."),
    SO_RULE_APPROVE_NODE_ERROR(26, "National Retail Manager or HQ Strategy Manager was not found."),
    EXPORT_TO_EXCEL_FAILED(27, "Export to excel failed."),
    ENCRYPT_ERROR(28, "Encryption and decryption failed."),
    CAN_NOT_OBTAIN_USER_INFO(29, " {%s} was not found."),
    SO_RULE_NO_CHANGED(30, "You haven't modified any data, so submission is not allowed."),
    SO_RULE_APPROVE_NO_EMAIL(31, "The {%s} approver's email address is not set, so approval cannot be initiated."),
    ;

    private int code;
    private String desc;

    public static MessageUS get(int code) {
        MessageUS[] elements = MessageUS.values();
        for (MessageUS element : elements) {
            if (element.getCode() == code) {
                return element;
            }
        }
        return null;
    }

}
