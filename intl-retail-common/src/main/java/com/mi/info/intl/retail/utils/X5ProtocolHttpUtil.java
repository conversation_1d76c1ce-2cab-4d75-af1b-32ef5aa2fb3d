package com.mi.info.intl.retail.utils;

import cn.hutool.json.JSONUtil;
import com.google.gson.Gson;
import com.xiaomi.core.auth.x5.X5AppInfo;
import com.xiaomi.core.auth.x5.X5Response;
import com.xiaomi.core.auth.x5.X5Utils;
import com.xiaomi.core.http.MitHttpClients;
import com.xiaomi.keycenter.org.apache.http.HttpEntity;
import com.xiaomi.keycenter.org.apache.http.HttpResponse;
import com.xiaomi.keycenter.org.apache.http.NameValuePair;
import com.xiaomi.keycenter.org.apache.http.client.config.RequestConfig;
import com.xiaomi.keycenter.org.apache.http.client.entity.UrlEncodedFormEntity;
import com.xiaomi.keycenter.org.apache.http.client.methods.HttpPost;
import com.xiaomi.keycenter.org.apache.http.impl.client.CloseableHttpClient;
import com.xiaomi.keycenter.org.apache.http.impl.client.HttpClients;
import com.xiaomi.keycenter.org.apache.http.message.BasicNameValuePair;
import com.xiaomi.keycenter.org.apache.http.util.EntityUtils;
import com.xiaomi.mit.api.error.Exceptions;
import com.xiaomi.mit.common.http.Body;
import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * X5协议http工具类
 *
 * <AUTHOR>
 * @date 2025/7/4 18:12
 */
@Slf4j
public final class X5ProtocolHttpUtil {

    private X5ProtocolHttpUtil() {
    }

    /**
     * X5协议http请求，使用MitHttpClients封装的x5调用
     *
     * @param url         请求服务url，不包含路由的最后一段
     * @param x5AppInfo   X5AppInfo对象，包含appId, appSecret, method
     * @param requestBody 请求体
     * @param <T>         请求体类型
     * @param <R>         响应体类型
     * @return 响应体
     */
    public static <T extends Body, R> R post(String url, X5AppInfo x5AppInfo, T requestBody, Class<R> responseType) {
        X5Response<R> x5Response;
        try {
            x5Response = MitHttpClients.x5(x5AppInfo).url(url).post(requestBody).as(responseType);
        } catch (Exception e) {
            log.error("request failed with body : {}", JSONUtil.toJsonStr(requestBody), e);
            throw Exceptions.internalServerError("request failed: " + e.getMessage());
        }
        if (x5Response.getHeader().getIntCode() == 200) {
            return x5Response.getBody();
        }
        throw Exceptions.internalServerError("request failed with bad code: " + x5Response.getHeader().getIntCode() + x5Response);
    }

    /**
     * 使用原始方式x5协议http请求
     *
     * @param url         请求服务url，不包含路由的最后一段
     * @param x5AppInfo   X5AppInfo对象，包含appId, appSecret, method
     * @param requestBody 请求体
     * @param <T>         请求体类型
     * @param <R>         响应体类型
     * @return 响应体
     */
    public static <T, R> R doX5Post(String url, X5AppInfo x5AppInfo, T requestBody, Class<R> responseType) {
        // 原始方式
        Gson gson = new Gson();
        String requestBodyJson = gson.toJson(requestBody);
        String body = X5Utils.buildX5RequestBody(x5AppInfo, requestBodyJson);
        X5Response<?> x5Response;
        try {
            String response = doX5Post(url, body);
            x5Response = gson.fromJson(response, X5Response.class);
            log.info("X5 request: {}, with body: {}, response: {}", url, gson.toJson(requestBody), response);
        } catch (Exception e) {
            log.error("X5 request: {}, with body: {} , failed.", url, gson.toJson(requestBody), e);
            throw Exceptions.internalServerError("request failed. " + e.getMessage());
        }
        if (x5Response.getHeader().getIntCode() == 200) {
            return gson.fromJson(gson.toJson(x5Response.getBody()), responseType);
        }
        throw Exceptions.internalServerError("request failed with bad code: " + x5Response.getHeader().getIntCode() + x5Response);
    }


    private static String doX5Post(String url, String data) throws Exception {
        Map<String, String> params = new HashMap<>();
        params.put("data", data);
        CloseableHttpClient httpclient = HttpClients.createDefault();
        HttpPost httpPost = new HttpPost();
        String content;
        // 开始添加参数
        List<NameValuePair> baluePairs = new ArrayList<>();

        Set<String> keys = params.keySet();
        for (String k : keys) {
            baluePairs.add(new BasicNameValuePair(k, params.get(k)));
        }

        UrlEncodedFormEntity uefEntity;
        try {
            RequestConfig requestConfig = RequestConfig.custom()
                    .setSocketTimeout(2000)
                    .setConnectTimeout(2000)
                    .build();
            httpPost.setConfig(requestConfig);
            uefEntity = new UrlEncodedFormEntity(baluePairs, StandardCharsets.UTF_8);

            httpPost.setURI(new java.net.URI(url));
            httpPost.setEntity(uefEntity);

            HttpResponse response = httpclient.execute(httpPost);
            HttpEntity entity = response.getEntity();
            content = EntityUtils.toString(entity, StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new Exception(e);
        } finally {
            httpPost.abort();
            httpclient.close();
        }
        return content;
    }

    public static void main(String[] args) {
        // 本模块缺okhttp3依赖，调用会报错
        String url = "https://cn-gw.zg.mi.com/wms/upc/api/x5/skuBaseQuery/timeQuery";
        String appid = "xm_market";
        String appKey = "a090904f449448de8a34c8bcd76690cb";
        String method = "skuBaseTimeQuery";

        Map<String, Object> params = new HashMap<>();
        params.put("page", 1);
        params.put("pageSize", 1);
        List<String> list = new ArrayList<>();
        list.add("65543");
        params.put("queryKey", list);
        params.put("queryType", "goods_id");
//       查询分类
//        String url = "https://cn-gw.zg.mi.com/wms/upc/api/x5/skuBaseCategory/listCategory";
//        String appid = "xm_market";
//        String appKey = "a090904f449448de8a34c8bcd76690cb";
//        String method = "listCategoryByLevel";
//
//        Map<String, Object> params = new HashMap<>();
//        params.put("categoryId", 1);
//        params.put("level", 2);
//        params.put("status", 1);

        post(url, new X5AppInfo(appid, appKey, method), Body.json(JSONUtil.toJsonStr(params)), List.class);
    }

}
