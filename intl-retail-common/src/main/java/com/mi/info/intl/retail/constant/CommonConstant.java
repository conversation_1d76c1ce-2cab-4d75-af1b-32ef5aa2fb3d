package com.mi.info.intl.retail.constant;


/**
 * 公共常量
 *
 * <AUTHOR>
 * @date 2025/08/14
 */
@SuppressWarnings("all")
public class CommonConstant {

    public static final String COLON = ":";

    public static final Integer SUCCESS_CODE = 0;
    public static final Integer  DEFAULT_VALUE = 0;
    /**
     * AES GCM密钥的key
     */
    public static final String AES_GCM_KEY = "aesGcmKey";

    /**
     * HMAC SHA256密钥的key
     */
    public static final String HMAC_SHA256_KEY = "hmacSha256Key";

    public static final Integer DEFAULT_INTEGER = 0;

    public static final Long DEFAULT_LONG = 0L;
    /**
     * TraceId 在 RpcContext 中的 key
     */
    public static final String TRACE_ID_KEY = "_trace_id_";
    /**
     * TraceId 在 MDC 中的 key
     */
    public static final String MDC_TRACE_ID_KEY = "traceId";
    /**
     * TraceId 在 HTTP Header 中的 key
     */
    public static final String TRACE_ID_HEADER = "X-Trace-Id";

    public static final Integer REPORT_ENABLED = 1;  // 已上报
    public static final Integer REPORT_DISABLED = 0; // 未上报
    public static final String DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
    public static final String SUCCESS = "success";
    public static final String TEMPLATE_TYPE_UPLOADED = "1"; // 模板类型
    public static final String TEMPLATE_TYPE_STOP = "2";
    public static final String EXPORT_FAILED_ERROR = "导出失败";
    public static final Integer ERROR_STATUS = 500;
    public static final String QUERY_STATISTICS_FAILED_ERROR = "查询统计失败:";

    public interface appTag {
        String demandPlanningCenter = "DEMAND_PLANNING_CENTER";
    }

    /**
     * 是否生效
     */
    public interface Valid {
        /**
         * 生效
         */
        int VALID = 1;
        /**
         * 失效
         */
        int NOT_VALID = 2;
    }

    /**
     * 特殊占位符
     */
    public interface Placeholder {
        int DB_NULL_INT = -1;
        Long DB_NULL_LONG = -1L;
        String DB_NULL_STR = "-1";
        String SYMBOL_UNDERLINE = "_";
        String SYMBOL_COMMA_EN = ",";
        String SYMBOL_COMMA_ZH = "，";
        String SYMBOL_POUND = "#";
        String SYMBOL_COLON_EN = ":";
        String SYMBOL_SLASH = "/";
        Integer LIMIT_REMARK = 150;
    }

    /**
     * 常用数字
     */
    public interface Number {
        int ZERO = 0;
        int ONE = 1;
        int NINETY = 90;
        int ONE_HUNDRED = 100;
    }

    /**
     * SQL  数量大小
     */
    public interface QueryPage {
        public static final int SQL_SIZE = 10;
        public static final int DEAULT_SQL_SIZE = 20;
        public static final int SQL_NUMBER = 1;
        public static final int MAX_SQL_SIZE = 100000;
    }

    public interface CommonMsg {
        public static final String MID_NOT_NUll = "miId can not be null";
    }

    public interface File {
        public static final String EXCEL_END_POINT_XLSX = ".xlsx";
    }

    public interface JwtAuth {

        /**
         * jwt token 用户信息key，在http请求开始前解析用户信息，并保存到请求上下文中
         */
        public static final String JWT_TOKEN_USER = "JwtAuthUser";
    }
}
