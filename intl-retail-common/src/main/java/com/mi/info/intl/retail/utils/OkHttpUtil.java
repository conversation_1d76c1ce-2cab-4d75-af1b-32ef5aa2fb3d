package com.mi.info.intl.retail.utils;

import com.google.common.base.Joiner;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.io.IOUtils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * okhttp工具类
 *
 * <AUTHOR>
 * @date 2025/07/21
 */
public class OkHttpUtil {
    public static final MediaType JSON_TYPE = MediaType.parse("application/json; charset=utf-8");
    private OkHttpClient client;

    private OkHttpUtil(long connectTimeout, long readTimeout, long writeTimeout) {
        client = new OkHttpClient.Builder()
                .connectTimeout(connectTimeout, TimeUnit.SECONDS)
                .readTimeout(readTimeout, TimeUnit.SECONDS)
                .writeTimeout(writeTimeout, TimeUnit.SECONDS)
                .build();
    }

    private OkHttpUtil() {
        this(10, 10, 10);
    }

    // 静态内部类实现单例，线程安全且懒加载
    private static class Holder {
        private static final OkHttpUtil INSTANCE = new OkHttpUtil();
    }

    public static OkHttpUtil getInstance() {
        return Holder.INSTANCE;
    }

    /**
     * get请求
     *
     * @param url URL
     * @param headers 标题
     * @return {@link String }
     */
    public String get(String url, Map<String, String> headers) {
        Request.Builder builder = new Request.Builder().url(url);
        if (MapUtils.isNotEmpty(headers)) {
            headers.forEach(builder::addHeader);
        }
        Request request = builder.get().build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            return Objects.requireNonNull(response.body()).string();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * get请求，支持requestParam参数
     *
     * @param url URL
     * @param headers 标题
     * @param params 参数
     * @return {@link String }
     */
    public String get(String url, Map<String, String> headers, Map<String, Object> params) {
        if (MapUtils.isNotEmpty(params)) {
            if (!url.contains("?")) {
                url = url + "?";
            }
            url = url + Joiner.on("&").withKeyValueSeparator("=").join(params);
        }
        return get(url, headers);
    }

    /**
     * POST请求，发送JSON
     *
     * @param url URL
     * @param jsonObj JSON OBJ
     * @param headers 标题
     * @return {@link String }
     */
    public String postJson(String url, Object jsonObj, Map<String, String> headers) {
        String json = JsonUtil.bean2json(jsonObj);
        RequestBody body = RequestBody.create(JSON_TYPE, json);
        Request.Builder builder = new Request.Builder().url(url).post(body);
        if (headers != null) {
            headers.forEach(builder::addHeader);
        }
        Request request = builder.build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            return Objects.requireNonNull(response.body()).string();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * PUT请求，发送JSON
     *
     * @param url URL
     * @param jsonObj JSON OBJ
     * @param headers 标题
     * @return {@link String }
     * @throws IOException ioException
     */
    public String putJson(String url, Object jsonObj, Map<String, String> headers) {
        String json = JsonUtil.bean2json(jsonObj);
        RequestBody body = RequestBody.create(JSON_TYPE, json);
        Request.Builder builder = new Request.Builder().url(url).put(body);
        if (headers != null) {
            headers.forEach(builder::addHeader);
        }
        Request request = builder.build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            return Objects.requireNonNull(response.body()).string();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * DELETE请求
     *
     * @param url URL
     * @param headers 标题
     * @return {@link String }
     * @throws IOException ioException
     */
    public String delete(String url, Map<String, String> headers) {
        Request.Builder builder = new Request.Builder().url(url).delete();
        if (headers != null) {
            headers.forEach(builder::addHeader);
        }
        Request request = builder.build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            return Objects.requireNonNull(response.body()).string();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 文件上传（multipart/form-data）
     *
     * @param url URL
     * @param file 文件
     * @param fileField 文件字段名
     * @param formFields 表单字段
     * @param headers 标题
     * @return {@link String }
     * @throws IOException ioException
     */
    public String uploadFile(String url, File file, String fileField, Map<String, String> formFields,
                             Map<String, String> headers) {
        MultipartBody.Builder multipartBuilder = new MultipartBody.Builder().setType(MultipartBody.FORM);
        if (formFields != null) {
            formFields.forEach(multipartBuilder::addFormDataPart);
        }
        multipartBuilder.addFormDataPart(fileField, file.getName(),
                RequestBody.create(MediaType.parse("application/octet-stream"), file));
        RequestBody requestBody = multipartBuilder.build();
        Request.Builder builder = new Request.Builder().url(url).post(requestBody);
        if (headers != null) {
            headers.forEach(builder::addHeader);
        }
        Request request = builder.build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            return Objects.requireNonNull(response.body()).string();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 文件下载（回调中处理流）
     *
     * @param url URL
     * @param destPath 目标路径
     * @param headers 标题
     * @throws IOException ioException
     */
    public void downloadFile(String url, String destPath, Map<String, String> headers) {
        Request.Builder builder = new Request.Builder().url(url);
        if (headers != null) {
            headers.forEach(builder::addHeader);
        }
        Request request = builder.build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            assert response.body() != null;
            try (InputStream is = response.body().byteStream();
                 FileOutputStream fos = new FileOutputStream(destPath)) {
                IOUtils.copyLarge(is, fos);
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * PATCH请求，发送JSON
     *
     * @param url URL
     * @param jsonObj JSON OBJ
     * @param headers 标题
     * @return {@link String }
     * @throws IOException ioException
     */
    public String patchJson(String url, Object jsonObj, Map<String, String> headers) {
        String json = JsonUtil.bean2json(jsonObj);
        RequestBody body = RequestBody.create(JSON_TYPE, json);
        Request.Builder builder = new Request.Builder().url(url).patch(body);
        if (MapUtils.isNotEmpty(headers)) {
            headers.forEach(builder::addHeader);
        }
        Request request = builder.build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            return Objects.requireNonNull(response.body()).string();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 异步GET请求
     *
     * @param url URL
     * @param headers 标题
     * @param callback 回调
     */
    public void getAsync(String url, Map<String, String> headers, okhttp3.Callback callback) {
        Request.Builder builder = new Request.Builder().url(url);
        if (headers != null && !headers.isEmpty()) {
            headers.forEach(builder::addHeader);
        }
        Request request = builder.get().build();
        client.newCall(request).enqueue(callback);
    }

    /**
     * 异步POST请求，发送JSON
     *
     * @param url URL
     * @param jsonObj JSON OBJ
     * @param headers 标题
     * @param callback 回调
     */
    public void postJsonAsync(String url, Object jsonObj, Map<String, String> headers, okhttp3.Callback callback) {
        String json = JsonUtil.bean2json(jsonObj);
        RequestBody body = RequestBody.create(JSON_TYPE, json);
        Request.Builder builder = new Request.Builder().url(url).post(body);
        if (headers != null && !headers.isEmpty()) {
            headers.forEach(builder::addHeader);
        }
        Request request = builder.build();
        client.newCall(request).enqueue(callback);
    }

    /**
     * 异步PUT请求，发送JSON
     *
     * @param url URL
     * @param jsonObj JSON OBJ
     * @param headers 标题
     * @param callback 回调
     */
    public void putJsonAsync(String url, Object jsonObj, Map<String, String> headers, okhttp3.Callback callback) {
        String json = JsonUtil.bean2json(jsonObj);
        RequestBody body = RequestBody.create(JSON_TYPE, json);
        Request.Builder builder = new Request.Builder().url(url).put(body);
        if (headers != null && !headers.isEmpty()) {
            headers.forEach(builder::addHeader);
        }
        Request request = builder.build();
        client.newCall(request).enqueue(callback);
    }

    /**
     * 异步DELETE请求
     *
     * @param url URL
     * @param headers 标题
     * @param callback 回调
     */
    public void deleteAsync(String url, Map<String, String> headers, okhttp3.Callback callback) {
        Request.Builder builder = new Request.Builder().url(url).delete();
        if (headers != null && !headers.isEmpty()) {
            headers.forEach(builder::addHeader);
        }
        Request request = builder.build();
        client.newCall(request).enqueue(callback);
    }

    /**
     * 异步PATCH请求，发送JSON
     *
     * @param url URL
     * @param jsonObj JSON OBJ
     * @param headers 标题
     * @param callback 回调
     */
    public void patchJsonAsync(String url, Object jsonObj, Map<String, String> headers, okhttp3.Callback callback) {
        String json = JsonUtil.bean2json(jsonObj);
        RequestBody body = RequestBody.create(JSON_TYPE, json);
        Request.Builder builder = new Request.Builder().url(url).patch(body);
        if (headers != null && !headers.isEmpty()) {
            headers.forEach(builder::addHeader);
        }
        Request request = builder.build();
        client.newCall(request).enqueue(callback);
    }

    /**
     * 异步文件上传（multipart/form-data）
     *
     * @param url URL
     * @param file 文件
     * @param fileField 文件字段名
     * @param formFields 表单字段
     * @param headers 标题
     * @param callback
     * @return void
     */
    public void uploadFileAsync(String url, File file, String fileField, Map<String, String> formFields,
                                Map<String, String> headers, okhttp3.Callback callback) {
        MultipartBody.Builder multipartBuilder = new MultipartBody.Builder().setType(MultipartBody.FORM);
        if (formFields != null && !formFields.isEmpty()) {
            formFields.forEach(multipartBuilder::addFormDataPart);
        }
        multipartBuilder.addFormDataPart(fileField, file.getName(),
                RequestBody.create(MediaType.parse("application/octet-stream"), file));
        RequestBody requestBody = multipartBuilder.build();
        Request.Builder builder = new Request.Builder().url(url).post(requestBody);
        if (headers != null && !headers.isEmpty()) {
            headers.forEach(builder::addHeader);
        }
        Request request = builder.build();
        client.newCall(request).enqueue(callback);
    }

    /**
     * 异步文件下载（回调中处理流）
     *
     * @param url URL
     * @param headers 标题
     * @param callback 回调
     * @return void
     */
    public void downloadFileAsync(String url, Map<String, String> headers, okhttp3.Callback callback) {
        Request.Builder builder = new Request.Builder().url(url);
        if (headers != null && !headers.isEmpty()) {
            headers.forEach(builder::addHeader);
        }
        Request request = builder.build();
        client.newCall(request).enqueue(callback);
    }
}
