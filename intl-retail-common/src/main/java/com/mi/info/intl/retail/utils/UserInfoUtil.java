package com.mi.info.intl.retail.utils;

import java.util.Objects;
import java.util.Optional;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.rpc.RpcContext;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.mi.info.intl.retail.constant.CommonConstant;
import com.mi.info.intl.retail.enums.AreaEnum;
import com.mi.info.intl.retail.exception.BizException;
import com.mi.info.intl.retail.exception.ErrorCodes;
import com.mi.info.intl.retail.model.UserInfo;

import lombok.extern.slf4j.Slf4j;

import java.util.Objects;
import javax.servlet.http.HttpServletRequest;

/**
 * 用户信息工具类
 *
 * <AUTHOR>
 * @date 2025/07/28
 */
@Slf4j
@Component
public class UserInfoUtil {

    private UserInfoUtil() {}

    /**
     * 用户id的key
     */
    public static final String USER_ID = "userId";
    /**
     * 自定义language
     */
    public static final String LANGUAGE = "language";

    private static final String UPC_ACCOUNT = "$upc_account";

    private static final String UPC_NAME = "$upc_userName";

    private static final String UPC_LANG = "$language";

    private static final String AREA_ID = "$area_id";

    private static final String UPC_MIID = "$upc_miID";

    private static final String UPC_EMAIL = "$upc_email";

    private static HttpServletRequest getHttpServletRequest() {
        ServletRequestAttributes servletRequestAttributes =
            (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        // 获取request
        if (Objects.isNull(servletRequestAttributes)) {
            throw new BizException(ErrorCodes.SYS_ERROR, "get httpServletRequest is null");
        }
        return servletRequestAttributes.getRequest();
    }

    /**
     * 获取当前登录用户语言信息
     *
     * @return 用户当前语言，默认en
     */
    public static String getLanguage() {
        try {
            HttpServletRequest request = getHttpServletRequest();
            Object language = request.getHeader(LANGUAGE);
            log.info("当前登录用户language = {}", language);
            return Objects.isNull(language) ? AreaEnum.US.getLanguage() : String.valueOf(language);
        } catch (Exception e) {
            return AreaEnum.US.getLanguage();
        }
    }

    /**
     * 从RpcContext中获取用户信息。 若RpcContext中获取不到用户信息，则从request中获取。 若都获取不到，抛出异常。 <br/>
     * 注意：若需从dubbo接口中获取用户信息，需要在网关中配置过滤器 upc鉴权Filter<br/>
     * 若调用的是http接口，则由HandlerInterceptor拦截注入用户信息<br/>
     * <a href="https://xiaomi.f.mioffice.cn/docx/doxk42ffsr5QdBZ1oeXaNoC98Qf">upc鉴权Filter</a>
     *
     * @return 登录用户信息。若获取不到，则抛出 {@link BizException}
     */
    public static UserInfo getUserContext() {
        Optional<UserInfo> optional = getUserInfo();
        if (optional.isPresent()) {
            return optional.get();
        } else {
            throw new BizException(ErrorCodes.SYS_ERROR, "User context can not be null.");
        }
    }

    /**
     * 从请求上下文中获取用户信息，若获取不到，则返回空
     *
     * @return 用户信息Optional
     */
    public static Optional<UserInfo> getUserInfo() {
        UserInfo userInfo = null;
        log.info("getObjectAttachments= {}", RpcContext.getContext().getObjectAttachments());
        String upcAccount = RpcContext.getContext().getAttachment(UPC_ACCOUNT);
        String upcMiId = RpcContext.getContext().getAttachment(UPC_MIID);
        String upcUserName = RpcContext.getContext().getAttachment(UPC_NAME);
        String upcLanguage = RpcContext.getContext().getAttachment(UPC_LANG);
        String upcAreaId = RpcContext.getContext().getAttachment(AREA_ID);
        String upcEmail = RpcContext.getContext().getAttachment(UPC_EMAIL);
        if (StringUtils.isNotBlank(upcAccount) || StringUtils.isNotBlank(upcMiId)) {
            userInfo = UserInfo.builder().userId(upcAccount).userName(upcUserName).email(upcEmail).areaId(upcAreaId)
                .language(upcLanguage).miID(Long.valueOf(upcMiId)).build();
        } else {
            // 若从RpcContext中获取不到用户信息，则从request中获取（若从dubbo调过来，无法获取到）
            ServletRequestAttributes requestAttributes =
                (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (Objects.nonNull(requestAttributes)) {
                userInfo = (UserInfo) getHttpServletRequest().getAttribute(CommonConstant.JwtAuth.JWT_TOKEN_USER);
                log.info("Get user info from HttpServletRequest, miId: {}",
                    Objects.nonNull(userInfo) ? userInfo.getMiID() : null);
            }
        }
        if (Objects.nonNull(userInfo)) {
            userInfo.setLanguage(
                Objects.nonNull(userInfo.getLanguage()) ? userInfo.getLanguage() : AreaEnum.CN.getLocal());
        }
        return Optional.ofNullable(userInfo);
    }

    /**
     * 获取用户邮箱前缀
     *
     * @param userEmail 用户邮箱
     * @return 用户邮箱前缀
     */
    public static String getEmailPrefix(String userEmail) {
        if (StringUtils.isNotBlank(userEmail)) {
            return userEmail.substring(0, userEmail.indexOf("@"));
        }
        return userEmail;
    }
}
