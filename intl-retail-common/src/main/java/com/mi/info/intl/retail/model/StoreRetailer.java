package com.mi.info.intl.retail.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 门店零售商信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StoreRetailer implements Serializable {
    private static final long serialVersionUID = 1L;
    
    /**
     * 门店编码
     */
    private String storeCode;

    /**
     * 零售商名称
     */
    private String retailerName;

    /**
     * 零售商代码
     */
    private String retailerCode;

    /**
     * 渠道类型
     */
    private Integer channelType;

} 