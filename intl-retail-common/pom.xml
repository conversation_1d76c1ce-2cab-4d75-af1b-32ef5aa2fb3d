<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.mi.info.intl.retail</groupId>
        <artifactId>intl-retail</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>

    <artifactId>intl-retail-common</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

        <!-- 依赖版本管理 - 与父 POM 保持一致 -->
        <cola.components.version>4.0.0</cola.components.version>
        <easyexcel.version>3.1.0</easyexcel.version>
        <poi.version>5.1.0</poi.version>
        <hutool.version>5.8.22</hutool.version>
        <i18n-area-java-sdk.version>1.1-nacos-SNAPSHOT</i18n-area-java-sdk.version>
        <miapi-doc-annos.version>2.7.12-mone-v20-SNAPSHOT</miapi-doc-annos.version>
        <mockwebserver.version>4.12.0</mockwebserver.version>
        <orika.version>1.5.2</orika.version>
        <redisson.version>3.17.7</redisson.version>
        <unirest.version>3.14.1</unirest.version>
        <google.zxing.version>3.4.1</google.zxing.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.alibaba.cola</groupId>
            <artifactId>cola-component-dto</artifactId>
            <version>${cola.components.version}</version>
        </dependency>
        <dependency>
            <groupId>com.xiaomi.youpin</groupId>
            <artifactId>hera-trace</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>${easyexcel.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.poi</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>cglib</groupId>
                    <artifactId>cglib</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>${poi.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>${poi.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>${hutool.version}</version>
        </dependency>
        <dependency>
            <groupId>com.xiaomi.nr</groupId>
            <artifactId>i18n-area-java-sdk</artifactId>
            <version>${i18n-area-java-sdk.version}</version>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
        </dependency>
        <!-- miapi 相关 -->
        <dependency>
            <groupId>com.xiaomi.mone</groupId>
            <artifactId>miapi-doc-annos</artifactId>
            <version>${miapi-doc-annos.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>mockwebserver</artifactId>
            <version>${mockwebserver.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>ma.glasnost.orika</groupId>
            <artifactId>orika-core</artifactId>
            <version>${orika.version}</version>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-boot-starter</artifactId>
            <version>${redisson.version}</version>
        </dependency>
        <dependency>
            <groupId>com.konghq</groupId>
            <artifactId>unirest-java</artifactId>
            <version>${unirest.version}</version> <!-- 最新版本可查询Maven仓库 -->
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>core</artifactId>
            <version>${google.zxing.version}</version>
        </dependency>
        <dependency>
            <groupId>com.xiaomi.mone</groupId>
            <artifactId>dubbo-docs-core</artifactId>
            <version>${http-docs.version}</version>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
            <!-- 关键配置 -->
            <resource>
                <directory>src/main/resources/META-INF</directory>
                <targetPath>META-INF</targetPath>
            </resource>
        </resources>
    </build>
</project>