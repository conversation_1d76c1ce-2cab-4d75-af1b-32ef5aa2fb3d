package com.mi.info.intl.retail.api.fieldforce.user.enums;

/**
 * 组织通道枚举
 *
 * <AUTHOR>
 * @date 2025/09/15
 */
public enum OrgChannelEnum {
    NEW_RETAIL_DIRECT_SALES(1, "新零售直营"), NEW_RETAIL_LICENSE(2, "新零售授权"), CHANNEL_RETAIL(27, "渠道零售"),;

    private final Integer code;
    private final String name;

    OrgChannelEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 根据code获取枚举
     */
    public static OrgChannelEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (OrgChannelEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
