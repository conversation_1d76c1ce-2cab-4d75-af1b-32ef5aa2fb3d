package com.mi.info.intl.retail.api.cooperation.audit.dto;

import java.io.Serializable;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 审计日志DTO
 */
@Data
@Accessors(chain = true)
public class AuditLogDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 日志唯一主键
     */
    private Long auditLogId;

    /**
     * 日志类型：USER_OPER(用户操作)/DATA_CHANGE(数据变更)/SYSTEM_EVENT(系统事件)/LOGIN_LOG(登录日志)
     */
    private String logType;

    /**
     * 操作类型：INSERT(新增)/UPDATE(修改)/DELETE(删除)/LOGIN(登录)/LOGOUT(登出)/EXPORT(导出)
     */
    private String operType;

    /**
     * 操作人ID
     */
    private String operUserId;

    /**
     * 操作人姓名
     */
    private String operUserName;

    /**
     * 操作时间（时间戳，精确到毫秒）
     */
    private Long operTime;

    /**
     * 业务模块：USER_MANAGE(用户管理)/ORDER(订单)/PRODUCT(商品)/FINANCE(财务)
     */
    private String businessModule;

    /**
     * 业务对象唯一标识（如ORDER_123456）
     */
    private String businessKey;

    /**
     * 请求地址（如/api/v1/order/update）
     */
    private String requestUrl;

    /**
     * 操作前数据（JSON格式，仅数据变更日志）
     */
    private String beforeData;

    /**
     * 操作后数据（JSON格式，仅数据变更日志）
     */
    private String afterData;

    /**
     * 操作结果：0(失败)/1(成功)/2(部分成功)
     */
    private Integer operResult;

    /**
     * 错误信息（仅失败时填充）
     */
    private String errorMsg;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间（时间戳，精确到毫秒）
     */
    private Long createdAt;

    /**
     * 更新时间（时间戳，精确到毫秒）
     */
    private Long updatedAt;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 日志类型显示名称
     */
    private String logTypeName;

    /**
     * 操作类型显示名称
     */
    private String operTypeName;

    /**
     * 业务模块显示名称
     */
    private String businessModuleName;

    /**
     * 操作结果显示名称
     */
    private String operResultName;
}
