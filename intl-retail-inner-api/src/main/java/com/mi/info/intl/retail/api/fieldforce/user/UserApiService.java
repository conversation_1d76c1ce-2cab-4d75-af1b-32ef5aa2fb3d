package com.mi.info.intl.retail.api.fieldforce.user;

import com.mi.info.intl.retail.api.fieldforce.user.dto.IntlRmsUserNewDto;
import com.mi.info.intl.retail.api.fieldforce.user.dto.IntlRmsUserReqDto;
import com.mi.info.intl.retail.api.fieldforce.user.dto.UserInfoDTO;

import java.util.List;
import java.util.Optional;

public interface UserApiService {

    /**
     * 米id单个查询用户
     *
     * @param
     * @return
     */
    Optional<IntlRmsUserNewDto> getUserByMiId(Long miId);

    /**
     * 米id批量查询用户
     *
     * @param miIds
     * @return
     */
    Optional<List<IntlRmsUserNewDto>> getUserListByMiIds(List<Long> miIds);

    /**
     * 根据miId查询用户信息
     *
     * @param miId 用户miId
     * @return 用户信息
     */
    Optional<UserInfoDTO> queryUserByMiId(Long miId);

    /**
     * 根据用户名查询用户信息
     *
     * @param userName 用户名
     * @return 用户信息
     */
    List<UserInfoDTO> getUserByName(String userName);

    List<UserInfoDTO> getUserListByRmsIds(List<String> rmsUserIdList);
}
