package com.mi.info.intl.retail.api.front.position;

import com.mi.info.intl.retail.api.front.dto.RmsPositionIAndStoreRes;
import com.mi.info.intl.retail.api.front.dto.RmsPositionInfoRes;
import com.mi.info.intl.retail.api.front.dto.RmsStoreInfoDto;
import com.mi.info.intl.retail.api.front.position.dto.IntlPositionDTO;
import com.mi.info.intl.retail.api.front.position.dto.PositionStoreInfoDTO;
import com.mi.info.intl.retail.api.front.position.dto.StoreValidationInfoDTO;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 */
public interface IntlPositionApiService {

    /**
     * 按阵地获取零售商信息
     *
     * @param dto DTO
     * @return {@link IntlPositionDTO }
     */
    Optional<IntlPositionDTO> getRetailerByPositionCode(IntlPositionDTO dto);

    /**
     * 根据阵地编码查询阵地和门店的联合信息
     *
     * @param positionCode 阵地编码
     * @return 阵地门店信息
     */
    Optional<PositionStoreInfoDTO> queryPositionWithStoreByCode(String positionCode);

    /**
     * 查询门店下所有阵地
     *
     * @param storeCode 门店编码
     * @return 阵地列表
     */
    List<RmsPositionInfoRes> getPositionsByStoreCode(String storeCode);


    /**
     * 获取最优阵地
     * @param positions 阵地列表
     * @return 最优阵地
     */
    List<RmsPositionInfoRes> getBestPositions(List<RmsPositionInfoRes> positions);

    List<RmsPositionIAndStoreRes> getPositionsByPositionIds(List<String> positionIds);

    /**
     * 按国家代码获得阵地信息
     *
     * @param countryCode 国家代码
     * @return {@link List }<{@link IntlPositionDTO }>
     */
    List<IntlPositionDTO> getPositionsByCountryName(String countryCode);

    /**
     * 一次性查询门店信息、用户门店关系和阵地信息（用于IMEI导入校验）
     *
     * @param storeCode 门店编码
     * @param miId 用户miId
     * @return 门店校验信息，如果门店不存在或用户无权限返回null
     */
    Optional<StoreValidationInfoDTO> getStoreValidationInfo(String storeCode, Long miId);

    /**
     * 检查门店是否存在（用于区分门店不存在和用户无权限）
     *
     * @param storeCode 门店编码
     * @return 门店是否存在
     */
    RmsStoreInfoDto getStoreByCode(String storeCode);

    Map<String, RmsPositionInfoRes> getPositionInfoByPositionCodes(List<String> positionCodes);


    /**
     * 根据阵地编码获取阵地类型名称
     *
     * @param positionCode 阵地编码
     * @return 阵地类型名称
     */
    String getPositionTypeName(String positionCode);
}
