package com.mi.info.intl.retail.api.front.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class RmsStoreInfoDto implements Serializable {


    private static final long serialVersionUID = 8447111045719350849L;

    /**
     *
     */
    private Integer id;

    /**
     * 唯一标识
     */
    private String storeId;

    /**
     * 门店代码
     */
    private String name;

    /**
     * 门店名称
     */
    private Integer type;

    /**
     * 门店类型
     */
    private String typeName;

    /**
     * 零售商名称
     */
    private String retailerName;

    /**
     * 零售商代码
     */
    private String retailerId;

    /**
     * 零售商代码标签
     */
    private String retailerIdName;

    /**
     * 国家/地区
     */
    private String countryId;

    /**
     * 国家/地区标签
     */
    private String countryIdName;

    /**
     * 省份code
     */
    private String provinceCode;

    /**
     * 省份名称
     */
    private String provinceName;

    /**
     * 城市code
     */
    private String cityCode;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 渠道类型
     */
    private Integer channelType;

    /**
     * 渠道类型标签
     */
    private String channelTypeName;

    /**
     * 门店等级
     */
    private Integer grade;

    /**
     * 门店等级标签
     */
    private String gradeName;

    /**
     * 运营状态
     */
    private Integer operationStatus;

    /**
     * 运营状态标签
     */
    private String operationStatusName;

    /**
     * 是否有促
     */
    private Integer hasPc;

    private Integer hasSr;

    // 是否有做功
    private Integer storeClass;

    /**
     * 是否可用
     */
    private Integer stateCode;

    /**
     * 门店编码
     */
    private String code;

    /**
     * 县
     */
    private String countyCode;

    /**
     * 国家缩写
     */
    private String countryShortcode;

    /**
     * 网格编码
     */
    private String districtOrgCode;

    /**
     * 城市编码
     */
    private String divisionOrgCode;

    /**
     * 大区
     */
    private String areaOrgCode;

    /**
     * 国家
     */
    private String countryOrgCode;

    /**
     * 区域
     */
    private String regionOrgCode;


}
