package com.mi.info.intl.retail.api.cooperation.audit;

import java.util.List;

import com.mi.info.intl.retail.api.cooperation.audit.dto.AuditLogDTO;
import com.mi.info.intl.retail.bean.PageDTO;

/**
 * 审计日志Service接口
 *
 * <AUTHOR>
 * @date 2025/09/02
 */
public interface AuditLogApiService {

    /**
     * 分页查询审计日志
     *
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    PageDTO<AuditLogDTO> queryPage(Integer pageNum, Integer pageSize, AuditLogDTO queryDTO);

    /**
     * 插入审计日志
     *
     * @param auditLogDTO 审计日志DTO
     * @return 是否插入成功
     */
    boolean insertAuditLog(AuditLogDTO auditLogDTO);

    /**
     * 批量插入审计日志
     *
     * @param auditLogDTOList 审计日志DTO列表
     * @return 是否插入成功
     */
    boolean batchInsertAuditLog(List<AuditLogDTO> auditLogDTOList);

    /**
     * 根据业务对象标识查询审计日志
     *
     * @param queryDTO 查询条件
     * @return 审计日志列表
     */
    List<AuditLogDTO> queryByBusinessKey(AuditLogDTO queryDTO);

    /**
     * 根据操作人ID查询审计日志
     *
     * @param queryDTO 查询条件
     * @param limit 限制数量
     * @return 审计日志列表
     */
    List<AuditLogDTO> queryByOperUserId(AuditLogDTO queryDTO, Integer limit);

    /**
     * 根据时间范围查询审计日志
     *
     * @param queryDTO 查询条件
     * @return 审计日志列表
     */
    List<AuditLogDTO> queryByTimeRange(AuditLogDTO queryDTO);

    /**
     * 统计指定时间范围内的操作次数
     *
     * @param queryDTO 查询条件
     * @return 操作次数
     */
    Long countByTimeRangeAndOperType(AuditLogDTO queryDTO);

    /**
     * 根据业务模块统计操作次数
     *
     * @param queryDTO 查询条件
     * @return 操作次数
     */
    Long countByBusinessModule(AuditLogDTO queryDTO);

    /**
     * 记录用户操作日志
     *
     * @param auditLogDTO 审计日志DTO
     * @return 是否记录成功
     */
    boolean recordUserOperation(AuditLogDTO auditLogDTO);

    /**
     * 记录系统事件日志
     *
     * @param auditLogDTO 审计日志DTO
     * @return 是否记录成功
     */
    boolean recordSystemEvent(AuditLogDTO auditLogDTO);

    /**
     * 记录登录日志
     *
     * @param auditLogDTO 审计日志DTO
     * @return 是否记录成功
     */
    boolean recordLoginLog(AuditLogDTO auditLogDTO);

    /**
     * 记录数据变更日志（新增）
     *
     * @param auditLogDTO 审计日志DTO
     * @return 是否记录成功
     */
    boolean recordDataInsert(AuditLogDTO auditLogDTO);

    /**
     * 批量记录数据变更日志
     *
     * @param auditLogDTOList 审计日志DTO列表
     * @return 是否记录成功
     */
    boolean batchRecordDataChange(List<AuditLogDTO> auditLogDTOList);

}
