package com.mi.info.intl.retail.api.file;

import com.mi.info.intl.retail.api.file.dto.FileAddrRequest;
import com.mi.info.intl.retail.api.file.dto.FileAddressDTO;
import com.mi.info.intl.retail.api.file.dto.IntlFileUploadDto;
import com.mi.info.intl.retail.api.file.dto.IntlFileUploadReqDto;
import com.mi.info.intl.retail.api.file.dto.PhotoDataInfoDTO;
import com.mi.info.intl.retail.model.CommonApiResponse;

import java.util.List;

/**
 * 文件API服务接口
 *
 * <AUTHOR>
 * @date 2025/7/29
 */
public interface FileUploadApiService {

    /**
     * 批量创建图片数据
     *
     * @param photoDataList 图片数据列表
     * @return 创建成功的数量
     */
    int createPhotoData(List<PhotoDataInfoDTO> photoDataList);


    /**
     * 根据条件查询文件数据列表
     * @param reqDto
     * @return
     * 根据业务关联ID和所属模块名称查询文件数据列表
     * @param relatedId 业务关联ID
     * @param moduleName 所属模块名称
     * @return 文件数据列表
     */
    CommonApiResponse<List<IntlFileUploadDto>> getFileUploadListByParams(IntlFileUploadReqDto reqDto);
    CommonApiResponse<List<IntlFileUploadDto>> getFileUploadListByRelatedIdAndModuleName(Long relatedId, String moduleName);

    /**
     * 通过文件ID获取下载地址
     *
     * @param request
     * @return
     */
    List<FileAddressDTO> getDownLoadAddr(FileAddrRequest request);

    /**
     * 二次提交
     * @param idList 文件ID
     */
    void commit(List<Long> idList);

}
