package com.mi.info.intl.retail.api.country.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Setter
@Getter
public class CountryDTO implements Serializable {

    /**
     * 主键
     */
    private Integer id;

    /**
     * 国家真实名称
     */
    private String countryName;

    /**
     * 国家编码
     */
    private String countryCode;

    /**
     * 国家区域
     */
    private Integer stateCode;

    /**
     * 国家区域名称
     */
    private String area;

    /**
     * 国家区域编码
     */
    private String areaCode;

    /**
     * 时区编码
     */
    private String timezoneCode;

    /**
     * 创建时间
     */
    private Long createdAt;

    /**
     * 更新时间
     */
    private Long updatedAt;
}
