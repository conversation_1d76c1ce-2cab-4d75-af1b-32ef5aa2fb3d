package com.mi.info.intl.retail.api.so.rule.model;

import java.io.Serializable;

import com.xiaomi.mone.docs.annotations.dubbo.ApiDocClassDefine;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2025/07/30
 */
@Getter
@Setter
public class SoRuleRetailerModel implements Serializable {

    @ApiDocClassDefine(value = "国家编码")
    private String countryCode;

    /**
     * 国家
     */
    @ApiDocClassDefine(value = "国家")
    private String countryName;

    /**
     * 零售商编码
     */
    @ApiDocClassDefine(value = "零售商编码")
    private String retailerCode;

    /**
     * 零售商名称
     */
    @ApiDocClassDefine(value = "零售商名称")
    private String retailerName;

    /**
     * 渠道类型
     */
    @ApiDocClassDefine(value = "渠道类型")
    private String channelType;

    /**
     * 创建时间
     */
    @ApiDocClassDefine(value = "创建时间")
    private long createRetailerTime;

}
