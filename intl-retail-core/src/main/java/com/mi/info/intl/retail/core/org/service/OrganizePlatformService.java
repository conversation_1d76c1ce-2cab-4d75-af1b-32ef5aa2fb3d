package com.mi.info.intl.retail.core.org.service;

import com.mi.info.intl.retail.core.org.configuration.JobInfo;
import com.xiaomi.nr.eiam.admin.vo.provider.user.UserSensitiveInfoResp;
import com.xiaomi.nr.eiam.api.dto.userinfo.GetUserInfoResp;
import com.xiaomi.nr.eiam.api.dto.userinfo.UserPosition;

import java.util.List;
import java.util.Map;

/**
 * 组织平台服务
 *
 * <AUTHOR>
 * @date 2025/07/31
 */
public interface OrganizePlatformService {

    /**
     * 获得组织平台岗位信息
     *
     * @param jobInfo@return {@link List }<{@link GetUserInfoResp }>
     */
    List<UserPosition> getOrganizePlatform(JobInfo jobInfo);

    /**
     * 获取批处理岗位信息
     *
     * @param jobInfoList 工作信息清单
     * @return {@link Map }<{@link String }, {@link List }<{@link UserPosition }>>
     */
    Map<Integer, List<UserPosition>> getBatchOrganizePlatform(List<JobInfo> jobInfoList);

    /**
     * 获取批处理用户信息
     *
     * @param userIds 用户ID
     * @param scene 场景
     * @return {@link List }<{@link GetUserInfoResp }>
     */
    List<UserSensitiveInfoResp> getBatchUserInfo(List<Long> userIds, String scene);

    /**
     * 获取用户信息
     *
     * @param miId
     * @return {@link Object }<{@link GetUserInfoResp }>
     */
    GetUserInfoResp getUserInfo(Long miId);

    /**
     * 获取用户信息集合
     *
     * @param miIdList
     * @param scene
     * @return {@link Object }<{@link GetUserInfoResp }>
     */
    List<GetUserInfoResp> getUserInfoList(List<Long> miIdList, String scene);
}
