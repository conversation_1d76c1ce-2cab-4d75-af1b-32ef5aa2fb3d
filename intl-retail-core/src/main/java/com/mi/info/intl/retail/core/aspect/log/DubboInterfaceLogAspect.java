package com.mi.info.intl.retail.core.aspect.log;

import static com.mi.info.intl.retail.constant.DubboUserConstant.UPC_MIID;

import java.lang.reflect.Method;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.rpc.Invocation;
import org.apache.dubbo.rpc.RpcContext;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import com.mi.info.intl.retail.core.fastjson.filter.HighPerformanceSensitiveFieldFilter;
import com.mi.info.intl.retail.core.utils.LogUtil;
import com.mi.info.intl.retail.exception.BizException;
import com.mi.info.intl.retail.exception.ErrorCodes;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.xiaomi.mone.docs.annotations.dubbo.ApiDoc;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;

import lombok.extern.slf4j.Slf4j;

/**
 * Dubbo接口日志切面 用于记录Dubbo服务提供者和消费者的接口调用日志 支持敏感数据脱敏
 */
@Aspect
@Component
@Slf4j
@Order(2) // 设置优先级，确保在业务逻辑之前执行
public class DubboInterfaceLogAspect {
    private static final Logger LOGGER = LoggerFactory.getLogger("interfaceLog");
    public static final String AT = "@";

    @Resource
    private HighPerformanceSensitiveFieldFilter highPerformanceSensitiveFieldFilter;

    /**
     * Dubbo服务提供者切面 - 记录服务端调用日志 匹配类级别或方法级别的@DubboService注解
     */
    @Around("@within(org.apache.dubbo.config.annotation.DubboService) || @annotation(org.apache.dubbo.config.annotation.DubboService)")
    public Object logDubboProvider(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        Object result = null;
        int code = 0;
        String requestUrl = getRequestUrl(joinPoint);
        AccessLog accessLog = LogUtil.getAccessLog(joinPoint);
        try {
            // 执行目标方法
            result = joinPoint.proceed();
            code = result instanceof CommonApiResponse ? ((CommonApiResponse<?>) result).getCode() : ErrorCodes.SUCCESS;
            return result;
        } catch (Exception e) {
            String message = e.getMessage();
            code = e instanceof BizException ? ((BizException) e).getCode() : GeneralCodes.InternalError.getCode();
            return CommonApiResponse.failure(code, message);
        } finally {
            try {
                // 获取RPC上下文信息
                Invocation invocation = RpcContext.getContext().getInvocation();
                // 获取远程调用信息
                String version = "";
                String group = "";
                if (invocation != null) {
                    // 获取接口版本和分组信息
                    version = invocation.getAttachment("version");
                    group = invocation.getAttachment("group");
                }
                LOGGER.info("miId={},url={},method={},status={},costTime={},request={},response={}",
                    RpcContext.getContext().getAttachment(UPC_MIID), requestUrl, group + AT + version, code,
                    System.currentTimeMillis() - startTime,
                    LogUtil.getRequestStr(accessLog, joinPoint.getArgs(), highPerformanceSensitiveFieldFilter),
                    LogUtil.getResponseStr(accessLog, result, highPerformanceSensitiveFieldFilter));
            } catch (Exception e) {
                log.error("记录HTTP接口日志失败", e);
            }

        }
    }

    /**
     * 检查是否需要打印HTTP请求参数
     *
     * @param joinPoint 连接点
     * @return true表示需要打印请求参数，false表示不需要
     */
    private String getRequestUrl(ProceedingJoinPoint joinPoint) {
        try {
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();

            // 检查方法是否有@ApiDoc注解（优先级更高）
            ApiDoc apiDoc = method.getAnnotation(ApiDoc.class);
            if (apiDoc != null && StringUtils.isNotBlank(apiDoc.value())) {
                return apiDoc.value();
            }

            String methodName = joinPoint.getSignature().getName();
            String interfaceName = joinPoint.getSignature().getDeclaringTypeName();

            // 默认打印请求参数
            return interfaceName + "." + methodName;
        } catch (Exception e) {
            log.warn("检查@AccessLog注解时发生异常: {}", e.getMessage());
            return ""; // 异常情况下默认打印请求参数
        }
    }
}
