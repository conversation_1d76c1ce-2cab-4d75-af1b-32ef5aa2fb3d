package com.mi.info.intl.retail.core.config;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.Getter;

import java.util.concurrent.Executor;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

public enum CommonThreadPoolConfig {

    BATCH_QUERY_DEVICE_INFO(8, 16, 600, 256, "batch-query-imei-active-info-%d", new ThreadPoolExecutor.CallerRunsPolicy());

    /**
     * 核心线程数
     */
    @Getter
    private final int corePoolSize;

    /**
     * 最大线程数
     */
    @Getter
    private final int maxPoolSize;

    /**
     * 大于核心线程池维持时间
     */
    @Getter
    private final int keepAliveTime;

    /**
     * 任务队列大小
     */
    @Getter
    private final int queueSize;

    /**
     * 线程名称前缀
     */
    @Getter
    private final String threadNamePrefix;

    /**
     * 拒绝策略
     */
    @Getter
    private final RejectedExecutionHandler rejectedExecutionHandler;

    /**
     * 线程池
     */
    @Getter
    private final Executor executor;

    CommonThreadPoolConfig(int corePoolSize, int maxPoolSize, int keepAliveTime, int queueSize,
                           String threadNamePrefix, RejectedExecutionHandler rejectedExecutionHandler) {
        this.corePoolSize = corePoolSize;
        this.maxPoolSize = maxPoolSize;
        this.keepAliveTime = keepAliveTime;
        this.queueSize = queueSize;
        this.threadNamePrefix = threadNamePrefix;
        this.rejectedExecutionHandler = rejectedExecutionHandler;
        this.executor = buildExecutor(this);
    }

    /**
     * 构建线程池
     *
     * @param commonThreadPoolConfig
     * @return
     */
    private static Executor buildExecutor(CommonThreadPoolConfig commonThreadPoolConfig) {
        return new ThreadPoolExecutor(
                commonThreadPoolConfig.getCorePoolSize(),
                commonThreadPoolConfig.getMaxPoolSize(),
                commonThreadPoolConfig.getKeepAliveTime(),
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(commonThreadPoolConfig.getQueueSize()),
                new ThreadFactoryBuilder().setNameFormat(commonThreadPoolConfig.getThreadNamePrefix()).build(),
                commonThreadPoolConfig.getRejectedExecutionHandler()
        );
    }
}
