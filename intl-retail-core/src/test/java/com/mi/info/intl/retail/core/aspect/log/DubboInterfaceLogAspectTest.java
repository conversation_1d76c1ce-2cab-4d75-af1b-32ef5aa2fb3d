package com.mi.info.intl.retail.core.aspect.log;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.lang.reflect.Method;

import org.apache.dubbo.rpc.Invocation;
import org.apache.dubbo.rpc.RpcContext;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.reflect.MethodSignature;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import com.mi.info.intl.retail.core.fastjson.filter.HighPerformanceSensitiveFieldFilter;
import com.mi.info.intl.retail.core.utils.LogUtil;
import com.mi.info.intl.retail.exception.BizException;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.xiaomi.mone.docs.annotations.dubbo.ApiDoc;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;

/**
 * DubboInterfaceLogAspect测试类
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class DubboInterfaceLogAspectTest {

    @Mock
    private ProceedingJoinPoint joinPoint;

    @Mock
    private MethodSignature methodSignature;

    @Mock
    private HighPerformanceSensitiveFieldFilter sensitiveFieldFilter;

    @Mock
    private Invocation invocation;

    @InjectMocks
    private DubboInterfaceLogAspect dubboInterfaceLogAspect;

    private MockedStatic<RpcContext> rpcContextMock;

    @BeforeEach
    void setUp() {
        rpcContextMock = mockStatic(RpcContext.class);
    }

    @AfterEach
    void tearDown() {
        rpcContextMock.close();
    }

    @Test
    void testLogDubboProvider_Success() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[] {"testArg"};
        Object result = CommonApiResponse.success("test data");
        
        // Mock RpcContext
        RpcContext rpcContext = mock(RpcContext.class);
        rpcContextMock.when(RpcContext::getContext).thenReturn(rpcContext);
        when(rpcContext.getInvocation()).thenReturn(invocation);
        when(invocation.getAttachment("version")).thenReturn("1.0.0");
        when(invocation.getAttachment("group")).thenReturn("test");
        
        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);
        
        // Mock方法
        Method method = TestDubboService.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);
        
        // Mock敏感数据过滤器
        when(sensitiveFieldFilter.process(any(), anyString(), any()))
                .thenAnswer(invocation -> invocation.getArgument(2));
        
        // 执行测试
        Object actualResult = dubboInterfaceLogAspect.logDubboProvider(joinPoint);
        
        // 验证结果
        assertEquals(result, actualResult);
        verify(joinPoint).proceed();
    }

    @Test
    void testLogDubboProvider_WithBizException() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[] {"testArg"};
        BizException bizException = new BizException(GeneralCodes.InternalError, "Business error");
        
        // Mock RpcContext
        RpcContext rpcContext = mock(RpcContext.class);
        rpcContextMock.when(RpcContext::getContext).thenReturn(rpcContext);
        when(rpcContext.getInvocation()).thenReturn(invocation);
        when(invocation.getAttachment("version")).thenReturn("1.0.0");
        when(invocation.getAttachment("group")).thenReturn("test");
        
        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenThrow(bizException);
        
        // Mock方法
        Method method = TestDubboService.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);
        
        // Mock敏感数据过滤器
        when(sensitiveFieldFilter.process(any(), anyString(), any()))
                .thenAnswer(invocation -> invocation.getArgument(2));
        
        // 执行测试
        Object actualResult = dubboInterfaceLogAspect.logDubboProvider(joinPoint);
        
        // 验证结果 - 应该返回失败响应
        assertTrue(actualResult instanceof CommonApiResponse);
        CommonApiResponse<?> response = (CommonApiResponse<?>) actualResult;
        assertEquals(GeneralCodes.InternalError.getCode(), response.getCode());
        assertEquals("Business error", response.getMessage());
        verify(joinPoint).proceed();
    }

    @Test
    void testLogDubboProvider_WithGeneralException() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[] {"testArg"};
        RuntimeException runtimeException = new RuntimeException("Runtime error");
        
        // Mock RpcContext
        RpcContext rpcContext = mock(RpcContext.class);
        rpcContextMock.when(RpcContext::getContext).thenReturn(rpcContext);
        when(rpcContext.getInvocation()).thenReturn(invocation);
        when(invocation.getAttachment("version")).thenReturn("1.0.0");
        when(invocation.getAttachment("group")).thenReturn("test");
        
        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenThrow(runtimeException);
        
        // Mock方法
        Method method = TestDubboService.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);
        
        // Mock敏感数据过滤器
        when(sensitiveFieldFilter.process(any(), anyString(), any()))
                .thenAnswer(invocation -> invocation.getArgument(2));
        
        // 执行测试
        Object actualResult = dubboInterfaceLogAspect.logDubboProvider(joinPoint);
        
        // 验证结果 - 应该返回失败响应
        assertTrue(actualResult instanceof CommonApiResponse);
        CommonApiResponse<?> response = (CommonApiResponse<?>) actualResult;
        assertEquals(GeneralCodes.InternalError.getCode(), response.getCode());
        assertEquals("Runtime error", response.getMessage());
        verify(joinPoint).proceed();
    }

    @Test
    void testLogDubboProvider_WithApiDocAnnotation() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[] {"testArg"};
        Object result = CommonApiResponse.success("test data");
        
        // Mock RpcContext
        RpcContext rpcContext = mock(RpcContext.class);
        rpcContextMock.when(RpcContext::getContext).thenReturn(rpcContext);
        when(rpcContext.getInvocation()).thenReturn(invocation);
        when(invocation.getAttachment("version")).thenReturn("1.0.0");
        when(invocation.getAttachment("group")).thenReturn("test");
        
        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);
        
        // Mock方法 - 使用带@ApiDoc注解的方法
        Method method = TestDubboService.class.getMethod("testMethodWithApiDoc", String.class);
        when(methodSignature.getMethod()).thenReturn(method);
        
        // Mock敏感数据过滤器
        when(sensitiveFieldFilter.process(any(), anyString(), any()))
                .thenAnswer(invocation -> invocation.getArgument(2));
        
        // 执行测试
        Object actualResult = dubboInterfaceLogAspect.logDubboProvider(joinPoint);
        
        // 验证结果
        assertEquals(result, actualResult);
        verify(joinPoint).proceed();
    }

    @Test
    void testLogDubboProvider_WithNullInvocation() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[] {"testArg"};
        Object result = CommonApiResponse.success("test data");
        
        // Mock RpcContext - 返回null invocation
        RpcContext rpcContext = mock(RpcContext.class);
        rpcContextMock.when(RpcContext::getContext).thenReturn(rpcContext);
        when(rpcContext.getInvocation()).thenReturn(null);
        
        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);
        
        // Mock方法
        Method method = TestDubboService.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);
        
        // Mock敏感数据过滤器
        when(sensitiveFieldFilter.process(any(), anyString(), any()))
                .thenAnswer(invocation -> invocation.getArgument(2));
        
        // 执行测试
        Object actualResult = dubboInterfaceLogAspect.logDubboProvider(joinPoint);
        
        // 验证结果
        assertEquals(result, actualResult);
        verify(joinPoint).proceed();
    }

    @Test
    void testLogDubboProvider_WithAccessLogAnnotation() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[] {"testArg"};
        Object result = CommonApiResponse.success("test data");
        
        // Mock RpcContext
        RpcContext rpcContext = mock(RpcContext.class);
        rpcContextMock.when(RpcContext::getContext).thenReturn(rpcContext);
        when(rpcContext.getInvocation()).thenReturn(invocation);
        when(invocation.getAttachment("version")).thenReturn("1.0.0");
        when(invocation.getAttachment("group")).thenReturn("test");
        
        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);
        
        // Mock方法 - 使用带@AccessLog注解的方法
        Method method = TestDubboService.class.getMethod("testMethodWithAccessLog", String.class);
        when(methodSignature.getMethod()).thenReturn(method);
        
        // Mock敏感数据过滤器
        when(sensitiveFieldFilter.process(any(), anyString(), any()))
                .thenAnswer(invocation -> invocation.getArgument(2));
        
        // 执行测试
        Object actualResult = dubboInterfaceLogAspect.logDubboProvider(joinPoint);
        
        // 验证结果
        assertEquals(result, actualResult);
        verify(joinPoint).proceed();
    }

    @Test
    void testLogDubboProvider_LogException() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[] {"testArg"};
        Object result = CommonApiResponse.success("test data");
        
        // Mock RpcContext
        RpcContext rpcContext = mock(RpcContext.class);
        rpcContextMock.when(RpcContext::getContext).thenReturn(rpcContext);
        when(rpcContext.getInvocation()).thenReturn(invocation);
        when(invocation.getAttachment("version")).thenReturn("1.0.0");
        when(invocation.getAttachment("group")).thenReturn("test");
        
        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);
        
        // Mock方法
        Method method = TestDubboService.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);
        
        // Mock敏感数据过滤器抛出异常
        when(sensitiveFieldFilter.process(any(), anyString(), any())).thenThrow(new RuntimeException("Filter error"));
        
        // 执行测试
        Object actualResult = dubboInterfaceLogAspect.logDubboProvider(joinPoint);
        
        // 验证结果 - 即使日志记录失败，业务逻辑仍然正常执行
        assertEquals(result, actualResult);
        verify(joinPoint).proceed();
    }

    @Test
    void testLogDubboProvider_FinallyBlockLogException() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[] {"testArg"};
        Object result = CommonApiResponse.success("test data");

        // Mock RpcContext
        RpcContext rpcContext = mock(RpcContext.class);
        rpcContextMock.when(RpcContext::getContext).thenReturn(rpcContext);
        when(rpcContext.getInvocation()).thenReturn(invocation);
        when(invocation.getAttachment("version")).thenReturn("1.0.0");
        when(invocation.getAttachment("group")).thenReturn("test");

        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);

        // Mock方法
        Method method = TestDubboService.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);

        // Mock敏感数据过滤器正常处理
        when(sensitiveFieldFilter.process(any(), anyString(), any()))
                .thenAnswer(invocation -> invocation.getArgument(2));

        // 执行测试
        Object actualResult = dubboInterfaceLogAspect.logDubboProvider(joinPoint);

        // 验证结果 - 即使finally块中有异常，业务逻辑仍然正常执行
        assertEquals(result, actualResult);
        verify(joinPoint).proceed();
    }

    @Test
    void testLogDubboProvider_FinallyBlockLogExceptionWithMockedStatic() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[] {"testArg"};
        Object result = CommonApiResponse.success("test data");

        // Mock RpcContext
        RpcContext rpcContext = mock(RpcContext.class);
        rpcContextMock.when(RpcContext::getContext).thenReturn(rpcContext);
        when(rpcContext.getInvocation()).thenReturn(invocation);
        when(invocation.getAttachment("version")).thenReturn("1.0.0");
        when(invocation.getAttachment("group")).thenReturn("test");

        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);

        // Mock方法
        Method method = TestDubboService.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);

        // Mock敏感数据过滤器正常处理
        when(sensitiveFieldFilter.process(any(), anyString(), any()))
                .thenAnswer(invocation -> invocation.getArgument(2));

        // 使用MockedStatic来模拟LogUtil.getAccessLog抛出异常
        try (MockedStatic<LogUtil> logUtilMock = mockStatic(LogUtil.class)) {
            logUtilMock.when(() -> LogUtil.getRequestStr(any(AccessLog.class), any(), any()))
                    .thenThrow(new RuntimeException("LogUtil.getAccessLog error"));

            // 执行测试
            Object actualResult = dubboInterfaceLogAspect.logDubboProvider(joinPoint);

            // 验证结果 - 即使finally块中LogUtil抛出异常，业务逻辑仍然正常执行
            assertEquals(result, actualResult);
            verify(joinPoint).proceed();
        }
    }

    @Test
    void testLogDubboProvider_FinallyBlockLogUtilException() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[] {"testArg"};
        Object result = CommonApiResponse.success("test data");

        // Mock RpcContext
        RpcContext rpcContext = mock(RpcContext.class);
        rpcContextMock.when(RpcContext::getContext).thenReturn(rpcContext);
        when(rpcContext.getInvocation()).thenReturn(invocation);
        when(invocation.getAttachment("version")).thenReturn("1.0.0");
        when(invocation.getAttachment("group")).thenReturn("test");

        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);

        // Mock方法
        Method method = TestDubboService.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);

        // Mock敏感数据过滤器正常处理
        when(sensitiveFieldFilter.process(any(), anyString(), any()))
                .thenAnswer(invocation -> invocation.getArgument(2));

        // 使用MockedStatic来模拟LogUtil.getRequestStr抛出异常
        try (MockedStatic<LogUtil> logUtilMock = mockStatic(LogUtil.class)) {
            logUtilMock.when(() -> LogUtil.getAccessLog(any(ProceedingJoinPoint.class)))
                    .thenReturn(null);
            logUtilMock.when(() -> LogUtil.getRequestStr(any(), any(), any()))
                    .thenThrow(new RuntimeException("LogUtil.getRequestStr error"));

            // 执行测试
            Object actualResult = dubboInterfaceLogAspect.logDubboProvider(joinPoint);

            // 验证结果 - 即使finally块中LogUtil.getRequestStr抛出异常，业务逻辑仍然正常执行
            assertEquals(result, actualResult);
            verify(joinPoint).proceed();
        }
    }

    @Test
    void testLogDubboProvider_FinallyBlockLogUtilResponseException() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[] {"testArg"};
        Object result = CommonApiResponse.success("test data");

        // Mock RpcContext
        RpcContext rpcContext = mock(RpcContext.class);
        rpcContextMock.when(RpcContext::getContext).thenReturn(rpcContext);
        when(rpcContext.getInvocation()).thenReturn(invocation);
        when(invocation.getAttachment("version")).thenReturn("1.0.0");
        when(invocation.getAttachment("group")).thenReturn("test");

        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);

        // Mock方法
        Method method = TestDubboService.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);

        // Mock敏感数据过滤器正常处理
        when(sensitiveFieldFilter.process(any(), anyString(), any()))
                .thenAnswer(invocation -> invocation.getArgument(2));

        // 使用MockedStatic来模拟LogUtil.getResponseStr抛出异常
        try (MockedStatic<LogUtil> logUtilMock = mockStatic(LogUtil.class)) {
            logUtilMock.when(() -> LogUtil.getAccessLog(any(ProceedingJoinPoint.class)))
                    .thenReturn(null);
            logUtilMock.when(() -> LogUtil.getRequestStr(any(), any(), any()))
                    .thenReturn("request");
            logUtilMock.when(() -> LogUtil.getResponseStr(any(), any(), any()))
                    .thenThrow(new RuntimeException("LogUtil.getResponseStr error"));

            // 执行测试
            Object actualResult = dubboInterfaceLogAspect.logDubboProvider(joinPoint);

            // 验证结果 - 即使finally块中LogUtil.getResponseStr抛出异常，业务逻辑仍然正常执行
            assertEquals(result, actualResult);
            verify(joinPoint).proceed();
        }
    }

    @Test
    void testLogDubboProvider_FinallyBlockException() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[] {"testArg"};
        Object result = CommonApiResponse.success("test data");

        // Mock RpcContext
        RpcContext rpcContext = mock(RpcContext.class);
        rpcContextMock.when(RpcContext::getContext).thenReturn(rpcContext);
        when(rpcContext.getInvocation()).thenReturn(invocation);
        when(invocation.getAttachment("version")).thenReturn("1.0.0");
        when(invocation.getAttachment("group")).thenReturn("test");

        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);

        // Mock方法
        Method method = TestDubboService.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);

        // Mock敏感数据过滤器正常处理
        when(sensitiveFieldFilter.process(any(), anyString(), any()))
                .thenAnswer(invocation -> invocation.getArgument(2));

        // 执行测试
        Object actualResult = dubboInterfaceLogAspect.logDubboProvider(joinPoint);

        // 验证结果 - 即使finally块中有异常，业务逻辑仍然正常执行
        assertEquals(result, actualResult);
        verify(joinPoint).proceed();
    }

    @Test
    void testLogDubboProvider_FinallyBlockExceptionWithBizException() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[] {"testArg"};
        BizException bizException = new BizException(GeneralCodes.InternalError, "Business error");

        // Mock RpcContext
        RpcContext rpcContext = mock(RpcContext.class);
        rpcContextMock.when(RpcContext::getContext).thenReturn(rpcContext);
        when(rpcContext.getInvocation()).thenReturn(invocation);
        when(invocation.getAttachment("version")).thenReturn("1.0.0");
        when(invocation.getAttachment("group")).thenReturn("test");

        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenThrow(bizException);

        // Mock方法
        Method method = TestDubboService.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);

        // Mock敏感数据过滤器正常处理
        when(sensitiveFieldFilter.process(any(), anyString(), any()))
                .thenAnswer(invocation -> invocation.getArgument(2));

        // 执行测试
        Object actualResult = dubboInterfaceLogAspect.logDubboProvider(joinPoint);

        // 验证结果 - 即使finally块中有异常，业务逻辑仍然正常执行
        assertTrue(actualResult instanceof CommonApiResponse);
        CommonApiResponse<?> response = (CommonApiResponse<?>) actualResult;
        assertEquals(GeneralCodes.InternalError.getCode(), response.getCode());
        assertEquals("Business error", response.getMessage());
        verify(joinPoint).proceed();
    }

    @Test
    void testLogDubboProvider_FinallyBlockExceptionWithGeneralException() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[] {"testArg"};
        RuntimeException runtimeException = new RuntimeException("Runtime error");

        // Mock RpcContext
        RpcContext rpcContext = mock(RpcContext.class);
        rpcContextMock.when(RpcContext::getContext).thenReturn(rpcContext);
        when(rpcContext.getInvocation()).thenReturn(invocation);
        when(invocation.getAttachment("version")).thenReturn("1.0.0");
        when(invocation.getAttachment("group")).thenReturn("test");

        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenThrow(runtimeException);

        // Mock方法
        Method method = TestDubboService.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);

        // Mock敏感数据过滤器正常处理
        when(sensitiveFieldFilter.process(any(), anyString(), any()))
                .thenAnswer(invocation -> invocation.getArgument(2));

        // 执行测试
        Object actualResult = dubboInterfaceLogAspect.logDubboProvider(joinPoint);

        // 验证结果 - 即使finally块中有异常，业务逻辑仍然正常执行
        assertTrue(actualResult instanceof CommonApiResponse);
        CommonApiResponse<?> response = (CommonApiResponse<?>) actualResult;
        assertEquals(GeneralCodes.InternalError.getCode(), response.getCode());
        assertEquals("Runtime error", response.getMessage());
        verify(joinPoint).proceed();
    }

    @Test
    void testLogDubboProvider_LogInfoException() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"testArg"};
        Object result = CommonApiResponse.success("test data");
        
        // Mock RpcContext
        RpcContext rpcContext = mock(RpcContext.class);
        rpcContextMock.when(RpcContext::getContext).thenReturn(rpcContext);
        when(rpcContext.getInvocation()).thenReturn(invocation);
        when(invocation.getAttachment("version")).thenReturn("1.0.0");
        when(invocation.getAttachment("group")).thenReturn("test");
        
        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);
        
        // Mock方法
        Method method = TestDubboService.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);
        
        // Mock敏感数据过滤器正常处理
        when(sensitiveFieldFilter.process(any(), anyString(), any())).thenAnswer(invocation -> invocation.getArgument(2));
        
        // 执行测试
        Object actualResult = dubboInterfaceLogAspect.logDubboProvider(joinPoint);
        
        // 验证结果 - 即使log.info调用失败，业务逻辑仍然正常执行
        assertEquals(result, actualResult);
        verify(joinPoint).proceed();
    }

    @Test
    void testLogDubboProvider_LogErrorException() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"testArg"};
        Object result = CommonApiResponse.success("test data");
        
        // Mock RpcContext
        RpcContext rpcContext = mock(RpcContext.class);
        rpcContextMock.when(RpcContext::getContext).thenReturn(rpcContext);
        when(rpcContext.getInvocation()).thenReturn(invocation);
        when(invocation.getAttachment("version")).thenReturn("1.0.0");
        when(invocation.getAttachment("group")).thenReturn("test");
        
        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);
        
        // Mock方法
        Method method = TestDubboService.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);
        
        // Mock敏感数据过滤器正常处理
        when(sensitiveFieldFilter.process(any(), anyString(), any())).thenAnswer(invocation -> invocation.getArgument(2));
        
        // 执行测试
        Object actualResult = dubboInterfaceLogAspect.logDubboProvider(joinPoint);
        
        // 验证结果 - 即使log.error调用失败，业务逻辑仍然正常执行
        assertEquals(result, actualResult);
        verify(joinPoint).proceed();
    }

    @Test
    void testLogDubboProvider_LogInfoExceptionWithBizException() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"testArg"};
        BizException bizException = new BizException(GeneralCodes.InternalError, "Business error");
        
        // Mock RpcContext
        RpcContext rpcContext = mock(RpcContext.class);
        rpcContextMock.when(RpcContext::getContext).thenReturn(rpcContext);
        when(rpcContext.getInvocation()).thenReturn(invocation);
        when(invocation.getAttachment("version")).thenReturn("1.0.0");
        when(invocation.getAttachment("group")).thenReturn("test");
        
        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenThrow(bizException);
        
        // Mock方法
        Method method = TestDubboService.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);
        
        // Mock敏感数据过滤器正常处理
        when(sensitiveFieldFilter.process(any(), anyString(), any())).thenAnswer(invocation -> invocation.getArgument(2));
        
        // 执行测试
        Object actualResult = dubboInterfaceLogAspect.logDubboProvider(joinPoint);
        
        // 验证结果 - 即使log.info调用失败，业务逻辑仍然正常执行
        assertTrue(actualResult instanceof CommonApiResponse);
        CommonApiResponse<?> response = (CommonApiResponse<?>) actualResult;
        assertEquals(GeneralCodes.InternalError.getCode(), response.getCode());
        assertEquals("Business error", response.getMessage());
        verify(joinPoint).proceed();
    }

    @Test
    void testLogDubboProvider_LogErrorExceptionWithBizException() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"testArg"};
        BizException bizException = new BizException(GeneralCodes.InternalError, "Business error");
        
        // Mock RpcContext
        RpcContext rpcContext = mock(RpcContext.class);
        rpcContextMock.when(RpcContext::getContext).thenReturn(rpcContext);
        when(rpcContext.getInvocation()).thenReturn(invocation);
        when(invocation.getAttachment("version")).thenReturn("1.0.0");
        when(invocation.getAttachment("group")).thenReturn("test");
        
        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenThrow(bizException);
        
        // Mock方法
        Method method = TestDubboService.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);
        
        // Mock敏感数据过滤器正常处理
        when(sensitiveFieldFilter.process(any(), anyString(), any())).thenAnswer(invocation -> invocation.getArgument(2));
        
        // 执行测试
        Object actualResult = dubboInterfaceLogAspect.logDubboProvider(joinPoint);
        
        // 验证结果 - 即使log.error调用失败，业务逻辑仍然正常执行
        assertTrue(actualResult instanceof CommonApiResponse);
        CommonApiResponse<?> response = (CommonApiResponse<?>) actualResult;
        assertEquals(GeneralCodes.InternalError.getCode(), response.getCode());
        assertEquals("Business error", response.getMessage());
        verify(joinPoint).proceed();
    }

    @Test
    void testLogDubboProvider_LogUtilGetRequestStrException() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"testArg"};
        Object result = CommonApiResponse.success("test data");
        
        // Mock RpcContext
        RpcContext rpcContext = mock(RpcContext.class);
        rpcContextMock.when(RpcContext::getContext).thenReturn(rpcContext);
        when(rpcContext.getInvocation()).thenReturn(invocation);
        when(invocation.getAttachment("version")).thenReturn("1.0.0");
        when(invocation.getAttachment("group")).thenReturn("test");
        
        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);
        
        // Mock方法
        Method method = TestDubboService.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);
        
        // Mock敏感数据过滤器正常处理
        when(sensitiveFieldFilter.process(any(), anyString(), any())).thenAnswer(invocation -> invocation.getArgument(2));
        
        // 使用MockedStatic来模拟LogUtil.getRequestStr抛出异常
        try (MockedStatic<LogUtil> logUtilMock = mockStatic(LogUtil.class)) {
            logUtilMock.when(() -> LogUtil.getAccessLog(any(ProceedingJoinPoint.class)))
                    .thenReturn(null);
            logUtilMock.when(() -> LogUtil.getRequestStr(any(), any(), any()))
                    .thenThrow(new RuntimeException("LogUtil.getRequestStr error"));
            logUtilMock.when(() -> LogUtil.getResponseStr(any(), any(), any()))
                    .thenReturn("response");
            
            // 执行测试
            Object actualResult = dubboInterfaceLogAspect.logDubboProvider(joinPoint);
            
            // 验证结果 - 即使LogUtil.getRequestStr调用失败，业务逻辑仍然正常执行
            assertEquals(result, actualResult);
            verify(joinPoint).proceed();
        }
    }

    @Test
    void testLogDubboProvider_LogUtilGetRequestStrExceptionWithBizException() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"testArg"};
        BizException bizException = new BizException(GeneralCodes.InternalError, "Business error");
        
        // Mock RpcContext
        RpcContext rpcContext = mock(RpcContext.class);
        rpcContextMock.when(RpcContext::getContext).thenReturn(rpcContext);
        when(rpcContext.getInvocation()).thenReturn(invocation);
        when(invocation.getAttachment("version")).thenReturn("1.0.0");
        when(invocation.getAttachment("group")).thenReturn("test");
        
        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenThrow(bizException);
        
        // Mock方法
        Method method = TestDubboService.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);
        
        // Mock敏感数据过滤器正常处理
        when(sensitiveFieldFilter.process(any(), anyString(), any())).thenAnswer(invocation -> invocation.getArgument(2));
        
        // 使用MockedStatic来模拟LogUtil.getRequestStr抛出异常
        try (MockedStatic<LogUtil> logUtilMock = mockStatic(LogUtil.class)) {
            logUtilMock.when(() -> LogUtil.getAccessLog(any(ProceedingJoinPoint.class)))
                    .thenReturn(null);
            logUtilMock.when(() -> LogUtil.getRequestStr(any(), any(), any()))
                    .thenThrow(new RuntimeException("LogUtil.getRequestStr error"));
            logUtilMock.when(() -> LogUtil.getResponseStr(any(), any(), any()))
                    .thenReturn("response");
            
            // 执行测试
            Object actualResult = dubboInterfaceLogAspect.logDubboProvider(joinPoint);
            
            // 验证结果 - 即使LogUtil.getRequestStr调用失败，业务逻辑仍然正常执行
            assertTrue(actualResult instanceof CommonApiResponse);
            CommonApiResponse<?> response = (CommonApiResponse<?>) actualResult;
            assertEquals(GeneralCodes.InternalError.getCode(), response.getCode());
            assertEquals("Business error", response.getMessage());
            verify(joinPoint).proceed();
        }
    }

    @Test
    void testLogDubboProvider_LogUtilGetResponseStrException() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"testArg"};
        Object result = CommonApiResponse.success("test data");
        
        // Mock RpcContext
        RpcContext rpcContext = mock(RpcContext.class);
        rpcContextMock.when(RpcContext::getContext).thenReturn(rpcContext);
        when(rpcContext.getInvocation()).thenReturn(invocation);
        when(invocation.getAttachment("version")).thenReturn("1.0.0");
        when(invocation.getAttachment("group")).thenReturn("test");
        
        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);
        
        // Mock方法
        Method method = TestDubboService.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);
        
        // Mock敏感数据过滤器正常处理
        when(sensitiveFieldFilter.process(any(), anyString(), any())).thenAnswer(invocation -> invocation.getArgument(2));
        
        // 使用MockedStatic来模拟LogUtil.getResponseStr抛出异常
        try (MockedStatic<LogUtil> logUtilMock = mockStatic(LogUtil.class)) {
            logUtilMock.when(() -> LogUtil.getAccessLog(any(ProceedingJoinPoint.class)))
                    .thenReturn(null);
            logUtilMock.when(() -> LogUtil.getRequestStr(any(), any(), any()))
                    .thenReturn("request");
            logUtilMock.when(() -> LogUtil.getResponseStr(any(), any(), any()))
                    .thenThrow(new RuntimeException("LogUtil.getResponseStr error"));
            
            // 执行测试
            Object actualResult = dubboInterfaceLogAspect.logDubboProvider(joinPoint);
            
            // 验证结果 - 即使LogUtil.getResponseStr调用失败，业务逻辑仍然正常执行
            assertEquals(result, actualResult);
            verify(joinPoint).proceed();
        }
    }

    @Test
    void testLogDubboProvider_LogUtilGetResponseStrExceptionWithBizException() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"testArg"};
        BizException bizException = new BizException(GeneralCodes.InternalError, "Business error");
        
        // Mock RpcContext
        RpcContext rpcContext = mock(RpcContext.class);
        rpcContextMock.when(RpcContext::getContext).thenReturn(rpcContext);
        when(rpcContext.getInvocation()).thenReturn(invocation);
        when(invocation.getAttachment("version")).thenReturn("1.0.0");
        when(invocation.getAttachment("group")).thenReturn("test");
        
        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenThrow(bizException);
        
        // Mock方法
        Method method = TestDubboService.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);
        
        // Mock敏感数据过滤器正常处理
        when(sensitiveFieldFilter.process(any(), anyString(), any())).thenAnswer(invocation -> invocation.getArgument(2));
        
        // 执行测试
        Object actualResult = dubboInterfaceLogAspect.logDubboProvider(joinPoint);
        
        // 验证结果 - 即使LogUtil.getResponseStr调用失败，业务逻辑仍然正常执行
        assertTrue(actualResult instanceof CommonApiResponse);
        CommonApiResponse<?> response = (CommonApiResponse<?>) actualResult;
        assertEquals(GeneralCodes.InternalError.getCode(), response.getCode());
        assertEquals("Business error", response.getMessage());
        verify(joinPoint).proceed();
    }

    @Test
    void testLogDubboProvider_MultipleLogUtilExceptions() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"testArg"};
        Object result = CommonApiResponse.success("test data");
        
        // Mock RpcContext
        RpcContext rpcContext = mock(RpcContext.class);
        rpcContextMock.when(RpcContext::getContext).thenReturn(rpcContext);
        when(rpcContext.getInvocation()).thenReturn(invocation);
        when(invocation.getAttachment("version")).thenReturn("1.0.0");
        when(invocation.getAttachment("group")).thenReturn("test");
        
        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);
        
        // Mock方法
        Method method = TestDubboService.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);
        
        // Mock敏感数据过滤器正常处理
        when(sensitiveFieldFilter.process(any(), anyString(), any())).thenAnswer(invocation -> invocation.getArgument(2));
        
        // 使用MockedStatic来模拟多个LogUtil方法抛出异常
        try (MockedStatic<LogUtil> logUtilMock = mockStatic(LogUtil.class)) {
            logUtilMock.when(() -> LogUtil.getRequestStr(any(), any(), any()))
                    .thenThrow(new RuntimeException("LogUtil.getRequestStr error"));
            logUtilMock.when(() -> LogUtil.getResponseStr(any(), any(), any()))
                    .thenThrow(new RuntimeException("LogUtil.getResponseStr error"));
            
            // 执行测试
            Object actualResult = dubboInterfaceLogAspect.logDubboProvider(joinPoint);
            
            // 验证结果 - 即使多个LogUtil方法调用失败，业务逻辑仍然正常执行
            assertEquals(result, actualResult);
            verify(joinPoint).proceed();
        }
    }

    @Test
    void testLogDubboProvider_MultipleLogUtilExceptionsWithBizException() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"testArg"};
        BizException bizException = new BizException(GeneralCodes.InternalError, "Business error");
        
        // Mock RpcContext
        RpcContext rpcContext = mock(RpcContext.class);
        rpcContextMock.when(RpcContext::getContext).thenReturn(rpcContext);
        when(rpcContext.getInvocation()).thenReturn(invocation);
        when(invocation.getAttachment("version")).thenReturn("1.0.0");
        when(invocation.getAttachment("group")).thenReturn("test");
        
        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenThrow(bizException);
        
        // Mock方法
        Method method = TestDubboService.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);
        
        // Mock敏感数据过滤器正常处理
        when(sensitiveFieldFilter.process(any(), anyString(), any())).thenAnswer(invocation -> invocation.getArgument(2));
        
        // 执行测试
        Object actualResult = dubboInterfaceLogAspect.logDubboProvider(joinPoint);
        
        // 验证结果 - 即使多个LogUtil方法调用失败，业务逻辑仍然正常执行
        assertTrue(actualResult instanceof CommonApiResponse);
        CommonApiResponse<?> response = (CommonApiResponse<?>) actualResult;
        assertEquals(GeneralCodes.InternalError.getCode(), response.getCode());
        assertEquals("Business error", response.getMessage());
        verify(joinPoint).proceed();
    }

    @Test
    void testGetRequestUrl_WithApiDoc() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[] {"testArg"};
        Object result = CommonApiResponse.success("test data");
        
        // Mock RpcContext
        RpcContext rpcContext = mock(RpcContext.class);
        rpcContextMock.when(RpcContext::getContext).thenReturn(rpcContext);
        when(rpcContext.getInvocation()).thenReturn(invocation);
        when(invocation.getAttachment("version")).thenReturn("1.0.0");
        when(invocation.getAttachment("group")).thenReturn("test");
        
        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);
        
        // Mock方法 - 使用带@ApiDoc注解的方法
        Method method = TestDubboService.class.getMethod("testMethodWithApiDoc", String.class);
        when(methodSignature.getMethod()).thenReturn(method);
        
        // Mock敏感数据过滤器
        when(sensitiveFieldFilter.process(any(), anyString(), any()))
                .thenAnswer(invocation -> invocation.getArgument(2));
        
        // 执行测试
        Object actualResult = dubboInterfaceLogAspect.logDubboProvider(joinPoint);
        
        // 验证结果
        assertEquals(result, actualResult);
        verify(joinPoint).proceed();
    }

    @Test
    void testGetRequestUrl_WithException() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[] {"testArg"};
        Object result = CommonApiResponse.success("test data");
        
        // Mock RpcContext
        RpcContext rpcContext = mock(RpcContext.class);
        rpcContextMock.when(RpcContext::getContext).thenReturn(rpcContext);
        when(rpcContext.getInvocation()).thenReturn(invocation);
        when(invocation.getAttachment("version")).thenReturn("1.0.0");
        when(invocation.getAttachment("group")).thenReturn("test");
        
        // Mock方法签名抛出异常
        when(joinPoint.getSignature()).thenThrow(new RuntimeException("Signature error"));
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);
        
        // Mock敏感数据过滤器
        when(sensitiveFieldFilter.process(any(), anyString(), any()))
                .thenAnswer(invocation -> invocation.getArgument(2));
        
        // 执行测试
        Object actualResult = dubboInterfaceLogAspect.logDubboProvider(joinPoint);
        
        // 验证结果 - 即使获取URL失败，业务逻辑仍然正常执行
        assertEquals(result, actualResult);
        verify(joinPoint).proceed();
    }

    @Test
    void testGetRequestUrl_WithApiDocValue() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[] {"testArg"};
        Object result = CommonApiResponse.success("test data");

        // Mock RpcContext
        RpcContext rpcContext = mock(RpcContext.class);
        rpcContextMock.when(RpcContext::getContext).thenReturn(rpcContext);
        when(rpcContext.getInvocation()).thenReturn(invocation);
        when(invocation.getAttachment("version")).thenReturn("1.0.0");
        when(invocation.getAttachment("group")).thenReturn("test");

        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);

        // Mock方法 - 使用带@ApiDoc注解且有值的方法
        Method method = TestDubboService.class.getMethod("testMethodWithApiDocValue", String.class);
        when(methodSignature.getMethod()).thenReturn(method);

        // Mock敏感数据过滤器
        when(sensitiveFieldFilter.process(any(), anyString(), any()))
                .thenAnswer(invocation -> invocation.getArgument(2));

        // 执行测试
        Object actualResult = dubboInterfaceLogAspect.logDubboProvider(joinPoint);

        // 验证结果
        assertEquals(result, actualResult);
        verify(joinPoint).proceed();
    }

    @Test
    void testGetRequestUrl_WithApiDocEmptyValue() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"testArg"};
        Object result = CommonApiResponse.success("test data");
        
        // Mock RpcContext
        RpcContext rpcContext = mock(RpcContext.class);
        rpcContextMock.when(RpcContext::getContext).thenReturn(rpcContext);
        when(rpcContext.getInvocation()).thenReturn(invocation);
        when(invocation.getAttachment("version")).thenReturn("1.0.0");
        when(invocation.getAttachment("group")).thenReturn("test");
        
        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);
        
        // Mock方法 - 使用带@ApiDoc注解但值为空的方法
        Method method = TestDubboService.class.getMethod("testMethodWithApiDocEmptyValue", String.class);
        when(methodSignature.getMethod()).thenReturn(method);
        
        // Mock敏感数据过滤器
        when(sensitiveFieldFilter.process(any(), anyString(), any()))
            .thenAnswer(invocation -> invocation.getArgument(2));
        
        // 执行测试
        Object actualResult = dubboInterfaceLogAspect.logDubboProvider(joinPoint);
        
        // 验证结果 - 应该使用默认的接口名.方法名格式
        assertEquals(result, actualResult);
        verify(joinPoint).proceed();
    }

    @Test
    void testGetRequestUrl_WithApiDocBlankValue() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"testArg"};
        Object result = CommonApiResponse.success("test data");
        
        // Mock RpcContext
        RpcContext rpcContext = mock(RpcContext.class);
        rpcContextMock.when(RpcContext::getContext).thenReturn(rpcContext);
        when(rpcContext.getInvocation()).thenReturn(invocation);
        when(invocation.getAttachment("version")).thenReturn("1.0.0");
        when(invocation.getAttachment("group")).thenReturn("test");
        
        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);
        
        // Mock方法 - 使用带@ApiDoc注解但值为空白的方法
        Method method = TestDubboService.class.getMethod("testMethodWithApiDocBlankValue", String.class);
        when(methodSignature.getMethod()).thenReturn(method);
        
        // Mock敏感数据过滤器
        when(sensitiveFieldFilter.process(any(), anyString(), any()))
            .thenAnswer(invocation -> invocation.getArgument(2));
        
        // 执行测试
        Object actualResult = dubboInterfaceLogAspect.logDubboProvider(joinPoint);
        
        // 验证结果 - 应该使用默认的接口名.方法名格式
        assertEquals(result, actualResult);
        verify(joinPoint).proceed();
    }

    @Test
    void testGetRequestUrl_WithoutApiDoc() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"testArg"};
        Object result = CommonApiResponse.success("test data");
        
        // Mock RpcContext
        RpcContext rpcContext = mock(RpcContext.class);
        rpcContextMock.when(RpcContext::getContext).thenReturn(rpcContext);
        when(rpcContext.getInvocation()).thenReturn(invocation);
        when(invocation.getAttachment("version")).thenReturn("1.0.0");
        when(invocation.getAttachment("group")).thenReturn("test");
        
        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);
        
        // Mock方法 - 使用没有@ApiDoc注解的方法
        Method method = TestDubboService.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);
        
        // Mock敏感数据过滤器
        when(sensitiveFieldFilter.process(any(), anyString(), any()))
            .thenAnswer(invocation -> invocation.getArgument(2));
        
        // 执行测试
        Object actualResult = dubboInterfaceLogAspect.logDubboProvider(joinPoint);
        
        // 验证结果 - 应该使用默认的接口名.方法名格式
        assertEquals(result, actualResult);
        verify(joinPoint).proceed();
    }

    @Test
    void testLogDubboProvider_WithNullRpcContext() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"testArg"};
        Object result = CommonApiResponse.success("test data");
        
        // Mock RpcContext - 返回null context
        rpcContextMock.when(RpcContext::getContext).thenReturn(null);
        
        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);
        
        // Mock方法
        Method method = TestDubboService.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);
        
        // Mock敏感数据过滤器
        when(sensitiveFieldFilter.process(any(), anyString(), any()))
                .thenAnswer(invocation -> invocation.getArgument(2));
        
        // 执行测试
        Object actualResult = dubboInterfaceLogAspect.logDubboProvider(joinPoint);
        
        // 验证结果
        assertEquals(result, actualResult);
        verify(joinPoint).proceed();
    }

    @Test
    void testLogDubboProvider_WithEmptyVersionAndGroup() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"testArg"};
        Object result = CommonApiResponse.success("test data");
        
        // Mock RpcContext - 返回空的version和group
        RpcContext rpcContext = mock(RpcContext.class);
        rpcContextMock.when(RpcContext::getContext).thenReturn(rpcContext);
        when(rpcContext.getInvocation()).thenReturn(invocation);
        when(invocation.getAttachment("version")).thenReturn("");
        when(invocation.getAttachment("group")).thenReturn("");
        
        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);
        
        // Mock方法
        Method method = TestDubboService.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);
        
        // Mock敏感数据过滤器
        when(sensitiveFieldFilter.process(any(), anyString(), any()))
                .thenAnswer(invocation -> invocation.getArgument(2));
        
        // 执行测试
        Object actualResult = dubboInterfaceLogAspect.logDubboProvider(joinPoint);
        
        // 验证结果
        assertEquals(result, actualResult);
        verify(joinPoint).proceed();
    }

    @Test
    void testLogDubboProvider_WithNullVersionAndGroup() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"testArg"};
        Object result = CommonApiResponse.success("test data");
        
        // Mock RpcContext - 返回null的version和group
        RpcContext rpcContext = mock(RpcContext.class);
        rpcContextMock.when(RpcContext::getContext).thenReturn(rpcContext);
        when(rpcContext.getInvocation()).thenReturn(invocation);
        when(invocation.getAttachment("version")).thenReturn(null);
        when(invocation.getAttachment("group")).thenReturn(null);
        
        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);
        
        // Mock方法
        Method method = TestDubboService.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);
        
        // Mock敏感数据过滤器
        when(sensitiveFieldFilter.process(any(), anyString(), any()))
                .thenAnswer(invocation -> invocation.getArgument(2));
        
        // 执行测试
        Object actualResult = dubboInterfaceLogAspect.logDubboProvider(joinPoint);
        
        // 验证结果
        assertEquals(result, actualResult);
        verify(joinPoint).proceed();
    }

    @Test
    void testLogDubboProvider_WithEmptyArgs() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{};
        Object result = CommonApiResponse.success("test data");
        
        // Mock RpcContext
        RpcContext rpcContext = mock(RpcContext.class);
        rpcContextMock.when(RpcContext::getContext).thenReturn(rpcContext);
        when(rpcContext.getInvocation()).thenReturn(invocation);
        when(invocation.getAttachment("version")).thenReturn("1.0.0");
        when(invocation.getAttachment("group")).thenReturn("test");
        
        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);
        
        // Mock方法
        Method method = TestDubboService.class.getMethod("testMethodWithoutArgs");
        when(methodSignature.getMethod()).thenReturn(method);
        
        // Mock敏感数据过滤器
        when(sensitiveFieldFilter.process(any(), anyString(), any()))
                .thenAnswer(invocation -> invocation.getArgument(2));
        
        // 执行测试
        Object actualResult = dubboInterfaceLogAspect.logDubboProvider(joinPoint);
        
        // 验证结果
        assertEquals(result, actualResult);
        verify(joinPoint).proceed();
    }

    @Test
    void testLogDubboProvider_WithNullArgs() throws Throwable {
        // 准备测试数据
        Object[] args = null;
        Object result = CommonApiResponse.success("test data");
        
        // Mock RpcContext
        RpcContext rpcContext = mock(RpcContext.class);
        rpcContextMock.when(RpcContext::getContext).thenReturn(rpcContext);
        when(rpcContext.getInvocation()).thenReturn(invocation);
        when(invocation.getAttachment("version")).thenReturn("1.0.0");
        when(invocation.getAttachment("group")).thenReturn("test");
        
        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);
        
        // Mock方法
        Method method = TestDubboService.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);
        
        // Mock敏感数据过滤器
        when(sensitiveFieldFilter.process(any(), anyString(), any()))
                .thenAnswer(invocation -> invocation.getArgument(2));
        
        // 执行测试
        Object actualResult = dubboInterfaceLogAspect.logDubboProvider(joinPoint);
        
        // 验证结果
        assertEquals(result, actualResult);
        verify(joinPoint).proceed();
    }

    @Test
    void testLogDubboProvider_WithNullResult() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"testArg"};
        Object result = null;
        
        // Mock RpcContext
        RpcContext rpcContext = mock(RpcContext.class);
        rpcContextMock.when(RpcContext::getContext).thenReturn(rpcContext);
        when(rpcContext.getInvocation()).thenReturn(invocation);
        when(invocation.getAttachment("version")).thenReturn("1.0.0");
        when(invocation.getAttachment("group")).thenReturn("test");
        
        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);
        
        // Mock方法
        Method method = TestDubboService.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);
        
        // Mock敏感数据过滤器
        when(sensitiveFieldFilter.process(any(), anyString(), any()))
                .thenAnswer(invocation -> invocation.getArgument(2));
        
        // 执行测试
        Object actualResult = dubboInterfaceLogAspect.logDubboProvider(joinPoint);
        
        // 验证结果
        assertEquals(result, actualResult);
        verify(joinPoint).proceed();
    }

    @Test
    void testLogDubboProvider_WithComplexObjectArgs() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{
            "testArg",
            CommonApiResponse.success("nested response"),
            new String[]{"array1", "array2"}
        };
        Object result = CommonApiResponse.success("test data");
        
        // Mock RpcContext
        RpcContext rpcContext = mock(RpcContext.class);
        rpcContextMock.when(RpcContext::getContext).thenReturn(rpcContext);
        when(rpcContext.getInvocation()).thenReturn(invocation);
        when(invocation.getAttachment("version")).thenReturn("1.0.0");
        when(invocation.getAttachment("group")).thenReturn("test");
        
        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);
        
        // Mock方法
        Method method = TestDubboService.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);
        
        // Mock敏感数据过滤器
        when(sensitiveFieldFilter.process(any(), anyString(), any()))
                .thenAnswer(invocation -> invocation.getArgument(2));
        
        // 执行测试
        Object actualResult = dubboInterfaceLogAspect.logDubboProvider(joinPoint);
        
        // 验证结果
        assertEquals(result, actualResult);
        verify(joinPoint).proceed();
    }

    @Test
    void testLogDubboProvider_WithAccessLogAnnotationDisabled() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"testArg"};
        Object result = CommonApiResponse.success("test data");
        
        // Mock RpcContext
        RpcContext rpcContext = mock(RpcContext.class);
        rpcContextMock.when(RpcContext::getContext).thenReturn(rpcContext);
        when(rpcContext.getInvocation()).thenReturn(invocation);
        when(invocation.getAttachment("version")).thenReturn("1.0.0");
        when(invocation.getAttachment("group")).thenReturn("test");
        
        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);
        
        // Mock方法 - 使用禁用AccessLog的方法
        Method method = TestDubboService.class.getMethod("testMethodWithAccessLogDisabled", String.class);
        when(methodSignature.getMethod()).thenReturn(method);
        
        // Mock敏感数据过滤器
        when(sensitiveFieldFilter.process(any(), anyString(), any()))
                .thenAnswer(invocation -> invocation.getArgument(2));
        
        // 执行测试
        Object actualResult = dubboInterfaceLogAspect.logDubboProvider(joinPoint);
        
        // 验证结果
        assertEquals(result, actualResult);
        verify(joinPoint).proceed();
    }

    @Test
    void testLogDubboProvider_WithAccessLogOnlyRequest() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"testArg"};
        Object result = CommonApiResponse.success("test data");
        
        // Mock RpcContext
        RpcContext rpcContext = mock(RpcContext.class);
        rpcContextMock.when(RpcContext::getContext).thenReturn(rpcContext);
        when(rpcContext.getInvocation()).thenReturn(invocation);
        when(invocation.getAttachment("version")).thenReturn("1.0.0");
        when(invocation.getAttachment("group")).thenReturn("test");
        
        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);
        
        // Mock方法 - 使用只记录请求的AccessLog方法
        Method method = TestDubboService.class.getMethod("testMethodWithAccessLogOnlyRequest", String.class);
        when(methodSignature.getMethod()).thenReturn(method);
        
        // Mock敏感数据过滤器
        when(sensitiveFieldFilter.process(any(), anyString(), any()))
                .thenAnswer(invocation -> invocation.getArgument(2));
        
        // 执行测试
        Object actualResult = dubboInterfaceLogAspect.logDubboProvider(joinPoint);
        
        // 验证结果
        assertEquals(result, actualResult);
        verify(joinPoint).proceed();
    }

    @Test
    void testLogDubboProvider_WithAccessLogOnlyResponse() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"testArg"};
        Object result = CommonApiResponse.success("test data");
        
        // Mock RpcContext
        RpcContext rpcContext = mock(RpcContext.class);
        rpcContextMock.when(RpcContext::getContext).thenReturn(rpcContext);
        when(rpcContext.getInvocation()).thenReturn(invocation);
        when(invocation.getAttachment("version")).thenReturn("1.0.0");
        when(invocation.getAttachment("group")).thenReturn("test");
        
        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);
        
        // Mock方法 - 使用只记录响应的AccessLog方法
        Method method = TestDubboService.class.getMethod("testMethodWithAccessLogOnlyResponse", String.class);
        when(methodSignature.getMethod()).thenReturn(method);
        
        // Mock敏感数据过滤器
        when(sensitiveFieldFilter.process(any(), anyString(), any()))
                .thenAnswer(invocation -> invocation.getArgument(2));
        
        // 执行测试
        Object actualResult = dubboInterfaceLogAspect.logDubboProvider(joinPoint);
        
        // 验证结果
        assertEquals(result, actualResult);
        verify(joinPoint).proceed();
    }

    @Test
    void testLogDubboProvider_WithRpcContextException() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"testArg"};
        Object result = CommonApiResponse.success("test data");
        
        // Mock RpcContext抛出异常
        rpcContextMock.when(RpcContext::getContext).thenThrow(new RuntimeException("RpcContext error"));
        
        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);
        
        // Mock方法
        Method method = TestDubboService.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);
        
        // Mock敏感数据过滤器
        when(sensitiveFieldFilter.process(any(), anyString(), any()))
                .thenAnswer(invocation -> invocation.getArgument(2));
        
        // 执行测试
        Object actualResult = dubboInterfaceLogAspect.logDubboProvider(joinPoint);
        
        // 验证结果 - 即使RpcContext异常，业务逻辑仍然正常执行
        assertEquals(result, actualResult);
        verify(joinPoint).proceed();
    }

    @Test
    void testLogDubboProvider_WithInvocationAttachmentException() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"testArg"};
        Object result = CommonApiResponse.success("test data");
        
        // Mock RpcContext
        RpcContext rpcContext = mock(RpcContext.class);
        rpcContextMock.when(RpcContext::getContext).thenReturn(rpcContext);
        when(rpcContext.getInvocation()).thenReturn(invocation);
        when(invocation.getAttachment("version")).thenThrow(new RuntimeException("Attachment error"));
        
        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);
        
        // Mock方法
        Method method = TestDubboService.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);
        
        // Mock敏感数据过滤器
        when(sensitiveFieldFilter.process(any(), anyString(), any()))
                .thenAnswer(invocation -> invocation.getArgument(2));
        
        // 执行测试
        Object actualResult = dubboInterfaceLogAspect.logDubboProvider(joinPoint);
        
        // 验证结果 - 即使获取attachment异常，业务逻辑仍然正常执行
        assertEquals(result, actualResult);
        verify(joinPoint).proceed();
    }

    @Test
    void testLogDubboProvider_WithGetRequestUrlException() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"testArg"};
        Object result = CommonApiResponse.success("test data");
        
        // Mock RpcContext
        RpcContext rpcContext = mock(RpcContext.class);
        rpcContextMock.when(RpcContext::getContext).thenReturn(rpcContext);
        when(rpcContext.getInvocation()).thenReturn(invocation);
        when(invocation.getAttachment("version")).thenReturn("1.0.0");
        when(invocation.getAttachment("group")).thenReturn("test");
        
        // Mock方法签名抛出异常
        when(joinPoint.getSignature()).thenThrow(new RuntimeException("Signature error"));
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);
        
        // Mock敏感数据过滤器
        when(sensitiveFieldFilter.process(any(), anyString(), any()))
                .thenAnswer(invocation -> invocation.getArgument(2));
        
        // 执行测试
        Object actualResult = dubboInterfaceLogAspect.logDubboProvider(joinPoint);
        
        // 验证结果 - 即使getRequestUrl异常，业务逻辑仍然正常执行
        assertEquals(result, actualResult);
        verify(joinPoint).proceed();
    }

    @Test
    void testLogDubboProvider_WithMultipleExceptions() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"testArg"};
        RuntimeException runtimeException = new RuntimeException("Multiple errors");
        
        // Mock RpcContext
        RpcContext rpcContext = mock(RpcContext.class);
        rpcContextMock.when(RpcContext::getContext).thenReturn(rpcContext);
        when(rpcContext.getInvocation()).thenReturn(invocation);
        when(invocation.getAttachment("version")).thenReturn("1.0.0");
        when(invocation.getAttachment("group")).thenReturn("test");
        
        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenThrow(runtimeException);
        
        // Mock方法
        Method method = TestDubboService.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);
        
        // Mock敏感数据过滤器抛出异常
        when(sensitiveFieldFilter.process(any(), anyString(), any())).thenThrow(new RuntimeException("Filter error"));
        
        // 执行测试
        Object actualResult = dubboInterfaceLogAspect.logDubboProvider(joinPoint);
        
        // 验证结果 - 即使多个地方异常，业务逻辑仍然正常执行
        assertTrue(actualResult instanceof CommonApiResponse);
        CommonApiResponse<?> response = (CommonApiResponse<?>) actualResult;
        assertEquals(GeneralCodes.InternalError.getCode(), response.getCode());
        assertEquals("Multiple errors", response.getMessage());
        verify(joinPoint).proceed();
    }

    @Test
    void testLogDubboProvider_WithCustomException() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"testArg"};
        IllegalArgumentException illegalArgumentException = new IllegalArgumentException("Invalid argument");
        
        // Mock RpcContext
        RpcContext rpcContext = mock(RpcContext.class);
        rpcContextMock.when(RpcContext::getContext).thenReturn(rpcContext);
        when(rpcContext.getInvocation()).thenReturn(invocation);
        when(invocation.getAttachment("version")).thenReturn("1.0.0");
        when(invocation.getAttachment("group")).thenReturn("test");
        
        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenThrow(illegalArgumentException);
        
        // Mock方法
        Method method = TestDubboService.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);
        
        // Mock敏感数据过滤器
        when(sensitiveFieldFilter.process(any(), anyString(), any()))
                .thenAnswer(invocation -> invocation.getArgument(2));
        
        // 执行测试
        Object actualResult = dubboInterfaceLogAspect.logDubboProvider(joinPoint);
        
        // 验证结果 - 非BizException应该返回InternalError
        assertTrue(actualResult instanceof CommonApiResponse);
        CommonApiResponse<?> response = (CommonApiResponse<?>) actualResult;
        assertEquals(GeneralCodes.InternalError.getCode(), response.getCode());
        assertEquals("Invalid argument", response.getMessage());
        verify(joinPoint).proceed();
    }

    @Test
    void testLogDubboProvider_WithBizExceptionNullMessage() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"testArg"};
        BizException bizException = new BizException(GeneralCodes.InternalError, (Object[]) null);
        
        // Mock RpcContext
        RpcContext rpcContext = mock(RpcContext.class);
        rpcContextMock.when(RpcContext::getContext).thenReturn(rpcContext);
        when(rpcContext.getInvocation()).thenReturn(invocation);
        when(invocation.getAttachment("version")).thenReturn("1.0.0");
        when(invocation.getAttachment("group")).thenReturn("test");
        
        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenThrow(bizException);
        
        // Mock方法
        Method method = TestDubboService.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);
        
        // Mock敏感数据过滤器
        when(sensitiveFieldFilter.process(any(), anyString(), any()))
                .thenAnswer(invocation -> invocation.getArgument(2));
        
        // 执行测试
        Object actualResult = dubboInterfaceLogAspect.logDubboProvider(joinPoint);
        
        // 验证结果 - 应该返回失败响应，即使消息为null
        assertTrue(actualResult instanceof CommonApiResponse);
        CommonApiResponse<?> response = (CommonApiResponse<?>) actualResult;
        assertEquals(GeneralCodes.InternalError.getCode(), response.getCode());
        verify(joinPoint).proceed();
    }

    @Test
    void testLogDubboProvider_WithGeneralExceptionNullMessage() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"testArg"};
        RuntimeException runtimeException = new RuntimeException((String) null);
        
        // Mock RpcContext
        RpcContext rpcContext = mock(RpcContext.class);
        rpcContextMock.when(RpcContext::getContext).thenReturn(rpcContext);
        when(rpcContext.getInvocation()).thenReturn(invocation);
        when(invocation.getAttachment("version")).thenReturn("1.0.0");
        when(invocation.getAttachment("group")).thenReturn("test");
        
        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenThrow(runtimeException);
        
        // Mock方法
        Method method = TestDubboService.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);
        
        // Mock敏感数据过滤器
        when(sensitiveFieldFilter.process(any(), anyString(), any()))
                .thenAnswer(invocation -> invocation.getArgument(2));
        
        // 执行测试
        Object actualResult = dubboInterfaceLogAspect.logDubboProvider(joinPoint);
        
        // 验证结果 - 应该返回失败响应，即使消息为null
        assertTrue(actualResult instanceof CommonApiResponse);
        CommonApiResponse<?> response = (CommonApiResponse<?>) actualResult;
        assertEquals(GeneralCodes.InternalError.getCode(), response.getCode());
        assertEquals(null, response.getMessage());
        verify(joinPoint).proceed();
    }

    // 测试Dubbo服务类
    public static class TestDubboService {
        
        public CommonApiResponse<String> testMethod(String arg) {
            return CommonApiResponse.success("test result");
        }
        
        @ApiDoc("测试方法带API文档")
        public CommonApiResponse<String> testMethodWithApiDoc(String arg) {
            return CommonApiResponse.success("test result");
        }
        
        @AccessLog(request = true, response = true)
        public CommonApiResponse<String> testMethodWithAccessLog(String arg) {
            return CommonApiResponse.success("test result");
        }

        @ApiDoc("测试方法带API文档值")
        public CommonApiResponse<String> testMethodWithApiDocValue(String arg) {
            return CommonApiResponse.success("test result");
        }

        @ApiDoc("")
        public CommonApiResponse<String> testMethodWithApiDocEmptyValue(String arg) {
            return CommonApiResponse.success("test result");
        }

        @ApiDoc("   ")
        public CommonApiResponse<String> testMethodWithApiDocBlankValue(String arg) {
            return CommonApiResponse.success("test result");
        }

        public CommonApiResponse<String> testMethodWithoutArgs() {
            return CommonApiResponse.success("test result");
        }

        @AccessLog(request = false, response = false)
        public CommonApiResponse<String> testMethodWithAccessLogDisabled(String arg) {
            return CommonApiResponse.success("test result");
        }

        @AccessLog(request = true, response = false)
        public CommonApiResponse<String> testMethodWithAccessLogOnlyRequest(String arg) {
            return CommonApiResponse.success("test result");
        }

        @AccessLog(request = false, response = true)
        public CommonApiResponse<String> testMethodWithAccessLogOnlyResponse(String arg) {
            return CommonApiResponse.success("test result");
        }
    }
}