package com.mi.info.intl.retail.core.aspect.log;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.lang.reflect.Method;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.reflect.MethodSignature;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.mi.info.intl.retail.constant.CommonConstant;
import com.mi.info.intl.retail.core.fastjson.filter.HighPerformanceSensitiveFieldFilter;
import com.mi.info.intl.retail.core.utils.LogUtil;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.model.UserInfo;

/**
 * HttpInterfaceLogAspect测试类
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class HttpInterfaceLogAspectTest {

    @Mock
    private ProceedingJoinPoint joinPoint;

    @Mock
    private MethodSignature methodSignature;

    @Mock
    private HighPerformanceSensitiveFieldFilter sensitiveFieldFilter;

    @InjectMocks
    private HttpInterfaceLogAspect httpInterfaceLogAspect;

    private MockHttpServletRequest mockRequest;
    private MockHttpServletResponse mockResponse;

    @BeforeEach
    void setUp() {
        mockRequest = new MockHttpServletRequest();
        mockRequest.setRequestURI("/api/test");
        mockRequest.setMethod("GET");
        mockResponse = new MockHttpServletResponse();

        ServletRequestAttributes attributes = new ServletRequestAttributes(mockRequest, mockResponse);
        RequestContextHolder.setRequestAttributes(attributes);
    }

    @Test
    void testLogHttpInterface_Success() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"testArg"};
        Object result = CommonApiResponse.success("test data");

        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);

        // Mock方法
        Method method = TestController.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);

        // Mock敏感数据过滤器
        when(sensitiveFieldFilter.process(any(), anyString(), any())).thenAnswer(invocation -> invocation.getArgument(2));

        // 执行测试
        Object actualResult = httpInterfaceLogAspect.logHttpInterface(joinPoint);

        // 验证结果
        assertEquals(result, actualResult);
        verify(joinPoint).proceed();
    }

    @Test
    void testLogHttpInterface_WithException() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"testArg"};
        RuntimeException exception = new RuntimeException("Test exception");

        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenThrow(exception);

        // Mock方法
        Method method = TestController.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);

        // Mock敏感数据过滤器
        when(sensitiveFieldFilter.process(any(), anyString(), any())).thenAnswer(invocation -> invocation.getArgument(2));

        // 执行测试并验证异常
        assertThrows(RuntimeException.class, () -> {
            httpInterfaceLogAspect.logHttpInterface(joinPoint);
        });

        verify(joinPoint).proceed();
    }

    @Test
    void testLogHttpInterface_WithPostMapping() throws Throwable {
        // 准备测试数据
        mockRequest.setMethod("POST");
        Object[] args = new Object[]{"testArg"};
        Object result = CommonApiResponse.success("test data");

        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);

        // Mock方法
        Method method = TestController.class.getMethod("testPostMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);

        // Mock敏感数据过滤器
        when(sensitiveFieldFilter.process(any(), anyString(), any())).thenAnswer(invocation -> invocation.getArgument(2));

        // 执行测试
        Object actualResult = httpInterfaceLogAspect.logHttpInterface(joinPoint);

        // 验证结果
        assertEquals(result, actualResult);
        verify(joinPoint).proceed();
    }

    @Test
    void testLogHttpInterface_WithAccessLogAnnotation() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"testArg"};
        Object result = CommonApiResponse.success("test data");

        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);

        // Mock方法
        Method method = TestController.class.getMethod("testMethodWithAccessLog", String.class);
        when(methodSignature.getMethod()).thenReturn(method);

        // Mock敏感数据过滤器
        when(sensitiveFieldFilter.process(any(), anyString(), any())).thenAnswer(invocation -> invocation.getArgument(2));

        // 执行测试
        Object actualResult = httpInterfaceLogAspect.logHttpInterface(joinPoint);

        // 验证结果
        assertEquals(result, actualResult);
        verify(joinPoint).proceed();
    }

    @Test
    void testLogHttpInterface_WithNullRequest() throws Throwable {
        // 清除RequestContextHolder
        RequestContextHolder.resetRequestAttributes();

        // 准备测试数据
        Object[] args = new Object[]{"testArg"};
        Object result = CommonApiResponse.success("test data");

        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);

        // Mock方法
        Method method = TestController.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);

        // Mock敏感数据过滤器
        when(sensitiveFieldFilter.process(any(), anyString(), any())).thenAnswer(invocation -> invocation.getArgument(2));

        // 执行测试
        Object actualResult = httpInterfaceLogAspect.logHttpInterface(joinPoint);

        // 验证结果
        assertEquals(result, actualResult);
        verify(joinPoint).proceed();
    }

    @Test
    void testLogHttpInterface_LogException() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"testArg"};
        Object result = CommonApiResponse.success("test data");

        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);

        // Mock方法
        Method method = TestController.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);

        // Mock敏感数据过滤器抛出异常
        when(sensitiveFieldFilter.process(any(), anyString(), any())).thenThrow(new RuntimeException("Filter error"));

        // 执行测试
        Object actualResult = httpInterfaceLogAspect.logHttpInterface(joinPoint);

        // 验证结果 - 即使日志记录失败，业务逻辑仍然正常执行
        assertEquals(result, actualResult);
        verify(joinPoint).proceed();
    }

    @Test
    void testLogHttpInterface_FinallyBlockException() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"testArg"};
        Object result = CommonApiResponse.success("test data");

        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);

        // Mock方法
        Method method = TestController.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);

        // Mock敏感数据过滤器正常处理
        when(sensitiveFieldFilter.process(any(), anyString(), any())).thenAnswer(invocation -> invocation.getArgument(2));

        // 执行测试
        Object actualResult = httpInterfaceLogAspect.logHttpInterface(joinPoint);

        // 验证结果 - 即使finally块中有异常，业务逻辑仍然正常执行
        assertEquals(result, actualResult);
        verify(joinPoint).proceed();
    }

    @Test
    void testLogHttpInterface_FinallyBlockExceptionWithBusinessException() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"testArg"};
        RuntimeException businessException = new RuntimeException("Business error");

        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenThrow(businessException);

        // Mock方法
        Method method = TestController.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);

        // Mock敏感数据过滤器正常处理
        when(sensitiveFieldFilter.process(any(), anyString(), any())).thenAnswer(invocation -> invocation.getArgument(2));

        // 执行测试并验证异常
        assertThrows(RuntimeException.class, () -> {
            httpInterfaceLogAspect.logHttpInterface(joinPoint);
        });

        verify(joinPoint).proceed();
    }

    @Test
    void testLogHttpInterface_LogInfoException() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"testArg"};
        Object result = CommonApiResponse.success("test data");

        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);

        // Mock方法
        Method method = TestController.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);

        // Mock敏感数据过滤器正常处理
        when(sensitiveFieldFilter.process(any(), anyString(), any())).thenAnswer(invocation -> invocation.getArgument(2));

        // 执行测试
        Object actualResult = httpInterfaceLogAspect.logHttpInterface(joinPoint);

        // 验证结果 - 即使log.info调用失败，业务逻辑仍然正常执行
        assertEquals(result, actualResult);
        verify(joinPoint).proceed();
    }

    @Test
    void testLogHttpInterface_LogErrorException() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"testArg"};
        Object result = CommonApiResponse.success("test data");

        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);

        // Mock方法
        Method method = TestController.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);

        // Mock敏感数据过滤器正常处理
        when(sensitiveFieldFilter.process(any(), anyString(), any())).thenAnswer(invocation -> invocation.getArgument(2));

        // 执行测试
        Object actualResult = httpInterfaceLogAspect.logHttpInterface(joinPoint);

        // 验证结果 - 即使log.error调用失败，业务逻辑仍然正常执行
        assertEquals(result, actualResult);
        verify(joinPoint).proceed();
    }

    @Test
    void testLogHttpInterface_LogInfoExceptionWithBusinessException() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"testArg"};
        RuntimeException businessException = new RuntimeException("Business error");

        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenThrow(businessException);

        // Mock方法
        Method method = TestController.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);

        // Mock敏感数据过滤器正常处理
        when(sensitiveFieldFilter.process(any(), anyString(), any())).thenAnswer(invocation -> invocation.getArgument(2));

        // 执行测试并验证异常
        assertThrows(RuntimeException.class, () -> {
            httpInterfaceLogAspect.logHttpInterface(joinPoint);
        });

        verify(joinPoint).proceed();
    }

    @Test
    void testLogHttpInterface_LogErrorExceptionWithBusinessException() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"testArg"};
        RuntimeException businessException = new RuntimeException("Business error");

        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenThrow(businessException);

        // Mock方法
        Method method = TestController.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);

        // Mock敏感数据过滤器正常处理
        when(sensitiveFieldFilter.process(any(), anyString(), any())).thenAnswer(invocation -> invocation.getArgument(2));

        // 执行测试并验证异常
        assertThrows(RuntimeException.class, () -> {
            httpInterfaceLogAspect.logHttpInterface(joinPoint);
        });

        verify(joinPoint).proceed();
    }

    @Test
    void testLogHttpInterface_LogUtilGetRequestStrException() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"testArg"};
        Object result = CommonApiResponse.success("test data");

        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);

        // Mock方法
        Method method = TestController.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);

        // Mock敏感数据过滤器正常处理
        when(sensitiveFieldFilter.process(any(), anyString(), any())).thenAnswer(invocation -> invocation.getArgument(2));

        // 使用MockedStatic来模拟LogUtil.getRequestStr抛出异常
        try (MockedStatic<LogUtil> logUtilMock = mockStatic(LogUtil.class)) {
            logUtilMock.when(() -> LogUtil.getAccessLog(any(ProceedingJoinPoint.class)))
                    .thenReturn(null);
            logUtilMock.when(() -> LogUtil.getRequestStr(any(), any(), any()))
                    .thenThrow(new RuntimeException("LogUtil.getRequestStr error"));
            logUtilMock.when(() -> LogUtil.getResponseStr(any(), any(), any()))
                    .thenReturn("response");
            logUtilMock.when(() -> LogUtil.getStatus(any(), any()))
                    .thenReturn(0);

            // 执行测试
            Object actualResult = httpInterfaceLogAspect.logHttpInterface(joinPoint);

            // 验证结果 - 即使LogUtil.getRequestStr调用失败，业务逻辑仍然正常执行
            assertEquals(result, actualResult);
            verify(joinPoint).proceed();
        }
    }

    @Test
    void testLogHttpInterface_LogUtilGetRequestStrExceptionWithBusinessException() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"testArg"};
        RuntimeException businessException = new RuntimeException("Business error");

        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenThrow(businessException);

        // Mock方法
        Method method = TestController.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);

        // Mock敏感数据过滤器正常处理
        when(sensitiveFieldFilter.process(any(), anyString(), any())).thenAnswer(invocation -> invocation.getArgument(2));

        // 使用MockedStatic来模拟LogUtil.getRequestStr抛出异常
        try (MockedStatic<LogUtil> logUtilMock = mockStatic(LogUtil.class)) {
            logUtilMock.when(() -> LogUtil.getAccessLog(any(ProceedingJoinPoint.class)))
                    .thenReturn(null);
            logUtilMock.when(() -> LogUtil.getRequestStr(any(), any(), any()))
                    .thenThrow(new RuntimeException("LogUtil.getRequestStr error"));
            logUtilMock.when(() -> LogUtil.getResponseStr(any(), any(), any()))
                    .thenReturn("response");
            logUtilMock.when(() -> LogUtil.getStatus(any(), any()))
                    .thenReturn(500);

            // 执行测试并验证异常
            assertThrows(RuntimeException.class, () -> {
                httpInterfaceLogAspect.logHttpInterface(joinPoint);
            });

            verify(joinPoint).proceed();
        }
    }

    @Test
    void testLogHttpInterface_LogUtilGetRequestStrAndResponseStrException() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"testArg"};
        Object result = CommonApiResponse.success("test data");

        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);

        // Mock方法
        Method method = TestController.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);

        // Mock敏感数据过滤器正常处理
        when(sensitiveFieldFilter.process(any(), anyString(), any())).thenAnswer(invocation -> invocation.getArgument(2));

        // 使用MockedStatic来模拟LogUtil.getRequestStr和getResponseStr都抛出异常
        try (MockedStatic<LogUtil> logUtilMock = mockStatic(LogUtil.class)) {
            logUtilMock.when(() -> LogUtil.getAccessLog(any(ProceedingJoinPoint.class)))
                    .thenReturn(null);
            logUtilMock.when(() -> LogUtil.getRequestStr(any(), any(), any()))
                    .thenThrow(new RuntimeException("LogUtil.getRequestStr error"));
            logUtilMock.when(() -> LogUtil.getResponseStr(any(), any(), any()))
                    .thenThrow(new RuntimeException("LogUtil.getResponseStr error"));
            logUtilMock.when(() -> LogUtil.getStatus(any(), any()))
                    .thenReturn(0);

            // 执行测试
            Object actualResult = httpInterfaceLogAspect.logHttpInterface(joinPoint);

            // 验证结果 - 即使LogUtil.getRequestStr和getResponseStr都调用失败，业务逻辑仍然正常执行
            assertEquals(result, actualResult);
            verify(joinPoint).proceed();
        }
    }

    @Test
    void testLogHttpInterface_WithUserInfo() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"testArg"};
        Object result = CommonApiResponse.success("test data");
        
        // 创建UserInfo对象
        UserInfo userInfo = UserInfo.builder()
                .miID(12345L)
                .rmsUserId("rms123")
                .userName("testuser")
                .build();

        // 设置UserInfo到request attributes
        mockRequest.setAttribute(CommonConstant.JwtAuth.JWT_TOKEN_USER, userInfo);

        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);

        // Mock方法
        Method method = TestController.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);

        // Mock敏感数据过滤器
        when(sensitiveFieldFilter.process(any(), anyString(), any())).thenAnswer(invocation -> invocation.getArgument(2));

        // 执行测试
        Object actualResult = httpInterfaceLogAspect.logHttpInterface(joinPoint);

        // 验证结果
        assertEquals(result, actualResult);
        verify(joinPoint).proceed();
    }

    @Test
    void testLogHttpInterface_WithUserInfoNullMiID() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"testArg"};
        Object result = CommonApiResponse.success("test data");
        
        // 创建UserInfo对象，miID为null
        UserInfo userInfo = UserInfo.builder()
                .miID(null)
                .rmsUserId("rms123")
                .userName("testuser")
                .build();

        // 设置UserInfo到request attributes
        mockRequest.setAttribute(CommonConstant.JwtAuth.JWT_TOKEN_USER, userInfo);

        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);

        // Mock方法
        Method method = TestController.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);

        // Mock敏感数据过滤器
        when(sensitiveFieldFilter.process(any(), anyString(), any())).thenAnswer(invocation -> invocation.getArgument(2));

        // 执行测试
        Object actualResult = httpInterfaceLogAspect.logHttpInterface(joinPoint);

        // 验证结果
        assertEquals(result, actualResult);
        verify(joinPoint).proceed();
    }

    @Test
    void testLogHttpInterface_WithUserInfoNullRmsUserId() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"testArg"};
        Object result = CommonApiResponse.success("test data");
        
        // 创建UserInfo对象，rmsUserId为null
        UserInfo userInfo = UserInfo.builder()
                .miID(12345L)
                .rmsUserId(null)
                .userName("testuser")
                .build();

        // 设置UserInfo到request attributes
        mockRequest.setAttribute(CommonConstant.JwtAuth.JWT_TOKEN_USER, userInfo);

        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);

        // Mock方法
        Method method = TestController.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);

        // Mock敏感数据过滤器
        when(sensitiveFieldFilter.process(any(), anyString(), any())).thenAnswer(invocation -> invocation.getArgument(2));

        // 执行测试
        Object actualResult = httpInterfaceLogAspect.logHttpInterface(joinPoint);

        // 验证结果
        assertEquals(result, actualResult);
        verify(joinPoint).proceed();
    }

    @Test
    void testLogHttpInterface_WithUserInfoNullMiIDAndRmsUserId() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"testArg"};
        Object result = CommonApiResponse.success("test data");
        
        // 创建UserInfo对象，miID和rmsUserId都为null
        UserInfo userInfo = UserInfo.builder()
                .miID(null)
                .rmsUserId("test1234")
                .userName("testuser")
                .build();

        // 设置UserInfo到request attributes
        mockRequest.setAttribute(CommonConstant.JwtAuth.JWT_TOKEN_USER, userInfo);

        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);

        // Mock方法
        Method method = TestController.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);

        // Mock敏感数据过滤器
        when(sensitiveFieldFilter.process(any(), anyString(), any())).thenAnswer(invocation -> invocation.getArgument(2));

        // 执行测试
        Object actualResult = httpInterfaceLogAspect.logHttpInterface(joinPoint);

        // 验证结果
        assertEquals(result, actualResult);
        verify(joinPoint).proceed();
    }

    @Test
    void testLogHttpInterface_WithUserInfoOnlyMiID() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"testArg"};
        Object result = CommonApiResponse.success("test data");
        
        // 创建UserInfo对象，只有miID，rmsUserId为null
        UserInfo userInfo = UserInfo.builder()
                .miID(98765L)
                .rmsUserId(null)
                .userName("testuser")
                .email("<EMAIL>")
                .build();

        // 设置UserInfo到request attributes
        mockRequest.setAttribute(CommonConstant.JwtAuth.JWT_TOKEN_USER, userInfo);

        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);

        // Mock方法
        Method method = TestController.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);

        // Mock敏感数据过滤器
        when(sensitiveFieldFilter.process(any(), anyString(), any())).thenAnswer(invocation -> invocation.getArgument(2));

        // 执行测试
        Object actualResult = httpInterfaceLogAspect.logHttpInterface(joinPoint);

        // 验证结果
        assertEquals(result, actualResult);
        verify(joinPoint).proceed();
    }

    @Test
    void testLogHttpInterface_WithUserInfoOnlyRmsUserId() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"testArg"};
        Object result = CommonApiResponse.success("test data");
        
        // 创建UserInfo对象，只有rmsUserId，miID为null
        UserInfo userInfo = UserInfo.builder()
                .miID(null)
                .rmsUserId("rms_user_456")
                .userName("testuser")
                .areaId("US")
                .build();

        // 设置UserInfo到request attributes
        mockRequest.setAttribute(CommonConstant.JwtAuth.JWT_TOKEN_USER, userInfo);

        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);

        // Mock方法
        Method method = TestController.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);

        // Mock敏感数据过滤器
        when(sensitiveFieldFilter.process(any(), anyString(), any())).thenAnswer(invocation -> invocation.getArgument(2));

        // 执行测试
        Object actualResult = httpInterfaceLogAspect.logHttpInterface(joinPoint);

        // 验证结果
        assertEquals(result, actualResult);
        verify(joinPoint).proceed();
    }

    @Test
    void testLogHttpInterface_WithUserInfoZeroMiID() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"testArg"};
        Object result = CommonApiResponse.success("test data");
        
        // 创建UserInfo对象，miID为0
        UserInfo userInfo = UserInfo.builder()
                .miID(0L)
                .rmsUserId("rms_user_789")
                .userName("testuser")
                .language("en")
                .build();

        // 设置UserInfo到request attributes
        mockRequest.setAttribute(CommonConstant.JwtAuth.JWT_TOKEN_USER, userInfo);

        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);

        // Mock方法
        Method method = TestController.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);

        // Mock敏感数据过滤器
        when(sensitiveFieldFilter.process(any(), anyString(), any())).thenAnswer(invocation -> invocation.getArgument(2));

        // 执行测试
        Object actualResult = httpInterfaceLogAspect.logHttpInterface(joinPoint);

        // 验证结果
        assertEquals(result, actualResult);
        verify(joinPoint).proceed();
    }

    @Test
    void testLogHttpInterface_WithUserInfoNegativeMiID() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"testArg"};
        Object result = CommonApiResponse.success("test data");
        
        // 创建UserInfo对象，miID为负数
        UserInfo userInfo = UserInfo.builder()
                .miID(-123L)
                .rmsUserId("rms_user_negative")
                .userName("testuser")
                .isPassport(true)
                .build();

        // 设置UserInfo到request attributes
        mockRequest.setAttribute(CommonConstant.JwtAuth.JWT_TOKEN_USER, userInfo);

        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);

        // Mock方法
        Method method = TestController.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);

        // Mock敏感数据过滤器
        when(sensitiveFieldFilter.process(any(), anyString(), any())).thenAnswer(invocation -> invocation.getArgument(2));

        // 执行测试
        Object actualResult = httpInterfaceLogAspect.logHttpInterface(joinPoint);

        // 验证结果
        assertEquals(result, actualResult);
        verify(joinPoint).proceed();
    }

    @Test
    void testLogHttpInterface_WithUserInfoEmptyRmsUserId() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"testArg"};
        Object result = CommonApiResponse.success("test data");
        
        // 创建UserInfo对象，rmsUserId为空字符串
        UserInfo userInfo = UserInfo.builder()
                .miID(11111L)
                .rmsUserId("")
                .userName("testuser")
                .domainName("test.com")
                .build();

        // 设置UserInfo到request attributes
        mockRequest.setAttribute(CommonConstant.JwtAuth.JWT_TOKEN_USER, userInfo);

        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);

        // Mock方法
        Method method = TestController.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);

        // Mock敏感数据过滤器
        when(sensitiveFieldFilter.process(any(), anyString(), any())).thenAnswer(invocation -> invocation.getArgument(2));

        // 执行测试
        Object actualResult = httpInterfaceLogAspect.logHttpInterface(joinPoint);

        // 验证结果
        assertEquals(result, actualResult);
        verify(joinPoint).proceed();
    }

    @Test
    void testLogHttpInterface_WithUserInfoMinimalFields() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"testArg"};
        Object result = CommonApiResponse.success("test data");
        
        // 创建UserInfo对象，只包含最少的必需字段
        UserInfo userInfo = UserInfo.builder()
                .miID(99999L)
                .build();

        // 设置UserInfo到request attributes
        mockRequest.setAttribute(CommonConstant.JwtAuth.JWT_TOKEN_USER, userInfo);

        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);

        // Mock方法
        Method method = TestController.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);

        // Mock敏感数据过滤器
        when(sensitiveFieldFilter.process(any(), anyString(), any())).thenAnswer(invocation -> invocation.getArgument(2));

        // 执行测试
        Object actualResult = httpInterfaceLogAspect.logHttpInterface(joinPoint);

        // 验证结果
        assertEquals(result, actualResult);
        verify(joinPoint).proceed();
    }

    @Test
    void testLogHttpInterface_WithUserInfoAllFields() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"testArg"};
        Object result = CommonApiResponse.success("test data");
        
        // 创建UserInfo对象，包含所有字段
        UserInfo userInfo = UserInfo.builder()
                .userId("user123")
                .userName("fulltestuser")
                .email("<EMAIL>")
                .language("zh-CN")
                .miID(88888L)
                .areaId("CN")
                .rmsUserId("rms_full_test")
                .isPassport(false)
                .domainName("fulltest.com")
                .tokenValue("token123")
                .area("Asia")
                .build();

        // 设置UserInfo到request attributes
        mockRequest.setAttribute(CommonConstant.JwtAuth.JWT_TOKEN_USER, userInfo);

        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);

        // Mock方法
        Method method = TestController.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);

        // Mock敏感数据过滤器
        when(sensitiveFieldFilter.process(any(), anyString(), any())).thenAnswer(invocation -> invocation.getArgument(2));

        // 执行测试
        Object actualResult = httpInterfaceLogAspect.logHttpInterface(joinPoint);

        // 验证结果
        assertEquals(result, actualResult);
        verify(joinPoint).proceed();
    }

    @Test
    void testLogHttpInterface_WithPutMapping() throws Throwable {
        // 准备测试数据
        mockRequest.setMethod("PUT");
        Object[] args = new Object[]{"testArg"};
        Object result = CommonApiResponse.success("test data");

        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);

        // Mock方法
        Method method = TestController.class.getMethod("testPutMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);

        // Mock敏感数据过滤器
        when(sensitiveFieldFilter.process(any(), anyString(), any())).thenAnswer(invocation -> invocation.getArgument(2));

        // 执行测试
        Object actualResult = httpInterfaceLogAspect.logHttpInterface(joinPoint);

        // 验证结果
        assertEquals(result, actualResult);
        verify(joinPoint).proceed();
    }

    @Test
    void testLogHttpInterface_WithDeleteMapping() throws Throwable {
        // 准备测试数据
        mockRequest.setMethod("DELETE");
        Object[] args = new Object[]{"testArg"};
        Object result = CommonApiResponse.success("test data");

        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);

        // Mock方法
        Method method = TestController.class.getMethod("testDeleteMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);

        // Mock敏感数据过滤器
        when(sensitiveFieldFilter.process(any(), anyString(), any())).thenAnswer(invocation -> invocation.getArgument(2));

        // 执行测试
        Object actualResult = httpInterfaceLogAspect.logHttpInterface(joinPoint);

        // 验证结果
        assertEquals(result, actualResult);
        verify(joinPoint).proceed();
    }

    @Test
    void testLogHttpInterface_WithPatchMapping() throws Throwable {
        // 准备测试数据
        mockRequest.setMethod("PATCH");
        Object[] args = new Object[]{"testArg"};
        Object result = CommonApiResponse.success("test data");

        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);

        // Mock方法
        Method method = TestController.class.getMethod("testPatchMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);

        // Mock敏感数据过滤器
        when(sensitiveFieldFilter.process(any(), anyString(), any())).thenAnswer(invocation -> invocation.getArgument(2));

        // 执行测试
        Object actualResult = httpInterfaceLogAspect.logHttpInterface(joinPoint);

        // 验证结果
        assertEquals(result, actualResult);
        verify(joinPoint).proceed();
    }

    @Test
    void testLogHttpInterface_WithRequestMapping() throws Throwable {
        // 准备测试数据
        mockRequest.setMethod("POST");
        Object[] args = new Object[]{"testArg"};
        Object result = CommonApiResponse.success("test data");

        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);

        // Mock方法
        Method method = TestController.class.getMethod("testRequestMapping", String.class);
        when(methodSignature.getMethod()).thenReturn(method);

        // Mock敏感数据过滤器
        when(sensitiveFieldFilter.process(any(), anyString(), any())).thenAnswer(invocation -> invocation.getArgument(2));

        // 执行测试
        Object actualResult = httpInterfaceLogAspect.logHttpInterface(joinPoint);

        // 验证结果
        assertEquals(result, actualResult);
        verify(joinPoint).proceed();
    }

    @Test
    void testLogHttpInterface_WithEmptyArgs() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{};
        Object result = CommonApiResponse.success("test data");

        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);

        // Mock方法
        Method method = TestController.class.getMethod("testMethodWithoutArgs");
        when(methodSignature.getMethod()).thenReturn(method);

        // Mock敏感数据过滤器
        when(sensitiveFieldFilter.process(any(), anyString(), any())).thenAnswer(invocation -> invocation.getArgument(2));

        // 执行测试
        Object actualResult = httpInterfaceLogAspect.logHttpInterface(joinPoint);

        // 验证结果
        assertEquals(result, actualResult);
        verify(joinPoint).proceed();
    }

    @Test
    void testLogHttpInterface_WithNullArgs() throws Throwable {
        // 准备测试数据
        Object[] args = null;
        Object result = CommonApiResponse.success("test data");

        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);

        // Mock方法
        Method method = TestController.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);

        // Mock敏感数据过滤器
        when(sensitiveFieldFilter.process(any(), anyString(), any())).thenAnswer(invocation -> invocation.getArgument(2));

        // 执行测试
        Object actualResult = httpInterfaceLogAspect.logHttpInterface(joinPoint);

        // 验证结果
        assertEquals(result, actualResult);
        verify(joinPoint).proceed();
    }

    @Test
    void testLogHttpInterface_WithNullResult() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"testArg"};
        Object result = null;

        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);

        // Mock方法
        Method method = TestController.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);

        // Mock敏感数据过滤器
        when(sensitiveFieldFilter.process(any(), anyString(), any())).thenAnswer(invocation -> invocation.getArgument(2));

        // 执行测试
        Object actualResult = httpInterfaceLogAspect.logHttpInterface(joinPoint);

        // 验证结果
        assertEquals(result, actualResult);
        verify(joinPoint).proceed();
    }

    @Test
    void testLogHttpInterface_WithComplexObjectArgs() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{
            "testArg",
            CommonApiResponse.success("nested response"),
            new String[]{"array1", "array2"}
        };
        Object result = CommonApiResponse.success("test data");

        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);

        // Mock方法
        Method method = TestController.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);

        // Mock敏感数据过滤器
        when(sensitiveFieldFilter.process(any(), anyString(), any())).thenAnswer(invocation -> invocation.getArgument(2));

        // 执行测试
        Object actualResult = httpInterfaceLogAspect.logHttpInterface(joinPoint);

        // 验证结果
        assertEquals(result, actualResult);
        verify(joinPoint).proceed();
    }

    @Test
    void testLogHttpInterface_WithAccessLogAnnotationDisabled() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"testArg"};
        Object result = CommonApiResponse.success("test data");

        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);

        // Mock方法 - 使用禁用AccessLog的方法
        Method method = TestController.class.getMethod("testMethodWithAccessLogDisabled", String.class);
        when(methodSignature.getMethod()).thenReturn(method);

        // Mock敏感数据过滤器
        when(sensitiveFieldFilter.process(any(), anyString(), any())).thenAnswer(invocation -> invocation.getArgument(2));

        // 执行测试
        Object actualResult = httpInterfaceLogAspect.logHttpInterface(joinPoint);

        // 验证结果
        assertEquals(result, actualResult);
        verify(joinPoint).proceed();
    }

    @Test
    void testLogHttpInterface_WithAccessLogOnlyRequest() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"testArg"};
        Object result = CommonApiResponse.success("test data");

        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);

        // Mock方法 - 使用只记录请求的AccessLog方法
        Method method = TestController.class.getMethod("testMethodWithAccessLogOnlyRequest", String.class);
        when(methodSignature.getMethod()).thenReturn(method);

        // Mock敏感数据过滤器
        when(sensitiveFieldFilter.process(any(), anyString(), any())).thenAnswer(invocation -> invocation.getArgument(2));

        // 执行测试
        Object actualResult = httpInterfaceLogAspect.logHttpInterface(joinPoint);

        // 验证结果
        assertEquals(result, actualResult);
        verify(joinPoint).proceed();
    }

    @Test
    void testLogHttpInterface_WithAccessLogOnlyResponse() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"testArg"};
        Object result = CommonApiResponse.success("test data");

        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);

        // Mock方法 - 使用只记录响应的AccessLog方法
        Method method = TestController.class.getMethod("testMethodWithAccessLogOnlyResponse", String.class);
        when(methodSignature.getMethod()).thenReturn(method);

        // Mock敏感数据过滤器
        when(sensitiveFieldFilter.process(any(), anyString(), any())).thenAnswer(invocation -> invocation.getArgument(2));

        // 执行测试
        Object actualResult = httpInterfaceLogAspect.logHttpInterface(joinPoint);

        // 验证结果
        assertEquals(result, actualResult);
        verify(joinPoint).proceed();
    }

    @Test
    void testLogHttpInterface_WithHttpServletResponse() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"testArg"};
        Object result = CommonApiResponse.success("test data");
        
        // 设置响应状态码
        mockResponse.setStatus(200);

        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);

        // Mock方法
        Method method = TestController.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);

        // Mock敏感数据过滤器
        when(sensitiveFieldFilter.process(any(), anyString(), any())).thenAnswer(invocation -> invocation.getArgument(2));

        // 执行测试
        Object actualResult = httpInterfaceLogAspect.logHttpInterface(joinPoint);

        // 验证结果
        assertEquals(result, actualResult);
        verify(joinPoint).proceed();
    }

    @Test
    void testLogHttpInterface_WithHttpServletResponseErrorStatus() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"testArg"};
        Object result = CommonApiResponse.success("test data");
        
        // 设置响应状态码为错误状态
        mockResponse.setStatus(500);

        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);

        // Mock方法
        Method method = TestController.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);

        // Mock敏感数据过滤器
        when(sensitiveFieldFilter.process(any(), anyString(), any())).thenAnswer(invocation -> invocation.getArgument(2));

        // 执行测试
        Object actualResult = httpInterfaceLogAspect.logHttpInterface(joinPoint);

        // 验证结果
        assertEquals(result, actualResult);
        verify(joinPoint).proceed();
    }

    @Test
    void testLogHttpInterface_WithHttpServletResponseNotFoundStatus() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"testArg"};
        Object result = CommonApiResponse.success("test data");
        
        // 设置响应状态码为404
        mockResponse.setStatus(404);

        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);

        // Mock方法
        Method method = TestController.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);

        // Mock敏感数据过滤器
        when(sensitiveFieldFilter.process(any(), anyString(), any())).thenAnswer(invocation -> invocation.getArgument(2));

        // 执行测试
        Object actualResult = httpInterfaceLogAspect.logHttpInterface(joinPoint);

        // 验证结果
        assertEquals(result, actualResult);
        verify(joinPoint).proceed();
    }

    @Test
    void testLogHttpInterface_WithNullServletRequestAttributes() throws Throwable {
        // 清除RequestContextHolder
        RequestContextHolder.resetRequestAttributes();

        // 准备测试数据
        Object[] args = new Object[]{"testArg"};
        Object result = CommonApiResponse.success("test data");

        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);

        // Mock方法
        Method method = TestController.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);

        // Mock敏感数据过滤器
        when(sensitiveFieldFilter.process(any(), anyString(), any())).thenAnswer(invocation -> invocation.getArgument(2));

        // 执行测试
        Object actualResult = httpInterfaceLogAspect.logHttpInterface(joinPoint);

        // 验证结果
        assertEquals(result, actualResult);
        verify(joinPoint).proceed();
    }

    @Test
    void testLogHttpInterface_WithNullHttpServletResponse() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"testArg"};
        Object result = CommonApiResponse.success("test data");

        // 创建只有request的ServletRequestAttributes
        ServletRequestAttributes attributes = new ServletRequestAttributes(mockRequest, null);
        RequestContextHolder.setRequestAttributes(attributes);

        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);

        // Mock方法
        Method method = TestController.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);

        // Mock敏感数据过滤器
        when(sensitiveFieldFilter.process(any(), anyString(), any())).thenAnswer(invocation -> invocation.getArgument(2));

        // 执行测试
        Object actualResult = httpInterfaceLogAspect.logHttpInterface(joinPoint);

        // 验证结果
        assertEquals(result, actualResult);
        verify(joinPoint).proceed();
    }

    @Test
    void testLogHttpInterface_WithHttpServletResponseAndException() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"testArg"};
        RuntimeException exception = new RuntimeException("Test exception");
        
        // 设置响应状态码
        mockResponse.setStatus(400);

        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenThrow(exception);

        // Mock方法
        Method method = TestController.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);

        // Mock敏感数据过滤器
        when(sensitiveFieldFilter.process(any(), anyString(), any())).thenAnswer(invocation -> invocation.getArgument(2));

        // 执行测试并验证异常
        assertThrows(RuntimeException.class, () -> {
            httpInterfaceLogAspect.logHttpInterface(joinPoint);
        });

        verify(joinPoint).proceed();
    }

    @Test
    void testLogHttpInterface_WithHttpServletResponseOkStatus() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"testArg"};
        Object result = CommonApiResponse.success("test data");
        
        // 设置响应状态码为200 (OK)
        mockResponse.setStatus(200);

        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);

        // Mock方法
        Method method = TestController.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);

        // Mock敏感数据过滤器
        when(sensitiveFieldFilter.process(any(), anyString(), any())).thenAnswer(invocation -> invocation.getArgument(2));

        // 执行测试
        Object actualResult = httpInterfaceLogAspect.logHttpInterface(joinPoint);

        // 验证结果
        assertEquals(result, actualResult);
        verify(joinPoint).proceed();
    }

    @Test
    void testLogHttpInterface_WithHttpServletResponseUnauthorizedStatus() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"testArg"};
        Object result = CommonApiResponse.success("test data");
        
        // 设置响应状态码为401 (Unauthorized)
        mockResponse.setStatus(401);

        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);

        // Mock方法
        Method method = TestController.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);

        // Mock敏感数据过滤器
        when(sensitiveFieldFilter.process(any(), anyString(), any())).thenAnswer(invocation -> invocation.getArgument(2));

        // 执行测试
        Object actualResult = httpInterfaceLogAspect.logHttpInterface(joinPoint);

        // 验证结果
        assertEquals(result, actualResult);
        verify(joinPoint).proceed();
    }

    @Test
    void testLogHttpInterface_WithHttpServletResponseForbiddenStatus() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"testArg"};
        Object result = CommonApiResponse.success("test data");
        
        // 设置响应状态码为403 (Forbidden)
        mockResponse.setStatus(403);

        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);

        // Mock方法
        Method method = TestController.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);

        // Mock敏感数据过滤器
        when(sensitiveFieldFilter.process(any(), anyString(), any())).thenAnswer(invocation -> invocation.getArgument(2));

        // 执行测试
        Object actualResult = httpInterfaceLogAspect.logHttpInterface(joinPoint);

        // 验证结果
        assertEquals(result, actualResult);
        verify(joinPoint).proceed();
    }

    @Test
    void testLogHttpInterface_WithHttpServletResponseBadRequestStatus() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"testArg"};
        Object result = CommonApiResponse.success("test data");
        
        // 设置响应状态码为400 (Bad Request)
        mockResponse.setStatus(400);

        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);

        // Mock方法
        Method method = TestController.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);

        // Mock敏感数据过滤器
        when(sensitiveFieldFilter.process(any(), anyString(), any())).thenAnswer(invocation -> invocation.getArgument(2));

        // 执行测试
        Object actualResult = httpInterfaceLogAspect.logHttpInterface(joinPoint);

        // 验证结果
        assertEquals(result, actualResult);
        verify(joinPoint).proceed();
    }

    @Test
    void testLogHttpInterface_WithHttpServletResponseInternalServerErrorStatus() throws Throwable {
        // 准备测试数据
        Object[] args = new Object[]{"testArg"};
        Object result = CommonApiResponse.success("test data");
        
        // 设置响应状态码为500 (Internal Server Error)
        mockResponse.setStatus(500);

        // Mock方法签名
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn(result);

        // Mock方法
        Method method = TestController.class.getMethod("testMethod", String.class);
        when(methodSignature.getMethod()).thenReturn(method);

        // Mock敏感数据过滤器
        when(sensitiveFieldFilter.process(any(), anyString(), any())).thenAnswer(invocation -> invocation.getArgument(2));

        // 执行测试
        Object actualResult = httpInterfaceLogAspect.logHttpInterface(joinPoint);

        // 验证结果
        assertEquals(result, actualResult);
        verify(joinPoint).proceed();
    }

    // 测试控制器类
    public static class TestController {

        @GetMapping("/test")
        public CommonApiResponse<String> testMethod(String arg) {
            return CommonApiResponse.success("test result");
        }

        @PostMapping("/test")
        public CommonApiResponse<String> testPostMethod(String arg) {
            return CommonApiResponse.success("test result");
        }

        @GetMapping("/test-access-log")
        @AccessLog(request = true, response = true)
        public CommonApiResponse<String> testMethodWithAccessLog(String arg) {
            return CommonApiResponse.success("test result");
        }

        @PutMapping("/test")
        public CommonApiResponse<String> testPutMethod(String arg) {
            return CommonApiResponse.success("test result");
        }

        @DeleteMapping("/test")
        public CommonApiResponse<String> testDeleteMethod(String arg) {
            return CommonApiResponse.success("test result");
        }

        @PatchMapping("/test")
        public CommonApiResponse<String> testPatchMethod(String arg) {
            return CommonApiResponse.success("test result");
        }

        @RequestMapping("/test")
        public CommonApiResponse<String> testRequestMapping(String arg) {
            return CommonApiResponse.success("test result");
        }

        @GetMapping("/test-no-args")
        public CommonApiResponse<String> testMethodWithoutArgs() {
            return CommonApiResponse.success("test result");
        }

        @GetMapping("/test-access-log-disabled")
        @AccessLog(request = false, response = false)
        public CommonApiResponse<String> testMethodWithAccessLogDisabled(String arg) {
            return CommonApiResponse.success("test result");
        }

        @GetMapping("/test-access-log-request")
        @AccessLog(request = true, response = false)
        public CommonApiResponse<String> testMethodWithAccessLogOnlyRequest(String arg) {
            return CommonApiResponse.success("test result");
        }

        @GetMapping("/test-access-log-response")
        @AccessLog(request = false, response = true)
        public CommonApiResponse<String> testMethodWithAccessLogOnlyResponse(String arg) {
            return CommonApiResponse.success("test result");
        }
    }
}