package com.mi.info.intl.retail.so.app.provider.rule;

import java.util.List;

import javax.annotation.Resource;

import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import com.mi.info.intl.retail.bean.PageDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.dto.ApproverDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.dto.SoRuleChangeLogDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.dto.SoRuleChangeLogListDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.provider.SoRuleChangeLogProvider;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.request.QueryApproverListReq;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.request.QuerySoRuleChangeLogReq;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.request.QuerySoRuleLogReq;
import com.mi.info.intl.retail.intlretail.util.AppProviderUtil;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.so.domain.rule.service.IntlSoRuleChangeLogService;
import com.mi.info.intl.retail.so.domain.sys.service.OrganizationUserService;
import com.xiaomi.mone.docs.annotations.dubbo.ApiDoc;
import com.xiaomi.mone.docs.annotations.dubbo.ApiModule;
import com.xiaomi.youpin.infra.rpc.Result;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2025/7/24 19:16
 */
@Slf4j
@Service
@DubboService(group = "${center.dubbo.group:}", interfaceClass = SoRuleChangeLogProvider.class, validation = "true")
@ApiModule(value = "国际渠道零售服务", apiInterface = SoRuleChangeLogProvider.class)
public class SoRuleChangeLogProviderImpl implements SoRuleChangeLogProvider {

    @Resource
    private IntlSoRuleChangeLogService intlSoRuleChangeLogService;

    @Resource
    private OrganizationUserService organizationUserService;

    /**
     * 获取SO规则更改日志列表
     *
     * @param req req
     * @return {@link Result }<{@link List }<{@link SoRuleChangeLogListDTO }>>
     */
    @ApiDoc(value = "/api/so/v1/getSoRuleChangeLogList", description = "获取SO规则更改日志列表")
    @Override
    public CommonApiResponse<PageDTO<SoRuleChangeLogListDTO>> getSoRuleChangeLogList(QuerySoRuleLogReq req) {
        return AppProviderUtil.wrap(log, "SoRuleChangeLogProvider::getSoRuleChangeLogList",
                req, intlSoRuleChangeLogService::getSoRuleDetailLogList,
                false);
    }

    /**
     * 通过ID获取SO规则更改日志
     *
     * @param req req
     * @return {@link Result }<{@link SoRuleChangeLogDTO }>
     */
    @ApiDoc(description = "通过ID获取SO规则更改日志", value = "/api/so/v1/getApprovalDetail")
    @Override
    public CommonApiResponse<SoRuleChangeLogDTO> getApprovalDetail(QuerySoRuleChangeLogReq req) {
        return AppProviderUtil.wrap(log, "SoRuleChangeLogProvider::getApprovalDetail",
                req, intlSoRuleChangeLogService::getSoRuleChangeLogById,
                false);
    }

    /**
     * 获取批准者列表
     *
     * @param req req
     * @return {@link Result }<{@link List }<{@link ApproverDTO }>>
     */
    @ApiDoc(description = "获取审批人列表", value = "/api/so/v1/getApproverList")
    @Override
    public CommonApiResponse<List<ApproverDTO>> getApproverList(QueryApproverListReq req) {
        return AppProviderUtil.wrap(log, "SoRuleChangeLogProvider::getApproverList",
                req, organizationUserService::getApproverList,
                false);
    }
}
