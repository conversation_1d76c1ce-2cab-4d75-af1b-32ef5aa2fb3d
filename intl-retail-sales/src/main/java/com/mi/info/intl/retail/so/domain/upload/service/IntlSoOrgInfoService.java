package com.mi.info.intl.retail.so.domain.upload.service;

import com.mi.info.intl.retail.api.fieldforce.retailer.dto.IntlRetailerDTO;
import com.mi.info.intl.retail.api.front.dto.RmsPositionInfoRes;
import com.mi.info.intl.retail.api.front.dto.RmsStoreInfoDto;
import com.mi.info.intl.retail.api.front.position.dto.PositionStoreInfoDTO;
import com.mi.info.intl.retail.api.front.store.dto.StoreChangeLogDto;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoOrgInfo;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @description 针对表【intl_so_org_info(销量阵地信息)】的数据库操作Service
 * @createDate 2025-07-25 16:40:50
 */
public interface IntlSoOrgInfoService extends IService<IntlSoOrgInfo> {

    List<IntlSoOrgInfo> batchGetByIds(List<Long> idList);

    Map<Long, IntlSoOrgInfo> batchGetByIdsMap(List<Long> idList);

    /**
     * 创建门店信息数据
     *
     * @param positionStoreInfo
     * @param countryCode
     * @return
     */
    Long createOrgInfo(PositionStoreInfoDTO positionStoreInfo, String countryCode);

    Long createOrgInfoWithStoreLogs(PositionStoreInfoDTO positionStoreInfo, String countryCode,
                                    List<StoreChangeLogDto> allStoreLogs, Long salesTime);

    /**
     * 处理组织信息
     */
    IntlSoOrgInfo handleOrgInfo(Optional<StoreChangeLogDto> storeChannelLogOpt,
                                Optional<RmsStoreInfoDto> storeInfoOpt,
                                Optional<RmsPositionInfoRes> positionInfoOpt,
                                Optional<IntlRetailerDTO> retailerOpt,
                                Object data, // 使用Object以兼容不同类型的data
                                List<String> abnormalList);

    /**
     * 填充基础字段
     */
    void fillBaseFields(IntlSoOrgInfo intlSoOrgInfo, Object data);

    /**
     * 处理门店信息
     */
    void handleStoreInfo(IntlSoOrgInfo intlSoOrgInfo,
                         Optional<RmsStoreInfoDto> storeInfoOpt,
                         Optional<StoreChangeLogDto> storeChannelLogOpt,
                         Object data,
                         List<String> abnormalList);

    /**
     * 从DTO填充门店信息
     */
    void fillStoreInfoFromDto(IntlSoOrgInfo intlSoOrgInfo, RmsStoreInfoDto dto);

    /**
     * 从日志填充门店信息
     */
    void fillStoreInfoFromLog(IntlSoOrgInfo intlSoOrgInfo, StoreChangeLogDto logDto);

    /**
     * 处理阵地信息
     */
    void handlePositionInfo(IntlSoOrgInfo intlSoOrgInfo,
                            Optional<RmsPositionInfoRes> positionInfoOpt,
                            Object data,
                            List<String> abnormalList);

    /**
     * 处理零售商信息
     */
    void handleRetailerInfo(IntlSoOrgInfo intlSoOrgInfo,
                            Optional<IntlRetailerDTO> retailerOpt,
                            Object data,
                            List<String> abnormalList);
}
