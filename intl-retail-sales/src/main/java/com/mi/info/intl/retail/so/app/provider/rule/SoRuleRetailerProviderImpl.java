package com.mi.info.intl.retail.so.app.provider.rule;

import javax.annotation.Resource;

import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import com.mi.info.intl.retail.bean.PageDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.dto.SoRuleRetailerDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.provider.SoRuleRetailerProvider;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.request.QuerySuRuleRetailerReq;
import com.mi.info.intl.retail.intlretail.util.AppProviderUtil;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.so.domain.rule.service.IntlSoRuleRetailerService;
import com.xiaomi.mone.docs.annotations.dubbo.ApiDoc;
import com.xiaomi.mone.docs.annotations.dubbo.ApiModule;
import com.xiaomi.youpin.infra.rpc.Result;

import lombok.extern.slf4j.Slf4j;

/**
 * 因此，规则零售商提供商Impl
 *
 * <AUTHOR>
 * @date 2025/07/31
 */
@Slf4j
@Service
@DubboService(group = "${center.dubbo.group:}", interfaceClass = SoRuleRetailerProvider.class, validation = "true")
@ApiModule(value = "国际渠道零售服务", apiInterface = SoRuleRetailerProvider.class)
public class SoRuleRetailerProviderImpl implements SoRuleRetailerProvider {

    @Resource
    private IntlSoRuleRetailerService intlSoRuleRetailerService;

    /**
     * 按条件导出零售商，并返回文件路径excel下载地址
     *
     * @param req req
     * @return {@link Result }<{@link String }>
     */
    @ApiDoc(description = "按条件导出零售商，并返回文件路径excel下载地址", value = "/api/so/v1/exportRetailerList")
    @Override
    public CommonApiResponse<String> exportRetailerList(QuerySuRuleRetailerReq req) {

        return AppProviderUtil.wrap(log, "SoRuleRetailerProvider::exportRetailerList", req,
            intlSoRuleRetailerService::exportRetailerList, false);
    }

    /**
     * 根据条件获取零售商
     *
     * @param req req
     * @return {@link CommonApiResponse }<{@link PageDTO }<{@link SoRuleRetailerDTO }>>
     */
    @ApiDoc(description = "根据条件获取零售商", value = "/api/so/v1/getRetailerByCondition")
    @Override
    public CommonApiResponse<PageDTO<SoRuleRetailerDTO>> getRetailerByCondition(QuerySuRuleRetailerReq req) {
        return AppProviderUtil.wrap(log, "SoRuleRetailerProvider::getRetailerByCondition", req,
            intlSoRuleRetailerService::getRetailerByCondition, false);
    }
}
