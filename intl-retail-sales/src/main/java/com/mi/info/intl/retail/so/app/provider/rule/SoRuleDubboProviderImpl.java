package com.mi.info.intl.retail.so.app.provider.rule;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import com.mi.info.intl.retail.bean.PageDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.dto.*;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.provider.SoRuleDubboProvider;
import com.mi.info.intl.retail.intlretail.util.AppProviderUtil;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.so.domain.rule.aggregate.SoRuleAggregateService;
import com.xiaomi.mone.docs.annotations.dubbo.ApiDoc;
import com.xiaomi.mone.docs.annotations.dubbo.ApiModule;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2025/7/24 19:16
 */
@Slf4j
@Service
@DubboService(group = "${center.dubbo.group:}", interfaceClass = SoRuleDubboProvider.class)
@ApiModule(value = "国际渠道零售服务", apiInterface = SoRuleDubboProvider.class)
public class SoRuleDubboProviderImpl implements SoRuleDubboProvider {

    @Resource
    private SoRuleAggregateService soRuleAggregateService;

    /**
     * 获取规则列表
     *
     * @param soRuleQueryDto 因此，规则查询DTO
     * @return {@link CommonApiResponse }<{@link PageDTO }<{@link SoRuleDetailResultDTO }>>
     */
    @ApiDoc(description = "获取规则列表", value = "/api/so/v1/getRuleList")
    @Override
    public CommonApiResponse<PageDTO<SoRuleDetailResultDTO>> getRuleList(SoRuleDetailQueryDTO soRuleQueryDto) {
        return AppProviderUtil.wrap(log, "SoRuleDubboProvider::getRuleList", soRuleQueryDto,
            soRuleAggregateService::getRuleList, false);
    }

    /**
     * 创建规则
     *
     * @param soRuleDetailCreateDto 因此，规则细节创建DTO
     * @return {@link CommonApiResponse }<{@link Long }>
     */
    @ApiDoc(description = "创建规则", value = "/api/so/v1/createRule")
    @Override
    public CommonApiResponse<Long> createRule(@NotNull SoRuleDetailCreateDTO soRuleDetailCreateDto) {
        return AppProviderUtil.wrap(log, "SoRuleDubboProvider::createRule", soRuleDetailCreateDto,
            soRuleAggregateService::createRuleFlow, false);
    }

    /**
     * 验证规则零售商
     * @deprecated 废弃，后续删除，使用 {@link SoRuleDubboProviderImpl#validateRule(SoRuleValidateDTO)}
     * @param retailerValidateDto 校验参数
     * @return {@link CommonApiResponse }<{@link Boolean }>
     */
    @Deprecated
    @ApiDoc(description = "验证规则零售商", value = "/api/so/v1/validateRuleRetailers")
    @Override
    public CommonApiResponse<Boolean> validateRuleRetailers(SoRuleValidateDTO retailerValidateDto) {
        return AppProviderUtil.wrap(log, "SoRuleDubboProvider::validateRuleRetailers", retailerValidateDto,
            soRuleAggregateService::validateRuleRetailers, false);
    }

    /**
     * 校验规则
     * @param ruleValidateDto 请求参数
     * @return {@link CommonApiResponse }<{@link SoRuleValidateResultDTO }>
     */
    @ApiDoc(description = "校验规则", value = "/api/so/v1/validateRule")
    @Override
    public CommonApiResponse<SoRuleValidateResultDTO> validateRule(SoRuleValidateDTO ruleValidateDto) {
        return AppProviderUtil.wrap(log, "SoRuleDubboProvider::validateRuleRetailers", ruleValidateDto,
                soRuleAggregateService::validateRule, false);
    }

    /**
     * 修改规则
     *
     * @param soRuleDetailModifyDto 因此，规则细节修改DTO
     * @return {@link CommonApiResponse }<{@link Long }>
     */
    @ApiDoc(description = "修改规则", value = "/api/so/v1/modifyRule")
    @Override
    public CommonApiResponse<Long> modifyRule(@NotNull SoRuleDetailModifyDTO soRuleDetailModifyDto) {
        return AppProviderUtil.wrap(log, "SoRuleDubboProvider::modifyRule", soRuleDetailModifyDto,
            soRuleAggregateService::modifyRuleFlow, false);
    }

    /**
     * 获取规则详情
     * 
     * @param getDto 规则明细id
     * @return {@link CommonApiResponse }<{@link SoRuleDetailResultDTO }>
     */
    @ApiDoc(description = "获取规则详情", value = "/api/so/v1/getRuleDetail")
    @Override
    public CommonApiResponse<SoRuleDetailResultDTO> getRuleDetail(SoRuleDetailGetDTO getDto) {
        return AppProviderUtil.wrap(log, "SoRuleDubboProvider::getRuleDetail", getDto.getId(),
            soRuleAggregateService::getRuleDetail, false);
    }

    /**
     * so规则修改审批流程撤回
     * 
     * @param soRuleDetailModifyRecallDto 修改参数
     * @return {@link CommonApiResponse }<{@link Void }>
     */
    @ApiDoc(description = "规则修改审批流程撤回", value = "/api/so/v1/modifyRuleRecall")
    @Override
    public CommonApiResponse<Long> modifyRuleRecall(SoRuleDetailModifyRecallDTO soRuleDetailModifyRecallDto) {
        return AppProviderUtil.wrap(log, "SoRuleDubboProvider::modifyRuleRecall", soRuleDetailModifyRecallDto,
            soRuleAggregateService::approveRecalled, false);
    }
}
