package com.mi.info.intl.retail.so.domain.upload.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.io.Serializable;

/**
 * 省份
 *
 * <AUTHOR>
 * @since 2025-07-31 10:27:22
 */
@TableName(value = "intl_rms_province")
@Data
public class IntlRmsProvince  implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 国家ID
     */
    @TableField(value = "country_id")
    private String countryId;
    /**
     * 国家名称
     */
    @TableField(value = "country_name")
    private String countryName;
    /**
     * 国家编码
     */
    @TableField(value = "country_code")
    private String countryCode;
    /**
     * 国家短编码
     */
    @TableField(value = "country_shortcode")
    private String countryShortcode;
    /**
     * 省份ID
     */
    @TableField(value = "province_id")
    private String provinceId;
    /**
     * 省份名称
     */
    @TableField(value = "province_name")
    private String provinceName;
    /**
     * 省份编码
     */
    @TableField(value = "province_code")
    private String provinceCode;
    /**
     * 是否启用（0：可用；1 停用）
     */
    @TableField(value = "status")
    private Integer status;
    /**
     * 创建人mid
     */
    @TableField(value = "created_by")
    private Long createdBy;

    /**
     * 创建时间戳
     */
    @TableField(value = "created_on")
    private Long createdOn;

    /**
     * 修改人mid
     */
    @TableField(value = "modified_by")
    private Long modifiedBy;

    /**
     * 修改时间戳
     */
    @TableField(value = "modified_on")
    private Long modifiedOn;

    /**
     * 写入时间
     */
    @TableField(value = "created_at")
    private Long createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at")
    private Long updatedAt;
}

