package com.mi.info.intl.retail.so.domain.datasync.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.mi.info.intl.retail.exception.BizException;
import com.mi.info.intl.retail.intlretail.service.api.mq.RmsSyncSoDataService;
import com.mi.info.intl.retail.intlretail.service.api.mq.dto.RmsSyncDataRequest;
import com.mi.info.intl.retail.service.DistributionLock;
import com.mi.info.intl.retail.service.DistributionLockService;
import com.mi.info.intl.retail.so.app.mq.dto.RmsStockDataSyncDto;
import com.mi.info.intl.retail.so.app.mq.dto.RmsSyncImeiData;
import com.mi.info.intl.retail.so.app.mq.dto.RmsSyncQtyData;
import com.mi.info.intl.retail.so.domain.datasync.dto.StockDataSyncReqDto;
import com.mi.info.intl.retail.so.domain.datasync.enums.DataSyncDataTypeEnum;
import com.mi.info.intl.retail.so.domain.datasync.enums.DataSyncOperateTypeTypeEnum;
import com.mi.info.intl.retail.so.domain.datasync.enums.SyncStatusEnum;
import com.mi.info.intl.retail.so.domain.datasync.service.RmsStockDataSyncService;
import com.mi.info.intl.retail.so.infra.database.mapper.datasync.RmsStockDataSyncMapper;
import com.mi.info.intl.retail.utils.redis.RedisClient;
import com.mi.info.intl.retail.utils.redis.RedisKeyEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;

/**
 * RMS库存数据同步服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class RmsStockDataSyncServiceImpl implements RmsStockDataSyncService {

    @Resource
    private RmsStockDataSyncMapper rmsStockDataSyncMapper;

    @NacosValue(value = "${intl-retail.rmsSoDataSync.acceptRmsMaxSize:500}", autoRefreshed = true)
    private Integer batchSendMqSize;

    @NacosValue(value = "${intl-retail.rmsSoDataSync.stockSoMaxQuerySize:5000}", autoRefreshed = true)
    private Integer queryMaxSize;

    @Resource
    private RedisClient redisClient;
    @Resource
    private RmsSyncSoDataService rmsSyncSoDataService;

    private static final String ID_NEW_FIELD = "idNew";

    @Resource
    private DistributionLockService distributionLockService;

    /**
     * 同步库存 IMEI 数据
     * <p>
     * 通过调用 processSyncData 方法，使用 IMEI 相关的查询函数和处理器，完成 IMEI 数据的批量同步。
     * </p>
     */
    @Override
    public void syncStockImeiData(Integer querySize, Integer isUpdate) {
        long start = System.currentTimeMillis();
        //分布式锁防止重复执行
        try (DistributionLock ignored = distributionLockService.tryLockNoArgs(
                RedisKeyEnum.LOCK_SYNC_DATA_IMEI_TASK.getKey())) {
            processSyncData(RedisKeyEnum.SYNC_IMEI_DATA_LAST_ID, this::processImeiDataBatch,
                    rmsStockDataSyncMapper::getImeiMinId, rmsStockDataSyncMapper::getImeiMaxId,
                    rmsStockDataSyncMapper::queryStockImeiDataResult, querySize, isUpdate);
        }
        log.info("syncStockImeiData 耗时：{}", System.currentTimeMillis() - start);
    }

    /**
     * 同步库存数量(Qty)数据
     * <p>
     * 通过调用 processSyncData 方法，使用 Qty 相关的查询函数和处理器，完成 Qty 数据的批量同步。
     * </p>
     */
    @Override
    public void syncStockQtyData(Integer querySize, Integer isUpdate) {
        long start = System.currentTimeMillis();
        //分布式锁防止重复执行
        try (DistributionLock ignored = distributionLockService.tryLockNoArgs(
                RedisKeyEnum.LOCK_SYNC_DATA_QTY_TASK.getKey())) {
            processSyncData(RedisKeyEnum.SYNC_QTY_DATA_LAST_ID, this::processQtyDataBatch,
                    rmsStockDataSyncMapper::getQtyMinId, rmsStockDataSyncMapper::getQtyMaxId,
                    rmsStockDataSyncMapper::queryStockQtyDataResult, querySize, isUpdate);
        }
        log.info("同步Qty数据结束，耗时：{}", System.currentTimeMillis() - start);
    }

    /**
     * 通用数据同步处理方法（性能优化版）
     *
     * @param redisKey Redis键，用于记录上次同步ID
     * @param processor 数据处理器，用于处理一批数据
     * @param minIdSupplier 最小ID获取器
     * @param maxIdSupplier 最大ID获取器
     * @param queryFunction 数据查询函数
     * @param querySize 查询大小
     * @param <T> 数据类型
     */
    private <T> void processSyncData(RedisKeyEnum redisKey, DataProcessor<T> processor, IdSupplier minIdSupplier,
                                     IdSupplier maxIdSupplier, DataQueryFunction<T> queryFunction, Integer querySize,
                                     Integer isUpdate) {
        log.info("into processSyncData");

        String redisRes = redisClient.get(redisKey.get());
        Long lastSyncId;
        try {
            lastSyncId = StringUtils.isEmpty(redisRes) ? safeDecrement(minIdSupplier.get()) : parseLongSafely(redisRes);
        } catch (IllegalArgumentException e) {
            log.error("processSyncData 解析最后同步 ID 失败", e);
            return;
        }

        Long maxId = maxIdSupplier.get();
        if (maxId <= lastSyncId) {
            log.info("processSyncData {} 没有可同步数据", redisKey.get());
            return;
        }

        if (ObjectUtils.isEmpty(querySize) || querySize <= 0) {
            querySize = queryMaxSize;
        }

        RmsStockDataSyncDto syncDto = new RmsStockDataSyncDto()
                .setLastSyncId(lastSyncId)
                .setBatchSize(querySize)
                .setIsUpdate(isUpdate);

        List<T> dataList = queryFunction.query(syncDto);

        if (CollectionUtils.isNotEmpty(dataList)) {
            final int updateMode = 1;
            if (isUpdate != null && isUpdate == updateMode) {
                for (T t : dataList) {
                    try {
                        processor.process(Collections.singletonList(t));
                    } catch (Exception e) {
                        log.error("processSyncData 单条处理失败", e);
                        throw new BizException("处理单条数据失败>>>" + e.getMessage());
                    }
                }
            } else {
                List<List<T>> dataLists = CollectionUtil.split(dataList, batchSendMqSize);
                for (List<T> list : dataLists) {
                    try {
                        processor.process(list);
                    } catch (Exception e) {
                        log.error("processSyncData 批量处理失败", e);
                        throw new BizException("处理数据批次失败>>>" + e.getMessage());
                    }
                }
            }

            // 统一更新 Redis 最后同步 ID
            redisClient.set(redisKey.get(), String.valueOf(getLastId(dataList)));
        }
    }

    private Long safeDecrement(Long value) {
        if (value == Long.MIN_VALUE) {
            log.warn("minIdSupplier 返回 Long.MIN_VALUE，无法安全减一");
            return value; // 或抛异常
        }
        return value - 1;
    }

    /**
     * 处理 IMEI 数据批次
     *
     * @param list IMEI 数据列表
     */
    private void processImeiDataBatch(List<RmsSyncImeiData> list) {
        sendMqData(list, DataSyncDataTypeEnum.IMEI);
    }

    /**
     * 处理 Qty 数据批次
     *
     * @param list Qty 数据列表
     */
    private void processQtyDataBatch(List<RmsSyncQtyData> list) {
        sendMqData(list, DataSyncDataTypeEnum.QTY);
    }

    /**
     * 发送数据到 MQ
     *
     * @param dataList 数据列表
     * @param dataType 数据类型（IMEI 或 Qty）
     */
    private void sendMqData(List<?> dataList, DataSyncDataTypeEnum dataType) {
        if (dataList == null || dataList.isEmpty()) {
            return;
        }

        List<JSONObject> jsonObjects = new ArrayList<>(dataList.size());

        for (Object data : dataList) {
            // 使用FastJSON序列化，并配置不输出null值
            String jsonString = JSON.toJSONString(data, SerializerFeature.WriteMapNullValue,
                    SerializerFeature.WriteNullStringAsEmpty,
                    SerializerFeature.WriteNullNumberAsZero,
                    SerializerFeature.WriteNullBooleanAsFalse);
            JSONObject jsonObject = JSON.parseObject(jsonString);
            jsonObjects.add(jsonObject);
        }
        List<String> rmsIdList = new ArrayList<>(jsonObjects.size());
        jsonObjects.forEach(e -> {
            rmsIdList.add(e.getString("rmsId"));
        });
        log.info("RmsStockDataSyncServiceImpl sendMqData, dataType: {}, rmsIdList: {}", dataType, rmsIdList);
        RmsSyncDataRequest request = new RmsSyncDataRequest()
                .setDataList(jsonObjects)
                .setType(dataType.getMessage())
                .setOperateType(DataSyncOperateTypeTypeEnum.CREATE.getMessage());

        List<Long> idList = jsonObjects.stream()
                .map(obj -> obj.getLong(ID_NEW_FIELD))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        StockDataSyncReqDto stockDataSyncReqDto = new StockDataSyncReqDto()
                .setSyncStatus(SyncStatusEnum.SYNCING.getCode())
                .setSyncStartTime(LocalDateTime.now())
                .setIdList(idList);

        try {
            // 更新同步状态为同步中
            if (dataType.equals(DataSyncDataTypeEnum.IMEI)) {
                rmsStockDataSyncMapper.updateImeiSyncStatus(stockDataSyncReqDto);
            } else {
                rmsStockDataSyncMapper.updateQtySyncStatus(stockDataSyncReqDto);
            }
            //单条走单条发送的方法，并使用update保证一定会进行更新
            if (dataList.size() == 1) {
                request.setOperateType(DataSyncOperateTypeTypeEnum.REPORT_VERIFICATION.getMessage());
                rmsSyncSoDataService.syncRmsSoData(request);
                return;
            }
            rmsSyncSoDataService.bathSyncRmsSoData(request);
        } catch (Exception e) {
            throw new RuntimeException("同步数据过程中发生异常", e);
        }
    }

    /**
     * 安全地解析字符串为 Long 类型
     *
     * @param str 字符串
     * @return Long 值
     * @throws IllegalArgumentException 如果解析失败
     */
    private Long parseLongSafely(String str) {
        if (StringUtils.isEmpty(str)) {
            log.warn("Redis 中 last ID 为空，无法解析");
            throw new IllegalArgumentException("Redis 中 last ID 为空");
        }
        try {
            return NumberUtils.createLong(str);
        } catch (NumberFormatException e) {
            log.warn("Failed to parse Long value: {}", str);
            throw new IllegalArgumentException("Redis 中 last ID 解析失败: " + str, e);
        }
    }

    /**
     * 获取列表中最后一个元素的 ID
     *
     * @param list 数据列表
     * @return 最后一个元素的 ID
     */
    private Long getLastId(List<?> list) {
        if (list.get(list.size() - 1) instanceof RmsSyncImeiData) {
            return ((RmsSyncImeiData) list.get(list.size() - 1)).getIdNew();
        } else if (list.get(list.size() - 1) instanceof RmsSyncQtyData) {
            return ((RmsSyncQtyData) list.get(list.size() - 1)).getIdNew();
        }
        return 0L;
    }

    /**
     * 数据处理器函数式接口
     *
     * @param <T> 数据类型
     */
    @FunctionalInterface
    private interface DataProcessor<T> {
        void process(List<T> list);
    }

    /**
     * ID 获取器函数式接口
     */
    @FunctionalInterface
    private interface IdSupplier {
        Long get();
    }

    /**
     * 数据查询函数式接口
     *
     * @param <T> 数据类型
     */
    @FunctionalInterface
    private interface DataQueryFunction<T> {
        List<T> query(RmsStockDataSyncDto dto);
    }
}
