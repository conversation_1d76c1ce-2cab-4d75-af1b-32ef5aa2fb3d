package com.mi.info.intl.retail.so.domain.datasync.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 1新增 2修改
 */
@Getter
@AllArgsConstructor
public enum DataSyncOperateTypeTypeEnum {

    CREATE(1, "create"),

    UPDATE(2, "update"),

    REPORT_VERIFICATION(3, "report verification"),

    ACTIVATE_VERIFICATION(4, "activate verification"),

    ;

    private int code;

    private String message;

    public static DataSyncOperateTypeTypeEnum getEnumByMsg(String message) {
        for (DataSyncOperateTypeTypeEnum value : DataSyncOperateTypeTypeEnum.values()) {
            if (value.getMessage().equals(message)) {
                return value;
            }
        }
        return null;
    }

}
