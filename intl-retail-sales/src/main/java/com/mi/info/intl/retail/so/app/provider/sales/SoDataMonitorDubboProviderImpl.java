package com.mi.info.intl.retail.so.app.provider.sales;

import java.util.Collections;
import java.util.LinkedList;
import java.util.List;

import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.mi.info.intl.retail.exception.BizException;
import com.mi.info.intl.retail.intlretail.service.api.so.SoDataMonitorService;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.ImeiMonitorDto;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.QtyMonitorDto;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoImei;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoQty;
import com.mi.info.intl.retail.so.domain.upload.service.IntlSoImeiService;
import com.mi.info.intl.retail.so.domain.upload.service.IntlSoQtyService;
import com.xiaomi.mone.docs.annotations.dubbo.ApiModule;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@DubboService(group = "${dubbo-group.provider.intl-retail:}", interfaceClass = SoDataMonitorService.class)
@ApiModule(value = "国际渠道零售服务", apiInterface = SoDataMonitorService.class)
public class SoDataMonitorDubboProviderImpl implements SoDataMonitorService {

    private static final Integer MAX_BATCH_SIZE = 500;

    @Autowired
    private IntlSoImeiService intlSoImeiService;
    @Autowired
    private IntlSoQtyService intlSoQtyService;


    private void volidate(List<String> rmsIdList) {
        if (rmsIdList == null || rmsIdList.isEmpty()) {
            throw new BizException("rmsIdList is empty");
        }
        if (rmsIdList.size() > MAX_BATCH_SIZE) {
            throw new BizException("batch size is too large");
        }
    }

    @Override
    public List<ImeiMonitorDto> getImeiList(List<String> rmsIdList) {
        volidate(rmsIdList);
        List<IntlSoImei> imeiList = intlSoImeiService.batchGetByRmsId(rmsIdList);
        if (imeiList == null || imeiList.isEmpty()) {
            return Collections.emptyList();
        }
        List<ImeiMonitorDto> rtlist = new LinkedList<>();

        imeiList.forEach(imei -> {
            rtlist.add(convert(imei));
        });
        return rtlist;
    }

    @Override
    public List<QtyMonitorDto> getQtyList(List<String> rmsIdList) {
        volidate(rmsIdList);
        List<IntlSoQty> qtyList = intlSoQtyService.getByRmsIds(rmsIdList);
        if (qtyList == null || qtyList.isEmpty()) {
            return Collections.emptyList();
        }
        List<QtyMonitorDto> rtlist = new LinkedList<>();

        qtyList.forEach(qty -> {
            rtlist.add(convert(qty));
        });
        return rtlist;
    }


    private ImeiMonitorDto convert(IntlSoImei imei) {
        return ImeiMonitorDto.builder()
                .rmsId(imei.getRmsId())
                .retailId(imei.getId())
                .siVerifyResult(imei.getSiVerifyResult())
                .verificationResult(imei.getVerificationResult())
                .statusCode(imei.getStatus())
                .build();
    }

    private QtyMonitorDto convert(IntlSoQty qty) {
        return QtyMonitorDto.builder()
                .rmsId(qty.getRmsId())
                .retailId(qty.getId())
                .statusCode(qty.getStatus())
                .build();
    }


}
