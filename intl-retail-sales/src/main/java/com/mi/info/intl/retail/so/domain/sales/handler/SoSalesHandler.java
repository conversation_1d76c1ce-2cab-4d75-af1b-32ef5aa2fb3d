package com.mi.info.intl.retail.so.domain.sales.handler;

import com.fasterxml.jackson.core.type.TypeReference;
import com.mi.info.intl.retail.api.fieldforce.user.UserApiService;
import com.mi.info.intl.retail.api.fieldforce.user.dto.IntlRmsUserNewDto;
import com.mi.info.intl.retail.dto.LabelValueDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.sys.dto.DictSysDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.sys.request.DictSysRequest;
import com.mi.info.intl.retail.so.domain.sales.constant.SalesConstant;
import com.mi.info.intl.retail.so.domain.sales.enums.SalesDictEnum;
import com.mi.info.intl.retail.so.domain.sys.service.IntlSysDictService;
import com.mi.info.intl.retail.so.domain.upload.config.SalesQueryPageConfig;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoImei;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoQty;
import com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlSoImeiMapper;
import com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlSoQtyMapper;
import com.mi.info.intl.retail.utils.JsonUtil;
import com.mi.info.intl.retail.utils.redis.RedisClient;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 功能描述：
 *
 * <AUTHOR>
 * @date 2025/8/1
 */
@Component
public class SoSalesHandler {
    
    @Resource
    private IntlSysDictService intlSysDictService;
    
    @Resource
    private UserApiService userService;
    
    @Resource
    private IntlSoImeiMapper intlSoImeiMapper;
    
    @Resource
    private IntlSoQtyMapper intlSoQtyMapper;

    @Resource
    private SalesQueryPageConfig salesQueryPageConfig;

    @Resource
    private RedisClient redisClient;
    
    public Map<String, List<LabelValueDTO>> getLabelValueList4Sales() {
        String salesDictKey = SalesConstant.CACHE_SALES_DICT_KEY;
        String salesDictSwitchCache = salesQueryPageConfig.getSalesDictSwitchCache();
        if (SalesConstant.SWITCH_YES.equals(salesDictSwitchCache)) {
            String salesDictStr = redisClient.get(salesDictKey);
            if (StringUtils.isNotEmpty(salesDictStr)) {
                return JsonUtil.json2map(salesDictStr, new TypeReference<Map<String, List<LabelValueDTO>>>() {});
            }
        }
        DictSysRequest dictRequest = new DictSysRequest();
        List<DictSysDTO> dictCodeList = new ArrayList<>();
        for (SalesDictEnum salesDictEnum : SalesDictEnum.values()) {
            DictSysDTO dictSysDTO = new DictSysDTO();
            dictSysDTO.setDictType(salesDictEnum.getType());
            dictSysDTO.setDictCode(salesDictEnum.getCode());
            dictCodeList.add(dictSysDTO);
        }
        dictRequest.setDictCodeList(dictCodeList);
        Map<String, List<LabelValueDTO>> salesDictMap = intlSysDictService.getLabelValueListByDictCode(dictRequest);
        if (SalesConstant.SWITCH_YES.equals(salesDictSwitchCache)) {
            // 设置字典缓存
            redisClient.set(salesDictKey, JsonUtil.bean2json(salesDictMap), salesQueryPageConfig.getSalesDictExpireTime());
        }
        return salesDictMap;
    }
    
    public Map<Long, IntlRmsUserNewDto> getRmsUserListByMiIds(List<Long> miIds) {
        if (CollectionUtils.isEmpty(miIds)) {
            return new HashMap<>();
        }
        Optional<List<IntlRmsUserNewDto>> rmsUserOpt = userService.getUserListByMiIds(miIds);
        if (rmsUserOpt.isPresent()) {
            List<IntlRmsUserNewDto> intlRmsUserNewDtoList = rmsUserOpt.get();
            if (CollectionUtils.isNotEmpty(intlRmsUserNewDtoList)) {
                Map<Long, IntlRmsUserNewDto> rmsUserNewMap = new HashMap<>();
                for (IntlRmsUserNewDto rmsUserNewDto : intlRmsUserNewDtoList) {
                    rmsUserNewMap.put(rmsUserNewDto.getMiId(), rmsUserNewDto);
                }
                return rmsUserNewMap;
            }
        }
        return new HashMap<>();
    }
    
    public Map<Long, IntlSoImei> getIntlSoImeiListByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new HashMap<>();
        }
        List<IntlSoImei> intlSoImeiList = intlSoImeiMapper.selectBatchIds(ids);
        if (CollectionUtils.isNotEmpty(intlSoImeiList)) {
            return intlSoImeiList.stream().collect(Collectors.toMap(IntlSoImei::getId, Function.identity()));
        }
        return new HashMap<>();
    }
    
    public Map<Long, IntlSoQty> getIntlSoQtyListByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new HashMap<>();
        }
        List<IntlSoQty> intlSoQtyList = intlSoQtyMapper.selectBatchIds(ids);
        if (CollectionUtils.isNotEmpty(intlSoQtyList)) {
            return intlSoQtyList.stream().collect(Collectors.toMap(IntlSoQty::getId, Function.identity()));
        }
        return new HashMap<>();
    }
    
    
}
