package com.mi.info.intl.retail.so.infra.database.mapper.upload;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlImportLog;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【intl_import_log(导入日志表)】的数据库操作Mapper
* @createDate 2025-08-08 10:48:23
* @Entity com.mi.info.intl.retail.so.domain.upload.entity.IntlImportLog
*/
public interface IntlImportLogMapper extends BaseMapper<IntlImportLog> {

    /**
     * 根据ID查询单个导入日志
     *
     * @param logId 日志ID
     * @return 导入日志
     */
    IntlImportLog selectById(@Param("logId") Long logId);

    /**
     * 分页查询导入日志列表
     *
     * @param page 分页参数
     * @param taskName 任务名称（模糊查询）
     * @param dataSource 数据源列表
     * @param status 状态列表
     * @param importDuration 导入耗时列表
     * @param operationStartTime 操作开始时间
     * @param operationEndTime 操作结束时间
     * @param importTypes 导入类型列表
     * @param createdBy 创建人miId
     * @return 分页查询结果
     */
    Page<IntlImportLog> selectImportLogList(Page<IntlImportLog> page,
                                           @Param("taskName") String taskName,
                                           @Param("dataSource") java.util.List<String> dataSource,
                                           @Param("status") java.util.List<Integer> status,
                                           @Param("importDuration") java.util.List<Integer> importDuration,
                                           @Param("operationStartTime") Long operationStartTime,
                                           @Param("operationEndTime") Long operationEndTime,
                                           @Param("importTypes") java.util.List<Integer> importTypes,
                                           @Param("createdBy") Long createdBy);

}




