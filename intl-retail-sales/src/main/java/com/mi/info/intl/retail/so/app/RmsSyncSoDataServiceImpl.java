package com.mi.info.intl.retail.so.app;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.google.common.collect.Lists;
import com.mi.info.intl.retail.exception.BizException;
import com.mi.info.intl.retail.intlretail.service.api.mq.RmsSyncSoDataService;
import com.mi.info.intl.retail.intlretail.service.api.mq.dto.RmsSyncDataRequest;
import com.mi.info.intl.retail.intlretail.service.api.retailer.dto.CommonResponse;
import com.mi.info.intl.retail.so.app.mq.dto.RmsSyncSoDataReqDto;
import com.xiaomi.cnzone.commons.exception.CommonBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@DubboService(group = "${center.dubbo.group:}", version = "1.0.0", interfaceClass = RmsSyncSoDataService.class)
public class RmsSyncSoDataServiceImpl implements RmsSyncSoDataService {

    @Autowired
    private RocketMQTemplate rocketMQTemplate;

    @Value("${intl-retail.rocketmq.so-sync.topic}")
    private String topic;

    @NacosValue(value = "${intl-retail.rmsSoDataSync.batchSendMqSize:500}", autoRefreshed = true)
    private Integer batchSendMqSize;

    @NacosValue(value = "${intl-retail.rmsSoDataSync.acceptRmsMaxSize:500}", autoRefreshed = true)
    private Integer acceptRmsMaxSize;

    @NacosValue(value = "${intl-retail.rmsSoDataSync.batchSaveSize:100}", autoRefreshed = true)
    private Integer batchSaveSize;

    static {
        JSON.DEFAULT_GENERATE_FEATURE |= SerializerFeature.WriteMapNullValue.getMask();
    }

    /**
     * RMS调用时，使用批量发送单条消息，单个消费
     *
     * @param request
     * @return
     */
    @Override
    public CommonResponse<String> syncRmsSoData(RmsSyncDataRequest request) {
        log.info("syncRmsSoData get request, type={}, operateType={}, dataList size={},request={}",
                request.getType(), request.getOperateType(), CollectionUtils.size(request.getDataList()), request);
        List<JSONObject> dataList = request.getDataList();
        int dataSize = CollectionUtils.size(dataList);
        if (dataSize == 0 || dataSize > acceptRmsMaxSize) {
            log.error("dataList size 异常，dataSize: {}", dataSize);
            throw new BizException("dataList error");
        }
        List<RmsSyncSoDataReqDto> dataReqDtoList = convertRmsSyncSoDataList(dataList, request);
        List<List<RmsSyncSoDataReqDto>> partitionList = Lists.partition(dataReqDtoList, batchSendMqSize);
        long startTime = System.currentTimeMillis();
        partitionList.forEach(rmsSyncSoDataReqDtos -> {
            List<Message<List<RmsSyncSoDataReqDto>>> messages = rmsSyncSoDataReqDtos.stream()
                    .map(data -> MessageBuilder.withPayload(Collections.singletonList(data)).build())
                    .collect(Collectors.toList());
            try {
                SendResult sendResult = rocketMQTemplate.syncSend(topic, messages);
                log.info("消息发送结果：{}", sendResult);
            } catch (Exception e) {
                log.error("发送 syncRmsSoData 数据失败，topic={}, error={}", topic, e.getMessage(), e);
                throw e;
            }
        });
        long endTime = System.currentTimeMillis();
        log.info("syncRmsSoData 完成{}条数据的发送，耗费时间：{}毫秒", dataList.size(), endTime - startTime);
        return new CommonResponse<>("success");
    }

    /**
     * 同步存量时，使用批量发送，批量消费，提升速度
     *
     * @param request
     * @return
     */
    @Override
    public CommonResponse bathSyncRmsSoData(RmsSyncDataRequest request) {
        log.info("bathSyncRmsSoData get request, type={}, operateType={}, dataList size={},request={}",
                request.getType(), request.getOperateType(), CollectionUtils.size(request.getDataList()), request);
        List<JSONObject> dataList = request.getDataList();
        int dataSize = CollectionUtils.size(dataList);
        if (dataSize == 0 || dataSize > acceptRmsMaxSize) {
            log.error("dataList size invalid: {}", dataSize);
            throw new BizException("dataList exception");
        }
        List<RmsSyncSoDataReqDto> dataReqDtoList = convertRmsSyncSoDataList(dataList, request);
        List<List<RmsSyncSoDataReqDto>> partitionList = Lists.partition(dataReqDtoList, batchSendMqSize);
        long startTime = System.currentTimeMillis();
        partitionList.forEach(rmsSyncSoDataReqDtos -> {
            //每批100条处理
            List<List<RmsSyncSoDataReqDto>> batchList = Lists.partition(rmsSyncSoDataReqDtos, batchSaveSize);
            List<Message<List<RmsSyncSoDataReqDto>>> messages = batchList.stream()
                    .map(data -> MessageBuilder.withPayload(data).build())
                    .collect(Collectors.toList());
            try {
                SendResult sendResult = rocketMQTemplate.syncSend(topic, messages);
                log.info("消息发送结果：{}", sendResult);
            } catch (Exception e) {
                log.error("发送 syncRmsSoData 数据失败，topic={}, error={}", topic, e.getMessage(), e);
                throw e;
            }
        });
        long endTime = System.currentTimeMillis();
        log.info("syncRmsSoData 完成{}条数据的发送，耗费时间：{}毫秒", dataList.size(), endTime - startTime);
        return new CommonResponse<>("success");
    }

    private List<RmsSyncSoDataReqDto> convertRmsSyncSoDataList(List<JSONObject> dataList,
                                                               RmsSyncDataRequest request) {
        if (CollectionUtils.isEmpty(dataList)) {
            return Lists.newArrayList();
        }
        return dataList.stream().map(data -> {
            RmsSyncSoDataReqDto item = new RmsSyncSoDataReqDto();
            item.setData(data);
            item.setType(request.getType());
            item.setOperateType(request.getOperateType());
            item.setFields(request.getFields());
            return item;
        }).collect(Collectors.toList());
    }

}
