package com.mi.info.intl.retail.so.domain.upload.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.excel.util.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.info.intl.retail.api.fieldforce.retailer.dto.IntlRetailerDTO;
import com.mi.info.intl.retail.api.front.dto.RmsPositionInfoRes;
import com.mi.info.intl.retail.api.front.dto.RmsStoreInfoDto;
import com.mi.info.intl.retail.api.front.position.dto.PositionStoreInfoDTO;
import com.mi.info.intl.retail.api.front.store.dto.StoreChangeLogDto;
import com.mi.info.intl.retail.so.app.mq.dto.RmsSyncImeiData;
import com.mi.info.intl.retail.so.app.mq.dto.RmsSyncQtyData;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoOrgInfo;
import com.mi.info.intl.retail.so.domain.upload.service.IntlSoOrgInfoService;
import com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlSoOrgInfoMapper;
import com.xiaomi.keycenter.org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【intl_so_org_info(销量阵地信息)】的数据库操作Service实现
 * @createDate 2025-07-25 16:40:50
 */
@Service
public class IntlSoOrgInfoServiceImpl extends ServiceImpl<IntlSoOrgInfoMapper, IntlSoOrgInfo>
        implements IntlSoOrgInfoService {

    @Override
    public List<IntlSoOrgInfo> batchGetByIds(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return Collections.emptyList();
        }
        return baseMapper.selectBatchIds(idList);
    }

    @Override
    public Map<Long, IntlSoOrgInfo> batchGetByIdsMap(List<Long> idList) {
        List<IntlSoOrgInfo> list = baseMapper.selectBatchIds(idList);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }
        return list.stream().collect(Collectors.toMap(IntlSoOrgInfo::getId, item -> item));
    }

    @Override
    public Long createOrgInfo(PositionStoreInfoDTO positionStoreInfo, String countryCode) {
        // 先查询是否存在完全相同的数据
        QueryWrapper<IntlSoOrgInfo> queryWrapper = buildQueryWrapper(positionStoreInfo, countryCode, 0L);
        IntlSoOrgInfo existingOrgInfo = getOne(queryWrapper);

        if (existingOrgInfo != null) {
            return existingOrgInfo.getId();
        }

        // 创建新的门店信息
        IntlSoOrgInfo orgInfo = getIntlSoOrgInfo(positionStoreInfo, countryCode);
        save(orgInfo);
        return orgInfo.getId();
    }

    private Long createOrgInfoWithStoreLog(PositionStoreInfoDTO positionStoreInfo, String countryCode,
                                           StoreChangeLogDto storeLog) {
        PositionStoreInfoDTO finalPositionStoreInfo;
        if (storeLog != null) {
            // 使用门店日志信息更新PositionStoreInfoDTO
            finalPositionStoreInfo = updatePositionStoreInfoFromLog(positionStoreInfo, storeLog);
        } else {
            // 使用原始门店信息
            finalPositionStoreInfo = positionStoreInfo;
        }

        // 创建新的门店信息
        IntlSoOrgInfo orgInfo = getIntlSoOrgInfo(finalPositionStoreInfo, countryCode);
        save(orgInfo);
        return orgInfo.getId();
    }

    /**
     * 根据销售时间从门店变更日志中创建门店信息
     */
    @Override
    public Long createOrgInfoWithStoreLogs(PositionStoreInfoDTO positionStoreInfo, String countryCode,
                                           List<StoreChangeLogDto> allStoreLogs, Long salesTime) {
        // 根据销售时间匹配对应的门店日志
        StoreChangeLogDto matchedLog = findMatchedStoreLog(allStoreLogs, salesTime);

        // 直接传入匹配的日志（可能为null）
        return createOrgInfoWithStoreLog(positionStoreInfo, countryCode, matchedLog);
    }

    /**
     * 从门店变更日志列表中找到销售时间之前最近的一条日志
     */
    private StoreChangeLogDto findMatchedStoreLog(List<StoreChangeLogDto> storeLogs, Long salesTime) {
        if (CollectionUtils.isEmpty(storeLogs) || salesTime == null) {
            return null;
        }
        for (StoreChangeLogDto log : storeLogs) {
            if (log.getEffectiveTime() != null && log.getEffectiveTime() <= salesTime) {
                return log;
            }
        }
        return null;
    }

    /**
     * 构建查询条件
     */
    private QueryWrapper<IntlSoOrgInfo> buildQueryWrapper(PositionStoreInfoDTO dto, String countryCode,
                                                          Long storeLogId) {
        QueryWrapper<IntlSoOrgInfo> queryWrapper = new QueryWrapper<>();

        queryWrapper.eq("store_changelog_id", storeLogId)
                .eq("store_id", getValueOrDefault(dto.getStoreId(), 0L))
                .eq("store_rms_code", getValueOrDefault(dto.getStoreCode(), ""))
                .eq("store_grade", getValueOrDefault(dto.getStoreGrade(), 0))
                .eq("store_type", getValueOrDefault(dto.getStoreType(), 0))
                .eq("store_channel_type", getValueOrDefault(dto.getStoreChannelType(), 0))
                .eq("store_hasSR", getValueOrDefault(dto.getStoreHasSR(), 0))
                .eq("store_hasPC", getValueOrDefault(dto.getStoreHasPC(), 0))
                .eq("country_code", getValueOrDefault(countryCode, ""))
                .eq("position_changelog_id", 0L)
                .eq("position_id", getValueOrDefault(dto.getPositionId(), 0L))
                .eq("position_rms_code", getValueOrDefault(dto.getPositionCode(), ""))
                .eq("position_type", getValueOrDefault(dto.getPositionType(), 0))
                .eq("retailer_id", getValueOrDefault(dto.getRetailerId(), 0L))
                .eq("retailer_code", getValueOrDefault(dto.getRetailerCode(), ""))
                .eq("store_class", getValueOrDefault(dto.getStoreClass(), 0))
                .eq("store_code", getValueOrDefault(dto.getCrssCode(), ""))
                .eq("position_code", getValueOrDefault(dto.getCrpsCode(), ""))
                .eq("store_status", getValueOrDefault(dto.getStoreStatus(), 0))
                .last("limit 1");

        return queryWrapper;
    }

    @NotNull
    private IntlSoOrgInfo getIntlSoOrgInfo(PositionStoreInfoDTO dto, String countryCode) {
        IntlSoOrgInfo orgInfo = new IntlSoOrgInfo();

        orgInfo.setStoreChangelogId(0L);
        orgInfo.setStoreId(getValueOrDefault(dto.getStoreId(), 0));
        orgInfo.setStoreCode(getValueOrDefault(dto.getCrssCode(), ""));
        orgInfo.setStoreGrade(getValueOrDefault(dto.getStoreGrade(), 0));
        orgInfo.setStoreType(getValueOrDefault(dto.getStoreType(), 0));
        orgInfo.setStoreChannelType(getValueOrDefault(dto.getStoreChannelType(), 0));
        orgInfo.setStoreHasSR(getValueOrDefault(dto.getStoreHasSR(), 0));
        orgInfo.setStoreHasPC(getValueOrDefault(dto.getStoreHasPC(), 0));
        orgInfo.setCountryCode(getValueOrDefault(countryCode, ""));
        orgInfo.setPositionChangelogId(0L);
        orgInfo.setPositionId(getValueOrDefault(dto.getPositionId(), 0));
        orgInfo.setPositionCode(getValueOrDefault(dto.getCrpsCode(), ""));
        orgInfo.setPositionType(getValueOrDefault(dto.getPositionType(), 0));
        orgInfo.setRetailerId(getValueOrDefault(dto.getRetailerId(), 0L));
        orgInfo.setRetailerCode(getValueOrDefault(dto.getRetailerCode(), ""));
        orgInfo.setPositionRmsCode(getValueOrDefault(dto.getPositionCode(), ""));
        orgInfo.setStoreRmsCode(getValueOrDefault(dto.getStoreCode(), ""));
        orgInfo.setStoreClass(getValueOrDefault(dto.getStoreClass(), 0));
        orgInfo.setStoreStatus(getValueOrDefault(dto.getStoreStatus(), 0));

        return orgInfo;
    }

    /**
     * 处理null值的通用方法
     */
    private static <T> T getValueOrDefault(T value, T defaultValue) {
        return value != null ? value : defaultValue;
    }

    /**
     * 使用门店日志信息更新PositionStoreInfoDTO
     */
    private PositionStoreInfoDTO updatePositionStoreInfoFromLog(PositionStoreInfoDTO original,
                                                                StoreChangeLogDto storeLog) {
        PositionStoreInfoDTO updated = new PositionStoreInfoDTO();

        // 复制原始信息
        BeanUtil.copyProperties(original, updated);

        // 解析门店等级、类型、渠道类型等信息，使用门店日志中的信息覆盖门店相关字段
        updated.setStoreGrade(storeLog.getStoreGrade() != 0 ? storeLog.getStoreGrade() : original.getStoreGrade());
        updated.setStoreType(storeLog.getStoreType() != 0 ? storeLog.getStoreType() : original.getStoreType());
        updated.setStoreChannelType(storeLog.getStoreChannelType() != 0 ? storeLog.getStoreChannelType() :
                original.getStoreChannelType());
        updated.setStoreHasPC(storeLog.getHasPc());
        updated.setStoreHasSR(storeLog.getHasSr());
        updated.setStoreStatus(
                storeLog.getStoreStatus() != 0 ? storeLog.getStoreStatus() : original.getStoreStatus());
        updated.setStoreClass(storeLog.getStoreClass() != 0 ? storeLog.getStoreClass() : original.getStoreClass());

        return updated;
    }

    @Override
    public IntlSoOrgInfo handleOrgInfo(Optional<StoreChangeLogDto> storeChannelLogOpt,
                                       Optional<RmsStoreInfoDto> storeInfoOpt,
                                       Optional<RmsPositionInfoRes> positionInfoOpt,
                                       Optional<IntlRetailerDTO> retailerOpt,
                                       Object data,
                                       List<String> abnormalList) {
        IntlSoOrgInfo intlSoOrgInfo = new IntlSoOrgInfo();

        // 填充基础字段
        fillBaseFields(intlSoOrgInfo, data);

        // 处理门店信息
        handleStoreInfo(intlSoOrgInfo, storeInfoOpt, storeChannelLogOpt, data, abnormalList);

        // 处理阵地信息
        handlePositionInfo(intlSoOrgInfo, positionInfoOpt, data, abnormalList);

        // 处理零售商信息
        handleRetailerInfo(intlSoOrgInfo, retailerOpt, data, abnormalList);

        return intlSoOrgInfo;
    }

    @Override
    public void fillBaseFields(IntlSoOrgInfo intlSoOrgInfo, Object data) {
        if (data instanceof RmsSyncQtyData) {
            RmsSyncQtyData qtyData = (RmsSyncQtyData) data;
            intlSoOrgInfo.setStoreCode(ObjectUtils.defaultIfNull(qtyData.getStoreCodeNew(), ""));
            intlSoOrgInfo.setStoreRmsCode(ObjectUtils.defaultIfNull(qtyData.getStoreCodeRMS(), ""));
            intlSoOrgInfo.setPositionRmsCode(ObjectUtils.defaultIfNull(qtyData.getPositionCodeRMS(), ""));
            intlSoOrgInfo.setPositionCode(ObjectUtils.defaultIfNull(qtyData.getPositionCodeNew(), ""));
            intlSoOrgInfo.setRetailerCode(ObjectUtils.defaultIfNull(qtyData.getRetailerCode(), ""));
        } else if (data instanceof RmsSyncImeiData) {
            RmsSyncImeiData imeiData = (RmsSyncImeiData) data;
            intlSoOrgInfo.setStoreCode(ObjectUtils.defaultIfNull(imeiData.getStoreCodeNew(), ""));
            intlSoOrgInfo.setStoreRmsCode(ObjectUtils.defaultIfNull(imeiData.getStoreCodeRMS(), ""));
            intlSoOrgInfo.setPositionRmsCode(ObjectUtils.defaultIfNull(imeiData.getPositionCodeRMS(), ""));
            intlSoOrgInfo.setPositionCode(ObjectUtils.defaultIfNull(imeiData.getPositionCodeNew(), ""));
            intlSoOrgInfo.setRetailerCode(ObjectUtils.defaultIfNull(imeiData.getRetailerCode(), ""));
        }
    }

    @Override
    public void handleStoreInfo(IntlSoOrgInfo intlSoOrgInfo,
                                Optional<RmsStoreInfoDto> storeInfoOpt,
                                Optional<StoreChangeLogDto> storeChannelLogOpt,
                                Object data,
                                List<String> abnormalList) {
        if (storeInfoOpt.isPresent()) {
            fillStoreInfoFromDto(intlSoOrgInfo, storeInfoOpt.get());
        } else {
            String storeCodeRms = null;
            if (data instanceof RmsSyncQtyData) {
                storeCodeRms = ((RmsSyncQtyData) data).getStoreCodeRMS();
            } else if (data instanceof RmsSyncImeiData) {
                storeCodeRms = ((RmsSyncImeiData) data).getStoreCodeRMS();
            }
            abnormalList.add("未查询到门店信息，storeCodeRms：" + (storeCodeRms == null ? "" : storeCodeRms));
        }

        // 增量同步如果日志表有，优先从日志表取，否则实时从门店表取，存量同步不走日志表，实时查门店表
        if (storeChannelLogOpt.isPresent()) {
            fillStoreInfoFromLog(intlSoOrgInfo, storeChannelLogOpt.get());
        }
    }

    @Override
    public void fillStoreInfoFromDto(IntlSoOrgInfo intlSoOrgInfo, RmsStoreInfoDto dto) {
        intlSoOrgInfo.setStoreType(ObjectUtils.defaultIfNull(dto.getType(), 0));
        intlSoOrgInfo.setStoreGrade(ObjectUtils.defaultIfNull(dto.getGrade(), 0));
        intlSoOrgInfo.setStoreHasPC(ObjectUtils.defaultIfNull(dto.getHasPc(), 0));
        intlSoOrgInfo.setStoreHasSR(ObjectUtils.defaultIfNull(dto.getHasSr(), 0));
        intlSoOrgInfo.setStoreChannelType(ObjectUtils.defaultIfNull(dto.getChannelType(), 0));
        intlSoOrgInfo.setCountryCode(ObjectUtils.defaultIfNull(dto.getCountryShortcode(), ""));
        intlSoOrgInfo.setStoreClass(ObjectUtils.defaultIfNull(dto.getStoreClass(), 0));
        intlSoOrgInfo.setStoreStatus(ObjectUtils.defaultIfNull(dto.getOperationStatus(), 0));
        intlSoOrgInfo.setStoreId(ObjectUtils.defaultIfNull(dto.getId(), 0));
    }

    @Override
    public void fillStoreInfoFromLog(IntlSoOrgInfo intlSoOrgInfo, StoreChangeLogDto logDto) {
        intlSoOrgInfo.setStoreHasPC(ObjectUtils.defaultIfNull(logDto.getHasPc(), 0));
        intlSoOrgInfo.setStoreHasSR(ObjectUtils.defaultIfNull(logDto.getHasSr(), 0));
        intlSoOrgInfo.setStoreChangelogId(ObjectUtils.defaultIfNull(logDto.getId(), 0L));

        // 只有当log中的值非0时才更新，否则保持原有值
        intlSoOrgInfo.setStoreType(
                ObjectUtils.defaultIfNull(logDto.getStoreType(), 0) != 0
                        ? logDto.getStoreType()
                        : intlSoOrgInfo.getStoreType()
        );

        intlSoOrgInfo.setStoreGrade(
                ObjectUtils.defaultIfNull(logDto.getStoreGrade(), 0) != 0
                        ? logDto.getStoreGrade()
                        : intlSoOrgInfo.getStoreGrade()
        );

        intlSoOrgInfo.setStoreChannelType(
                ObjectUtils.defaultIfNull(logDto.getStoreChannelType(), 0) != 0
                        ? logDto.getStoreChannelType()
                        : intlSoOrgInfo.getStoreChannelType()
        );

        intlSoOrgInfo.setStoreClass(
                ObjectUtils.defaultIfNull(logDto.getStoreClass(), 0) != 0
                        ? logDto.getStoreClass()
                        : intlSoOrgInfo.getStoreClass()
        );

        intlSoOrgInfo.setStoreStatus(
                ObjectUtils.defaultIfNull(logDto.getStoreStatus(), 0) != 0
                        ? logDto.getStoreStatus()
                        : intlSoOrgInfo.getStoreStatus()
        );
    }

    @Override
    public void handlePositionInfo(IntlSoOrgInfo intlSoOrgInfo,
                                   Optional<RmsPositionInfoRes> positionInfoOpt,
                                   Object data,
                                   List<String> abnormalList) {
        if (positionInfoOpt.isPresent()) {
            RmsPositionInfoRes positionInfo = positionInfoOpt.get();
            intlSoOrgInfo.setPositionType(ObjectUtils.defaultIfNull(positionInfo.getType(), 0));
            intlSoOrgInfo.setPositionId(ObjectUtils.defaultIfNull(positionInfo.getId(), 0).intValue());
            if (StringUtils.isEmpty(intlSoOrgInfo.getCountryCode())) {
                intlSoOrgInfo.setCountryCode(ObjectUtils.defaultIfNull(positionInfo.getCountryShortcode(), ""));
            }
        } else {
            String positionCodeRms = null;
            if (data instanceof RmsSyncQtyData) {
                positionCodeRms = ((RmsSyncQtyData) data).getPositionCodeRMS();
            } else if (data instanceof RmsSyncImeiData) {
                positionCodeRms = ((RmsSyncImeiData) data).getPositionCodeRMS();
            }
            abnormalList.add("未查询到阵地信息，positionCodeRms：" + positionCodeRms);
        }
    }

    @Override
    public void handleRetailerInfo(IntlSoOrgInfo intlSoOrgInfo,
                                   Optional<IntlRetailerDTO> retailerOpt,
                                   Object data,
                                   List<String> abnormalList) {
        if (retailerOpt.isPresent()) {
            IntlRetailerDTO retailer = retailerOpt.get();
            intlSoOrgInfo.setRetailerId(ObjectUtils.defaultIfNull(retailer.getId(), 0L));
        } else {
            String retailerCode = null;
            if (data instanceof RmsSyncQtyData) {
                retailerCode = ((RmsSyncQtyData) data).getRetailerCode();
            } else if (data instanceof RmsSyncImeiData) {
                retailerCode = ((RmsSyncImeiData) data).getRetailerCode();
            }
            abnormalList.add("未查询到零售商信息，retailerCode：" + retailerCode);
        }
    }
}




