package com.mi.info.intl.retail.so.infra.database.mapper.datasync;

import com.mi.info.intl.retail.so.app.mq.dto.RmsStockDataSyncDto;
import com.mi.info.intl.retail.so.app.mq.dto.RmsSyncImeiData;
import com.mi.info.intl.retail.so.app.mq.dto.RmsSyncQtyData;
import com.mi.info.intl.retail.so.app.mq.dto.StockRmsSyncImeiData;
import com.mi.info.intl.retail.so.app.mq.dto.StockRmsSyncQtyData;
import com.mi.info.intl.retail.so.domain.datasync.dto.StockDataSyncReqDto;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface RmsStockDataSyncMapper {

    Long getImeiMinId();

    Long getImeiMaxId();

    int updateImeiSyncStatus(StockDataSyncReqDto syncReqDto);

    Long getQtyMinId();

    Long getQtyMaxId();

    void updateQtySyncStatus(StockDataSyncReqDto stockDataSyncReqDto);

    List<RmsSyncImeiData> queryStockImeiDataResult(RmsStockDataSyncDto dto);

    List<RmsSyncQtyData> queryStockQtyDataResult(RmsStockDataSyncDto dto);

}
