package com.mi.info.intl.retail.so.domain.sales.enums;

import cn.hutool.extra.spring.SpringUtil;
import com.google.common.collect.Lists;
import com.mi.info.intl.retail.bean.PageDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.dto.SoRuleRetailerDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.request.QuerySuRuleRetailerReq;
import com.mi.info.intl.retail.intlretail.service.api.so.sales.dto.SearchReferenceReqDto;
import com.mi.info.intl.retail.intlretail.service.api.so.sales.dto.SearchReferenceRespDto;
import com.mi.info.intl.retail.so.domain.rule.service.IntlSoRuleDetailService;
import com.mi.info.intl.retail.so.domain.rule.service.IntlSoRuleRetailerService;
import com.mi.info.intl.retail.so.infra.database.dataobject.sales.SearchReference;
import com.mi.info.intl.retail.so.infra.database.mapper.sales.SearchReferenceMapper;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 搜索参考枚举
 *
 * <AUTHOR>
 * @date 2025/8/1
 */

public enum SearchReferenceEnum {

    INTL_RMS_POSITION {
        @Override
        public List<SearchReferenceRespDto> getSearchReferencesData(SearchReferenceReqDto reqDto) {
            List<SearchReference> searchReferenceList = SEARCH_REFERENCE_MAPPER.getSearchReferencesFromIntlRmsPosition(reqDto.getKeyWord());
            return transform2SearchReferenceRespDtoList(searchReferenceList);
        }
    },
    INTL_RMS_STORE {
        @Override
        public List<SearchReferenceRespDto> getSearchReferencesData(SearchReferenceReqDto reqDto) {
            List<SearchReference> searchReferenceList = SEARCH_REFERENCE_MAPPER.getSearchReferencesFromIntlRmsStore(reqDto.getKeyWord());
            return transform2SearchReferenceRespDtoList(searchReferenceList);
        }
    },

    INTL_RMS_USER {
        @Override
        public List<SearchReferenceRespDto> getSearchReferencesData(SearchReferenceReqDto reqDto) {
            List<SearchReference> searchReferenceList = SEARCH_REFERENCE_MAPPER.getSearchReferencesFromIntlRmsUser(reqDto.getKeyWord(),
                    reqDto.getCountryCode());
            return transform2SearchReferenceRespDtoList(searchReferenceList);
        }
    },

    INTL_RMS_RETAILER {
        @Override
        public List<SearchReferenceRespDto> getSearchReferencesData(SearchReferenceReqDto reqDto) {
            List<SearchReference> searchReferenceList = SEARCH_REFERENCE_MAPPER.getSearchReferencesFromIntlRmsRetailer(reqDto.getKeyWord());
            return transform2SearchReferenceRespDtoList(searchReferenceList);
        }
    },

    INTL_RMS_CITY {
        @Override
        public List<SearchReferenceRespDto> getSearchReferencesData(SearchReferenceReqDto reqDto) {
            List<SearchReference> searchReferenceList = SEARCH_REFERENCE_MAPPER.getSearchReferencesFromIntlRmsCity(reqDto.getKeyWord(), reqDto.getParentKey());
            return transform2SearchReferenceRespDtoList(searchReferenceList);
        }
    },

    INTL_RMS_PRODUCT {
        @Override
        public List<SearchReferenceRespDto> getSearchReferencesData(SearchReferenceReqDto reqDto) {
            List<SearchReference> searchReferenceList = SEARCH_REFERENCE_MAPPER.getSearchReferencesFromIntlIntlRmsProduct(reqDto.getKeyWord());
            return transform2SearchReferenceRespDtoList(searchReferenceList);
        }
    },

    INTL_SO_RULE_RETAILERS {
        @Override
        public List<SearchReferenceRespDto> getSearchReferencesData(SearchReferenceReqDto reqDto) {
            QuerySuRuleRetailerReq req = new QuerySuRuleRetailerReq();
            req.setPageNum(1);
            req.setPageSize(100);
            req.setRetailerName(reqDto.getKeyWord());
            req.setCountryCode(reqDto.getCountryCode());
            req.setType(reqDto.getType());
            PageDTO<SoRuleRetailerDTO> pageList = INTL_SO_RULE_RETAILER_SERVICE.getRetailerByCondition(req);
            if (CollectionUtils.isEmpty(pageList.getRecords())) {
                return Lists.newArrayList();
            }
            return pageList.getRecords().stream().map(t -> {
                SearchReferenceRespDto dto = new SearchReferenceRespDto();
                dto.setId(t.getRetailerCode());
                dto.setTitle(t.getRetailerName());
                return dto;
            }).collect(Collectors.toList());
        }
    },

    INTL_RMS_PRODUCT_SPU_NAME_EN {
        @Override
        public List<SearchReferenceRespDto> getSearchReferencesData(SearchReferenceReqDto reqDto) {
            List<SearchReference> searchReferenceList = SEARCH_REFERENCE_MAPPER.getSearchReferencesForSpuNameEn(reqDto.getKeyWord());
            return transform2SearchReferenceRespDtoListFromName(searchReferenceList);
        }
    },

    INTL_SO_RULE_DETAIL {
        @Override
        public List<SearchReferenceRespDto> getSearchReferencesData(SearchReferenceReqDto reqDto) {
            List<SearchReference> searchReferenceList = INTL_SO_RULE_DETAIL_SERVICE.getCreatorPairList(reqDto.getKeyWord());
            return transform2SearchReferenceRespDtoList(searchReferenceList);
        }
    },

    INTL_RMS_PRODUCT_SKU {
        @Override
        public List<SearchReferenceRespDto> getSearchReferencesData(SearchReferenceReqDto reqDto) {
            List<SearchReference> searchReferenceList = SEARCH_REFERENCE_MAPPER.getSkuFromIntlRmsProduct(reqDto.getKeyWord());
            return transform2SearchReferenceRespDtoListFromName(searchReferenceList);
        }
    },
    INTL_RMS_PRODUCT_SHORT_NAME {
        @Override
        public List<SearchReferenceRespDto> getSearchReferencesData(SearchReferenceReqDto reqDto) {
            List<SearchReference> searchReferenceList = SEARCH_REFERENCE_MAPPER.getShortNameFromIntlRmsProduct(reqDto.getKeyWord());
            return transform2SearchReferenceRespDtoListFromName(searchReferenceList);
        }
    },
    INTL_RMS_PRODUCT_CATEGORY_EN {
        @Override
        public List<SearchReferenceRespDto> getSearchReferencesData(SearchReferenceReqDto reqDto) {
            List<SearchReference> searchReferenceList = SEARCH_REFERENCE_MAPPER.getCategoryEnFromIntlRmsProduct(reqDto.getKeyWord());
            return transform2SearchReferenceRespDtoListFromName(searchReferenceList);
        }
    },
    INTL_SO_SN_BLACKLIST {
        @Override
        public List<SearchReferenceRespDto> getSearchReferencesData(SearchReferenceReqDto reqDto) {
            List<SearchReference> searchReferenceList = SEARCH_REFERENCE_MAPPER.getSearchReferencesFromIntlSnBlackListUser(reqDto.getKeyWord());
            return transform2SearchReferenceRespDtoList(searchReferenceList);
        }
    },
    INTL_SO_PLAINTEXT_IMEI {
        @Override
        public List<SearchReferenceRespDto> getSearchReferencesData(SearchReferenceReqDto reqDto) {
            List<SearchReference> searchReferenceList = SEARCH_REFERENCE_MAPPER.getSearchReferencesFromIntlSnBlackListUser(reqDto.getKeyWord());
            return transform2SearchReferenceRespDtoList(searchReferenceList);
        }
    },
    ;

    private static List<SearchReferenceRespDto> transform2SearchReferenceRespDtoList(List<SearchReference> searchReferenceList) {
        if (CollectionUtils.isEmpty(searchReferenceList)) {
            return new ArrayList<>();
        }
        List<SearchReferenceRespDto> searchReferenceRespDtoList = new ArrayList<>();
        for (SearchReference searchReference : searchReferenceList) {
            SearchReferenceRespDto searchReferenceRespDto = new SearchReferenceRespDto();
            String title = searchReference.getName() + "(" + searchReference.getCode() + ")";
            searchReferenceRespDto.setId(searchReference.getCode());
            searchReferenceRespDto.setTitle(title);
            searchReferenceRespDtoList.add(searchReferenceRespDto);
        }
        return searchReferenceRespDtoList;
    }

    private static List<SearchReferenceRespDto> transform2SearchReferenceRespDtoListFromName(List<SearchReference> searchReferenceList) {
        if (CollectionUtils.isEmpty(searchReferenceList)) {
            return new ArrayList<>();
        }
        List<SearchReferenceRespDto> searchReferenceRespDtoList = new ArrayList<>();
        for (SearchReference searchReference : searchReferenceList) {
            SearchReferenceRespDto searchReferenceRespDto = new SearchReferenceRespDto();
            searchReferenceRespDto.setId(searchReference.getName());
            searchReferenceRespDto.setTitle(searchReference.getName());
            searchReferenceRespDtoList.add(searchReferenceRespDto);
        }
        return searchReferenceRespDtoList;
    }

    private static final SearchReferenceMapper SEARCH_REFERENCE_MAPPER = SpringUtil.getBean(SearchReferenceMapper.class);

    private static final IntlSoRuleRetailerService INTL_SO_RULE_RETAILER_SERVICE =
            SpringUtil.getBean(IntlSoRuleRetailerService.class);

    private static final IntlSoRuleDetailService INTL_SO_RULE_DETAIL_SERVICE =
            SpringUtil.getBean(IntlSoRuleDetailService.class);

    public abstract List<SearchReferenceRespDto> getSearchReferencesData(SearchReferenceReqDto reqDto);
}
