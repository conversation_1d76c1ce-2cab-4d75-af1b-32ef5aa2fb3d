package com.mi.info.intl.retail.so.domain.task.rule.syncdata;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.mi.info.intl.retail.so.app.mq.dto.RmsStockDataSyncDto;
import com.mi.info.intl.retail.so.domain.datasync.service.RmsStockDataSyncService;
import com.mi.info.intl.retail.so.infra.database.mapper.datasync.IntlDatasyncLogMapper;
import com.xiaomi.nr.job.core.context.JobHelper;
import com.xiaomi.nr.job.core.handler.annotation.NrJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class SyncRmsSoDataJobHandler {

    @Resource
    private RmsStockDataSyncService rmsStockDataSyncService;

    @Resource
    private IntlDatasyncLogMapper intlDatasyncLogMapper;

    private static final Integer SAVE_DAYS = 90;

    @NrJob("syncRmsImeiDataJobHandler")
    public void syncRmsImeiDataJobHandler() {
        String jobParam = JobHelper.getJobParam();
        log.info("into syncRmsImeiDataJobHandler,jobParam:{}", jobParam);
        //单词查询数量，可配置参数传入，不配置时取nacos配置
        Integer querySize = null;
        //是否为差额（更新）同步，1：差额，0/null新增
        Integer isUpdate = null;
        if (CharSequenceUtil.isNotBlank(jobParam)) {
            RmsStockDataSyncDto rmsStockDataSyncDto = JSONUtil.toBean(jobParam, RmsStockDataSyncDto.class);
            querySize = rmsStockDataSyncDto.getQuerySize();
            isUpdate = rmsStockDataSyncDto.getIsUpdate();
        }
        log.info("syncRmsImeiDataJobHandler 开始执行");
        rmsStockDataSyncService.syncStockImeiData(querySize, isUpdate);
        log.info("syncRmsImeiDataJobHandler 执行成功");
    }

    @NrJob("syncRmsQtyDataJobHandler")
    public void syncRmsQtyDataJobHandler() {
        String jobParam = JobHelper.getJobParam();
        log.info("into syncRmsQtyDataJobHandler,jobParam:{}", jobParam);
        //单词查询数量，可配置参数传入，不配置时取nacos配置
        Integer querySize = null;
        //是否为差额（更新）同步，1：差额，0/null新增
        Integer isUpdate = null;
        if (CharSequenceUtil.isNotBlank(jobParam)) {
            RmsStockDataSyncDto rmsStockDataSyncDto = JSONUtil.toBean(jobParam, RmsStockDataSyncDto.class);
            querySize = rmsStockDataSyncDto.getQuerySize();
            isUpdate = rmsStockDataSyncDto.getIsUpdate();
        }
        log.info("syncRmsQtyDataJobHandler 开始执行");
        rmsStockDataSyncService.syncStockQtyData(querySize, isUpdate);
        log.info("syncRmsQtyDataJobHandler 执行成功");
    }

    @NrJob("cleanDataSyncLogs")
    public void cleanDataSyncLogs() {
        String jobParam = JobHelper.getJobParam();
        JSONObject bean = JSONUtil.toBean(jobParam, JSONObject.class);
        Integer saveDays = bean.getInteger("saveDays");
        log.info("cleanDataSyncLogs 开始执行");
        intlDatasyncLogMapper.deleteOldLogs(ObjectUtils.isEmpty(saveDays) ? SAVE_DAYS : saveDays);
        log.info("cleanDataSyncLogs 执行成功");
    }

}
