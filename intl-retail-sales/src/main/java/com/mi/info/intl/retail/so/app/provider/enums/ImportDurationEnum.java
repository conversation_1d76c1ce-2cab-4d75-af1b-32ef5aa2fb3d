package com.mi.info.intl.retail.so.app.provider.enums;

import lombok.Getter;

/**
 * 导入耗时枚举
 *
 * <AUTHOR>
 * @date 2025/1/28
 */
@Getter
public enum ImportDurationEnum {
    
    /**
     * 0-30秒
     */
    DURATION_0_30S(1, "0-30s", 0, 30),
    
    /**
     * 30秒-1分钟
     */
    DURATION_30S_1MIN(2, "30s-1min", 30, 60),
    
    /**
     * 1分钟-5分钟
     */
    DURATION_1MIN_5MIN(3, "1min-5min", 60, 300),
    
    /**
     * 5分钟-10分钟
     */
    DURATION_5MIN_10MIN(4, "5min-10min", 300, 600),
    
    /**
     * 超过10分钟
     */
    DURATION_OVER_10MIN(5, ">10min", 600, Integer.MAX_VALUE);
    
    /**
     * 枚举值
     */
    private final Integer value;
    
    /**
     * 描述
     */
    private final String description;
    
    /**
     * 最小秒数
     */
    private final Integer minSeconds;
    
    /**
     * 最大秒数
     */
    private final Integer maxSeconds;
    
    ImportDurationEnum(Integer value, String description, Integer minSeconds, Integer maxSeconds) {
        this.value = value;
        this.description = description;
        this.minSeconds = minSeconds;
        this.maxSeconds = maxSeconds;
    }
    
    /**
     * 根据耗时秒数获取对应的枚举
     *
     * @param durationSeconds 耗时秒数
     * @return 对应的枚举值
     */
    public static ImportDurationEnum getByDurationSeconds(Integer durationSeconds) {
        if (durationSeconds == null || durationSeconds < 0) {
            return DURATION_0_30S;
        }
        
        for (ImportDurationEnum duration : values()) {
            if (durationSeconds >= duration.getMinSeconds() && durationSeconds < duration.getMaxSeconds()) {
                return duration;
            }
        }
        
        return DURATION_OVER_10MIN;
    }
    
    /**
     * 根据耗时毫秒数获取对应的枚举
     *
     * @param durationMillis 耗时毫秒数
     * @return 对应的枚举值
     */
    public static ImportDurationEnum getByDurationMillis(Long durationMillis) {
        if (durationMillis == null || durationMillis < 0) {
            return DURATION_0_30S;
        }
        
        Integer durationSeconds = (int) (durationMillis / 1000);
        return getByDurationSeconds(durationSeconds);
    }
} 