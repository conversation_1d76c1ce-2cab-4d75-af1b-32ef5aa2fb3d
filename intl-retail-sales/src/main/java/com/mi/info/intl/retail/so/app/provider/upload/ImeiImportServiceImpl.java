package com.mi.info.intl.retail.so.app.provider.upload;

import com.alibaba.fastjson.JSON;
import com.mi.info.intl.retail.api.front.dto.RmsStoreInfoDto;
import com.mi.info.intl.retail.api.front.position.IntlPositionApiService;
import com.mi.info.intl.retail.api.front.position.dto.StoreValidationInfoDTO;
import com.mi.info.intl.retail.api.so.rule.req.GetRetailerSoRuleReq;
import com.mi.info.intl.retail.core.utils.EasyExcelUtil;
import com.mi.info.intl.retail.intlretail.service.api.fds.FdsService;
import com.mi.info.intl.retail.intlretail.service.api.fds.dto.FdsUploadResult;
import com.mi.info.intl.retail.intlretail.service.api.so.ImeiReportVerifyService;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.*;
import com.mi.info.intl.retail.intlretail.service.api.so.upload.ImeiImportService;
import com.mi.info.intl.retail.intlretail.service.api.so.upload.ImeiUploadService;
import com.mi.info.intl.retail.intlretail.service.api.so.upload.dto.ImeiImportRequest;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.so.app.provider.enums.ReportingTypeEnum;
import com.mi.info.intl.retail.so.app.provider.enums.StoreOperationStatusEnums;
import com.mi.info.intl.retail.so.app.provider.upload.dto.ImeiImportExcelData;
import com.mi.info.intl.retail.so.app.provider.upload.dto.StoreValidationContext;
import com.mi.info.intl.retail.so.app.provider.upload.dto.ValidationResult;
import com.mi.info.intl.retail.so.domain.rule.bean.GetRetailerSoRuleResp;
import com.mi.info.intl.retail.so.domain.rule.service.IntlSoRuleRetailerService;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlImportLog;
import com.mi.info.intl.retail.so.domain.upload.service.IntlImportLogService;
import com.mi.info.intl.retail.so.listener.ImeiImportExcelListener;
import com.mi.info.intl.retail.so.util.ExcelFileUtil;
import com.mi.info.intl.retail.so.util.SalesTimeValidUtil;
import com.mi.info.intl.retail.so.util.dto.ExcelFileResource;
import com.mi.info.intl.retail.utils.intl.IntlTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * IMEI数据导入服务实现类
 *
 * <AUTHOR>
 * @date 2025/8/8
 */
@Slf4j
@Service
public class ImeiImportServiceImpl implements ImeiImportService {

    @Resource
    private IntlImportLogService intlImportLogService;

    @Resource
    private EasyExcelUtil easyExcelUtil;

    @Resource
    private FdsService fdsService;

    @Resource
    private IntlSoRuleRetailerService intlSoRuleRetailerService;

    @Resource
    private ImeiReportVerifyService imeiReportVerifyService;

    @Resource
    private ImeiUploadService imeiUploadService;

    @Resource
    private IntlPositionApiService intlPositionApiService;

    // 最大导入行数限制
    private static final int MAX_IMPORT_ROWS = 5000;
    // 文件前缀/后缀
    private static final String FILE_PREFIX = "imei_import_error_";
    private static final String FILE_SUFFIX = ".xlsx";

    // 错误信息常量
    private static final String ERROR_EXCEED_MAX_ROWS = "A maximum of 5,000 pieces of data are allowed to be uploaded.";
    private static final String ERROR_VALIDATION_FAILED = "Validation failed.";
    private static final String ERROR_MANDATORY_FIELDS = "Items in red font are mandatory fields.";
    private static final String ERROR_REPEATED_DATA = "Repeated data.";
    private static final String ERROR_NOT_IN_STORE = "You are not in this store.";
    private static final String ERROR_NO_SO_RULE = "No SO rule.";
    private static final String ERROR_NO_IMEI_RULE = "No imei rule.";
    private static final String ERROR_PRODUCT_VALIDATION_FAILED = "Product validation failed.";
    private static final String ERROR_DUPLICATE_VALIDATION_FAILED = "Duplicate validation failed.";
    private static final String ERROR_LEGALITY_VALIDATION_FAILED = "Legality validation failed.";
    private static final String ERROR_INVALID_DATE_FORMAT = "Invalid sales time format.";
    private static final String ERROR_STORE_NOT_FOUND = "Store Code was not found.";
    private static final String ERROR_STORE_NOT_OPENING = "This store is not open.";
    private static final String ERROR_TEMPLATE_HEADER_MISMATCH = "Please use the template to upload.";

    // IMEI导入模板表头定义（按顺序）
    private static final String[] EXPECTED_HEADERS = {
        "Store Code",
        "Sales Time",
        "IMEI",
        "SN",
        "Remark"
    };


    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonApiResponse<ImportDataResponse> importImeiData(ImeiImportRequest request) {
        log.info("importImeiData start, request: {}", JSON.toJSONString(request));

        try {
            // 1. 参数校验
            if (request.getImportLogId() == null || request.getMiId() == null) {
                return new CommonApiResponse<>(400, "importLogId and miId are required", null);
            }

            // 2. 获取源文件URL
            String sourceFileUrl = getSourceFileUrl(request);
            if (StringUtils.isBlank(sourceFileUrl)) {
                return new CommonApiResponse<>(400, "Source file URL is required", null);
            }

            // 3. 下载文件并校验表头，然后解析Excel数据
            List<ImeiImportExcelData> excelDataList = downloadValidateAndParseExcel(sourceFileUrl);
            if (excelDataList == null) {
                // 表头校验失败
                updateImportLogStatus(request.getImportLogId(), 2, ERROR_TEMPLATE_HEADER_MISMATCH, null);
                return createErrorResponse(request.getImportLogId(), 2, ERROR_TEMPLATE_HEADER_MISMATCH, null);
            }

            // 5. 检查数据量是否超过限制
            if (excelDataList.size() > MAX_IMPORT_ROWS) {
                updateImportLogStatus(request.getImportLogId(), 2, ERROR_EXCEED_MAX_ROWS, null);
                return createErrorResponse(request.getImportLogId(), 2, ERROR_EXCEED_MAX_ROWS, null);
            }

            // 6. 数据校验
            ValidationResult validationResult = validateData(excelDataList, request.getMiId());

            // 7. 统计数据
            int totalCount = validationResult.getDataList().size();
            int failedCount = (int) validationResult.getDataList().stream()
                .filter(data -> StringUtils.isNotBlank(data.getFailedReason()))
                .count();

            // 8. 检查是否有校验失败的数据
            boolean hasErrors = failedCount > 0;

            if (hasErrors) {
                // 生成错误文件并上传
                String errorFileUrl = generateErrorFile(validationResult.getDataList());
                updateImportLogStatus(request.getImportLogId(), 2, ERROR_VALIDATION_FAILED, errorFileUrl, totalCount);
                return createErrorResponse(request.getImportLogId(), 2, ERROR_VALIDATION_FAILED, errorFileUrl, totalCount, failedCount);
            }

            // 9. 创建IMEI数据
            boolean createSuccess = createImeiData(validationResult, request.getImportLogId());

            if (createSuccess) {
                updateImportLogStatus(request.getImportLogId(), 1, null, null, totalCount);
                return createSuccessResponse(request.getImportLogId(), totalCount, failedCount);
            } else {
                updateImportLogStatus(request.getImportLogId(), 2, "IMEI creation failed", null, totalCount);
                return createErrorResponse(request.getImportLogId(), 2, "IMEI creation failed", null, totalCount, failedCount);
            }

        } catch (Exception e) {
            log.error("importImeiData error", e);
            updateImportLogStatus(request.getImportLogId(), 2, "System error: " + e.getMessage(), null);
            return new CommonApiResponse<>(500, "System error: " + e.getMessage(), null);
        }
    }


    /**
     * 获取源文件URL
     */
    private String getSourceFileUrl(ImeiImportRequest request) {
        if (StringUtils.isNotBlank(request.getSourceFileUrl())) {
            return request.getSourceFileUrl();
        }

        // 根据importLogId查询
        IntlImportLog importLog = intlImportLogService.getById(request.getImportLogId());
        return importLog != null ? importLog.getSourceFileUrl() : null;
    }

    /**
     * 下载文件，校验表头，并解析Excel数据
     * @param fileUrl 文件URL
     * @return 解析的数据列表，如果表头校验失败则返回null
     */
    private List<ImeiImportExcelData> downloadValidateAndParseExcel(String fileUrl) throws IOException {
        log.info("开始下载并处理文件: {}", fileUrl);

        try (ExcelFileResource tempFile = ExcelFileUtil.downloadFileFromUrl(fileUrl)) {
            File inputFile = tempFile.getFile();

            // 先校验表头
            if (!ExcelFileUtil.validateExcelHeaders(inputFile, EXPECTED_HEADERS)) {
                log.warn("Excel表头校验失败");
                return null;
            }

            // 表头校验通过，解析数据
            List<ImeiImportExcelData> dataList = new ArrayList<>();
            try (InputStream inputStream = Files.newInputStream(inputFile.toPath())) {
                easyExcelUtil.readFromStream(
                        inputStream,
                        ImeiImportExcelData.class,
                        new ImeiImportExcelListener(dataList),
                        1
                );
            }

            log.info("Excel文件处理完成，共{}行数据", dataList.size());
            return dataList;
        }
    }


    /**
     * 数据校验
     */
    private ValidationResult validateData(List<ImeiImportExcelData> dataList, Long miId) {
        log.info("开始数据校验，数据量: {}", dataList.size());

        // 1. 必填项校验和合法性校验
        validateMandatoryAndLegality(dataList);

        // 2. 重复性校验
        validateDuplicates(dataList);

        // 3. 门店和用户权限校验，返回校验上下文
        Map<String, StoreValidationContext> storeContextMap = validateStoreAndUserPermissions(dataList, miId);

        log.info("数据校验完成");

        ValidationResult result = new ValidationResult();
        result.setDataList(dataList);
        result.setStoreContextMap(storeContextMap);
        return result;
    }

    /**
     * 必填项校验和合法性校验
     */
    private void validateMandatoryAndLegality(List<ImeiImportExcelData> dataList) {
        for (ImeiImportExcelData data : dataList) {
            if (StringUtils.isNotBlank(data.getFailedReason())) {
                continue; // 已经有错误的跳过
            }

            // 必填字段校验
            if (StringUtils.isBlank(data.getStoreCode()) ||
                StringUtils.isBlank(data.getSalesTime()) ||
                (StringUtils.isBlank(data.getImei()) && StringUtils.isBlank(data.getSn()))) {
                data.setFailedReason(ERROR_MANDATORY_FIELDS);
                continue;
            }

            // 销售时间格式校验（这里只做基本格式校验，时区相关校验在门店校验阶段进行）
            if (IntlTimeUtil.parseLocalTimeToTimestamp(null, data.getSalesTime()) == null) {
                data.setFailedReason(ERROR_INVALID_DATE_FORMAT);
            }
        }
    }


    
    /**
     * 重复性校验
     */
    private void validateDuplicates(List<ImeiImportExcelData> dataList) {
        // 记录已出现的IMEI/SN及其第一次出现的信息，用于检测重复
        Map<String, String> seenImeiSnMap = new HashMap<>();

        for (ImeiImportExcelData data : dataList) {
            if (StringUtils.isNotBlank(data.getFailedReason())) {
                continue; // 已经有错误的跳过
            }

            String imeiSn = StringUtils.isNotBlank(data.getImei()) ? data.getImei() : data.getSn();
            if (StringUtils.isNotBlank(imeiSn)) {
                String upperImeiSn = imeiSn.toUpperCase();
                if (seenImeiSnMap.containsKey(upperImeiSn)) {
                    // 发现重复，设置错误信息，包含第一次出现的IMEI/SN
                    String firstOccurrence = seenImeiSnMap.get(upperImeiSn);
                    data.setFailedReason(ERROR_REPEATED_DATA + " First occurrence: " + firstOccurrence);
                } else {
                    seenImeiSnMap.put(upperImeiSn, imeiSn);
                }
            }
        }
    }

    /**
     * 门店和用户权限校验
     */
    private Map<String, StoreValidationContext> validateStoreAndUserPermissions(List<ImeiImportExcelData> dataList, Long miId) {
        // 使用ConcurrentHashMap保证线程安全
        Map<String, StoreValidationContext> storeContextMap = new ConcurrentHashMap<>();

        // 按Store Code分组
        Map<String, List<ImeiImportExcelData>> storeGroups = dataList.stream()
            .filter(data -> StringUtils.isBlank(data.getFailedReason()))
            .collect(Collectors.groupingBy(ImeiImportExcelData::getStoreCode));

        // 并行处理每个门店的数据
        storeGroups.entrySet().parallelStream().forEach(entry -> {
            String storeCode = entry.getKey();
            List<ImeiImportExcelData> storeDataList = entry.getValue();

            StoreValidationContext context = new StoreValidationContext();
            context.setStoreCode(storeCode);

            try {
                // 1. 一次性查询门店信息、用户门店关系和阵地信息
                Optional<StoreValidationInfoDTO> storeValidationInfoOpt = intlPositionApiService.getStoreValidationInfo(storeCode, miId);

                if (!storeValidationInfoOpt.isPresent()) {
                    // 查询门店，进一步判断门店状态
                    RmsStoreInfoDto store = intlPositionApiService.getStoreByCode(storeCode);
                    if (store == null) {
                        markStoreDataAsFailed(storeDataList, ERROR_STORE_NOT_FOUND);
                    } else if (store.getOperationStatus() != StoreOperationStatusEnums.OPENING.getValue()) {
                        markStoreDataAsFailed(storeDataList, ERROR_STORE_NOT_OPENING);
                    } else {
                        markStoreDataAsFailed(storeDataList, ERROR_NOT_IN_STORE);
                    }
                    return; // 并行流中使用return代替continue
                }


                StoreValidationInfoDTO storeValidationInfo = storeValidationInfoOpt.get();
                context.setStoreValidationInfo(storeValidationInfo);

                // 2. 校验销售日期是否小于门店创建时间
                validateSalesTime(storeDataList, storeValidationInfo);

                // 3. 检查是否有最优阵地
                if (storeValidationInfo.getBestPosition() == null) {
                    markStoreDataAsFailed(storeDataList, "No valid position found for store: " + storeCode);
                    return; // 并行流中使用return代替continue
                }

                // 4. 查询IMEI上报规则和执行IMEI校验（移到单独的方法中）
                validateImeiRulesAndReporting(storeDataList, storeValidationInfo, miId, context);

                storeContextMap.put(storeCode, context);
            } catch (Exception e) {
                log.error("门店{}校验失败", storeCode, e);
                markStoreDataAsFailed(storeDataList, "Store validation failed: " + e.getMessage());
            }
        });

        return storeContextMap;
    }

    /**
     * 校验IMEI规则和执行IMEI上报校验
     */
    private void validateImeiRulesAndReporting(List<ImeiImportExcelData> storeDataList,
                                               StoreValidationInfoDTO storeValidationInfo,
                                               Long miId,
                                               StoreValidationContext context) {
        try {
            // 1. 查询IMEI上报规则
            GetRetailerSoRuleReq ruleReq = new GetRetailerSoRuleReq();
            ruleReq.setCountryCode(storeValidationInfo.getCountryShortcode());
            ruleReq.setMiId(String.valueOf(miId));
            ruleReq.setPositionCode(storeValidationInfo.getBestPosition().getCode());
            ruleReq.setStoreCode(storeValidationInfo.getStoreCode());
            ruleReq.setUserTitle(String.valueOf(storeValidationInfo.getJobId()));

            GetRetailerSoRuleResp ruleResp;
            try {
                ruleResp = intlSoRuleRetailerService.getRetailerSoRule(ruleReq);
            } catch (Exception e) {
                markStoreDataAsFailed(storeDataList, ERROR_NO_SO_RULE);
                return;
            }

            if (ruleResp == null || ruleResp.getEnableImei() == null || ruleResp.getEnableImei() != 1) {
                markStoreDataAsFailed(storeDataList, ERROR_NO_IMEI_RULE);
                return;
            }
            context.setSoRule(ruleResp);

            // 2. IMEI上报校验并保存结果
            Map<String, ImeiReportVerifyResponse> verifyResultMap = imeiReportingValidation(storeDataList, storeValidationInfo);
            context.setVerifyResultMap(verifyResultMap);

            // 3. 执行SN重复校验
            snDuplicateValidation(storeDataList, verifyResultMap);

        } catch (Exception e) {
            log.error("IMEI规则和校验失败", e);
            markStoreDataAsFailed(storeDataList, "IMEI validation failed: " + e.getMessage());
        }
    }

    /**
     * 标记门店下所有数据为失败
     */
    private void markStoreDataAsFailed(List<ImeiImportExcelData> storeDataList, String errorMsg) {
        storeDataList.forEach(data -> {
            if (StringUtils.isBlank(data.getFailedReason())) {
                data.setFailedReason(errorMsg);
            }
        });
    }

    /**
     * 校验销售日期是否小于门店创建时间，并进行时区相关校验
     *
     * @param storeDataList 门店数据列表
     * @param storeValidationInfo 门店校验信息
     */
    private void validateSalesTime(List<ImeiImportExcelData> storeDataList,
                                                             StoreValidationInfoDTO storeValidationInfo) {
        String countryCode = storeValidationInfo.getCountryShortcode();
        Long storeCreatedTimestamp = null;

        // 将门店创建时间（UTC时间字符串）转换为时间戳
        if (storeValidationInfo.getStoreCreatedOn() != null) {
            storeCreatedTimestamp = IntlTimeUtil.parseUtcDateTimeStringToTimestamp(storeValidationInfo.getStoreCreatedOn());
        }

        for (ImeiImportExcelData data : storeDataList) {
            if (StringUtils.isNotBlank(data.getFailedReason())) {
                continue;
            }

            // 使用通用的校验方法
            String validationError = SalesTimeValidUtil.validateSalesTime(data.getSalesTime(), countryCode, storeCreatedTimestamp);
            if (validationError != null) {
                data.setFailedReason(validationError);
            }
        }
    }

    /**
     * 执行IMEI上报校验并返回结果（使用StoreValidationInfoDTO）
     */
    private Map<String, ImeiReportVerifyResponse> imeiReportingValidation(
            List<ImeiImportExcelData> storeDataList,
            StoreValidationInfoDTO storeValidationInfo) {

        Map<String, ImeiReportVerifyResponse> resultMap = new HashMap<>();
        int batchSize = 100;

        for (int i = 0; i < storeDataList.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, storeDataList.size());
            List<ImeiImportExcelData> batch = storeDataList.subList(i, endIndex);
            processBatch(batch, storeValidationInfo, resultMap);
        }

        return resultMap;
    }

    /**
     * 处理单批数据
     */
    private void processBatch(
            List<ImeiImportExcelData> batch,
            StoreValidationInfoDTO storeValidationInfo,
            Map<String, ImeiReportVerifyResponse> resultMap) {

        List<ImeiImportExcelData> validBatch = filterValidData(batch);
        if (validBatch.isEmpty()) {
            return;
        }

        ImeiReportVerifyRequest verifyRequest = buildVerifyRequest(validBatch, storeValidationInfo);

        try {
            processVerificationResult(validBatch, verifyRequest, resultMap);
        } catch (Exception e) {
            handleVerificationError(validBatch, e);
        }
    }

    /**
     * 过滤出没有错误的数据
     */
    private List<ImeiImportExcelData> filterValidData(List<ImeiImportExcelData> batch) {
        return batch.stream()
                .filter(data -> StringUtils.isBlank(data.getFailedReason()))
                .collect(Collectors.toList());
    }

    /**
     * 构建IMEI校验请求
     */
    private ImeiReportVerifyRequest buildVerifyRequest(
            List<ImeiImportExcelData> validBatch,
            StoreValidationInfoDTO storeValidationInfo) {

        ImeiReportVerifyRequest verifyRequest = new ImeiReportVerifyRequest();
        verifyRequest.setUserTitle(storeValidationInfo.getJobId());
        verifyRequest.setMiId(String.valueOf(storeValidationInfo.getMiId()));
        verifyRequest.setCountryCode(storeValidationInfo.getCountryShortcode());
        verifyRequest.setSns(extractSnsFromData(validBatch));

        return verifyRequest;
    }

    /**
     * 从数据中提取SNS列表
     */
    private List<String> extractSnsFromData(List<ImeiImportExcelData> validBatch) {
        return validBatch.stream()
                .map(data -> StringUtils.isNotBlank(data.getImei()) ? data.getImei() : data.getSn())
                .collect(Collectors.toList());
    }

    /**
     * 处理校验结果
     */
    private void processVerificationResult(
            List<ImeiImportExcelData> validBatch,
            ImeiReportVerifyRequest verifyRequest,
            Map<String, ImeiReportVerifyResponse> resultMap) {

        CommonApiResponse<Object> verifyResponse = imeiReportVerifyService.imeiReportVerify(verifyRequest);

        if (!(verifyResponse.getData() instanceof List)) {
            return;
        }

        @SuppressWarnings("unchecked")
        List<ImeiReportVerifyResponse> responseList = (List<ImeiReportVerifyResponse>) verifyResponse.getData();

        for (int j = 0; j < responseList.size() && j < validBatch.size(); j++) {
            processSingleResult(validBatch.get(j), responseList.get(j), resultMap);
        }
    }

    /**
     * 处理单个校验结果
     */
    private void processSingleResult(
            ImeiImportExcelData data,
            ImeiReportVerifyResponse response,
            Map<String, ImeiReportVerifyResponse> resultMap) {

        String key = StringUtils.isNotBlank(data.getImei()) ? data.getImei() : data.getSn();
        resultMap.put(key, response);

        if (isVerificationFailed(response)) {
            data.setFailedReason(getVerifyErrorMessage(response.getVerifyResult()));
        }
    }

    /**
     * 判断校验是否失败
     */
    private boolean isVerificationFailed(ImeiReportVerifyResponse response) {
        return response.getVerifyResult() != null && response.getVerifyResult() != 0;
    }

    /**
     * 处理校验异常
     */
    private void handleVerificationError(List<ImeiImportExcelData> validBatch, Exception e) {
        log.error("IMEI校验失败", e);
        String errorMsg = "IMEI verification failed: " + e.getMessage();
        validBatch.forEach(data -> data.setFailedReason(errorMsg));
    }

    /**
     * 执行SN重复校验
     */
    private void snDuplicateValidation(List<ImeiImportExcelData> storeDataList,
                                              Map<String, ImeiReportVerifyResponse> verifyResultMap) {
        // 记录已出现的SN及其对应的第一次出现的IMEI/SN，用于检测重复
        Map<String, String> seenSnMap = new HashMap<>();

        for (ImeiImportExcelData data : storeDataList) {
            if (StringUtils.isNotBlank(data.getFailedReason())) {
                continue; // 已经有错误的跳过
            }

            String inputKey = StringUtils.isNotBlank(data.getImei()) ? data.getImei() : data.getSn();
            ImeiReportVerifyResponse verifyResult = verifyResultMap.get(inputKey);

            if (verifyResult != null && StringUtils.isNotBlank(verifyResult.getSn())) {
                String sn = verifyResult.getSn();
                if (seenSnMap.containsKey(sn)) {
                    // 发现重复的SN，标记为失败（保留第一条），并提供第一次出现的IMEI/SN信息
                    String firstOccurrence = seenSnMap.get(sn);
                    data.setFailedReason(ERROR_REPEATED_DATA + " First occurrence: " + firstOccurrence);
                } else {
                    seenSnMap.put(sn, inputKey);
                }
            }
        }
    }

    /**
     * 获取校验错误信息
     */
    private String getVerifyErrorMessage(Integer verifyResult) {
        switch (verifyResult) {
            case 1:
                return ERROR_PRODUCT_VALIDATION_FAILED;
            case 2:
                return ERROR_DUPLICATE_VALIDATION_FAILED;
            case 3:
                return ERROR_LEGALITY_VALIDATION_FAILED;
            default:
                return "Unknown verification error";
        }
    }





    /**
     * 构建SubmitImeiReq（使用StoreValidationInfoDTO）
     */
    private SubmitImeiReq buildSubmitImeiRequest(List<ImeiImportExcelData> storeDataList,
                                                 GetRetailerSoRuleResp ruleResp,
                                                 StoreValidationInfoDTO storeValidationInfo,
                                                 Map<String, ImeiReportVerifyResponse> verifyResultMap, Long importLogId) {

        // 生成批次Id(UUID)
        String batchIdStr = UUID.randomUUID().toString();

        SubmitImeiReq submitReq = new SubmitImeiReq();
        submitReq.setRuleId(ruleResp.getRuleId());
        submitReq.setCountryCode(storeValidationInfo.getCountryShortcode());
        submitReq.setPositionCode(storeValidationInfo.getBestPosition().getCode());
        submitReq.setStoreCode(storeValidationInfo.getStoreCode());
        submitReq.setUserId(storeValidationInfo.getRmsUserid());
        submitReq.setMiId(storeValidationInfo.getMiId());
        submitReq.setUserTitle(storeValidationInfo.getJobId().longValue());
        submitReq.setBatchId(importLogId);
        submitReq.setBatchIdStr(batchIdStr);

        // 构建明细列表
        List<ImeiDetailDto> detailList = new ArrayList<>();
        for (ImeiImportExcelData data : storeDataList) {
            String key = StringUtils.isNotBlank(data.getImei()) ? data.getImei() : data.getSn();
            ImeiReportVerifyResponse verifyResult = verifyResultMap.get(key);

            if (verifyResult != null && verifyResult.getVerifyResult() != null && verifyResult.getVerifyResult() == 0) {
                ImeiDetailDto detail = new ImeiDetailDto();
                detail.setDetailId(UUID.randomUUID().toString());
                detail.setImei(verifyResult.getImei1());
                detail.setImei2(verifyResult.getImei2());
                detail.setSn(verifyResult.getSn());
                detail.setProductId(verifyResult.getProductId());
                detail.setProductCode(verifyResult.getProductCode());
                detail.setReportingType(ReportingTypeEnum.PC.getValue()); // ReportingTypeEnum.PC
                detail.setNote(data.getRemark());
                detail.setInputImei(key);
                // 使用Excel中的销售时间，按照门店国家时区转换为时间戳
                Long salesTimeMillis = IntlTimeUtil.parseLocalTimeToTimestamp(storeValidationInfo.getCountryShortcode(), data.getSalesTime());
                detail.setSalesTime(salesTimeMillis != null ? salesTimeMillis : System.currentTimeMillis());
                detail.setSnhash(verifyResult.getSnHash());
                detail.setIsHashCountry(verifyResult.getIsHashCountry());

                detailList.add(detail);
            }
        }

        submitReq.setDetailList(detailList);
        submitReq.setPhotoList(new ArrayList<>()); // 空的图片列表

        return submitReq;
    }
    /**
     * 生成错误文件
     */
    private String generateErrorFile(List<ImeiImportExcelData> dataList) throws IOException {
        try (ExcelFileResource excelResource = ExcelFileUtil.generateExcelFile(
                dataList,
                ImeiImportExcelData.class,
                "数据报表")) {

            File excelFile = excelResource.getFile();
            // 上传到FDS服务器
            String timestamp = String.valueOf(System.currentTimeMillis() / 1000L);
            String fileName = FILE_PREFIX + timestamp + FILE_SUFFIX;
            FdsUploadResult uploadResult = fdsService.upload(fileName, excelFile, true);
            String fileUrl = uploadResult.getUrl();

            log.info("错误数据文件已上传到FDS: {}", fileUrl);
            return fileUrl;
        }
    }

    /**
     * 创建IMEI数据
     */
    private boolean createImeiData(ValidationResult validationResult, Long importLogId) {
        try {
            List<ImeiImportExcelData> dataList = validationResult.getDataList();
            Map<String, StoreValidationContext> storeContextMap = validationResult.getStoreContextMap();

            // 按Store Code分组
            Map<String, List<ImeiImportExcelData>> storeGroups = dataList.stream()
                .filter(data -> StringUtils.isBlank(data.getFailedReason()))
                .collect(Collectors.groupingBy(ImeiImportExcelData::getStoreCode));

            // 使用AtomicBoolean来跟踪并行处理中的任何失败
            AtomicBoolean allSuccess = new AtomicBoolean(true);
            
            // 并行处理每个门店的数据
            storeGroups.entrySet().parallelStream().forEach(entry -> {
                String storeCode = entry.getKey();
                List<ImeiImportExcelData> storeDataList = entry.getValue();

                // 从校验上下文中获取已有的信息
                StoreValidationContext context = storeContextMap.get(storeCode);
                if (context == null) {
                    log.error("门店{}校验未通过，跳过IMEI创建", storeCode);
                    return; // 并行流中使用return代替continue
                }

                // 构建SubmitImeiReq，使用已有的校验结果
                SubmitImeiReq submitReq = buildSubmitImeiRequest(
                    storeDataList,
                    context.getSoRule(),
                    context.getStoreValidationInfo(),
                    context.getVerifyResultMap(), importLogId
                );

                // 调用IMEI创建服务
                CommonApiResponse<Object> submitResult = imeiUploadService.submitImei(submitReq);
                if (submitResult.getCode() != 0) {
                    log.error("门店{}IMEI创建失败: {}", storeCode, submitResult.getMessage());
                    allSuccess.set(false);
                }
            });

            return allSuccess.get();
        } catch (Exception e) {
            log.error("创建IMEI数据失败", e);
            return false;
        }
    }

    /**
     * 更新导入日志状态
     */
    private void updateImportLogStatus(Long importLogId, Integer status, String errorMsg, String resultFileUrl) {
        updateImportLogStatus(importLogId, status, errorMsg, resultFileUrl, null);
    }

    /**
     * 更新导入日志状态（包含总数）
     */
    private void updateImportLogStatus(Long importLogId, Integer status, String errorMsg, String resultFileUrl, Integer totalCount) {
        IntlImportLog importLog = new IntlImportLog();
        importLog.setId(importLogId);
        importLog.setStatus(status);
        importLog.setErrorMsg(errorMsg);
        importLog.setResultFileUrl(resultFileUrl);
        if (totalCount != null) {
            importLog.setTotalCount(totalCount);
        }
        importLog.setUpdatedAt(System.currentTimeMillis());
        intlImportLogService.updateById(importLog);
    }

    /**
     * 创建成功响应（包含统计信息）
     */
    private CommonApiResponse<ImportDataResponse> createSuccessResponse(Long importLogId, Integer totalCount, Integer failedCount) {
        ImportDataResponse response = new ImportDataResponse();
        response.setImportLogId(importLogId);
        response.setStatus(1);
        response.setTotalCount(totalCount);
        response.setFailedCount(failedCount);
        return new CommonApiResponse<>(response);
    }

    /**
     * 创建错误响应
     */
    private CommonApiResponse<ImportDataResponse> createErrorResponse(Long importLogId, Integer status, String errorMsg, String resultFileUrl) {
        return createErrorResponse(importLogId, status, errorMsg, resultFileUrl, null, null);
    }

    /**
     * 创建错误响应（包含统计信息）
     */
    private CommonApiResponse<ImportDataResponse> createErrorResponse(Long importLogId, Integer status, String errorMsg, String resultFileUrl,
                                                                      Integer totalCount, Integer failedCount) {
        ImportDataResponse response = new ImportDataResponse();
        response.setImportLogId(importLogId);
        response.setStatus(status);
        response.setErrorMsg(errorMsg);
        response.setResultFileUrl(resultFileUrl);
        response.setTotalCount(totalCount);
        response.setFailedCount(failedCount);
        return new CommonApiResponse<>(response);
    }
}
