package com.mi.info.intl.retail.so.infra.database.mapper.store;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 门店动作记录表 Mapper
 */
@Mapper
public interface StoreActionRecordMapper {
    /**
     * 将 mid 从 originalMid 更新为 newMid
     *
     * @param originalMid 原始 mid (varchar)
     * @param newMid      新 mid (varchar)
     * @return 受影响行数
     */
    int updateMid(@Param("originalMid") String originalMid, @Param("newMid") String newMid);
} 