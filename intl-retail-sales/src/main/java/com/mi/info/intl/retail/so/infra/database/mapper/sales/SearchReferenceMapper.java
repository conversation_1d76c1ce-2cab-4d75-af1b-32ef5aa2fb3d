package com.mi.info.intl.retail.so.infra.database.mapper.sales;

import com.mi.info.intl.retail.so.infra.database.dataobject.sales.SearchReference;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SearchReferenceMapper {

    List<SearchReference> getSearchReferencesFromIntlRmsPosition(@Param("keyWord") String keyWord);

    List<SearchReference> getSearchReferencesFromIntlRmsStore(@Param("keyWord") String keyWord);

    List<SearchReference> getSearchReferencesFromIntlRmsUser(@Param("keyWord") String keyWord,
                                                             @Param("countryShortcode") String countryShortcode);

    List<SearchReference> getSearchReferencesFromIntlRmsRetailer(@Param("keyWord") String keyWord);

    List<SearchReference> getSearchReferencesFromIntlRmsCity(@Param("keyWord") String keyWord,
        @Param("countryShortcode") String countryShortcode);

    List<SearchReference> getSearchReferencesFromIntlIntlRmsProduct(@Param("keyWord") String keyWord);

    List<SearchReference> getSearchReferencesForSpuNameEn(@Param("keyWord") String keyWord);

    List<SearchReference> getSkuFromIntlRmsProduct(@Param("keyWord") String keyWord);

    List<SearchReference> getShortNameFromIntlRmsProduct(@Param("keyWord") String keyWord);

    List<SearchReference> getCategoryEnFromIntlRmsProduct(@Param("keyWord") String keyWord);

    List<SearchReference> getSearchReferencesFromIntlSnBlackListUser(@Param("keyWord") String keyWord);

    List<SearchReference> getSearchReferencesFromIntlPlainTextImeiUser(@Param("keyWord") String keyWord);
}
