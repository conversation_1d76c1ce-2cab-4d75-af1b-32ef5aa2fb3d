package com.mi.info.intl.retail.so.listener;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.SimpleColumnWidthStyleStrategy;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mi.info.intl.retail.api.country.CountryTimeZoneApiService;
import com.mi.info.intl.retail.api.country.dto.CountryDTO;
import com.mi.info.intl.retail.api.rms.RmsStoreTokenApiService;
import com.mi.info.intl.retail.api.rms.dto.RmsApiResponseBody;
import com.mi.info.intl.retail.constant.CommonConstant;
import com.mi.info.intl.retail.core.org.service.OrganizePlatformService;
import com.mi.info.intl.retail.excel.ContentCellFontRgbColorStyleStrategy;
import com.mi.info.intl.retail.exception.BizException;
import com.mi.info.intl.retail.intlretail.service.api.fds.FdsService;
import com.mi.info.intl.retail.intlretail.service.api.masterdata.dto.ImportSnBlacklistErrorDTO;
import com.mi.info.intl.retail.model.UserInfo;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoSnBlacklist;
import com.mi.info.intl.retail.so.domain.upload.service.IntlSoSnBlacklistService;
import com.xiaomi.nr.eiam.api.dto.userinfo.GetUserInfoResp;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.mi.info.intl.retail.exception.ErrorCodes.SYS_ERROR;
import static com.mi.info.intl.retail.exception.ErrorCodes.BIZ_ERROR;

/**
 * 批量停用监听器
 *
 * <AUTHOR>
 * @date 2025/7/29
 **/
@Slf4j
public class ImportBatchDisableListener implements ReadListener<ImportBatchDisableListener.BatchDisableImportData> {


    private final IntlSoSnBlacklistService blacklistService;

    private final List<BatchDisableImportData> batchDisableImportDataList;

    private final CountryTimeZoneApiService countryTimeZoneApiService;

    private final String rmsUrl = "/api/data/v9.2/new_UpdateSNBlackListAction";

    private final UserInfo userContext;

    private final RmsStoreTokenApiService rmsStoreTokenApiService;

    private final OrganizePlatformService organizePlatformService;

    private final TransactionTemplate transactionTemplate;

    private final ImportSnBlacklistErrorDTO errorDTO;
    private final  FdsService fdsService;

    /**
     * 国家信息映射表
     */
    private Map<String, CountryDTO> countryDTOMap;

    /**
     * 异常数据列表
     */
    private List<ErrorData> errorDataList = new ArrayList<>();

    /**
     * 操作人名称
     */
    private String userName;

    public ImportBatchDisableListener(IntlSoSnBlacklistService blacklistService,
                                      CountryTimeZoneApiService countryTimeZoneApiService,
                                      RmsStoreTokenApiService rmsStoreTokenApiService,
                                      OrganizePlatformService organizePlatformService,
                                      TransactionTemplate transactionTemplate,
                                      List<BatchDisableImportData> dataList,
                                      UserInfo userContext,
                                      ImportSnBlacklistErrorDTO errorDTO,
                                      FdsService fdsService) {
        this.blacklistService = blacklistService;
        this.countryTimeZoneApiService = countryTimeZoneApiService;
        this.rmsStoreTokenApiService = rmsStoreTokenApiService;
        this.organizePlatformService = organizePlatformService;
        this.transactionTemplate = transactionTemplate;
        this.batchDisableImportDataList = dataList;
        this.userContext = userContext;
        this.errorDTO = errorDTO;
        this.fdsService = fdsService;
    }

    @Override
    public void invoke(ImportBatchDisableListener.BatchDisableImportData batchDisableImportData, AnalysisContext analysisContext) {
        batchDisableImportDataList.add(batchDisableImportData);
    }


    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        try {
            if (batchDisableImportDataList.isEmpty()) {
                log.warn("没有需要停用的记录");
                return;
            }
            log.info("开始处理批量停用数据，共{}条", batchDisableImportDataList.size());

            // 批量导入条数限制：上限5000条
            if (batchDisableImportDataList.size() > 5000) {
                throw new BizException(SYS_ERROR, "A maximum of 5,000 pieces of data are allowed to be uploaded.");
            }

            // 初始化国家信息映射表（忽略大小写）
            List<CountryDTO> countryInfo = countryTimeZoneApiService.getCountryInfo();
            countryDTOMap = countryInfo.stream()
                    .collect(Collectors.toMap(
                            country -> country.getCountryName().toLowerCase(),
                            Function.identity(),
                            (v1, v2) -> v1));

            // 获取操作人名称
            GetUserInfoResp userInfo = organizePlatformService.getUserInfo(userContext.getMiID());
            userName = (userInfo != null) ? userInfo.getName() : "system";

            // 逐条处理数据
            for (BatchDisableImportData data : batchDisableImportDataList) {
                try {
                    processSingleData(data);
                } catch (Exception e) {
                    log.error("处理单条数据异常，Country: {}, SN/IMEI: {}", data.getCountry(), data.getSnImei(), e);
                    // 确保异常数据被记录
                    if (errorDataList.stream().noneMatch(
                            ed -> ed.getCountry().equals(data.getCountry()) && ed.getSnImei().equals(data.getSnImei())
                    )) {
                        ErrorData errorData = new ErrorData();
                        errorData.setCountry(data.getCountry());
                        errorData.setSnImei(data.getSnImei());
                        errorData.setErrorMessage("处理失败: " + e.getMessage());
                        errorDataList.add(errorData);
                    }
                }
            }

            // 处理异常数据导出
            if (!errorDataList.isEmpty()) {
                exportErrorDataToExcel(errorDataList);
                errorDTO.setFailedCount(errorDataList.size());
                log.warn("共{}条数据处理失败，已导出异常信息", errorDataList.size());
            }

            log.info("批量停用处理完成，总条数: {}, 失败条数: {}",
                    batchDisableImportDataList.size(), errorDataList.size());

        } catch (Exception e) {
            log.error("批量停用整体处理失败: {}", e.getMessage(), e);
            throw new BizException(SYS_ERROR, e.getMessage());
        } finally {
            batchDisableImportDataList.clear();
        }
    }

    /**
     * 处理单条数据（包含独立事务）
     */
    private void processSingleData(BatchDisableImportData data) {
        // 前置校验
        String validateMsg = validateSingleData(data);
        if (StringUtils.isNotBlank(validateMsg)) {
            ErrorData errorData = new ErrorData();
            errorData.setCountry(data.getCountry());
            errorData.setSnImei(data.getSnImei());
            errorData.setErrorMessage(validateMsg);
            errorDataList.add(errorData);
            return;
        }

        // 提取关键信息
        String countryCode = getCountryCode(data.getCountry());
        String snImei = data.getSnImei().toUpperCase();

        // 单条数据事务处理
        transactionTemplate.execute(new TransactionCallbackWithoutResult() {
            @Override
            protected void doInTransactionWithoutResult(TransactionStatus status) {
                try {
                    // 查询需要停用的记录（仅启用状态）
                    IntlSoSnBlacklist blacklist = blacklistService.getOne(Wrappers.<IntlSoSnBlacklist>lambdaQuery()
                            .eq(IntlSoSnBlacklist::getCountryCode, countryCode)
                            .eq(IntlSoSnBlacklist::getSnImei, snImei)
                            .eq(IntlSoSnBlacklist::getStatus, CommonConstant.Number.ZERO));

                    if (blacklist == null) {
                        String errorMsg = "No matching activation status record found.";
                        log.warn("{}，Country: {}, SN/IMEI: {}", errorMsg, data.getCountry(), snImei);
                        throw new BizException(BIZ_ERROR, errorMsg);
                    }

                    // 更新记录状态
                    blacklist.setStatus(CommonConstant.Number.ONE);
                    blacklist.setModifiedBy(userContext.getMiID());
                    blacklist.setModifiedByName(userName);
                    blacklist.setModifiedOn(System.currentTimeMillis());

                    boolean updateResult = blacklistService.updateById(blacklist);
                    if (!updateResult) {
                        throw new BizException(SYS_ERROR, "数据库更新失败");
                    }

                    // 同步RMS
                    Map<String, Object> rmsData = buildRmsData(blacklist);
                    String areaId = countryDTOMap.get(data.getCountry().toLowerCase()).getCountryCode();
                    RmsApiResponseBody responseBody = rmsStoreTokenApiService.httpForRmsResponseBody(areaId, rmsUrl, rmsData, "POST");

                    if (responseBody == null || responseBody.getCode() != 200) {
                        String errorMsg = "RMS更新失败";
                        if (responseBody != null && responseBody.getMessage() != null) {
                            errorMsg += ": " + responseBody.getMessage();
                        }
                        throw new BizException(SYS_ERROR, errorMsg);
                    }

                    log.info("单条数据停用成功，Country: {}, SN/IMEI: {}", data.getCountry(), snImei);

                } catch (Exception e) {
                    log.error("单条数据事务处理失败，将回滚: {}", e.getMessage());
                    status.setRollbackOnly(); // 标记事务回滚
                    // 记录异常信息
                    ErrorData errorData = new ErrorData();
                    errorData.setCountry(data.getCountry());
                    errorData.setSnImei(data.getSnImei());
                    errorData.setErrorMessage(e.getMessage());
                    errorDataList.add(errorData);
                    // 抛出异常中断当前处理，进入外层catch
                    throw new BizException(SYS_ERROR, e.getMessage(), e);
                }
            }
        });
    }

    /**
     * 单条数据校验
     */
    private String validateSingleData(BatchDisableImportData data) {
        // 国家校验
        if (StringUtils.isBlank(data.getCountry())) {
            return "Country cannot be empty";
        }
        CountryDTO countryDTO = countryDTOMap.get(data.getCountry().toLowerCase());
        if (countryDTO == null) {
            return "Invalid country: " + data.getCountry();
        }

        // SN/IMEI校验
        if (StringUtils.isBlank(data.getSnImei())) {
            return "SN/IMEI cannot be empty";
        }

        return null;
    }

    /**
     * 获取国家编码
     */
    private String getCountryCode(String countryName) {
        CountryDTO countryDTO = countryDTOMap.get(countryName.toLowerCase());
        return countryDTO != null ? countryDTO.getCountryCode() : null;
    }

    /**
     * 构造发送给RMS的黑名单数据格式
     */
    private Map<String, Object> buildRmsData(IntlSoSnBlacklist intlSoSnBlacklist) throws JsonProcessingException {
        Map<String, Object> rmsData = new HashMap<>();
        rmsData.put("SourceType", "retail");

        Map<String, Object> input = new HashMap<>();
        input.put("retailId", intlSoSnBlacklist.getId());
        input.put("rmsId", intlSoSnBlacklist.getRmsId());
        input.put("countryCode", intlSoSnBlacklist.getCountryCode());
        input.put("type", intlSoSnBlacklist.getType());
        input.put("snImei", intlSoSnBlacklist.getSnImei());
        input.put("status", 1);
        input.put("dataFrom", intlSoSnBlacklist.getDataFrom());

        ObjectMapper objectMapper = new ObjectMapper();
        String inputObject = objectMapper.writeValueAsString(input);
        rmsData.put("Input", inputObject);

        return rmsData;
    }

    /**
     * 导出错误数据到Excel
     */
    private void exportErrorDataToExcel(List<ErrorData> errorDataList) {
        File tempFile = null;
        ExcelWriter excelWriter = null;
        try {
            tempFile = File.createTempFile("batch_disable_error_", ".xlsx");
            // 配置列宽和错误信息红色字体
            excelWriter = EasyExcel.write(tempFile, ErrorData.class)
                    .registerWriteHandler(new SimpleColumnWidthStyleStrategy(25))
                    .registerWriteHandler(new ContentCellFontRgbColorStyleStrategy((short) 255, (short) 0, (short) 0, 2))
                    .build();

            WriteSheet writeSheet = EasyExcel.writerSheet("停用失败数据").build();
            excelWriter.write(errorDataList, writeSheet);
            excelWriter.finish();

            // 上传到FDS
            String timestamp = String.valueOf(System.currentTimeMillis() / 1000L);
            String fileUrl = fdsService.upload("batch_disable_error_" + timestamp + ".xlsx", tempFile, true).getUrl();
            log.info("停用错误数据已上传到FDS: {}", fileUrl);
            errorDTO.setResultFileUrl(fileUrl);

        } catch (Exception e) {
            log.error("导出停用错误数据失败", e);
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
            if (tempFile != null && tempFile.exists()) {
                tempFile.delete();
            }
        }
    }

    @Data
    public static class BatchDisableImportData {
        @ExcelProperty("Country")
        private String country;

        @ExcelProperty("SN/IMEI")
        private String snImei;
    }

    @Data
    public static class ErrorData {
        @ExcelProperty("Country")
        private String country;

        @ExcelProperty("SN/IMEI")
        private String snImei;

        @ExcelProperty("Failed Reason")
        private String errorMessage;
    }
}