package com.mi.info.intl.retail.so.app.provider.upload;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.GetImportLogListRequest;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.GetImportLogListResponse;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.GetImportTemplateRequest;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.GetImportTemplateResponse;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.ImportDataRequest;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.ImportDataResponse;
import com.mi.info.intl.retail.intlretail.service.api.so.upload.ImeiImportService;
import com.mi.info.intl.retail.intlretail.service.api.so.upload.QtyImportService;
import com.mi.info.intl.retail.intlretail.service.api.so.upload.SoImportService;
import com.mi.info.intl.retail.intlretail.service.api.so.upload.dto.ImeiImportRequest;
import com.mi.info.intl.retail.intlretail.service.api.so.upload.dto.QtyImportRequest;
import com.mi.info.intl.retail.intlretail.service.api.so.upload.dto.QtyImportResponse;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.model.UserInfo;
import com.mi.info.intl.retail.so.app.provider.enums.ImportTypeEnums;
import com.mi.info.intl.retail.so.app.rpc.FileApiService;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlImportLog;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlImportTemplate;
import com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlImportLogMapper;
import com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlImportTemplateMapper;
import com.mi.info.intl.retail.utils.UserInfoUtil;
import com.mi.info.intl.retail.utils.intl.IntlTimeUtil;
import com.xiaomi.nr.upload.client.api.FileApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.time.ZonedDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Service
@DubboService(group = "${center.dubbo.group:}", interfaceClass = SoImportService.class)
public class SoImportServiceImpl implements SoImportService {

    @Resource
    private IntlImportTemplateMapper intlImportTemplateMapper;
    @Resource
    private IntlImportLogMapper intlImportLogMapper;
    @Resource
    private ImeiImportService imeiImportService;
    @Resource
    private QtyImportService qtyImportService;
    @Resource
    private FileApiService fileApiService;
    @Reference(group = "${file.dubbo.group:}", check = false, interfaceClass = FileApi.class)
    private FileApi fileApi;

    @Override
    public CommonApiResponse<GetImportTemplateResponse> getImportTemplate(GetImportTemplateRequest request) {
        log.info("getImportTemplate start, request: {}", request);

        try {
            // 1. 参数校验
            if (request == null) {
                return new CommonApiResponse<>(400, "请求参数不能为空", null);
            }
            if (request.getType() == null) {
                return new CommonApiResponse<>(400, "模板类型不能为空", null);
            }
            if (request.getType() != 1 && request.getType() != 2) {
                return new CommonApiResponse<>(400, "模板类型只能是1(IMEI)或2(QTY)", null);
            }

            // 2. 查询模板信息
            IntlImportTemplate template = intlImportTemplateMapper.getTemplateByType(request.getType());
            if (template == null) {
                return new CommonApiResponse<>(404, "未找到对应的模板", null);
            }

            // 3. 构建响应
            GetImportTemplateResponse response = new GetImportTemplateResponse();
            response.setFileUrl(template.getFileUrl());

            log.info("getImportTemplate success: type={}, fileUrl={}", request.getType(), template.getFileUrl());

            return new CommonApiResponse<>(response);
        } catch (Exception e) {
            log.error("getImportTemplate error", e);
            return new CommonApiResponse<>(500, "查询失败: " + e.getMessage(), null);
        }
    }

    @Override
    public CommonApiResponse<ImportDataResponse> importData(ImportDataRequest request) {
        log.info("importData start, request: {}", request);

        try {
            // 1. 参数校验
            if (request == null) {
                return new CommonApiResponse<>(400, "请求参数不能为空", null);
            }
            if (request.getFileId() == null || request.getFileId() <= 0) {
                return new CommonApiResponse<>(400, "文件ID不能为空", null);
            }
            if (StringUtils.isBlank(request.getFileUrl())) {
                return new CommonApiResponse<>(400, "文件链接不能为空", null);
            }
            if (request.getType() == null) {
                return new CommonApiResponse<>(400, "导入类型不能为空", null);
            }
            if (request.getMiId() == null || request.getMiId() <= 0) {
                // PC端请求从上下文获取mid, 获取不到报错
                Optional<UserInfo> userInfoOptional = UserInfoUtil.getUserInfo();
                if (userInfoOptional.isPresent()) {
                    request.setMiId(userInfoOptional.get().getMiID());
                } else {
                    return new CommonApiResponse<>(400, "用户信息获取失败", null);
                }
            }

            // 文件二次确认
            fileApiService.commit(Collections.singletonList(request.getFileId()));

            // 创建导入记录
            IntlImportLog importLog = new IntlImportLog();
            importLog.setType(request.getType());
            importLog.setActionType(request.getActionType());
            importLog.setSourceFileId(request.getFileId());
            importLog.setSourceFileUrl(request.getFileUrl());
            importLog.setFileName(request.getFileName());
            importLog.setDataSource(request.getDataSource());
            importLog.setType(request.getType());
            importLog.setCreatedBy(request.getMiId());
            importLog.setStatus(0);
            importLog.setCreatedAt(System.currentTimeMillis());
            importLog.setUpdatedAt(System.currentTimeMillis());
            // 插入数据并返回id
            intlImportLogMapper.insert(importLog);

            ImportDataResponse response = new ImportDataResponse();

            // 根据type执行对应导入逻辑
            // IMEI导入
            if (Objects.equals(request.getType(), ImportTypeEnums.IMEI.getCode())) {
                // 构造IMEI导入请求参数
                ImeiImportRequest imeiImportRequest = new ImeiImportRequest();
                imeiImportRequest.setImportLogId(importLog.getId());
                imeiImportRequest.setSourceFileUrl(request.getFileUrl());
                imeiImportRequest.setMiId(request.getMiId());
                log.info("importData imeiImportRequest: {}", imeiImportRequest);
                CommonApiResponse<ImportDataResponse> importDataResponse = imeiImportService.importImeiData(imeiImportRequest);
                log.info("importData importDataResponse: {}", importDataResponse);
                return importDataResponse;

            } else if (Objects.equals(request.getType(), ImportTypeEnums.QTY.getCode())) {
                // 构造QTY导入请求参数
                QtyImportRequest qtyImportRequest = new QtyImportRequest();
                qtyImportRequest.setImportLogId(importLog.getId());
                qtyImportRequest.setSourceFileUrl(request.getFileUrl());
                qtyImportRequest.setMiId(request.getMiId());
                log.info("importData qtyImportRequest: {}", qtyImportRequest);
                
                // 调用QTY导入服务
                CommonApiResponse<QtyImportResponse> qtyImportResponse = qtyImportService.importData(qtyImportRequest);
                log.info("importData qtyImportResponse: {}", qtyImportResponse);

                // 将QtyImportResponse转换为ImportDataResponse
                if (qtyImportResponse.getCode() == 0 && qtyImportResponse.getData() != null) {
                    QtyImportResponse qtyData = qtyImportResponse.getData();
                    ImportDataResponse importDataResponse = new ImportDataResponse();
                    importDataResponse.setImportLogId(qtyData.getImportLogId());
                    importDataResponse.setResultFileUrl(qtyData.getResultFileUrl());
                    importDataResponse.setTotalCount(qtyData.getTotalCount());
                    importDataResponse.setFailedCount(qtyData.getFailedCount());
                    importDataResponse.setStatus(qtyData.getStatus());
                    importDataResponse.setErrorMsg(qtyData.getErrorMsg());
                    
                    return new CommonApiResponse<>(importDataResponse);
                } else {
                    // 如果QTY导入失败，返回错误响应
                    return new CommonApiResponse<>(qtyImportResponse.getCode(), qtyImportResponse.getMessage(), null);
                }
            } else {
                // 其他类型报错
                return new CommonApiResponse<>(400, "不支持的导入类型", null);
            }


        } catch (Exception e) {
            log.error("importData error", e);
            return new CommonApiResponse<>(500, "导入失败: " + e.getMessage(), null);
        }
    }

    @Override
    public CommonApiResponse<GetImportLogListResponse> getImportLogList(GetImportLogListRequest request) {
        log.info("getImportLogList start, request: {}", request);

        try {
            // 1. 基本参数校验
            if (request == null) {
                return new CommonApiResponse<>(400, "请求参数不能为空", null);
            }

            // 2. 获取用户信息
            Optional<UserInfo> userInfoOptional = UserInfoUtil.getUserInfo();
            if (!userInfoOptional.isPresent()) {
                return new CommonApiResponse<>(401, "用户未登录", null);
            }
            UserInfo userInfo = userInfoOptional.get();
            String countryCode = userInfo.getCountryCode();

            // 3. 如果有logId，单独走简单查询逻辑
            if (request.getLogId() != null) {
                return handleLogIdQuery(request.getLogId());
            }

            // 4. 获取用户miId - 入参无值则从上下文获取
            Long miId = request.getMiId();
            if (miId == null) {
                miId = userInfo.getMiId();
            }

            // 5. 处理时间参数 - operationStartTime无值，默认条件为最近一个月
            Long operationStartTime = request.getOperationStartTime();
            Long operationEndTime = request.getOperationEndTime();

            if (operationStartTime == null) {
                // 获取当前国家时区的时间，然后减去一个月
                ZoneId zoneId = getZoneIdByCountryCode(countryCode);
                ZonedDateTime now = ZonedDateTime.now(zoneId);
                ZonedDateTime oneMonthAgo = now.minusMonths(1);
                operationStartTime = oneMonthAgo.toInstant().toEpochMilli();
            }

            // 6. 设置分页参数
            long pageNum = request.getPageNum() != null ? request.getPageNum() : 1;
            long pageSize = request.getPageSize() != null ? request.getPageSize() : 20;
            Page<IntlImportLog> page = new Page<>(pageNum, pageSize);

            // 7. 执行分页查询
            Page<IntlImportLog> resultPage = intlImportLogMapper.selectImportLogList(
                page,
                request.getTaskName(),
                request.getDataSource(),
                request.getStatus(),
                request.getImportDuration(),
                operationStartTime,
                operationEndTime,
                request.getImportTypes(),
                miId
            );

            // 8. 构建响应数据
            GetImportLogListResponse response = new GetImportLogListResponse();
            response.setTotal((int) resultPage.getTotal());
            response.setCurrentPage((int) resultPage.getCurrent());
            response.setPageSize((int) resultPage.getSize());

            List<GetImportLogListResponse.ImportLogRecord> records = new ArrayList<>();
            for (IntlImportLog log : resultPage.getRecords()) {
                GetImportLogListResponse.ImportLogRecord record = new GetImportLogListResponse.ImportLogRecord();
                record.setTaskName(log.getTaskName());
                record.setDataSource(convertDataSourceToString(log.getDataSource()));
                record.setImportType(log.getType());
                record.setFileName(log.getFileName());
                record.setStatus(log.getStatus());
                record.setOperator(log.getOperator());
                record.setOperationTime(log.getCreatedAt());
                record.setAction(log.getActionType());

                // 9. 计算导入进度及赋值文件链接字段
                calculateProgressAndFileUrl(log, record);

                records.add(record);
            }
            response.setRecords(records);

            log.info("getImportLogList success: total={}", response.getTotal());
            return new CommonApiResponse<>(response);

        } catch (Exception e) {
            log.error("getImportLogList error", e);
            return new CommonApiResponse<>(500, "查询失败: " + e.getMessage(), null);
        }
    }

    /**
     * 处理logId单独查询逻辑
     */
    private CommonApiResponse<GetImportLogListResponse> handleLogIdQuery(Long logId) {
        try {
            IntlImportLog log = intlImportLogMapper.selectById(logId);

            GetImportLogListResponse response = new GetImportLogListResponse();
            if (log == null) {
                response.setTotal(0);
                response.setCurrentPage(1);
                response.setPageSize(20);
                response.setRecords(new ArrayList<>());
            } else {
                response.setTotal(1);
                response.setCurrentPage(1);
                response.setPageSize(20);

                List<GetImportLogListResponse.ImportLogRecord> records = new ArrayList<>();
                GetImportLogListResponse.ImportLogRecord record = new GetImportLogListResponse.ImportLogRecord();
                record.setTaskName(log.getTaskName());
                record.setDataSource(convertDataSourceToString(log.getDataSource()));
                record.setImportType(log.getType());
                record.setFileName(log.getFileName());
                record.setStatus(log.getStatus());
                record.setOperator(log.getOperator());
                record.setOperationTime(log.getCreatedAt());
                record.setAction(log.getActionType());

                // 计算导入进度及赋值文件链接字段
                calculateProgressAndFileUrl(log, record);

                records.add(record);
                response.setRecords(records);
            }

            return new CommonApiResponse<>(response);
        } catch (Exception e) {
            log.error("handleLogIdQuery error, logId: {}", logId, e);
            return new CommonApiResponse<>(500, "查询失败: " + e.getMessage(), null);
        }
    }

    /**
     * 根据国家代码获取时区ID
     */
    private ZoneId getZoneIdByCountryCode(String countryCode) {
        try {
            // 使用IntlTimeUtil获取时区，如果失败则使用UTC
            String timeStr = IntlTimeUtil.getOffsetDateTimeByCountryCode(countryCode);
            if (timeStr != null) {
                // 这里简化处理，实际应该从IntlTimeUtil获取ZoneId
                return ZoneId.of("UTC"); // 默认使用UTC，实际项目中应该有更好的实现
            }
        } catch (Exception e) {
            log.warn("Failed to get timezone for country: {}, using UTC", countryCode, e);
        }
        return ZoneId.of("UTC");
    }

    /**
     * 将数据源整数转换为字符串
     */
    private String convertDataSourceToString(Integer dataSource) {
        if (dataSource == null) {
            return "";
        }
        // 根据业务需求转换，这里简化处理
        return dataSource.toString();
    }

    /**
     * 计算导入进度及赋值文件链接字段
     */
    private void calculateProgressAndFileUrl(IntlImportLog log, GetImportLogListResponse.ImportLogRecord record) {
        Integer status = log.getStatus();
        if (status == null) {
            status = 0;
        }

        switch (status) {
            case 0: // 处理中
                // importProgress = finish_count / total_count (保留整数)
                if (log.getTotalCount() != null && log.getTotalCount() > 0 && log.getFinishCount() != null) {
                    int progress = (int) ((log.getFinishCount() * 100.0) / log.getTotalCount());
                    record.setImportProgress(Math.min(progress, 99)); // 处理中时最大99%
                } else {
                    record.setImportProgress(0);
                }
                // fileUrl = source_file_url
                record.setFileUrl(log.getSourceFileUrl());
                break;
            case 1: // 成功
                // importProgress = 100
                record.setImportProgress(100);
                // fileUrl = source_file_url
                record.setFileUrl(log.getSourceFileUrl());
                break;
            case 2: // 失败
                // importProgress = 100
                record.setImportProgress(100);
                // fileUrl = result_file_url
                record.setFileUrl(log.getResultFileUrl());
                break;
            default:
                record.setImportProgress(0);
                record.setFileUrl(log.getSourceFileUrl());
                break;
        }

        // 设置导入耗时分类
        record.setImportDuration(log.getImportDuration());
    }
}
