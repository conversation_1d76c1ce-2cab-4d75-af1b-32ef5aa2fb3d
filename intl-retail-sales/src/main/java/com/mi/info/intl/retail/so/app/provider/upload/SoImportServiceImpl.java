package com.mi.info.intl.retail.so.app.provider.upload;

import com.mi.info.intl.retail.intlretail.service.api.so.dto.GetImportTemplateRequest;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.GetImportTemplateResponse;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.ImportDataRequest;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.ImportDataResponse;
import com.mi.info.intl.retail.intlretail.service.api.so.upload.ImeiImportService;
import com.mi.info.intl.retail.intlretail.service.api.so.upload.QtyImportService;
import com.mi.info.intl.retail.intlretail.service.api.so.upload.SoImportService;
import com.mi.info.intl.retail.intlretail.service.api.so.upload.dto.ImeiImportRequest;
import com.mi.info.intl.retail.intlretail.service.api.so.upload.dto.QtyImportRequest;
import com.mi.info.intl.retail.intlretail.service.api.so.upload.dto.QtyImportResponse;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.model.UserInfo;
import com.mi.info.intl.retail.so.app.provider.enums.ImportTypeEnums;
import com.mi.info.intl.retail.so.app.rpc.FileApiService;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlImportLog;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlImportTemplate;
import com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlImportLogMapper;
import com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlImportTemplateMapper;
import com.mi.info.intl.retail.utils.UserInfoUtil;
import com.xiaomi.nr.upload.client.api.FileApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Service
@DubboService(group = "${center.dubbo.group:}", interfaceClass = SoImportService.class)
public class SoImportServiceImpl implements SoImportService {

    @Resource
    private IntlImportTemplateMapper intlImportTemplateMapper;
    @Resource
    private IntlImportLogMapper intlImportLogMapper;
    @Resource
    private ImeiImportService imeiImportService;
    @Resource
    private QtyImportService qtyImportService;
    @Resource
    private FileApiService fileApiService;
    @Reference(group = "${file.dubbo.group:}", check = false, interfaceClass = FileApi.class)
    private FileApi fileApi;

    @Override
    public CommonApiResponse<GetImportTemplateResponse> getImportTemplate(GetImportTemplateRequest request) {
        log.info("getImportTemplate start, request: {}", request);

        try {
            // 1. 参数校验
            if (request == null) {
                return new CommonApiResponse<>(400, "请求参数不能为空", null);
            }
            if (request.getType() == null) {
                return new CommonApiResponse<>(400, "模板类型不能为空", null);
            }
            if (request.getType() != 1 && request.getType() != 2) {
                return new CommonApiResponse<>(400, "模板类型只能是1(IMEI)或2(QTY)", null);
            }

            // 2. 查询模板信息
            IntlImportTemplate template = intlImportTemplateMapper.getTemplateByType(request.getType());
            if (template == null) {
                return new CommonApiResponse<>(404, "未找到对应的模板", null);
            }

            // 3. 构建响应
            GetImportTemplateResponse response = new GetImportTemplateResponse();
            response.setFileUrl(template.getFileUrl());

            log.info("getImportTemplate success: type={}, fileUrl={}", request.getType(), template.getFileUrl());

            return new CommonApiResponse<>(response);
        } catch (Exception e) {
            log.error("getImportTemplate error", e);
            return new CommonApiResponse<>(500, "查询失败: " + e.getMessage(), null);
        }
    }

    @Override
    public CommonApiResponse<ImportDataResponse> importData(ImportDataRequest request) {
        log.info("importData start, request: {}", request);

        try {
            // 1. 参数校验
            if (request == null) {
                return new CommonApiResponse<>(400, "请求参数不能为空", null);
            }
            if (request.getFileId() == null || request.getFileId() <= 0) {
                return new CommonApiResponse<>(400, "文件ID不能为空", null);
            }
            if (StringUtils.isBlank(request.getFileUrl())) {
                return new CommonApiResponse<>(400, "文件链接不能为空", null);
            }
            if (request.getType() == null) {
                return new CommonApiResponse<>(400, "导入类型不能为空", null);
            }
            if (request.getMiId() == null || request.getMiId() <= 0) {
                // PC端请求从上下文获取mid, 获取不到报错
                Optional<UserInfo> userInfoOptional = UserInfoUtil.getUserInfo();
                if (userInfoOptional.isPresent()) {
                    request.setMiId(userInfoOptional.get().getMiID());
                } else {
                    return new CommonApiResponse<>(400, "用户信息获取失败", null);
                }
            }

            // 文件二次确认
            fileApiService.commit(Collections.singletonList(request.getFileId()));

            // 创建导入记录
            IntlImportLog importLog = new IntlImportLog();
            importLog.setType(request.getType());
            importLog.setActionType(request.getActionType());
            importLog.setSourceFileId(request.getFileId());
            importLog.setSourceFileUrl(request.getFileUrl());
            importLog.setFileName(request.getFileName());
            importLog.setDataSource(request.getDataSource());
            importLog.setType(request.getType());
            importLog.setCreatedBy(request.getMiId());
            importLog.setStatus(0);
            importLog.setCreatedAt(System.currentTimeMillis());
            importLog.setUpdatedAt(System.currentTimeMillis());
            // 插入数据并返回id
            intlImportLogMapper.insert(importLog);

            ImportDataResponse response = new ImportDataResponse();

            // 根据type执行对应导入逻辑
            // IMEI导入
            if (Objects.equals(request.getType(), ImportTypeEnums.IMEI.getCode())) {
                // 构造IMEI导入请求参数
                ImeiImportRequest imeiImportRequest = new ImeiImportRequest();
                imeiImportRequest.setImportLogId(importLog.getId());
                imeiImportRequest.setSourceFileUrl(request.getFileUrl());
                imeiImportRequest.setMiId(request.getMiId());
                log.info("importData imeiImportRequest: {}", imeiImportRequest);
                CommonApiResponse<ImportDataResponse> importDataResponse = imeiImportService.importImeiData(imeiImportRequest);
                log.info("importData importDataResponse: {}", importDataResponse);
                return importDataResponse;

            } else if (Objects.equals(request.getType(), ImportTypeEnums.QTY.getCode())) {
                // 构造QTY导入请求参数
                QtyImportRequest qtyImportRequest = new QtyImportRequest();
                qtyImportRequest.setImportLogId(importLog.getId());
                qtyImportRequest.setSourceFileUrl(request.getFileUrl());
                qtyImportRequest.setMiId(request.getMiId());
                log.info("importData qtyImportRequest: {}", qtyImportRequest);
                
                // 调用QTY导入服务
                CommonApiResponse<QtyImportResponse> qtyImportResponse = qtyImportService.importData(qtyImportRequest);
                log.info("importData qtyImportResponse: {}", qtyImportResponse);

                // 将QtyImportResponse转换为ImportDataResponse
                if (qtyImportResponse.getCode() == 0 && qtyImportResponse.getData() != null) {
                    QtyImportResponse qtyData = qtyImportResponse.getData();
                    ImportDataResponse importDataResponse = new ImportDataResponse();
                    importDataResponse.setImportLogId(qtyData.getImportLogId());
                    importDataResponse.setResultFileUrl(qtyData.getResultFileUrl());
                    importDataResponse.setTotalCount(qtyData.getTotalCount());
                    importDataResponse.setFailedCount(qtyData.getFailedCount());
                    importDataResponse.setStatus(qtyData.getStatus());
                    importDataResponse.setErrorMsg(qtyData.getErrorMsg());
                    
                    return new CommonApiResponse<>(importDataResponse);
                } else {
                    // 如果QTY导入失败，返回错误响应
                    return new CommonApiResponse<>(qtyImportResponse.getCode(), qtyImportResponse.getMessage(), null);
                }
            } else {
                // 其他类型报错
                return new CommonApiResponse<>(400, "不支持的导入类型", null);
            }


        } catch (Exception e) {
            log.error("importData error", e);
            return new CommonApiResponse<>(500, "导入失败: " + e.getMessage(), null);
        }
    }
}
