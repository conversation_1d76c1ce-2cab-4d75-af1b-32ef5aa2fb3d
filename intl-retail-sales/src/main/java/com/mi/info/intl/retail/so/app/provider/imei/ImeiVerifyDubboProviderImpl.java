package com.mi.info.intl.retail.so.app.provider.imei;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.mi.info.intl.retail.so.app.mq.RetailSyncToRmsProducer;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.mi.info.intl.retail.core.feishu.service.SendMessageService;
import com.mi.info.intl.retail.intlretail.service.api.so.imei.provider.ImeiVerifyDubboProvider;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.so.app.mq.dto.RetailSyncToRmsInfo;
import com.mi.info.intl.retail.so.domain.datasync.enums.DataSyncOperateTypeTypeEnum;
import com.mi.info.intl.retail.so.domain.imei.service.IntlSoImeiVerifyService;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@DubboService(timeout = 20000, group = "${retail.dubbo.group:}", retries = 3, interfaceClass = ImeiVerifyDubboProvider.class)
public class ImeiVerifyDubboProviderImpl implements ImeiVerifyDubboProvider {

    @Resource
    private IntlSoImeiVerifyService intlSoImeiVerifyService;

    @Resource
    private SendMessageService sendMessageService;

    @Resource
    private RetailSyncToRmsProducer retailSyncToRmsProducer;

    @Value("${miwork.alarm.groupId.p0}")
    private String groupId;

    private static final String DATA_TYPE = "imei";

    @Override
    public CommonApiResponse<Void> imeiActivationVerify() {
        try {
            //1.激活校验，获取成功+失败的id
            List<Long> soImeiIdList = intlSoImeiVerifyService.verifyActivationStatusOfImei();

            if (CollectionUtils.isEmpty(soImeiIdList)) {
                log.debug("imeiActivationVerify soImeiIdList is empty,time:{}", System.currentTimeMillis() / 100);
                return CommonApiResponse.success(null);
            }

            this.sendMqfMsg(soImeiIdList);

        } catch (Exception e) {
            log.error("imeiActivationVerify failed:", e);
            sendMessageService.sendGroupTextMessage(groupId, "", "激活状态校验过程中发生异常：" + e.getMessage());
            throw new RuntimeException(e.getMessage());
        }
        return CommonApiResponse.success(null);

    }

    /**
     * send MQ message
     * @param soImeiIdList 激活校验成功&失败的id
     * @return mq result
     */
    private void sendMqfMsg(List<Long> soImeiIdList) {
        RetailSyncToRmsInfo retailSyncToRmsInfo = new RetailSyncToRmsInfo();
        retailSyncToRmsInfo.setOperateType(DataSyncOperateTypeTypeEnum.ACTIVATE_VERIFICATION.getMessage());
        retailSyncToRmsInfo.setDataType(DATA_TYPE);
        retailSyncToRmsInfo.setDataId(soImeiIdList.stream().filter(Objects::nonNull).map(Object::toString).collect(Collectors.joining(",")));
        retailSyncToRmsProducer.sendSyncRmsMsg(retailSyncToRmsInfo);
    }
}
