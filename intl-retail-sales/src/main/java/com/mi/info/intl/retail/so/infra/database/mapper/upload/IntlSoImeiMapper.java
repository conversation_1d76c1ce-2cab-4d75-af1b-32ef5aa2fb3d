package com.mi.info.intl.retail.so.infra.database.mapper.upload;

import com.mi.info.intl.retail.so.domain.datasync.dto.IntlSoImeiBatchSaveData;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoImei;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【intl_so_imei(销量imei)】的数据库操作Mapper
 * @createDate 2025-07-24 19:21:15
 * @Entity com.mi.info.intl.retail.so.infra.entity.IntlSoImei
 */
public interface IntlSoImeiMapper extends BaseMapper<IntlSoImei> {

    /**
     * 批量插入IMEI数据
     *
     * @param imeiList IMEI数据列表
     */
    void batchInsert(@Param("imeiList") List<IntlSoImeiBatchSaveData> imeiList);

    /**
     * 查询重复的IMEI记录
     *
     * @param snHashes       SN哈希列表
     * @param countryCode    国家代码
     * @param threeMonthsAgo 三个月前的时间戳
     * @param jobTitleCodes  职位代码列表
     * @return 重复记录列表
     */
    List<IntlSoImei> queryDuplicateRecords(@Param("snHashes") List<String> snHashes,
            @Param("countryCode") String countryCode,
            @Param("threeMonthsAgo") Long threeMonthsAgo,
            @Param("jobTitleCodes") List<Integer> jobTitleCodes);

    /**
     * 根据ID批量更新IMEI记录的指定字段
     *
     * @param imeiList IMEI数据列表
     * @return 更新记录数
     */
    int batchUpdateById(@Param("imeiList") List<IntlSoImei> imeiList);

    /**
     * 分页查询IMEI明细列表
     *
     * @param countryCode   国家代码
     * @param miId          用户miId
     * @param userStoreCodes  用户关联的门店ID列表
     * @param search        搜索关键字
     * @param reportingType 上报类型
     * @param storeCode     门店代码
     * @param productLine   产品线
     * @param storeType     门店类型
     * @param channelType   渠道类型
     * @param verifyResult  校验结果
     * @param startTime     开始时间
     * @param endTime       结束时间
     * @param offset        偏移量
     * @param limit         限制数量
     * @return IMEI明细列表
     */
    List<Map<String, Object>> queryImeiDetailList(@Param("countryCode") String countryCode,
            @Param("miId") Long miId,
            @Param("userStoreCodes") List<String> userStoreCodes,
            @Param("search") String search,
            @Param("reportingType") Integer reportingType,
            @Param("storeCode") List<String> storeCode,
            @Param("productLine") List<Integer> productLine,
            @Param("storeType") List<Integer> storeType,
            @Param("channelType") List<Integer> channelType,
            @Param("verifyResult") Integer verifyResult,
            @Param("startTime") Long startTime,
            @Param("endTime") Long endTime,
            @Param("offset") Integer offset,
            @Param("limit") Integer limit);

    /**
     * 按销售人员miId分页查询IMEI明细列表
     */
    List<Map<String, Object>> queryImeiDetailListBySalesmanMid(@Param("miId") Long miId,
            @Param("search") String search,
            @Param("reportingType") Integer reportingType,
            @Param("storeCode") List<String> storeCode,
            @Param("productLine") List<Integer> productLine,
            @Param("storeType") List<Integer> storeType,
            @Param("channelType") List<Integer> channelType,
            @Param("verifyResult") Integer verifyResult,
            @Param("startTime") Long startTime,
            @Param("endTime") Long endTime,
            @Param("offset") Integer offset,
            @Param("limit") Integer limit);

    /**
     * 按门店代码分页查询IMEI明细列表
     */
    List<Map<String, Object>> queryImeiDetailListByStoreCodes(@Param("userStoreCodes") List<String> userStoreCodes,
            @Param("search") String search,
            @Param("reportingType") Integer reportingType,
            @Param("storeCode") List<String> storeCode,
            @Param("productLine") List<Integer> productLine,
            @Param("storeType") List<Integer> storeType,
            @Param("channelType") List<Integer> channelType,
            @Param("verifyResult") Integer verifyResult,
            @Param("startTime") Long startTime,
            @Param("endTime") Long endTime,
            @Param("offset") Integer offset,
            @Param("limit") Integer limit);

    /**
     * 查询IMEI明细总数
     */
    Integer countImeiDetailList(@Param("countryCode") String countryCode,
            @Param("miId") Long miId,
            @Param("userStoreCodes") List<String> userStoreCodes,
            @Param("search") String search,
            @Param("reportingType") Integer reportingType,
            @Param("storeCode") List<String> storeCode,
            @Param("productLine") List<Integer> productLine,
            @Param("storeType") List<Integer> storeType,
            @Param("channelType") List<Integer> channelType,
            @Param("verifyResult") Integer verifyResult,
            @Param("startTime") Long startTime,
            @Param("endTime") Long endTime);

    /**
     * 按销售人员miId查询IMEI明细总数
     */
    Integer countImeiDetailListBySalesmanMid(@Param("miId") Long miId,
            @Param("search") String search,
            @Param("reportingType") Integer reportingType,
            @Param("storeCode") List<String> storeCode,
            @Param("productLine") List<Integer> productLine,
            @Param("storeType") List<Integer> storeType,
            @Param("channelType") List<Integer> channelType,
            @Param("verifyResult") Integer verifyResult,
            @Param("startTime") Long startTime,
            @Param("endTime") Long endTime);

    /**
     * 按门店代码查询IMEI明细总数
     */
    Integer countImeiDetailListByStoreCodes(@Param("userStoreCodes") List<String> userStoreCodes,
            @Param("search") String search,
            @Param("reportingType") Integer reportingType,
            @Param("storeCode") List<String> storeCode,
            @Param("productLine") List<Integer> productLine,
            @Param("storeType") List<Integer> storeType,
            @Param("channelType") List<Integer> channelType,
            @Param("verifyResult") Integer verifyResult,
            @Param("startTime") Long startTime,
            @Param("endTime") Long endTime);

    /**
     * 根据ID查询IMEI明细
     */
    List<Map<String, Object>> queryImeiDetailById(@Param("imeiId") String imeiId);

    /**
     * 查询IMEI汇总数据
     */
    List<Map<String, Object>> queryImeiSummary(@Param("countryCode") String countryCode,
            @Param("miId") Long miId,
            @Param("userStoreCodes") List<String> userStoreCodes,
            @Param("search") String search,
            @Param("storeCode") List<String> storeCode,
            @Param("productLine") List<String> productLine,
            @Param("storeType") List<Integer> storeType,
            @Param("channelType") List<Integer> channelType,
            @Param("verifyResult") Integer verifyResult,
            @Param("startTime") Long startTime,
            @Param("endTime") Long endTime);

    /**
     * 按销售人员miId查询IMEI汇总数据
     */
    List<Map<String, Object>> queryImeiSummaryBySalesmanMid(@Param("miId") Long miId,
            @Param("search") String search,
            @Param("storeCode") List<String> storeCode,
            @Param("productLine") List<String> productLine,
            @Param("storeType") List<Integer> storeType,
            @Param("channelType") List<Integer> channelType,
            @Param("verifyResult") Integer verifyResult,
            @Param("startTime") Long startTime,
            @Param("endTime") Long endTime);

    /**
     * 按门店代码查询IMEI汇总数据
     */
    List<Map<String, Object>> queryImeiSummaryByStoreCodes(@Param("userStoreCodes") List<String> userStoreCodes,
            @Param("search") String search,
            @Param("storeCode") List<String> storeCode,
            @Param("productLine") List<String> productLine,
            @Param("storeType") List<Integer> storeType,
            @Param("channelType") List<Integer> channelType,
            @Param("verifyResult") Integer verifyResult,
            @Param("startTime") Long startTime,
            @Param("endTime") Long endTime);

    List<IntlSoImei> selectByRetailerIds(@Param("retailIds") List<Long> retailIds);

    List<IntlSoImei> selectByRmsIds(@Param("rmsIds") List<String> rmsIds);

    void batchUpdate(@Param("list") List<IntlSoImei> imeiList);

    /**
     * 查询重复的IMEI记录
     *
     * @param verifyResult   验证状态
     * @param snHashes       SN哈希列表
     * @param jobTitleCodes  职位代码列表
     * @return 重复记录列表
     */
    List<IntlSoImei> queryBySnHashAndJobTitle(@Param("verifyResult") Integer verifyResult, @Param("snHashes") List<String> snHashes,
                                              @Param("jobTitleCodes") List<Integer> jobTitleCodes);

}
