package com.mi.info.intl.retail.so.domain.upload.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 销量阵地信息
 *
 * <AUTHOR>
 * @date 2025/07/29
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "intl_so_org_info")
@Data
@Accessors(chain = true)
public class IntlSoOrgInfo {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 门店变更日志关联ID
     */
    @TableField(value = "store_changelog_id")
    private Long storeChangelogId;

    /**
     * RMS门店code
     */
    @TableField(value = "store_rms_code")
    private String storeRmsCode;

    /**
     * 门店编码（唯一业务编码）
     */
    @TableField(value = "store_code")
    private String storeCode;

    /**
     * 门店等级（如 A、B、C 等）
     */
    @TableField(value = "store_grade")
    private Integer storeGrade;

    /**
     * 门店类型（如 直营店、加盟店 等）
     */
    @TableField(value = "store_type")
    private Integer storeType;

    /**
     * 门店渠道类型（线上/线下等，需结合业务明确）
     */
    @TableField(value = "store_channel_type")
    private Integer storeChannelType;

    /**
     * 门店是否含 SR（0：否；1：是）
     */
    @TableField(value = "store_hasSR")
    private Integer storeHasSR;

    /**
     * 门店是否含 PC（0：否；1：是）
     */
    @TableField(value = "store_hasPC")
    private Integer storeHasPC;

    /**
     * 国家缩写
     */
    @TableField(value = "country_code")
    private String countryCode;

    /**
     * 位置变更日志关联ID
     */
    @TableField(value = "position_changelog_id")
    private Long positionChangelogId;

    /**
     * RMS阵地code
     */
    @TableField(value = "position_rms_code")
    private String positionRmsCode;

    /**
     * 位置编码（唯一业务编码）
     */
    @TableField(value = "position_code")
    private String positionCode;

    /**
     * 位置类型（如 仓库、展厅 等）
     */
    @TableField(value = "position_type")
    private Integer positionType;

    /**
     * 关联零售商ID
     */
    @TableField(value = "retailer_code")
    private String retailerCode;

    /**
     * 门店唯一主键
     */
    @TableField(value = "store_id")
    private Integer storeId;

    /**
     * 阵地唯一主键
     */
    @TableField(value = "position_id")
    private Integer positionId;

    /**
     * 零售商唯一主键
     */
    @TableField(value = "retailer_id")
    private Long retailerId;

    /**
     * 是否有做功
     */
    @TableField(value = "store_class")
    private Integer storeClass;

    /**
     * 营业状态
     */
    @TableField(value = "store_status")
    private Integer storeStatus;
}