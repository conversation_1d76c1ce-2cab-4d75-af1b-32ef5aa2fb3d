package com.mi.info.intl.retail.so.domain.datasync;

import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoImei;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoQty;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * RMS系统服务接口
 * 提供与RMS系统交互的方法
 */
@Service
public class RmsService {

    /**
     * 根据零售系统ID批量获取IMEI数据
     */
    public List<IntlSoImei> batchGetImeiByRetailIds(List<Long> retailIds) {
        // TODO: 实现与RMS系统的API调用
        return Collections.emptyList();
    }

    /**
     * 根据RMS系统ID批量获取IMEI数据
     */
    public List<IntlSoImei> batchGetImeiByRmsIds(List<String> rmsIds) {
        // TODO: 实现与RMS系统的API调用
        return Collections.emptyList();
    }

    /**
     * 根据零售系统ID批量获取QTY数据
     */
    public List<IntlSoQty> batchGetQtyByRetailIds(List<Long> retailIds) {
        // TODO: 实现与RMS系统的API调用
        return Collections.emptyList();
    }

    /**
     * 根据RMS系统ID批量获取QTY数据
     */
    public List<IntlSoQty> batchGetQtyByRmsIds(List<String> rmsIds) {
        // TODO: 实现与RMS系统的API调用
        return Collections.emptyList();
    }

    /**
     * 分页获取RMS系统的IMEI数据
     */
    public List<IntlSoImei> getImeiFormRmsByPage(long modifiedStart, long modifiedEnd, String afterId) {
        // TODO: 实现与RMS系统的API调用
        return Collections.emptyList();
    }

    /**
     * 分页获取RMS系统的QTY数据
     */
    public List<IntlSoQty> getQtyFormRmsByPage(long modifiedStart, long modifiedEnd, String afterId) {
        // TODO: 实现与RMS系统的API调用
        return Collections.emptyList();
    }
}
