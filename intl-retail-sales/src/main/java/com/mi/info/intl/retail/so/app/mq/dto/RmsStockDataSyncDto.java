package com.mi.info.intl.retail.so.app.mq.dto;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Accessors(chain = true)
@Data
public class RmsStockDataSyncDto {

    /**
     * 上一次同步id
     */
    private Long lastSyncId;

    private Integer batchSize;

    /**
     * 一次查询数量
     */
    private Integer querySize;

    /**
     * 是否时差额同步
     */
    private Integer isUpdate;
}
