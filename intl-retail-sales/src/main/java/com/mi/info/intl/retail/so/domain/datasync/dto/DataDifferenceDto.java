package com.mi.info.intl.retail.so.domain.datasync.dto;

import com.mi.info.intl.retail.so.domain.datasync.enums.DataCompareResult;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DataDifferenceDto {
    // 差异分类：RETAIL_ONLY / RMS_ONLY / STATUS_INCONSISTENT
    private DataCompareResult differenceCategory;
    // 总差异数
    private Long totalNum;
    // 有差异的前10条数据ID
    private List<String> ids;
}
