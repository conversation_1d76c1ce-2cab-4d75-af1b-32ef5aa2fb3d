package com.mi.info.intl.retail.so.domain.upload.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.info.intl.retail.api.country.CountryTimeZoneApiService;
import com.mi.info.intl.retail.api.country.dto.CountryDTO;
import com.mi.info.intl.retail.api.fieldforce.user.UserApiService;
import com.mi.info.intl.retail.api.fieldforce.user.dto.IntlRmsUserNewDto;
import com.mi.info.intl.retail.api.so.rule.blacklist.SoBlacklistApiService;
import com.mi.info.intl.retail.component.ComponentLocator;
import com.mi.info.intl.retail.core.config.CommonThreadPoolConfig;
import com.mi.info.intl.retail.exception.BizException;
import com.mi.info.intl.retail.intlretail.service.api.masterdata.dto.SnBlackDTO;
import com.mi.info.intl.retail.intlretail.service.api.masterdata.dto.SnBlackRequest;
import com.mi.info.intl.retail.so.domain.datasync.enums.DataSyncDataTypeEnum;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoSnBlacklist;
import com.mi.info.intl.retail.so.domain.upload.enums.SerialNumberEnum;
import com.mi.info.intl.retail.so.domain.upload.service.IntlSoSnBlacklistService;
import com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlSoSnBlacklistMapper;
import com.mi.info.intl.retail.so.metrics.MqAction;
import com.mi.info.intl.retail.so.metrics.SyncSoToEsMetricsService;
import com.mi.info.intl.retail.utils.JsonUtil;
import com.mi.info.intl.retail.utils.intl.IntlTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【intl_so_sn_blacklist(销量SO序列号黑名单)】的数据库操作Service实现
 * @createDate 2025-07-24 20:05:41
 */
@Slf4j
@Service
public class IntlSoSnBlacklistServiceImpl extends ServiceImpl<IntlSoSnBlacklistMapper, IntlSoSnBlacklist>
        implements IntlSoSnBlacklistService, SoBlacklistApiService {
    
    @Resource
    private UserApiService userApiService;

    @Resource
    private CountryTimeZoneApiService countryTimeZoneApiService;

    @Resource
    private SyncSoToEsMetricsService syncSoToEsMetricsService;

    /**
     * 定义线程池配置为类属性
     */
    private final CommonThreadPoolConfig threadPoolConfig = CommonThreadPoolConfig.BATCH_QUERY_DEVICE_INFO;

    @Override
    public IPage<SnBlackDTO> pageList(SnBlackRequest request) {
        log.info("开始查询黑名单数据，查询条件: {}", request);
        // 创建分页对象
        request.setOffset((request.getPageNum() - 1) * request.getPageSize());

        Page<SnBlackDTO> pageDTO = new Page<>(request.getPageNum(), request.getPageSize());

        // 异步获取总数
        CompletableFuture<Integer> countFuture = CompletableFuture.supplyAsync(() ->
                this.baseMapper.countPageList(request), threadPoolConfig.getExecutor());

        // 异步获取黑名单列表
        CompletableFuture<List<IntlSoSnBlacklist>> blacklistListFuture = CompletableFuture.supplyAsync(() ->
                this.baseMapper.selectPageList(request), threadPoolConfig.getExecutor());

        try {
            long count = countFuture.get();
            if (count == 0) {
                return Page.of(request.getPageNum(), request.getPageSize(), count);
            }

            List<IntlSoSnBlacklist> blacklistList = blacklistListFuture.get();

            // 如果查询结果为空，直接返回空的分页对象
            if (CollUtil.isEmpty(blacklistList)) {
                return pageDTO;
            }

            List<SnBlackDTO> processedList = blacklistList.stream()
                    .map(item -> {
                        SnBlackDTO itemDTO = ComponentLocator.getConverter().convert(item, SnBlackDTO.class);
                        // 在这里添加数据处理逻辑
                        SerialNumberEnum serialNumber = SerialNumberEnum.getByCode(item.getType());
                        itemDTO.setType(null == serialNumber ? StringUtils.EMPTY : serialNumber.getName());
                        String createdByName = formatUserName(item.getCreatedByName(), item.getCreatedBy());
                        itemDTO.setCreatedByName(createdByName);
                        String modifiedByName = formatUserName(item.getModifiedByName(), item.getModifiedBy());
                        itemDTO.setModifiedByName(modifiedByName);
                        itemDTO.setCreatedOn(IntlTimeUtil.parseTimestampToAreaTime(request.getLocale(), item.getCreatedOn()));
                        itemDTO.setModifiedOn(IntlTimeUtil.parseTimestampToAreaTime(request.getLocale(), item.getModifiedOn()));
                        return itemDTO;
                    }).collect(Collectors.toList());

            // 设置处理后的记录
            // 创建返回的分页对象
            pageDTO.setTotal(count);
            pageDTO.setRecords(processedList);

            return pageDTO;
        } catch (InterruptedException e) {
            // 重新中断当前线程
            Thread.currentThread().interrupt();
            log.error("查询黑名单数据被中断", e);
            throw new BizException("查询黑名单数据失败");
        } catch (Exception e) {
            log.error("查询黑名单数据异常", e);
            throw new BizException("查询黑名单数据失败");
        }
    }



    /**
     * 同步SO序列号黑名单
     *
     * @param content
     */
    @Override
    public void syncSoSnBlacklist(Object content) {
        if (null == content) {
            log.error("syncSoSnBlacklist content is null");
            return;
        }

        IntlSoSnBlacklist intlSoSnBlacklist =
                JsonUtil.json2bean(JsonUtil.bean2json(content), IntlSoSnBlacklist.class);

        if (intlSoSnBlacklist != null && intlSoSnBlacklist.getRmsId() != null) {
          log.info("blacklist json to entity:{}", intlSoSnBlacklist);
            IntlSoSnBlacklist existRecord;
            if (intlSoSnBlacklist.getId() != null) {
                existRecord = this.baseMapper.selectOne(Wrappers.<IntlSoSnBlacklist>lambdaQuery()
                        .eq(IntlSoSnBlacklist::getId, intlSoSnBlacklist.getId()));
            } else {
                existRecord = this.baseMapper.selectOne(Wrappers.<IntlSoSnBlacklist>lambdaQuery()
                        .eq(IntlSoSnBlacklist::getRmsId, intlSoSnBlacklist.getRmsId()));
            }

            // 获取国家信息
            Optional<CountryDTO> countryInfoByCode =
                    countryTimeZoneApiService.getCountryInfoByCode(intlSoSnBlacklist.getCountryCode());

            String countryName = countryInfoByCode.map(CountryDTO::getCountryName).orElse(StringUtils.EMPTY);

            // 设置通用属性
            intlSoSnBlacklist.setCountryName(countryName);
            intlSoSnBlacklist.setModifiedOn(System.currentTimeMillis());

            if (existRecord != null) {
                // 更新记录
                intlSoSnBlacklist.setId(existRecord.getId());
                this.baseMapper.updateById(intlSoSnBlacklist);
                log.info("addSoSnBlacklist_update:{}", intlSoSnBlacklist);
            } else {
                // 插入新记录
                intlSoSnBlacklist.setCreatedOn(System.currentTimeMillis());
                this.baseMapper.insert(intlSoSnBlacklist);
                log.info("addSoSnBlacklist_insert:{}", intlSoSnBlacklist);
            }
        }
    }


    private String formatUserName(String name, Object account) {
        if (StringUtils.isNotEmpty(name)) {
            return String.format("%s(%s)", name, account);
        }
        return String.valueOf(account);
    }
}




