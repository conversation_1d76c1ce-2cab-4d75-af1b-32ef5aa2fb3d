package com.mi.info.intl.retail.so.domain.upload.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

/**
 * 导入日志表
 *
 * @TableName intl_import_log
 */
@TableName(value = "intl_import_log")
@Data
public class IntlImportLog {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 导入类型枚举：1-IMEI，2-QTY
     */
    @TableField(value = "type")
    private Integer type;

    /**
     * 导入动作类型：1：新增 2：修改
     */
    @TableField(value = "action_type")
    private Integer actionType;

    /**
     * 源文件id
     */
    @TableField(value = "source_file_id")
    private Long sourceFileId;

    /**
     * 结果文件id
     */
    @TableField(value = "result_file_id")
    private Long resultFileId;

    /**
     * 源文件链接
     */
    @TableField(value = "source_file_url")
    private String sourceFileUrl;

    /**
     * 结果文件链接
     */
    @TableField(value = "result_file_url")
    private String resultFileUrl;

    /**
     * 导入状态：0-处理中、1-成功、2-失败
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 失败原因
     */
    @TableField(value = "error_msg")
    private String errorMsg;

    /**
     * 创建时间
     */
    @TableField(value = "created_at")
    private Long createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at")
    private Long updatedAt;

    /**
     * 创建者
     */
    @TableField(value = "created_by")
    private Long createdBy;

    /**
     * 数据行总数
     */
    @TableField(value = "total_count")
    private Integer totalCount;

    /**
     * 任务名称
     */
    @TableField(value = "task_name")
    private String taskName;

    /**
     * 数据源
     */
    @TableField(value = "data_source")
    private Integer dataSource;

    /**
     * 文件名
     */
    @TableField(value = "file_name")
    private String fileName;

    /**
     * 执行状态
     */
    @TableField(value = "execute_status")
    private Integer executeStatus;

    /**
     * 完成数量
     */
    @TableField(value = "finish_count")
    private Integer finishCount;

    /**
     * 导入耗时（毫秒）
     */
    @TableField(value = "import_duration")
    private Integer importDuration;
}