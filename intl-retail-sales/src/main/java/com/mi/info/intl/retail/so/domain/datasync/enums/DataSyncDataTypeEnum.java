package com.mi.info.intl.retail.so.domain.datasync.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 0：SO;1 Qty;2 SNblacklis
 */
@Getter
@AllArgsConstructor
public enum DataSyncDataTypeEnum {

    IMEI(0, "imei"),

    QTY(1, "qty"),

    SN_BLACKLIST(2, "sn_blacklist"),
    ;

    private int code;

    private String message;

    public static DataSyncDataTypeEnum getEnumByMessage(String message) {
        for (DataSyncDataTypeEnum value : DataSyncDataTypeEnum.values()) {
            if (value.message.equals(message)) {
                return value;
            }
        }
        return null;
    }

}
