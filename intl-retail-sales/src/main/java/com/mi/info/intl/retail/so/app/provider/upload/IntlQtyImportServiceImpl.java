package com.mi.info.intl.retail.so.app.provider.upload;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.mi.info.intl.retail.api.fieldforce.user.IntlRmsUserApiService;
import com.mi.info.intl.retail.api.fieldforce.user.UserApiService;
import com.mi.info.intl.retail.api.fieldforce.user.dto.UserInfoDTO;
import com.mi.info.intl.retail.api.front.dto.RmsPositionInfoRes;
import com.mi.info.intl.retail.api.front.dto.RmsStoreInfoDto;
import com.mi.info.intl.retail.api.front.position.IntlPositionApiService;
import com.mi.info.intl.retail.api.front.position.dto.StoreValidationInfoDTO;
import com.mi.info.intl.retail.api.product.ProductApiService;
import com.mi.info.intl.retail.intlretail.service.api.fds.FdsService;
import com.mi.info.intl.retail.intlretail.service.api.fds.dto.FdsUploadResult;
import com.mi.info.intl.retail.intlretail.service.api.retailer.dto.CommonResponse;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.SubmitQtyReq;
import com.mi.info.intl.retail.intlretail.service.api.so.upload.QtyImportService;
import com.mi.info.intl.retail.intlretail.service.api.so.upload.dto.QtyImportRequest;
import com.mi.info.intl.retail.intlretail.service.api.so.upload.dto.QtyImportResponse;
import com.mi.info.intl.retail.intlretail.service.api.so.upload.dto.QtyImportRowData;
import com.mi.info.intl.retail.so.app.IntlQtyServiceImpl;
import com.mi.info.intl.retail.so.app.provider.enums.ReportingTypeEnum;
import com.mi.info.intl.retail.so.app.provider.upload.dto.QtyValidationResult;
import com.mi.info.intl.retail.so.app.provider.upload.dto.StoreValidationContext;
import com.mi.info.intl.retail.so.app.provider.enums.StoreOperationStatusEnums;
import com.mi.info.intl.retail.so.app.provider.upload.listener.QtyImportExcelListener;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlImportLog;
import com.mi.info.intl.retail.so.domain.upload.service.IntlImportLogService;
import com.mi.info.intl.retail.so.infra.database.mapper.upload.ImeiImportMapper;
import com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlSoQtyMapper;
import com.mi.info.intl.retail.so.util.ExcelFileUtil;
import com.mi.info.intl.retail.so.util.SalesTimeValidUtil;
import com.mi.info.intl.retail.so.util.dto.ExcelFileResource;
import com.mi.info.intl.retail.utils.intl.IntlTimeUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;
import com.mi.info.intl.retail.model.CommonApiResponse;
import java.util.concurrent.ConcurrentHashMap;
import com.mi.info.intl.retail.so.app.provider.enums.ImportDurationEnum;


/**
 * QTY数据导入服务实现类
 *
 * <AUTHOR>
 * @date 2025/1/28
 */
@Slf4j
@Service
public class IntlQtyImportServiceImpl implements QtyImportService {

    @Resource
    private IntlImportLogService intlImportLogService;
    @Autowired
    private UserApiService userApiService;
    @Resource
    private IntlPositionApiService intlPositionApiService;

    @Resource
    private ProductApiService productApiService;

    @Resource
    private FdsService fdsService;

    @Resource
    private IntlQtyServiceImpl intlQtyService;

    @Resource
    private ImeiImportMapper imeiImportMapper;

    private final OkHttpClient httpClient = new OkHttpClient();
    @Resource
    private IntlSoQtyMapper intlSoQtyMapper;
    // 最大导入行数限制
    private static final int MAX_IMPORT_ROWS = 5000;


    // 文件前缀/后缀
    private static final String FILE_PREFIX = "qty_import_error_";
    private static final String FILE_SUFFIX = ".xlsx";
    // 错误信息常量
    private static final String ERROR_EXCEED_MAX_ROWS = "The number of import rows exceeds the maximum limit of 5000";
    private static final String ERROR_EXCEED_MIN_ROWS = "There is no data available for uploading";
    private static final String ERROR_VALIDATION_FAILED = "Validation failed";
    private static final String ERROR_MANDATORY_FIELDS = "Items in red font are mandatory fields.";
    private static final String ERROR_DATE_WITHIN_45_DAYS = "Only data within 45 days can be reported.";
    private static final String ERROR_REPEATED_DATA = "Repeated data.";
    private static final String ERROR_NOT_IN_STORE = "You are not in this store.";
    private static final String ERROR_INVALID_DATE_FORMAT = "Invalid sales time format";
    private static final String ERROR_STORE_NOT_FOUND = "Store Code was not found.";
    private static final String ERROR_STORE_NOT_OPENING = "This store is not open.";
    private static final String ERROR_TEMPLATE_HEADER_MISMATCH = "Please use the template to upload.";

    // IMEI导入模板表头定义（按顺序）
    private static final String[] EXPECTED_HEADERS = {
            "Store Code",
            "69 Code",
            "SKU",
            "Product ID",
            "Qty",
            "Sales Time",
            "Remark"

    };


    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonApiResponse<QtyImportResponse> importQtyData(QtyImportRequest request) {
        log.info("importQtyData start, request: {}", JSON.toJSONString(request));
        int totalCount = 0;
        int failedCount = 0;
        
        // 记录开始时间
        long startTime = System.currentTimeMillis();
        

        try {
            // 1. 参数校验
            if (request.getImportLogId() == null || request.getMiId() == null) {
                return buildErrorResponse(new QtyImportResponse(), "importLogId and miId are required");
            }

            // 2. 获取源文件URL
            String sourceFileUrl = getSourceFileUrl(request);
            if (StringUtils.isBlank(sourceFileUrl)) {
                return buildErrorResponse(new QtyImportResponse(), "Source file URL is required");
            }

            // 3. 下载并解析Excel文件
            List<QtyImportRowData> excelDataList = downloadAndParseExcel(sourceFileUrl);
            if (excelDataList == null) {
                // 表头校验失败
                updateImportLogStatus(request.getImportLogId(), 2, ERROR_TEMPLATE_HEADER_MISMATCH, null);
                return createErrorResponse(request.getImportLogId(), 2, ERROR_TEMPLATE_HEADER_MISMATCH, null);
            }

            // 4. 检查数据量是否超过限制
            if (excelDataList.size() > MAX_IMPORT_ROWS) {
                updateImportLogStatus(request.getImportLogId(), 2, ERROR_EXCEED_MAX_ROWS, null);
                return createErrorResponse(request.getImportLogId(), 2, ERROR_EXCEED_MAX_ROWS, null);
            }
            if (excelDataList.size() == 0) {
                updateImportLogStatus(request.getImportLogId(), 2, ERROR_EXCEED_MIN_ROWS, null);
                return createErrorResponse(request.getImportLogId(), 2, ERROR_EXCEED_MIN_ROWS, null);
            }

            // 5. 必填项校验和合法性校验
            validateMandatoryAndLegality(excelDataList);

            // 6. 重复性校验
            validateDuplicatesByStoreAndProduct(excelDataList);

            // 7. 统计数据
            totalCount = excelDataList.size();
            List<QtyImportRowData> validDataList = excelDataList.stream()
                    .filter(QtyImportRowData::getValid)
                    .collect(Collectors.toList());
            failedCount = totalCount - validDataList.size();

            // 8. 检查是否有校验失败的数据
            boolean hasErrors = failedCount > 0;

            if (hasErrors) {
                // 生成错误文件并上传
                String errorFileUrl = generateResultFile(excelDataList);
                updateImportLogStatus(request.getImportLogId(), 2, ERROR_VALIDATION_FAILED, errorFileUrl, totalCount);
                return createErrorResponse(request.getImportLogId(), 2, ERROR_VALIDATION_FAILED, errorFileUrl, totalCount, failedCount);
            }

            // 9. 异步执行数据校验
            int finalTotalCount = totalCount;
            CompletableFuture<Void> validationFuture = CompletableFuture.runAsync(() -> {
                try {
                    QtyValidationResult validationResult = validateData(excelDataList, request.getMiId());
                    int finalFailedCount = 0;
                    int finalValidCount = 0;
                    // 7. 统计数据
                    finalValidCount = excelDataList.size();
                    List<QtyImportRowData> finavalidDataList = validationResult.getDataList().stream()
                            .filter(QtyImportRowData::getValid)
                            .collect(Collectors.toList());
                    finalFailedCount = finalValidCount - finavalidDataList.size();

                    // 8. 检查是否有校验失败的数据
                    boolean finahasErrors = finalFailedCount > 0;
                    String FileUrl = generateResultFile(excelDataList);
                    if (finahasErrors) {
                        // 生成错误文件并上传
                        updateImportLogStatus(request.getImportLogId(), 2, ERROR_VALIDATION_FAILED, FileUrl, finalValidCount);
                    }
                    // 10. 异步执行创建QTY数据
                    CompletableFuture<Void> importFuture = CompletableFuture.runAsync(() -> {
                        try {
                            boolean createSuccess = executeImport(validationResult, request.getMiId(), request.getImportLogId());

                            if (createSuccess) {
                                // 查询实际成功导入的数量
                                try {
                                    Integer actualSuccessCount = intlSoQtyMapper.getSuccessQtyCountByImportLogId(request.getImportLogId());
                                    if (actualSuccessCount != null && actualSuccessCount > 0) {
                                        updateImportLogStatus(request.getImportLogId(), 1, null, FileUrl, actualSuccessCount);
                                        log.info("QTY数据创建成功, importLogId: {}, 实际成功数量: {}", request.getImportLogId(), actualSuccessCount);
                                    } else {
                                        updateImportLogStatus(request.getImportLogId(), 1, null, FileUrl, finalTotalCount);
                                        log.info("QTY数据创建成功, importLogId: {}, totalCount: {}", request.getImportLogId(), finalTotalCount);
                                    }
                                } catch (Exception e) {
                                    log.error("查询成功导入数量失败, importLogId: {}", request.getImportLogId(), e);
                                    updateImportLogStatus(request.getImportLogId(), 1, null, FileUrl, finalTotalCount);
                                }
                            } else {
                                updateImportLogStatus(request.getImportLogId(), 2, "QTY creation failed", FileUrl, finalTotalCount);
                                log.error("QTY数据创建失败, importLogId: {}", request.getImportLogId());
                            }
                        } catch (Exception e) {
                            log.error("异步创建QTY数据失败, importLogId: {}", request.getImportLogId(), e);
                            updateImportLogStatus(request.getImportLogId(), 2, "QTY creation failed: " + e.getMessage(), FileUrl, finalTotalCount);
                        }
                    });

                    // 等待导入完成
                    importFuture.get();

                } catch (Exception e) {
                    log.error("异步数据校验失败, importLogId: {}", request.getImportLogId(), e);
                    updateImportLogStatus(request.getImportLogId(), 2, "Data validation failed: " + e.getMessage(), null, finalTotalCount);
                }
            });

            // 立即返回成功响应，不等待异步任务完成
            log.info("QTY导入任务已提交异步执行, importLogId: {}, totalCount: {}", request.getImportLogId(), totalCount);
            return createSuccessResponse(request.getImportLogId(), totalCount, failedCount);

        } catch (Exception e) {
            log.error("importQtyData error", e);
            updateImportLogStatus(request.getImportLogId(), 2, "System error: " + e.getMessage(), null, totalCount);
            return createErrorResponse(request.getImportLogId(), 2, "System error: " + e.getMessage(), null, totalCount, failedCount);
        } finally {
            // 计算总耗时并更新到日志表
            long endTime = System.currentTimeMillis();
            long totalDuration = endTime - startTime;
            ImportDurationEnum durationEnum = ImportDurationEnum.getByDurationMillis(totalDuration);
            
            try {
                updateImportDuration(request.getImportLogId(), durationEnum.getValue());
                log.info("导入耗时统计完成, importLogId: {}, 耗时: {}ms, 耗时等级: {}", 
                    request.getImportLogId(), totalDuration, durationEnum.getDescription());
            } catch (Exception e) {
                log.error("更新导入耗时失败, importLogId: {}", request.getImportLogId(), e);
            }
        }
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonApiResponse<QtyImportResponse> importData(QtyImportRequest request) {
        // 调用原有的importQtyData方法，保持逻辑一致性
        return importQtyData(request);
    }

    /**
     * 获取源文件URL
     */
    private String getSourceFileUrl(QtyImportRequest request) {
        if (StringUtils.isNotBlank(request.getSourceFileUrl())) {
            return request.getSourceFileUrl();
        }

        // 根据importLogId查询
        IntlImportLog importLog = intlImportLogService.getById(request.getImportLogId());
        return importLog != null ? importLog.getSourceFileUrl() : null;
    }

    /**
     * 下载并解析Excel文件
     */
    private List<QtyImportRowData> downloadAndParseExcel(String fileUrl) throws IOException {
        List<QtyImportRowData> rowDataList = new ArrayList<>();
        File tempFile = null;

        try {
            // 下载文件到临时目录
            tempFile = downloadFile(fileUrl);
            if (tempFile == null || !tempFile.exists()) {
                throw new IOException("Failed to download file from: " + fileUrl);
            }

            // 先校验表头
            if (!ExcelFileUtil.validateExcelHeaders(tempFile, EXPECTED_HEADERS)) {
                log.warn("Excel表头校验失败");
                return null;
            }

            // 使用EasyExcel解析
            AtomicInteger rowIndex = new AtomicInteger(1);
            EasyExcel.read(tempFile, QtyImportRowData.class, new QtyImportExcelListener(rowDataList, rowIndex))
                    .sheet()
                    .doRead();

        } finally {
            // 清理临时文件
            if (tempFile != null && tempFile.exists()) {
                Files.delete(tempFile.toPath());
            }
        }

        log.info("Excel文件解析完成，共解析{}行数据", rowDataList.size());
        return rowDataList;
    }

    /**
     * 下载文件到临时目录
     */
    private File downloadFile(String fileUrl) throws IOException {
        Request request = new Request.Builder().url(fileUrl).build();
        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Failed to download file: " + response.code());
            }

            File tempFile = File.createTempFile("qty_import_", ".xlsx");
            try (FileOutputStream fos = new FileOutputStream(tempFile);
                 InputStream is = response.body().byteStream()) {
                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = is.read(buffer)) != -1) {
                    fos.write(buffer, 0, bytesRead);
                }
            }
            return tempFile;
        }
    }



    /**
     * 数据校验
     */
    private QtyValidationResult validateData(List<QtyImportRowData> rowDataList, Long miId) {
        log.info("开始数据校验，数据量: {}", rowDataList.size());


        // 1. 门店和用户权限校验
        Map<String, StoreValidationContext> storeContextMap = validateStoreAndUserPermissions(rowDataList, miId);
        // 2. 统一产品验证
        validateProductUnified(rowDataList, miId);
        log.info("数据校验完成");

        QtyValidationResult result = new QtyValidationResult();
        result.setDataList(rowDataList);
        result.setStoreContextMap(storeContextMap);
        return result;
    }

    /**
     * 必填项校验和合法性校验
     */
    private void validateMandatoryAndLegality(List<QtyImportRowData> dataList) {
        for (QtyImportRowData data : dataList) {

            if (StringUtils.isNotBlank(data.getFailedReason())) {
                continue; // 已经有错误的跳过
            }
            // 必填字段校验
            if (StringUtils.isBlank(data.getStoreCode()) ||
                StringUtils.isBlank(data.getSalesTime()) ||
                StringUtils.isBlank(data.getQty()) ||
                (StringUtils.isBlank(data.getSku()) && StringUtils.isBlank(data.getProductId()) && StringUtils.isBlank(data.getCode69()))) {
                data.setValid(false);
                data.setFailedReason(ERROR_MANDATORY_FIELDS);
                continue;
            }


            // 销售时间格式校验（这里只做基本格式校验，时区相关校验在门店校验阶段进行）
            if (!isValidTimeFormat(data.getSalesTime())) {
                data.setValid(false);
                data.setFailedReason(ERROR_INVALID_DATE_FORMAT);
                continue;
            }
            // 数量校验 - 销售数量必须大于0
            if (StringUtils.isBlank(data.getQty()) || Integer.parseInt(data.getQty()) <= 0) {
                data.setValid(false);
                data.setFailedReason("The sales quantity must be greater than 0");
            }

            // Remark字段长度校验 - 不能超过200字符
            if (StringUtils.isNotBlank(data.getRemark()) && data.getRemark().length() > 200) {
                data.setValid(false);
                data.setFailedReason("The number of words in the remark should not exceed 200.");
            }
        }
    }

    /**
     * 检查时间格式是否有效
     */
    private boolean isValidTimeFormat(String timeStr) {
        if (StringUtils.isBlank(timeStr)) {
            return false;
        }

        String[] patterns = {
            "yyyy-MM-dd HH:mm:ss",
            "yyyy-MM-dd",
            "yyyy/MM/dd HH:mm:ss",
            "yyyy/MM/dd",
            "yyyy/M/dd",
            "yyyy/M/d",
            "MM/dd/yyyy",
            "M/dd/yyyy",
            "M/d/yyyy",
            "dd/MM/yyyy",
            "d/MM/yyyy",
            "d/M/yyyy"
        };

        for (String pattern : patterns) {
            try {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
                if (pattern.contains("HH:mm:ss")) {
                    LocalDateTime.parse(timeStr, formatter);
                } else {
                    LocalDate.parse(timeStr, formatter);
                }
                return true;
            } catch (DateTimeParseException ignored) {
                // 继续尝试下一个格式
            }
        }
        return false;
    }









    /**
     * 重复性校验 - 基于storeCode + sku/ProductId/code69组合
     */
    private void validateDuplicatesByStoreAndProduct(List<QtyImportRowData> dataList) {
        // 记录已出现的组合及其第一次出现的信息，用于检测重复
        Map<String, String> seenCombinationsMap = new HashMap<>();

        for (QtyImportRowData data : dataList) {
            if (StringUtils.isNotBlank(data.getFailedReason())) {
                continue; // 已经有错误的跳过
            }

            String storeCode = data.getStoreCode();
            if (StringUtils.isBlank(storeCode)) {
                continue; // 门店代码为空时跳过
            }

            // 构建组合键：storeCode + sku/ProductId/code69
            String productIdentifier = null;
            if (StringUtils.isNotBlank(data.getSku())) {
                productIdentifier = data.getSku();
            } else if (StringUtils.isNotBlank(data.getProductId())) {
                productIdentifier = data.getProductId();
            } else if (StringUtils.isNotBlank(data.getCode69())) {
                productIdentifier = data.getCode69();
            }

            if (StringUtils.isNotBlank(productIdentifier)) {
                String combinationKey = storeCode.toUpperCase() + "_" + productIdentifier.toUpperCase();
                if (seenCombinationsMap.containsKey(combinationKey)) {
                    // 发现重复，设置错误信息，包含第一次出现的组合信息
                    String firstOccurrence = seenCombinationsMap.get(combinationKey);
                    data.setValid(false);
                    data.setFailedReason(ERROR_REPEATED_DATA + " First occurrence: " + firstOccurrence);
                } else {
                    seenCombinationsMap.put(combinationKey, "storeCode:" + storeCode + ", product:" + productIdentifier);
                }
            }
        }
    }

    /**
     * 生成错误文件
     */
    private String generateResultFile(List<QtyImportRowData> dataList) throws IOException {
        try (ExcelFileResource excelResource = ExcelFileUtil.generateExcelFile(
                dataList,
                QtyImportRowData.class,
                "数据报表")) {

            File excelFile = excelResource.getFile();
            // 上传到FDS服务器
            String timestamp = String.valueOf(System.currentTimeMillis() / 1000L);
            String fileName = FILE_PREFIX + timestamp + FILE_SUFFIX;
            FdsUploadResult uploadResult = fdsService.upload(fileName, excelFile, true);
            String fileUrl = uploadResult.getUrl();

            log.info("错误数据文件已上传到FDS: {}", fileUrl);
            return fileUrl;
        }
    }


    /**
     * 执行数据导入
     */
    private boolean executeImport(QtyValidationResult validationResult, Long miId, Long importLogId) {
        try {
            List<QtyImportRowData> dataList = validationResult.getDataList();
            Map<String, StoreValidationContext> storeContextMap = validationResult.getStoreContextMap();

            // 按门店分组
            Map<String, List<QtyImportRowData>> storeGroups = dataList.stream()
                    .filter(QtyImportRowData::getValid)
                    .collect(Collectors.groupingBy(QtyImportRowData::getStoreCode));

            // 使用AtomicBoolean来跟踪并行处理中的任何失败
            AtomicBoolean allSuccess = new AtomicBoolean(true);

            // 并行处理每个门店的数据
            storeGroups.entrySet().parallelStream().forEach(entry -> {
                String storeCode = entry.getKey();
                List<QtyImportRowData> groupData = entry.getValue();

                // 从校验上下文中获取已有的信息，避免重复查询
                StoreValidationContext context = storeContextMap.get(storeCode);
                if (context == null) {
                    log.error("门店{}校验未通过，跳过QTY创建", storeCode);
                    return; // 并行流中使用return代替continue
                }

                StoreValidationInfoDTO storeValidationInfo = context.getStoreValidationInfo();

                // 转换为SubmitQtyReq并提交
                SubmitQtyReq submitReq = convertToSubmitQtyReq(groupData, storeValidationInfo, storeValidationInfo.getBestPosition(), miId, importLogId);
                CommonResponse<Object> submitResult = intlQtyService.submitQty(submitReq);

                if (submitResult.getCode() != 0) {
                    log.error("门店{}QTY提交失败: {}", storeCode, submitResult.getMessage());
                    allSuccess.set(false);
                } else {
                    // QTY提交成功后，查询成功导入的数量并更新日志表
                    try {
                        Integer successCount = intlSoQtyMapper.getSuccessQtyCountByImportLogId(importLogId);
                        if (successCount != null && successCount > 0) {
                            updateImportTotalCount(importLogId, successCount);
                            log.info("门店{}QTY提交成功，成功导入数量: {}", storeCode, successCount);
                        }
                    } catch (Exception e) {
                        log.error("更新导入日志成功数量失败, importLogId: {}, storeCode: {}", importLogId, storeCode, e);
                    }
                }
            });

            return allSuccess.get();
        } catch (Exception e) {
            log.error("执行数据导入失败", e);
            return false;
        }
    }

    /**
     * 转换为SubmitQtyReq
     */
    private SubmitQtyReq convertToSubmitQtyReq(List<QtyImportRowData> groupData,
                                               StoreValidationInfoDTO store,
                                               RmsPositionInfoRes position,
                                               Long miId,
                                               Long importLogId) {

        // 生成批次Id(UUID)
        String batchIdStr = UUID.randomUUID().toString();

        SubmitQtyReq submitReq = new SubmitQtyReq();
        submitReq.setMiId(miId);
        submitReq.setCountryCode(store.getCountryShortcode());
        submitReq.setPositionCode(position.getCode());
        submitReq.setUserId("user_" + miId); // 设置用户ID
        submitReq.setImportLogId(importLogId); // 设置导入日志ID
        submitReq.setStoreCode(store.getStoreCode());
        submitReq.setBatchIdStr(batchIdStr);

        // 转换明细数据
        List<SubmitQtyReq.QtyDetailDto> detailList = new ArrayList<>();
        for (QtyImportRowData rowData : groupData) {
            SubmitQtyReq.QtyDetailDto detail = new SubmitQtyReq.QtyDetailDto();
            detail.setDetailId(UUID.randomUUID().toString()); // 赋值为空

            detail.setProductId(rowData.getProductId());

            detail.setQuantity(rowData.getQty());
            detail.setSalesTime(convertSalesTime(rowData.getSalesTime(), store.getCountryShortcode()));
            detail.setNote(rowData.getNote());
            detail.setNote(rowData.getNote());
            detail.setReportingType(ReportingTypeEnum.PC.getValue()); // 默认值，根据实际情况调整
            detailList.add(detail);
        }
        submitReq.setDetailList(detailList);

        return submitReq;
    }

    /**
     * 转换销售时间
     */
    private Long convertSalesTime(String salesTime, String countryCode) {
        try {
            // 参考IMEI导入，使用parseLocalTimeToTimestamp方法
            return IntlTimeUtil.parseLocalTimeToTimestamp(countryCode, salesTime);
        } catch (Exception e) {
            log.warn("转换销售时间失败: {}, countryCode: {}", salesTime, countryCode, e);
            return System.currentTimeMillis();
        }
    }

    /**
     * 更新导入日志
     */
    private void updateImportLog(Long importLogId, Integer status, String errorMsg, String resultFileUrl) {
        updateImportLog(importLogId, status, errorMsg, resultFileUrl, null);
    }

    /**
     * 更新导入日志（包含总数）
     */
    private void updateImportLog(Long importLogId, Integer status, String errorMsg, String resultFileUrl, Integer totalCount) {
        try {
            IntlImportLog importLog = intlImportLogService.getById(importLogId);
            if (importLog != null) {
                importLog.setStatus(status);
                importLog.setErrorMsg(errorMsg);
                importLog.setResultFileUrl(resultFileUrl);
                if (totalCount != null) {
                    importLog.setTotalCount(totalCount);
                }
                importLog.setUpdatedAt(System.currentTimeMillis());
                intlImportLogService.updateById(importLog);
            }
        } catch (Exception e) {
            log.error("更新导入日志失败, importLogId: {}", importLogId, e);
        }
    }

    /**
     * 构建错误响应
     */
    private CommonApiResponse<QtyImportResponse> buildErrorResponse(QtyImportResponse response, String errorMsg) {
        return buildErrorResponse(response, errorMsg, null);
    }

    /**
     * 构建错误响应（带结果文件）
     */
    private CommonApiResponse<QtyImportResponse> buildErrorResponse(QtyImportResponse response, String errorMsg, String resultFileUrl) {
        response.setStatus(2);
        response.setErrorMsg(errorMsg);
        if (StringUtils.isNotBlank(resultFileUrl)) {
            response.setResultFileUrl(resultFileUrl);
        }
        return new CommonApiResponse<>(response);
    }

    /**
     * 构建错误响应（重载方法，包含统计信息）
     */
    private CommonApiResponse<QtyImportResponse>
    buildErrorResponse(QtyImportResponse response, String errorMsg, String resultFileUrl, Integer totalCount, Integer failedCount) {
        response.setStatus(2);
        response.setErrorMsg(errorMsg);
        if (StringUtils.isNotBlank(resultFileUrl)) {
            response.setResultFileUrl(resultFileUrl);
        }
        if (totalCount != null) {
            response.setTotalCount(totalCount);
        }
        if (failedCount != null) {
            response.setFailedCount(failedCount);
        }
        return new CommonApiResponse<>(response);
    }

    /**
     * 构建成功响应
     */
    private CommonApiResponse<QtyImportResponse> createSuccessResponse(Long importLogId, int totalCount, int failedCount) {
        QtyImportResponse response = new QtyImportResponse();
        response.setImportLogId(importLogId);
        response.setStatus(1); // 导入成功
        response.setTotalCount(totalCount);
        response.setFailedCount(failedCount);
        return new CommonApiResponse<>(response);
    }

    /**
     * 校验门店和用户权限
     */
    private Map<String, StoreValidationContext> validateStoreAndUserPermissions(List<QtyImportRowData> dataList, Long miId) {
        // 使用ConcurrentHashMap保证线程安全
        Map<String, StoreValidationContext> storeContextMap = new ConcurrentHashMap<>();

        // 按Store Code分组
        Map<String, List<QtyImportRowData>> storeGroups = dataList.stream()
                .filter(data -> StringUtils.isBlank(data.getFailedReason()))
                .collect(Collectors.groupingBy(QtyImportRowData::getStoreCode));

        // 并行处理每个门店的数据
        storeGroups.entrySet().parallelStream().forEach(entry -> {
            String storeCode = entry.getKey();
            List<QtyImportRowData> storeDataList = entry.getValue();

            StoreValidationContext context = new StoreValidationContext();
            context.setStoreCode(storeCode);

            try {
                // 1. 一次性查询门店信息、用户门店关系和阵地信息
                Optional<StoreValidationInfoDTO> storeValidationInfoOpt = intlPositionApiService.getStoreValidationInfo(storeCode, miId);
                if (!storeValidationInfoOpt.isPresent()) {
                    // 查询门店，进一步判断门店状态
                    RmsStoreInfoDto store = intlPositionApiService.getStoreByCode(storeCode);
                    if (store == null) {
                        qtyMarkStoreDataAsFailed(storeDataList, ERROR_STORE_NOT_FOUND);
                    } else if (store.getOperationStatus() != StoreOperationStatusEnums.OPENING.getValue()) {
                        qtyMarkStoreDataAsFailed(storeDataList, ERROR_STORE_NOT_OPENING);
                    } else {
                        qtyMarkStoreDataAsFailed(storeDataList, ERROR_NOT_IN_STORE);
                    }
                    return; // 并行流中使用return代替continue
                }
                StoreValidationInfoDTO storeValidationInfo = storeValidationInfoOpt.get();
                context.setStoreValidationInfo(storeValidationInfo);

                // 2. 校验销售时间
                validateSalesTimeForStore(storeDataList, storeValidationInfo);

                // 3. 检查是否有最优阵地
                if (storeValidationInfo.getBestPosition() == null) {
                    qtyMarkStoreDataAsFailed(storeDataList, "No valid position found for store: " + storeCode);
                    return; // 并行流中使用return代替continue
                }


                storeContextMap.put(storeCode, context);

            } catch (Exception e) {
                log.error("门店{}校验失败", storeCode, e);
                qtyMarkStoreDataAsFailed(storeDataList, "Store validation failed: " + e.getMessage());
            }
        });

        return storeContextMap;
    }

    /**
     * 标记门店下所有数据为失败
     */
    private void qtyMarkStoreDataAsFailed(List<QtyImportRowData> storeDataList, String errorMsg) {
        storeDataList.forEach(data -> {
            if (StringUtils.isBlank(data.getFailedReason())) {
                data.setValid(false);
               data.setFailedReason(errorMsg);
            }
        });
    }

    /**
     * 校验门店销售时间
     */
    private void validateSalesTimeForStore(List<QtyImportRowData> storeDataList, StoreValidationInfoDTO storeValidationInfo) {
        String countryCode = storeValidationInfo.getCountryShortcode();
        Long storeCreatedTimestamp = null;

        // 将门店创建时间（UTC时间字符串）转换为时间戳
        if (storeValidationInfo.getStoreCreatedOn() != null) {
            storeCreatedTimestamp = IntlTimeUtil.parseUtcDateTimeStringToTimestamp(storeValidationInfo.getStoreCreatedOn());
        }

        for (QtyImportRowData data : storeDataList) {
            if (StringUtils.isNotBlank(data.getFailedReason())) {
                continue;
            }

            // 使用通用的校验方法
            String validationError = SalesTimeValidUtil.validateSalesTime(data.getSalesTime(), countryCode, storeCreatedTimestamp);
            if (validationError != null) {
                data.setValid(false);
                data.setFailedReason(validationError);

            }
        }
    }

    /**
     * 统一产品验证 - 一次性查询所有产品数据，批量验证goods_id、sku_id、code69
     */
    private void validateProductUnified(List<QtyImportRowData> dataList, Long miId) {
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }

        try {
            // 一次性查询所有可售产品数据（按国家）
            UserInfoDTO user = queryUserInfo(miId);
            if (user == null) {
                log.warn("根据 miId 未找到用户信息，miId={}", miId);
                throw new IllegalStateException("User not found by miId: " + miId);
            }
            String countryCode = user.getCountryCode();
            List<Map<String, Object>> allProducts = intlSoQtyMapper.getAllAvailableProducts(countryCode);
            // 构建验证映射表
            Map<String, String> goodsIdMap = new HashMap<>();      // goods_id -> goods_id
            Map<String, String> skuIdMap = new HashMap<>();         // sku_id -> goods_id
            Map<String, String> code69Map = new HashMap<>();        // code69 -> goods_id

            for (Map<String, Object> product : allProducts) {
                String goodsId = getStringValue(product.get("goods_id"));
                String skuId = getStringValue(product.get("sku_id"));
                String code69 = getStringValue(product.get("code69"));

                if (StringUtils.isNotBlank(goodsId)) {
                    goodsIdMap.put(goodsId.toUpperCase(), goodsId);
                }
                if (StringUtils.isNotBlank(skuId)) {
                    skuIdMap.put(skuId.toUpperCase(), goodsId);
                }
                if (StringUtils.isNotBlank(code69)) {
                    code69Map.put(code69.toUpperCase(), goodsId);
                }
            }

            // 批量验证数据
            for (QtyImportRowData data : dataList) {
                if (StringUtils.isNotBlank(data.getFailedReason())) {
                    continue; // 已经有错误的跳过
                }

                boolean isValid = false;
                String matchedGoodsId = null;

                // 按优先级验证：goods_id > sku_id > code69
                if (StringUtils.isNotBlank(data.getProductId())) {
                    String upperProductId = data.getProductId().toUpperCase();
                    if (goodsIdMap.containsKey(upperProductId)) {
                        isValid = true;
                        matchedGoodsId = goodsIdMap.get(upperProductId);
                    }
                }

                if (!isValid && StringUtils.isNotBlank(data.getSku())) {
                    String upperSku = data.getSku().toUpperCase();
                    if (skuIdMap.containsKey(upperSku)) {
                        isValid = true;
                        matchedGoodsId = skuIdMap.get(upperSku);
                    }
                }

                if (!isValid && StringUtils.isNotBlank(data.getCode69())) {
                    String upperCode69 = data.getCode69().toUpperCase();
                    if (code69Map.containsKey(upperCode69)) {
                        isValid = true;
                        matchedGoodsId = code69Map.get(upperCode69);
                    }
                }

                if (!isValid) {
                    data.setValid(false);
                    data.setFailedReason("The product does not exist or is not available for sale: " +
                        (StringUtils.isNotBlank(data.getProductId()) ? "ProductId=" + data.getProductId() : "") +
                        (StringUtils.isNotBlank(data.getSku()) ? " SKU=" + data.getSku() : "") +
                        (StringUtils.isNotBlank(data.getCode69()) ? " Code69=" + data.getCode69() : ""));
                } else {
                    // 设置匹配到的goods_id作为productId
                    data.setProductId(matchedGoodsId);
                }
            }

            log.info("统一产品验证完成，总产品数: {}, 验证数据数: {}, 国家: {}", allProducts.size(), dataList.size(), countryCode);

        } catch (Exception e) {
            log.error("统一产品验证失败", e);
            // 验证失败时，将所有数据标记为无效
            for (QtyImportRowData data : dataList) {
                if (StringUtils.isBlank(data.getFailedReason())) {
                    data.setValid(false);
                    data.setFailedReason("产品验证服务异常: " + e.getMessage());
                }
            }
        }
    }
    /**
     * 查询用户信息
     *
     * 说明:
     * - 正常返回: 查询到的用户信息
     * - 返回 null: 表示根据 miId 未查询到用户（非异常场景）
     * - 异常: 下层 RPC/网络等异常将向上抛出，由上层统一捕获处理
     */
    private UserInfoDTO queryUserInfo(Long miId) {
        Optional<UserInfoDTO> result = userApiService.queryUserByMiId(miId);
        if (!result.isPresent()) {
            log.warn("未查询到用户信息, miId={}", miId);
        }
        return result.orElse(null);
    }
    /**
     * 安全获取字符串值
     */
    private String getStringValue(Object value) {
        return value != null ? String.valueOf(value) : null;
    }

    /**
     * 更新导入日志状态
     */
    private void updateImportLogStatus(Long importLogId, Integer status, String errorMsg, String resultFileUrl) {
        updateImportLogStatus(importLogId, status, errorMsg, resultFileUrl, null);
    }

    /**
     * 更新导入日志状态（包含总数）
     */
    private void updateImportLogStatus(Long importLogId, Integer status, String errorMsg, String resultFileUrl, Integer totalCount) {
        IntlImportLog importLog = new IntlImportLog();
        importLog.setId(importLogId);
        importLog.setStatus(status);
        importLog.setErrorMsg(errorMsg);
        importLog.setResultFileUrl(resultFileUrl);
        if (totalCount != null) {
            importLog.setTotalCount(totalCount);
        }
        importLog.setUpdatedAt(System.currentTimeMillis());
        intlImportLogService.updateById(importLog);
    }

    /**
     * 更新导入日志状态（包含总数）
     */
    private void updateImportTotalCount(Long importLogId,  Integer totalCount) {
        IntlImportLog importLog = new IntlImportLog();
        importLog.setId(importLogId);
        if (totalCount != null) {
            importLog.setTotalCount(totalCount);
        }
        importLog.setUpdatedAt(System.currentTimeMillis());
        intlImportLogService.updateById(importLog);
    }

    /**
     * 创建错误响应
     */
    private CommonApiResponse<QtyImportResponse> createErrorResponse(Long importLogId, Integer status, String errorMsg, String resultFileUrl) {
        return createErrorResponse(importLogId, status, errorMsg, resultFileUrl, null, null);
    }

    /**
     * 创建错误响应（包含统计信息）
     */
    private CommonApiResponse<QtyImportResponse> createErrorResponse(Long importLogId, Integer status, String errorMsg, String resultFileUrl,
                                                                      Integer totalCount, Integer failedCount) {
        QtyImportResponse response = new QtyImportResponse();
        response.setImportLogId(importLogId);
        response.setStatus(status);
        response.setErrorMsg(errorMsg);
        response.setResultFileUrl(resultFileUrl);
        response.setTotalCount(totalCount);
        response.setFailedCount(failedCount);
        return new CommonApiResponse<>(response);
    }

    /**
     * 更新导入耗时
     */
    private void updateImportDuration(Long importLogId, Integer durationValue) {
        IntlImportLog importLog = new IntlImportLog();
        importLog.setId(importLogId);
        importLog.setImportDuration(durationValue);
        importLog.setUpdatedAt(System.currentTimeMillis());
        intlImportLogService.updateById(importLog);
    }


} 