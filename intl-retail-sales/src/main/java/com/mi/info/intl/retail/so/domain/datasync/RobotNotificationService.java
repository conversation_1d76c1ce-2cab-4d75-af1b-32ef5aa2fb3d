package com.mi.info.intl.retail.so.domain.datasync;

import com.mi.info.intl.retail.so.domain.datasync.dto.DataDifferenceDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 机器人通知服务
 * 用于发送数据差异信息到机器人
 */
@Slf4j
@Service
public class RobotNotificationService {

    @Autowired
    private RestTemplate restTemplate;

    //@Value("${intl-retail.robot.webhook.url:}")
    private String robotWebhookUrl;

    //@Value("${intl-retail.robot.enabled:false}")
    private boolean robotEnabled;

    /**
     * 发送数据对比报告，即使正常也发送
     */
    public void sendDataDifferenceReport(List<DataDifferenceDto> imeiStatistics, List<DataDifferenceDto> qtyStatistics) {
        if (!robotEnabled || robotWebhookUrl.isEmpty()) {
            log.warn("机器人通知未启用或未配置webhook URL，跳过发送");
            return;
        }

        try {
            String message = buildComparisonReportMessage(imeiStatistics, qtyStatistics);
            sendToRobot(message);
            log.info("成功发送数据对比报告到机器人，IMEI差异数量：{}，QTY差异数量：{}", 
                    imeiStatistics != null ? imeiStatistics.size() : 0, 
                    qtyStatistics != null ? qtyStatistics.size() : 0);
        } catch (Exception e) {
            log.error("发送数据对比报告到机器人时发生错误", e);
        }
    }

    /**
     * 构建数据对比报告消息
     */
    private String buildComparisonReportMessage(List<DataDifferenceDto> imeiStatistics, List<DataDifferenceDto> qtyStatistics) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        StringBuilder message = new StringBuilder();
        
        // 标题
        message.append("📊 **数据对比监控报告**\n\n");
        message.append("**报告时间：** ").append(LocalDateTime.now().format(formatter)).append("\n\n");
        
        // IMEI差异分类统计
        message.append("**IMEI差异分类统计：**\n");
        message.append(buildCategoryTable(imeiStatistics)).append("\n");
        // QTY差异分类统计
        message.append("**QTY差异分类统计：**\n");
        message.append(buildCategoryTable(qtyStatistics)).append("\n");
        
        // 如果没有差异，显示正常信息
        if (isAllZero(imeiStatistics) && isAllZero(qtyStatistics)) {
            message.append("✅ **数据对比正常，未发现差异**\n\n");
        }
        
        // 处理建议
        message.append("**处理建议：**\n");
        message.append("1. 检查数据同步服务是否正常运行\n");
        message.append("2. 检查网络连接和API接口状态\n");
        message.append("3. 查看系统日志获取详细错误信息\n");
        message.append("4. 如需要，可手动触发数据同步\n");
        
        return message.toString();
    }
    
    private String buildCategoryTable(List<DataDifferenceDto> list) {
        Map<String, DataDifferenceDto> map = new HashMap<>();
        if (list != null) {
            for (DataDifferenceDto dto : list) {
                map.put(dto.getDifferenceCategory().name(), dto);
            }
        }
        String[] categories = new String[]{"RETAIL_ONLY", "RMS_ONLY", "STATUS_INCONSISTENT"};
        String[] labels = new String[]{"零售系统单边有数据", "RMS系统单边有数据", "两边数据状态不一致"};
        StringBuilder sb = new StringBuilder();
        sb.append("```\n");
        sb.append("| 差异类型 | 数量 | 数据ID列表 |\n");
        sb.append("|----------|------|------------|\n");
        for (int i = 0; i < categories.length; i++) {
            DataDifferenceDto dto = map.get(categories[i]);
            long count = dto != null && dto.getTotalNum() != null ? dto.getTotalNum() : 0L;
            String ids = (dto != null && dto.getIds() != null && !dto.getIds().isEmpty()) ?
                    String.join(", ", dto.getIds()) : "无";
            sb.append(String.format("| %s | %d | %s |\n", labels[i], count, ids));
        }
        sb.append("```\n");
        return sb.toString();
    }

    private boolean isAllZero(List<DataDifferenceDto> list) {
        if (list == null || list.isEmpty()) {
            return true;
        }
        long sum = 0L;
        for (DataDifferenceDto dto : list) {
            sum += dto.getTotalNum() == null ? 0L : dto.getTotalNum();
        }
        return sum == 0L;
    }

    /**
     * 发送消息到机器人
     */
    private void sendToRobot(String message) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            // 构建机器人消息格式（以钉钉为例）
            Map<String, Object> markdown = new HashMap<>();
            markdown.put("title", "数据差异监控告警");
            markdown.put("text", message);
            
            Map<String, Object> robotMessage = new HashMap<>();
            robotMessage.put("msgtype", "markdown");
            robotMessage.put("markdown", markdown);
            
            HttpEntity<Map<String, Object>> request = new HttpEntity<>(robotMessage, headers);
            
            // 发送到机器人webhook
            restTemplate.postForEntity(robotWebhookUrl, request, String.class);
            
        } catch (Exception e) {
            log.error("发送消息到机器人失败", e);
            throw e;
        }
    }

    /**
     * 发送简单通知消息
     */
    public void sendSimpleNotification(String title, String content) {
        if (!robotEnabled || robotWebhookUrl.isEmpty()) {
            return;
        }

        try {
            String message = String.format("**%s**\n\n%s", title, content);
            sendToRobot(message);
        } catch (Exception e) {
            log.error("发送简单通知失败", e);
        }
    }
}
