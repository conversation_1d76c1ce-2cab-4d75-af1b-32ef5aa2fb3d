package com.mi.info.intl.retail.so.app.provider.sys;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import com.mi.info.intl.retail.dto.LabelValueDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.sys.provider.DictSysDubboProvider;
import com.mi.info.intl.retail.intlretail.service.api.so.sys.request.DictSysRequest;
import com.mi.info.intl.retail.intlretail.util.AppProviderUtil;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.so.domain.sys.service.IntlSysDictService;
import com.xiaomi.mone.docs.annotations.dubbo.ApiDoc;
import com.xiaomi.mone.docs.annotations.dubbo.ApiModule;
import com.xiaomi.youpin.infra.rpc.Result;

import lombok.extern.slf4j.Slf4j;

/**
 * 数据字典dubbo接口提供提供商
 *
 * <AUTHOR>
 * @date 2025/07/28
 */
@Slf4j
@Service
@ApiModule(value = "国际渠道零售服务", apiInterface = DictSysDubboProvider.class)
@DubboService(timeout = 20000, group = "${center.dubbo.group:}", retries = 3,
    interfaceClass = DictSysDubboProvider.class, validation = "true")
public class DictSysDubboProviderImpl implements DictSysDubboProvider {

    @Resource
    private IntlSysDictService intlSysDictService;

    /**
     * 按类型获取dict标签列表
     *
     * @param request 要求
     * @return {@link Result }<{@link Map }<{@link String }, {@link List }<{@link LabelValueDTO }>>>
     */
    @ApiDoc(description = "按类型获取dict标签列表", value = "/api/so/v1/getDictLabelListByType")
    @Override
    public CommonApiResponse<Map<String, List<LabelValueDTO>>> getDictLabelListByType(DictSysRequest request) {
        return AppProviderUtil.wrap(log, "DictSysDubboProvider::getDictLabelListByType", request,
            intlSysDictService::getLabelValueListByDictCode, false);
    }
}
