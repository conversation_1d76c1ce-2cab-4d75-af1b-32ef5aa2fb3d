package com.mi.info.intl.retail.so.domain.datasync;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.mi.info.intl.retail.exception.BizException;
import com.mi.info.intl.retail.intlretail.domain.http.rms.RmsAppliUserOauthServiceProvider;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.RmsRequest;
import com.mi.info.intl.retail.so.app.mq.SyncSoToEsProducer;
import com.mi.info.intl.retail.so.app.mq.dto.RetailSyncRequestDTO;
import com.mi.info.intl.retail.so.app.mq.dto.RetailSyncRmsImeiCreateDTO;
import com.mi.info.intl.retail.so.app.mq.dto.RetailSyncRmsQtyCreateDTO;
import com.mi.info.intl.retail.so.app.mq.dto.RmsResponseDTO;
import com.mi.info.intl.retail.so.domain.datasync.enums.DataSyncDataTypeEnum;
import com.mi.info.intl.retail.so.domain.datasync.enums.DataSyncOperateTypeTypeEnum;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoImei;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoOrgInfo;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoQty;
import com.mi.info.intl.retail.so.domain.upload.service.IntlSoImeiService;
import com.mi.info.intl.retail.so.domain.upload.service.IntlSoOrgInfoService;
import com.mi.info.intl.retail.so.domain.upload.service.IntlSoQtyService;
import com.mi.info.intl.retail.so.metrics.MqAction;
import com.mi.info.intl.retail.so.metrics.SyncSoToEsMetricsService;
import com.mi.info.intl.retail.utils.redis.RedisClient;
import com.mi.info.intl.retail.utils.redis.RedisKey;
import com.mi.info.intl.retail.utils.redis.RedisKeyEnum;
import jodd.util.ThreadUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Resource;

/**
 * @Author: 黄涛
 * @CreateTime: 2025-08-11
 */
@Slf4j
@Component
public class RetailSyncToRmsManage {
    @Resource
    private IntlSoOrgInfoService intlSoOrgInfoService;
    @Resource
    private ObjectMapper objectMapper;
    @Resource
    private RmsAppliUserOauthServiceProvider rmsAppliUserOauthServiceProvider;
    @Resource
    private SyncSoToEsProducer syncSoToEsProducer;
    @Resource
    private IntlSoImeiService intlSoImeiService;
    @Resource
    private IntlSoQtyService intlSoQtyService;
    @Resource
    private RedisClient redisClient;
    @Resource
    private SyncSoToEsMetricsService syncSoToEsMetricsService;

    // RMS同步数量的URL
    public static final String RMS_QTY_SYNC_URL = "/api/data/v9.2/new_SOQtySync";
    // RMS同步IMEI的URL
    public static final String RMS_IMEI_SYNC_URL = "/api/data/v9.2/new_SoImeiSync";
    // 每次调用RMS接口的最大数据量
    private static final int BATCH_SIZE = 20;

    private static final int MAX_RETRIES = 2;
    private static final long RETRY_DELAY_MILLIS = 1000;

    // 缓存字段列表
    private static final List<String> IMEI_FIELDS = initImeiFields();
    private static final List<String> QTY_FIELDS = initQtyFields();

    private static List<String> initImeiFields() {
        return Arrays.asList(
                "imeiRuleIsActivingCheck", "imeiRuleBefore", "imeiRuleAfter",
                "activationVerificationTime", "verifyingState", "activationTime",
                "activationFrequency", "activationSite", "siVerifyResult",
                "verifyResult", "verifyResultDetail", "repeatUserDetail",
                "lastMd", "finalSalesCountry", "failedReason", "failedReasonDetail",
                "firstLevelAccountCode"
        );
    }

    private static List<String> initQtyFields() {
        return Arrays.asList("status", "modifiedby", "modifiedon");
    }

    /**
     * 根据数据类型处理不同的零售数据
     *
     * @param
     */
    public void handleRetailData(DataSyncDataTypeEnum dataTypeEnum, String operateType, List<Long> dataIds) {
        if (dataTypeEnum == null) {
            throw new RuntimeException("RetailSyncInfo: dataType not found : dataTypeEnum " + dataTypeEnum);
        }
        // sleep 3 秒
        ThreadUtil.sleep(3000);
        switch (dataTypeEnum) {
            case IMEI:
                // 批量处理IMEI数据
                handleDataWithRetry(() -> handleBatchImeiData(operateType, dataIds),
                        DataSyncDataTypeEnum.IMEI, dataIds);
                break;
            case QTY:
                // 批量处理数量数据
                handleDataWithRetry(() -> handleBatchQtyData(operateType, dataIds),
                        DataSyncDataTypeEnum.QTY, dataIds);
                break;
            default:
                throw new RuntimeException("RetailSyncInfo: dataType not found : dataTypeEnum " + dataTypeEnum);
        }
    }

    private void handleDataWithRetry(Runnable dataHandler, DataSyncDataTypeEnum dataType, List<Long> dataIds) {
        boolean success = false;
        int maxRetries = 2;
        int retryCount = 0;
        while (!success && retryCount < maxRetries) {
            try {
                dataHandler.run();
                success = true;
                //Metrics消费成功上报
                syncSoToEsMetricsService.syncToRmsSuccess(MqAction.consumer, dataType, dataIds.size());
            } catch (Exception e) {
                log.error("处理{}数据失败，第{}次重试，dataIds: {}", dataType, retryCount + 1, dataIds, e);
                retryCount++;
                if (retryCount >= maxRetries) {
                    log.error("处理{}数据经过{}次重试后仍然失败，dataIds: {}", dataType, maxRetries, dataIds, e);
                    //Metrics消费失败上报
                    syncSoToEsMetricsService.syncToRmsError(MqAction.consumer, dataType, dataIds.size());
                    throw new BizException("handle data failure dataType:{}", dataType, e);
                }
            }
        }
        // 确保无论是否成功都发送同步消息（仅一次）
        syncSoToEsProducer.sendSyncEsMsg(dataType, dataIds, false);
    }

    /**
     * 批量处理IMEI数据
     *
     * @param operateType 操作类型
     * @param dataIds ID列表
     */
    private void handleBatchImeiData(String operateType, List<Long> dataIds) {
        // 查询所有IMEI数据 ToDo 修改主库
        List<IntlSoImei> imeiList = intlSoImeiService.listByIds(dataIds);
        //等待重试机制，等待一秒，重试2次后，还是无法获取数据，则抛出异常
        int retryCount = 0;
        while ((imeiList == null || imeiList.isEmpty()) && retryCount < MAX_RETRIES) {
            retryCount++;
            log.warn("imeiList is empty, retrying... ({}/{})", retryCount, MAX_RETRIES);
            try {
                Thread.sleep(RETRY_DELAY_MILLIS);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("Thread interrupted during retry wait", e);
            }
            imeiList = intlSoImeiService.listByIds(dataIds);
        }

        if (imeiList == null || imeiList.isEmpty()) {
            log.error("IMEI not found for dataIds: {} after {} retries", dataIds, MAX_RETRIES);
            throw new RuntimeException("IMEI not found dataIds:" + dataIds);
        }

        //新增先查询是否存在rmsId，如存在，则不再更新
        List<IntlSoImei> processedList;
        if (operateType.equalsIgnoreCase(DataSyncOperateTypeTypeEnum.CREATE.getMessage())) {
            processedList = imeiList.stream().filter(imei -> imei.getRmsId() == null || imei.getRmsId().isEmpty()).collect(Collectors.toList());
        } else {
            processedList = imeiList;
        }

        // 按areaId分组
        Map<String, List<IntlSoImei>> groupedImeis = processedList.stream()
                .collect(Collectors.groupingBy(imei -> getAreaId(imei.getOrgInfoId())));

        // 对每组数据进行处理
        groupedImeis.forEach((areaId, imeis) -> {
            // 分批处理，每批最多BATCH_SIZE条
            List<List<IntlSoImei>> batches = Lists.partition(imeis, BATCH_SIZE);
            batches.forEach(batch -> {
                try {
                    if (operateType.equalsIgnoreCase(DataSyncOperateTypeTypeEnum.CREATE.getMessage())) {
                        createBatchImeiInfo(areaId, batch);
                    } else if (operateType.equalsIgnoreCase(
                            DataSyncOperateTypeTypeEnum.ACTIVATE_VERIFICATION.getMessage())) {
                        updateBatchImeiInfo(areaId, batch);
                    } else {
                        throw new RuntimeException(
                                "RetailSyncInfo: operateType not found : operateType " + operateType);
                    }
                } catch (Exception e) {
                    log.error("处理IMEI批次数据失败, areaId: {}, operateType: {}, batchSize: {}", areaId, operateType,
                            batch.size(), e);
                    throw new BizException("处理IMEI批次数据失败", e);
                }
            });
        });
    }

    /**
     * 批量创建IMEI信息并同步到RMS
     *
     * @param areaId 区域ID
     * @param imeiBatch IMEI实体对象列表
     */
    private void createBatchImeiInfo(String areaId, List<IntlSoImei> imeiBatch) {
        // 构建请求DTO
        RetailSyncRequestDTO imeiRequestDTO = buildBatchImeiCreateDTO(imeiBatch);
        // 调用RMS服务
        List<RmsResponseDTO> rmsResponseDTOS = callRmsService(areaId, RMS_IMEI_SYNC_URL, imeiRequestDTO);
        // 更新本地数据库中的RMS ID
        Map<String, IntlSoImei> imeiMap = imeiBatch.stream()
                .collect(Collectors.toMap(imei -> String.valueOf(imei.getId()), imei -> imei));
        List<String> retailIds = updateRmsIds(rmsResponseDTOS, imeiMap, this::updateImeiRmsId);
        if (!retailIds.isEmpty()) {
            log.error("createBatchImeiInfo error for retailIds: {}", retailIds);
            throw new BizException("createBatchImeiInfo error");
        }
    }

    private void updateImeiRmsId(IntlSoImei imei, String rmsId) {
        imei.setRmsId(rmsId);
        boolean updated = intlSoImeiService.updateById(imei);
        if (!updated) {
            log.error("updateImeiRmsId error for imei id: {}", imei.getId());
        }
    }

    private <T> List<String> updateRmsIds(List<RmsResponseDTO> responses, Map<String, T> entityMap,
                                          RmsIdUpdater<T> updater) {
        List<String> retailIds = new ArrayList<>();
        for (RmsResponseDTO response : responses) {
            String retailId = response.getRetailId();
            if (response.getRmsId() == null || response.getRmsId().isEmpty()) {
                log.error("RMS ID is null for retailId: {}", retailId);
                retailIds.add(retailId);
                continue;
            }
            T entity = entityMap.get(retailId);
            if (entity != null) {
                updater.update(entity, response.getRmsId());
            } else {
                log.error("Entity not found for retailId: {}", retailId);
            }
        }
        return retailIds;
    }

    @FunctionalInterface
    private interface RmsIdUpdater<T> {
        void update(T entity, String rmsId);
    }

    /**
     * 批量更新IMEI信息并同步到RMS
     *
     * @param areaId 区域ID
     * @param imeiBatch IMEI实体对象列表
     */
    private void updateBatchImeiInfo(String areaId, List<IntlSoImei> imeiBatch) {
        // 构建请求DTO
        RetailSyncRequestDTO imeiRequestDTO = buildBatchImeiUpdateDTO(imeiBatch);
        // 调用RMS服务
        callRmsService(areaId, RMS_IMEI_SYNC_URL, imeiRequestDTO);
    }

    /**
     * 批量处理QTY数据
     *
     * @param operateType 操作类型
     * @param dataIds ID列表
     */
    private void handleBatchQtyData(String operateType, List<Long> dataIds) {
        // 查询所有数量数据
        List<IntlSoQty> qtyList = intlSoQtyService.listByIds(dataIds);
        log.info("handleBatchQtyData: qtyList size: " + qtyList.size());
        //等待重试机制，等待一秒，重试2次后，还是无法获取数据，则抛出异常
        int retryCount = 0;
        while ((qtyList == null || qtyList.isEmpty()) && retryCount < MAX_RETRIES) {
            retryCount++;
            log.warn("qtyList is empty, retrying... ({}/{})", retryCount, MAX_RETRIES);
            try {
                Thread.sleep(RETRY_DELAY_MILLIS);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("Thread interrupted during retry wait", e);
            }
            qtyList = intlSoQtyService.listByIds(dataIds);
        }

        if (qtyList == null || qtyList.isEmpty()) {
            log.error("QTY not found for dataIds: {} after {} retries", dataIds, MAX_RETRIES);
            throw new RuntimeException("QTY not found dataIds:" + dataIds);
        }
        //新增先查询是否存在rmsId，如存在，则不再更新
        List<IntlSoQty> processedList;
        if (operateType.equalsIgnoreCase(DataSyncOperateTypeTypeEnum.CREATE.getMessage())) {
            processedList =
                    qtyList.stream().filter(qty -> qty.getRmsId() == null || qty.getRmsId().isEmpty()).collect(Collectors.toList());
        } else {
            processedList = qtyList;
        }
        // 按areaId分组
        Map<String, List<IntlSoQty>> groupedQtys = processedList.stream()
                .collect(Collectors.groupingBy(qty -> getAreaId(qty.getOrgInfoId())));

        // 对每组数据进行处理
        groupedQtys.forEach((areaId, qtys) -> {
            // 分批处理，每批最多BATCH_SIZE条
            List<List<IntlSoQty>> batches = Lists.partition(qtys, BATCH_SIZE);
            batches.forEach(batch -> {
                try {
                    if (operateType.equalsIgnoreCase(DataSyncOperateTypeTypeEnum.CREATE.getMessage())) {
                        createBatchQtyInfo(areaId, batch);
                    } else if (operateType.equalsIgnoreCase(DataSyncOperateTypeTypeEnum.UPDATE.getMessage())) {
                        updateBatchQtyInfo(areaId, batch);
                    } else {
                        throw new RuntimeException(
                                "RetailSyncInfo: operateType not found : operateType " + operateType);
                    }
                } catch (Exception e) {
                    log.error("处理QTY批次数据失败, areaId: {}, operateType: {}, batchSize: {}", areaId, operateType,
                            batch.size(), e);
                    throw new BizException("处理QTY批次数据失败", e);
                }
            });
        });
    }

    /**
     * 批量创建QTY信息并同步到RMS
     *
     * @param areaId 区域ID
     * @param qtyBatch 数量实体对象列表
     */
    private void createBatchQtyInfo(String areaId, List<IntlSoQty> qtyBatch) {
        // 构建请求DTO
        RetailSyncRequestDTO qtyRequestDTO = buildBatchQtyCreateDTO(qtyBatch);

        // 调用RMS服务
        List<RmsResponseDTO> rmsResponseDTOS = callRmsService(areaId, RMS_QTY_SYNC_URL, qtyRequestDTO);
        if (!rmsResponseDTOS.isEmpty()) {
            // 更新本地数据库中的RMS ID
            Map<String, IntlSoQty> qtyMap = qtyBatch.stream()
                    .collect(Collectors.toMap(qty -> String.valueOf(qty.getId()), qty -> qty));
            List<String> retailIds = updateRmsIds(rmsResponseDTOS, qtyMap, this::updateQtyRmsId);
            if (!retailIds.isEmpty()) {
                log.error("createBatchQtyInfo error for retailIds: {}", retailIds);
                throw new BizException("createBatchQtyInfo error");
            }
        }
    }

    private void updateQtyRmsId(IntlSoQty qty, String rmsId) {
        qty.setRmsId(rmsId);
        boolean updated = intlSoQtyService.updateById(qty);
        if (!updated) {
            log.error("updateQtyRmsId error for qty id: {}", qty.getId());
            throw new BizException("updateQtyRmsId error");
        }
    }

    /**
     * 批量更新QTY信息并同步到RMS
     *
     * @param areaId 区域ID
     * @param qtyBatch 数量实体对象列表
     */
    private void updateBatchQtyInfo(String areaId, List<IntlSoQty> qtyBatch) {
        // 构建请求DTO
        RetailSyncRequestDTO qtyRequestDTO = buildBatchQtyUpdateDTO(qtyBatch);
        // 调用RMS服务
        callRmsService(areaId, RMS_QTY_SYNC_URL, qtyRequestDTO);
    }

    /**
     * 构建批量创建IMEI的请求DTO
     *
     * @param imeiBatch IMEI实体对象列表
     * @return RetailSyncRequestDTO对象
     */
    private RetailSyncRequestDTO buildBatchImeiCreateDTO(List<IntlSoImei> imeiBatch) {
        return buildBatchImeiDTO(imeiBatch, DataSyncOperateTypeTypeEnum.CREATE.getMessage(), IMEI_FIELDS);
    }

    /**
     * 构建更新IMEI的fields
     *
     * @param imeiBatch IMEI实体对象列表
     * @return RetailSyncRequestDTO对象
     */
    private RetailSyncRequestDTO buildBatchImeiUpdateDTO(List<IntlSoImei> imeiBatch) {
        return buildBatchImeiDTO(imeiBatch, DataSyncOperateTypeTypeEnum.ACTIVATE_VERIFICATION.getMessage(),
                IMEI_FIELDS);
    }

    private RetailSyncRequestDTO buildBatchImeiDTO(List<IntlSoImei> imeiBatch, String operateType,
                                                   List<String> fields) {
        List<RetailSyncRmsImeiCreateDTO> dataList = imeiBatch.stream()
                .map(this::getRetailSyncRequestImeiCreateDTO)
                .collect(Collectors.toList());

        RetailSyncRequestDTO dto = new RetailSyncRequestDTO();
        dto.setType(DataSyncDataTypeEnum.IMEI.getMessage());
        dto.setOperateType(operateType);
        dto.setDataList(dataList);
        dto.setFields(fields);
        return dto;
    }

    /**
     * 构建批量创建QTY的请求DTO
     *
     * @param qtyBatch 数量实体对象列表
     * @return RetailSyncRequestDTO对象
     */
    private RetailSyncRequestDTO buildBatchQtyCreateDTO(List<IntlSoQty> qtyBatch) {
        return buildBatchQtyDTO(qtyBatch, DataSyncOperateTypeTypeEnum.CREATE.getMessage(), null);
    }

    /**
     * 构建批量更新QTY的fields
     *
     * @param qtyBatch 数量实体对象列表
     * @return RetailSyncRequestDTO对象
     */
    private RetailSyncRequestDTO buildBatchQtyUpdateDTO(List<IntlSoQty> qtyBatch) {
        return buildBatchQtyDTO(qtyBatch, DataSyncOperateTypeTypeEnum.UPDATE.getMessage(), QTY_FIELDS);
    }

    private RetailSyncRequestDTO buildBatchQtyDTO(List<IntlSoQty> qtyBatch, String operateType, List<String> fields) {
        List<RetailSyncRmsQtyCreateDTO> dataList = qtyBatch.stream()
                .map(this::getRetailSyncRmsQtyCreateDTO)
                .collect(Collectors.toList());

        RetailSyncRequestDTO dto = new RetailSyncRequestDTO();
        dto.setType(DataSyncDataTypeEnum.QTY.getMessage());
        dto.setOperateType(operateType);
        dto.setDataList(dataList);
        if (fields != null) {
            dto.setFields(fields);
        }
        return dto;
    }

    /**
     * 构建创建IMEI的请求DTO
     *
     * @param imei IMEI实体对象
     * @return RetailSyncRmsImeiCreateDTO对象
     */
    private RetailSyncRmsImeiCreateDTO getRetailSyncRequestImeiCreateDTO(IntlSoImei imei) {
        RetailSyncRmsImeiCreateDTO syncRmsImeiCreateDTO = new RetailSyncRmsImeiCreateDTO();
        syncRmsImeiCreateDTO.setRmsId(imei.getRmsId());
        syncRmsImeiCreateDTO.setRetailId(String.valueOf(imei.getId()));
        syncRmsImeiCreateDTO.setSn(imei.getSn());
        syncRmsImeiCreateDTO.setImei1(imei.getImei1());
        syncRmsImeiCreateDTO.setImei2(imei.getImei2());
        syncRmsImeiCreateDTO.setSnMask(imei.getSnMask());
        syncRmsImeiCreateDTO.setImei1Mask(imei.getImei1Mask());
        syncRmsImeiCreateDTO.setImei2Mask(imei.getImei2Mask());
        syncRmsImeiCreateDTO.setSnHash(imei.getSnHash());
        syncRmsImeiCreateDTO.setImeiFromHub(imei.getImeiFromHub());
        syncRmsImeiCreateDTO.setProductCode(imei.getProductCode());
        syncRmsImeiCreateDTO.setRrpCode(imei.getRrpCode());
        syncRmsImeiCreateDTO.setRrp(imei.getRrp());
        syncRmsImeiCreateDTO.setCurrency(imei.getCurrency());
        syncRmsImeiCreateDTO.setSalesTime(imei.getSalesTime());
        syncRmsImeiCreateDTO.setCreatedTime(imei.getCreatedOn());
        syncRmsImeiCreateDTO.setModifiedbyMiId(String.valueOf(imei.getModifiedBy()));
        syncRmsImeiCreateDTO.setModifiedon(imei.getModifiedOn());
        syncRmsImeiCreateDTO.setDataFrom(imei.getDataFrom());
        syncRmsImeiCreateDTO.setStatus(imei.getStatus());
        syncRmsImeiCreateDTO.setStoreCodeRMS(imei.getStoreRmsCode());
        syncRmsImeiCreateDTO.setPositionCodeRMS(imei.getPositionRmsCode());
        syncRmsImeiCreateDTO.setRepeatUserDetail(imei.getRepeatUser());
        syncRmsImeiCreateDTO.setAllowSalesCountry(imei.getAllowSalesCountry());
        syncRmsImeiCreateDTO.setReportType(imei.getReportingType());
        syncRmsImeiCreateDTO.setCreatedbyMiid(String.valueOf(imei.getCreatedBy()));
        syncRmsImeiCreateDTO.setSalesManMiid(String.valueOf(imei.getSalesmanMid()));
        syncRmsImeiCreateDTO.setImeiRuleIsActivingCheck(imei.getImeiRuleIsActivingCheck());
        syncRmsImeiCreateDTO.setImeiRuleBefore(imei.getImeiRuleBefore());
        syncRmsImeiCreateDTO.setImeiRuleAfter(imei.getImeiRuleAfter());
        syncRmsImeiCreateDTO.setActivationVerificationTime(imei.getActivationVerificationTime());
        syncRmsImeiCreateDTO.setVerifyingState(imei.getVerifyingState());
        syncRmsImeiCreateDTO.setActivationTime(imei.getActivationTime());
        syncRmsImeiCreateDTO.setActivationFrequency(imei.getActivationFrequency());
        syncRmsImeiCreateDTO.setActivationSite(imei.getActivationSite());
        syncRmsImeiCreateDTO.setSiVerifyResult(imei.getSiVerifyResult());
        syncRmsImeiCreateDTO.setVerifyResult(imei.getVerificationResult());
        syncRmsImeiCreateDTO.setVerifyResultDetail(imei.getVerifyResultDetail());
        syncRmsImeiCreateDTO.setRepeatUserDetail(imei.getRepeatUser());
        syncRmsImeiCreateDTO.setLastMd(imei.getLastMd());
        syncRmsImeiCreateDTO.setFinalSalesCuntry(imei.getFinalSalesCountry());
        syncRmsImeiCreateDTO.setFailedReason(imei.getFailedReason());
        syncRmsImeiCreateDTO.setFailedReasonDetail(imei.getFailedReasonDetail());
        syncRmsImeiCreateDTO.setFirstLevelAccountCode(imei.getFirstLevelAccountCode());
        syncRmsImeiCreateDTO.setNote(imei.getNote());
        syncRmsImeiCreateDTO.setProductLineCode(imei.getProductLineCode());
        syncRmsImeiCreateDTO.setProductName(imei.getProductName());
        syncRmsImeiCreateDTO.setProductShortName(imei.getProductShortName());
        return syncRmsImeiCreateDTO;
    }

    /**
     * 构建创建QTY的请求DTO
     *
     * @param qty 数量实体对象
     * @return RetailSyncRmsQtyCreateDTO对象
     */
    private RetailSyncRmsQtyCreateDTO getRetailSyncRmsQtyCreateDTO(IntlSoQty qty) {
        RetailSyncRmsQtyCreateDTO qtyCreateDTO = new RetailSyncRmsQtyCreateDTO();
        qtyCreateDTO.setRetailId(qty.getId());
        qtyCreateDTO.setProductCode(String.valueOf(qty.getProductCode()));
        qtyCreateDTO.setQuantity(qty.getQuantity());
        qtyCreateDTO.setRrpRMSCode(qty.getRrpCode());
        qtyCreateDTO.setRrp(qty.getRrp());
        qtyCreateDTO.setCurrency(qty.getCurrency());
        qtyCreateDTO.setSalesmanMiid(String.valueOf(qty.getSalesmanMid()));
        qtyCreateDTO.setSalesTime(qty.getSalesTime());
        qtyCreateDTO.setReportType(qty.getReportingType());
        qtyCreateDTO.setCreatedbyMiid(String.valueOf(qty.getCreatedby()));
        qtyCreateDTO.setCreatedon(qty.getCreatedon());
        qtyCreateDTO.setNote(qty.getNote());
        qtyCreateDTO.setPositionCodeRMS(qty.getPositionRmsCode());
        qtyCreateDTO.setModifiedby(String.valueOf(qty.getModifiedby()));
        qtyCreateDTO.setModifiedon(qty.getModifiedon());
        qtyCreateDTO.setStatus(qty.getStatus());
        qtyCreateDTO.setProductLineCode(qty.getProductLineCode());
        qtyCreateDTO.setProductName(qty.getProductName());
        qtyCreateDTO.setProductShortName(qty.getProductShortName());
        return qtyCreateDTO;
    }

    /**
     * 根据销量阵地ID获取区域ID（国家编码缩写）
     *
     * @param orgInfoId 销量阵地ID
     * @return 区域ID（国家编码缩写）
     */
    private String getAreaId(Long orgInfoId) {
        RedisKey redisKey = RedisKeyEnum.ORG_INFO_COUNTRY_CODE.get(orgInfoId);
        try {
            String areaId = redisClient.get(redisKey);
            if (areaId != null) {
                return areaId;
            }
        } catch (Exception e) {
            log.warn("Redis get failed for key: {}", redisKey, e);
        }

        LambdaQueryWrapper<IntlSoOrgInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IntlSoOrgInfo::getId, orgInfoId);
        IntlSoOrgInfo intlSoOrgInfo = intlSoOrgInfoService.getOne(queryWrapper);
        String areaId = Optional.ofNullable(intlSoOrgInfo)
                .map(IntlSoOrgInfo::getCountryCode)
                .filter(id -> !id.isEmpty())
                .orElseThrow(() -> new RuntimeException("areaId is null or empty for orgInfoId: " + orgInfoId));

        try {
            redisClient.set(redisKey, areaId);
        } catch (Exception e) {
            log.warn("Redis set failed for key: {}", redisKey, e);
        }
        log.info("areaId: {} for orgInfoId: {}", areaId, orgInfoId);
        return areaId;
    }

    /**
     * 调用RMS服务进行数据同步
     *
     * @param areaId 区域ID
     * @param imeiRequestDTO 请求数据
     * @return RMS响应体
     */
    private List<RmsResponseDTO> callRmsService(String areaId, String path, RetailSyncRequestDTO imeiRequestDTO) {
        String data = toJsonString(imeiRequestDTO);
        RmsRequest rmsRequest = new RmsRequest();
        rmsRequest.setInput(data);
        RmsResponseDTO[] rmsResponseDTOsArray =
                rmsAppliUserOauthServiceProvider.httpForObject(areaId, path, rmsRequest,
                        HttpMethod.POST.name(), RmsResponseDTO[].class);
        if (rmsResponseDTOsArray == null) {
            log.warn("RMS response is null, returning empty list");
            throw new BizException("RMS response is null");
        }
        List<RmsResponseDTO> rmsResponseDTOs = Arrays.asList(rmsResponseDTOsArray);
        log.info("RMS response: {}", rmsResponseDTOs);
        if (rmsResponseDTOs.size() != imeiRequestDTO.getDataList().size()) {
            log.warn("RMS response size is not equal to request size, returning empty list");
            throw new BizException("RMS response size is not equal to request size");
        }
        return rmsResponseDTOs;
    }

    /**
     * 将对象序列化为JSON字符串
     *
     * @param obj 需要序列化的对象
     * @return JSON字符串
     */
    private String toJsonString(Object obj) {
        try {
            return objectMapper.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Failed to serialize object to JSON", e);
        }
    }
}
