package com.mi.info.intl.retail.so.app.mq;

import com.mi.info.intl.retail.service.DistributionLock;
import com.mi.info.intl.retail.service.DistributionLockService;
import com.mi.info.intl.retail.so.app.mq.dto.*;
import com.mi.info.intl.retail.so.domain.datasync.RetailSyncToRmsManage;
import com.mi.info.intl.retail.so.domain.datasync.enums.DataSyncDataTypeEnum;
import com.mi.info.intl.retail.so.metrics.MqAction;
import com.mi.info.intl.retail.so.metrics.SyncSoToEsMetricsService;
import com.mi.info.intl.retail.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.redisson.api.*;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 零售数据同步到RMS系统的消费者
 * 处理来自RocketMQ的消息，将零售数据（IMEI、QTY）同步到RMS系统
 */
@Slf4j
@Service
@ConditionalOnProperty(name = "intl-retail.rocketmq.to-rms.enabled", havingValue = "true", matchIfMissing = true)
@RocketMQMessageListener(topic = "${intl-retail.rocketmq.to-rms.topic}", consumerGroup = "${intl-retail.rocketmq.to-rms.group}", enableMsgTrace = true)
public class RetailSyncToRmsConsumer implements RocketMQListener<String> {
    @Resource
    private SyncSoToEsMetricsService syncSoToEsMetricsService;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private RetailSyncToRmsManage retailSyncToRmsManage;

    // Redis锁前缀
    public static final String RETAIL_TO_RMS_PREFIX = "retail_to_rms_prefix:";
    // 限流器键名
    public static final String RETAIL_TO_RMS_RATE_LIMITER = "retail_to_rms_rate_limiter";

    // 每秒最大处理消息数
    private static final int RATE_LIMIT_PER_SECOND = 10;

    private RRateLimiter rateLimiter;

    /**
     * 初始化限流器，限制每秒处理的消息数量
     */
    @PostConstruct
    public void initRateLimiter() {
        rateLimiter = redissonClient.getRateLimiter(RETAIL_TO_RMS_RATE_LIMITER);
        if (!rateLimiter.isExists()) {
            rateLimiter.trySetRate(RateType.OVERALL, RATE_LIMIT_PER_SECOND, 1, RateIntervalUnit.SECONDS);
        }
    }

    /**
     * 处理来自RocketMQ的消息
     *
     * @param message 消息内容，包含需要同步的数据信息
     */
    @Override
    public void onMessage(String message) {
        // 获取限流许可，最多等待10秒
        boolean acquiredRate = rateLimiter.tryAcquire(1, 10, TimeUnit.SECONDS);
        if (!acquiredRate) {
            log.error("rate limit exceeded");
            throw new RuntimeException("rate limit");
        }

        // 解析消息内容
        RetailSyncToRmsInfo retailSyncInfo = Optional.ofNullable(JsonUtil.json2bean(message, RetailSyncToRmsInfo.class))
                    .orElseThrow(() -> new RuntimeException("retailSyncInfo is null"));
        log.info("retailSyncInfo:{}", retailSyncInfo);
        // 解析ID列表，支持单个ID或多个ID（逗号分隔）
        if (retailSyncInfo.getDataId() == null || retailSyncInfo.getDataId().isEmpty()) {
            throw new RuntimeException("RetailSyncInfo: dataId is null or empty");
        }
        List<Long> dataIds =
                Arrays.stream(retailSyncInfo.getDataId().split(",")).map(Long::parseLong).collect(Collectors.toList());
        try {
            // 处理零售数据
            retailSyncToRmsManage.handleRetailData(DataSyncDataTypeEnum.getEnumByMessage(retailSyncInfo.getDataType()),
                    retailSyncInfo.getOperateType(), dataIds);
        } catch (Exception e) {
            log.error("RetailSyncConsumer.onMessage error, dataId:{}", retailSyncInfo.getDataId(), e);
            // 消费失败
            //Metrics消费失败上报
            syncSoToEsMetricsService.syncToRmsError(MqAction.consumer,
                    DataSyncDataTypeEnum.getEnumByMessage(retailSyncInfo.getDataType()), dataIds.size());
            throw new RuntimeException("RetailSyncConsumer.onMessage error", e);
        }
    }
}
