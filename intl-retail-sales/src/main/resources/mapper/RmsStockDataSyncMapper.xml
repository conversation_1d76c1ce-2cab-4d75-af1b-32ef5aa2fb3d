<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.info.intl.retail.so.infra.database.mapper.datasync.RmsStockDataSyncMapper">

    <update id="updateImeiSyncStatus"
            parameterType="com.mi.info.intl.retail.so.domain.datasync.dto.StockDataSyncReqDto">
        update tmp_new_salesreport_detailimei i
        <set>
            i.sync_status=#{syncStatus},
            <if test="syncStartTime != null">
                i.sync_start_time=#{syncStartTime},
            </if>
            <if test="syncEndTime != null">
                i.sync_end_time=#{syncEndTime},
            </if>
        </set>
        where i.id_new in
        <foreach item="item" collection="idList" separator="," close=")" index="index" open="(">
            #{item}
        </foreach>
        and i.sync_status !=1
    </update>

    <update id="updateQtySyncStatus" parameterType="com.mi.info.intl.retail.so.domain.datasync.dto.StockDataSyncReqDto">
        update tmp_new_salesreport_detail i
        <set>
            i.sync_status=#{syncStatus},
            <if test="syncStartTime != null">
                i.sync_start_time=#{syncStartTime},
            </if>
            <if test="syncEndTime != null">
                i.sync_end_time=#{syncEndTime},
            </if>
        </set>
        where i.id in
        <foreach item="item" collection="idList" separator="," close=")" index="index" open="(">
            #{item}
        </foreach>
        and i.sync_status !=1
    </update>

    <select id="getImeiMinId" resultType="java.lang.Long">
        select min(id_new)
        from tmp_new_salesreport_detailimei
    </select>
    <select id="getImeiMaxId" resultType="java.lang.Long">
        select max(id_new)
        from tmp_new_salesreport_detailimei
    </select>

    <select id="queryStockImeiDataResult" parameterType="com.mi.info.intl.retail.so.app.mq.dto.RmsStockDataSyncDto"
            resultType="com.mi.info.intl.retail.so.app.mq.dto.RmsSyncImeiData">
        SELECT t.id_new,
        ifnull(t.statecode,0) AS status,
        IFNULL(t.new_faileddetail, '') AS failedReasonDetail,
        IFNULL(t.new_phase, '') AS failedReason,
        ifnull(t.new_reportingtype,0) AS reportType,
        IFNULL(t.new_siverifyresult, 0) AS siVerifyResult,
        ifnull(t.new_verificationresult, 0) AS verifyResult,
        ifnull(t.new_verifyingstate, 0) AS verifyingState,
        ifnull(t.new_isactivingcheck, 0) AS imeiRuleIsActivingCheck,
        t.modifiedby AS modifiedByRmsId,
        t.new_salestime as salesTimeStr,
        t.Createdon as createdTimeStr,
        t.modifiedon as modifiedTimeStr,
        t.new_activationtime as activationTimeStr,
        t.new_activechangetime as activationVerificationTimeStr,
        IFNULL(t.new_rrpdata, '') AS newRrpdata,
        IFNULL(t.new_activationfrequency, 0) AS activationFrequency,
        IFNULL(t.new_activationsite, '') AS activationSite,
        ifnull(t.new_aftertime,0) AS imeiRuleAfter,
        IFNULL(t.new_allowsalescountry, '') AS allowSalesCountry,
        ifnull(t.new_beforetime,0) AS imeiRuleBefore,
        ifnull(t.new_imei,'') AS imei1,
        ifnull(t.new_imei_show,'') AS imei1Mask,
        IFNULL(t.new_imei2, '') AS imei2,
        IFNULL(t.new_imei2_show, '') AS imei2Mask,
        IFNULL(t.new_imeifromhub, '') AS imeiFromHub,
        IFNULL(t.new_note, '') AS note,
        IFNULL(t.new_price, 0) AS rrp,
        IFNULL(t.new_repeatuserdetail, '') AS repeatUserDetail,
        IFNULL(t.new_resultdetail, '') AS verifyResultDetail,
        t.new_salesreport_detailimeiid AS rmsId,
        ifnull(t.new_sn,'') AS sn,
        ifnull(t.new_sn_show,'') AS snMask,
        ifnull(t.new_snhash,'') AS snHash,
        IFNULL(t.new_tertiarychannel, 0) AS firstLevelAccountCode,
        IFNULL(t.new_lastmd, '') AS lastMd,
        t.createdby AS createdByRmsId,
        t.ownerid AS salesManRmsId,
        IFNULL(t.new_product_code, 0) AS productCode,
        IFNULL(t.new_country_shortcode, '') AS finalSalesCountry,
        IFNULL(t.new_parentstore_id, '') AS storeId,
        t.new_store_id AS newStoreId,
        IFNULL(t.created_by_mid, 0) AS createdbyMiid,
        ifnull(t.sales_by_mid, 0) AS salesManMiid,
        ifnull(t.modified_by_mid, 0) AS modifiedbyMiId,
        IFNULL(t.position_rms_code, '') AS positionCodeRMS,
        IFNULL(t.position_crss_code, '') AS positionCodeNew,
        IFNULL(t.store_rms_code, '') AS storeCodeRms,
        IFNULL(t.store_crss_code, '') AS storeCodeNew,
        IFNULL(t.retailer_code, '') AS retailerCode,
        IFNULL(t.transactioncurrency_name, '') AS currency,
        IFNULL(t.rrp_code, '') as rrpCode,
        ifnull(t.store_supplier_code, '') as supplierCode,
        IFNULL(t.product_line_code, 0) AS productLineCode,
        IFNULL(t.product_name, '') as productName,
        ifnull(t.product_short_name, '') as productShortName,
        1 as isStockData,
        1 as dataFrom
        FROM tmp_new_salesreport_detailimei t
        where t.sync_status !=1
        and t.id_new > #{lastSyncId}
        <if test="isUpdate !=null">
            and t.is_update =#{isUpdate}
        </if>
        ORDER BY t.id_new
        LIMIT #{batchSize}
    </select>

    <select id="queryStockQtyDataResult" parameterType="com.mi.info.intl.retail.so.app.mq.dto.RmsStockDataSyncDto"
            resultType="com.mi.info.intl.retail.so.app.mq.dto.RmsSyncQtyData">
        SELECT t.id as idNew,
        ifnull(t.statecode,0) AS status,
        ifnull(t.new_salesreporttype,0) AS reportType,
        t.modifiedby AS modifiedByRmsId,
        t.rmsid AS rmsId,
        ifnull(t.new_quantity,0) AS quantity,
        IFNULL(t.new_price, 0) AS rrp,
        t.new_salestime as salesTimeStr,
        t.Createdon as createdTimeStr,
        t.modifiedon as modifiedTimeStr,
        IFNULL(t.new_note_detail, '') AS note,
        IFNULL(t.new_rrpdata, '') AS newRrpdata,
        t.createdby AS createdByRmsId,
        t.salesmanid AS salesManRmsId,
        IFNULL(t.new_product_code, '') AS productCode,
        IFNULL(t.store_id, '') AS storeId,
        IFNULL(t.positionid, '') AS positionId,
        IFNULL(t.created_by_mid, 0) AS createdbyMiid,
        ifnull(t.sales_by_mid, 0) AS salesManMiid,
        ifnull(t.modified_by_mid, 0) AS modifiedbyMiId,
        IFNULL(t.positioncode, '') AS positionCodeRMS,
        IFNULL(t.position_crss_code, '') AS positionCodeNew,
        IFNULL(t.store_rms_code, '') AS storeCodeRms,
        IFNULL(t.store_crss_code, '') AS storeCodeNew,
        IFNULL(t.retailer_code, '') AS retailerCode,
        IFNULL(t.transactioncurrency_name, '') AS currency,
        IFNULL(t.rrp_code, '') AS rrpRMSCode,
        IFNULL(t.product_line_code, 0) AS productLineCode,
        IFNULL(t.product_name, '') AS productName,
        ifnull(t.product_short_name, '') AS productShortName,
        1 as isStockData,
        1 as dataFrom
        FROM tmp_new_salesreport_detail t
        where t.sync_status !=1
        and t.id > #{lastSyncId}
        <if test="isUpdate !=null">
            and t.is_update =#{isUpdate}
        </if>
        ORDER BY t.id
        LIMIT #{batchSize}
    </select>



    <select id="getQtyMinId" resultType="java.lang.Long">
        select min(id)
        from tmp_new_salesreport_detail
    </select>

    <select id="getQtyMaxId" resultType="java.lang.Long">
        select max(id)
        from tmp_new_salesreport_detail
    </select>

</mapper>
