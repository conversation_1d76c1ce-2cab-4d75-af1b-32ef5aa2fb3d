<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlImportLogMapper">

    <resultMap id="BaseResultMap" type="com.mi.info.intl.retail.so.domain.upload.entity.IntlImportLog">
        <id property="id" column="id" />
        <result property="type" column="type" />
        <result property="actionType" column="action_type" />
        <result property="sourceFileId" column="source_file_id" />
        <result property="resultFileId" column="result_file_id" />
        <result property="sourceFileUrl" column="source_file_url" />
        <result property="resultFileUrl" column="result_file_url" />
        <result property="status" column="status" />
        <result property="errorMsg" column="error_msg" />
        <result property="createdAt" column="created_at" />
        <result property="updatedAt" column="updated_at" />
        <result property="createdBy" column="created_by" />
        <result property="totalCount" column="total_count" />
        <result property="taskName" column="task_name" />
        <result property="dataSource" column="data_source" />
        <result property="fileName" column="file_name" />
        <result property="executeStatus" column="execute_status" />
        <result property="finishCount" column="finish_count" />
        <result property="importDuration" column="import_duration" />
    </resultMap>

    <sql id="Base_Column_List">
        id,type,action_type,source_file_id,result_file_id,source_file_url,
        result_file_url,status,error_msg,created_at,updated_at,
        created_by,total_count,task_name,data_source,file_name,execute_status,finish_count,import_duration
    </sql>
</mapper>
