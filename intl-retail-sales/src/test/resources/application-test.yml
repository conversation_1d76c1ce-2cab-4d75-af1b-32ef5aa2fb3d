# 测试环境配置
spring:
  profiles:
    active: test

# 数据监控测试配置
intl-retail:
  data-monitor:
    enabled: true
    external-api:
      url: "http://test-external-api.com"
  
  # 机器人通知测试配置
  robot:
    enabled: true
    webhook:
      url: "https://test-robot-webhook.com"

# 日志配置
logging:
  level:
    com.mi.info.intl.retail.so.domain.datasync: DEBUG
    org.springframework.web.client: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
