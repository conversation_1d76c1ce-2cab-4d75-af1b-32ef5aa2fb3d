package com.mi.info.intl.retail.so.domain.datasync;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mi.info.intl.retail.intlretail.domain.http.rms.RmsAppliUserOauthServiceProvider;
import com.mi.info.intl.retail.so.app.mq.SyncSoToEsProducer;
import com.mi.info.intl.retail.so.app.mq.dto.RetailSyncToRmsInfo;
import com.mi.info.intl.retail.so.app.mq.dto.RmsResponseDTO;
import com.mi.info.intl.retail.so.domain.datasync.enums.DataSyncDataTypeEnum;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoImei;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoOrgInfo;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoQty;
import com.mi.info.intl.retail.so.domain.upload.service.IntlSoImeiService;
import com.mi.info.intl.retail.so.domain.upload.service.IntlSoOrgInfoService;
import com.mi.info.intl.retail.so.domain.upload.service.IntlSoQtyService;
import com.mi.info.intl.retail.utils.redis.RedisClient;
import com.mi.info.intl.retail.utils.redis.RedisKey;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * RetailSyncToRmsManage 单元测试类
 * 
 * 本测试类主要测试零售数据同步到RMS系统的核心业务逻辑，包括：
 * 1. IMEI数据的创建、更新和激活验证
 * 2. 数量数据的创建和更新
 * 3. 数据重试机制
 * 4. Redis缓存处理
 * 5. 异常情况处理
 * 6. 批量数据处理
 * 
 * 测试覆盖了正常流程、边界条件和异常场景，确保代码的健壮性和可靠性。
 * 
 * @Author: 黄涛
 * @CreateTime: 2025-08-11
 */
@ExtendWith(MockitoExtension.class)
class RetailSyncToRmsManageTest {

    /** 被测试的目标类 */
    @InjectMocks
    private RetailSyncToRmsManage retailSyncToRmsManage;

    /** 模拟组织信息服务 */
    @Mock
    private IntlSoOrgInfoService intlSoOrgInfoService;

    /** 模拟JSON序列化工具 */
    @Mock
    private ObjectMapper objectMapper;

    /** 模拟RMS系统HTTP调用服务 */
    @Mock
    private RmsAppliUserOauthServiceProvider rmsAppliUserOauthServiceProvider;

    /** 模拟ES同步消息生产者 */
    @Mock
    private SyncSoToEsProducer syncSoToEsProducer;

    /** 模拟IMEI服务 */
    @Mock
    private IntlSoImeiService intlSoImeiService;

    /** 模拟数量服务 */
    @Mock
    private IntlSoQtyService intlSoQtyService;

    /** 模拟Redis客户端 */
    @Mock
    private RedisClient redisClient;

    /** 零售同步信息对象 */
    private RetailSyncToRmsInfo retailSyncInfo;
    
    /** 数据ID列表 */
    private List<Long> dataIds;
    
    /** 测试用IMEI对象 */
    private IntlSoImei imei;
    
    /** 测试用数量对象 */
    private IntlSoQty qty;
    
    /** 测试用组织信息对象 */
    private IntlSoOrgInfo orgInfo;

    /**
     * 每个测试方法执行前的初始化设置
     * 创建测试数据和模拟对象的基础配置
     */
    @BeforeEach
    void setUp() {
        // 初始化测试数据
        dataIds = Arrays.asList(1L, 2L);
        
        // 创建零售同步信息对象
        retailSyncInfo = new RetailSyncToRmsInfo();
        retailSyncInfo.setDataId("1,2");
        
        // 创建测试用IMEI对象，设置完整的属性值
        imei = new IntlSoImei();
        imei.setId(1L);
        imei.setSn("SN001");
        imei.setImei1("IMEI001");
        imei.setImei2("IMEI002");
        imei.setProductCode("PROD001");
        imei.setOrgInfoId(100L);
        imei.setRmsId("RMS001");
        imei.setCurrency("USD");
        imei.setRrp(new BigDecimal("999.99"));
        imei.setSalesTime(System.currentTimeMillis());
        imei.setCreatedOn(System.currentTimeMillis());
        imei.setModifiedOn(System.currentTimeMillis());
        imei.setCreatedBy(1001L);
        imei.setModifiedBy(1002L);
        imei.setSalesmanMid(2001L);
        imei.setStatus(1);
        imei.setDataFrom(2);
        imei.setStoreRmsCode("STORE001");
        imei.setPositionRmsCode("POS001");
        imei.setReportingType(*********);
        imei.setAllowSalesCountry("US");
        imei.setFinalSalesCountry("US");
        imei.setNote("Test IMEI");
        imei.setImeiRuleIsActivingCheck(true);
        imei.setImeiRuleBefore(5);
        imei.setImeiRuleAfter(10);
        imei.setActivationVerificationTime(System.currentTimeMillis());
        imei.setVerifyingState(1);
        imei.setActivationTime(System.currentTimeMillis());
        imei.setActivationFrequency(3);
        imei.setActivationSite("US");
        imei.setSiVerifyResult(1);
        imei.setVerificationResult(1);
        imei.setVerifyResultDetail("Verification passed");
        imei.setRepeatUser("user1");
        imei.setLastMd("MD001");
        imei.setFailedReason(0);
        imei.setFailedReasonDetail(0);
        imei.setFirstLevelAccountCode("ACC001");

        // 创建测试用数量对象
        qty = new IntlSoQty();
        qty.setId(1L);
        qty.setProductCode("PROD001");
        qty.setQuantity(5);
        qty.setRrpCode("RRP001");
        qty.setRrp(new BigDecimal("999.99"));
        qty.setCurrency("USD");
        qty.setSalesmanMid(2001L);
        qty.setSalesTime(System.currentTimeMillis());
        qty.setReportingType(*********);
        qty.setCreatedby(1001L);
        qty.setCreatedon(System.currentTimeMillis());
        qty.setModifiedby(1002L);
        qty.setModifiedon(System.currentTimeMillis());
        qty.setNote("Test QTY");
        qty.setPositionRmsCode("POS001");
        qty.setStatus(1);
        qty.setOrgInfoId(100L);

        // 创建测试用组织信息对象
        orgInfo = new IntlSoOrgInfo();
        orgInfo.setId(100L);
        orgInfo.setCountryCode("US");
    }

    /**
     * 测试处理有效的IMEI数据创建操作
     * 验证正常流程：数据查询 -> RMS调用 -> 更新RMS ID -> 发送ES同步消息
     */
    @Test
    void handleRetailData_ValidImeiData_ShouldProcessSuccessfully() throws Exception {
        // 准备数据 - 设置IMEI数据类型和创建操作
        retailSyncInfo.setDataType("imei");
        retailSyncInfo.setOperateType("create");
        
        // 模拟依赖服务的行为
        when(intlSoImeiService.listByIds(dataIds)).thenReturn(Arrays.asList(imei));
        when(intlSoOrgInfoService.getOne(any())).thenReturn(orgInfo);
        when(redisClient.get(any(RedisKey.class))).thenReturn(null);
        when(objectMapper.writeValueAsString(any())).thenReturn("{}");
        when(rmsAppliUserOauthServiceProvider.httpForObject(anyString(), anyString(), any(), anyString(), eq(RmsResponseDTO[].class)))
                .thenReturn(new RmsResponseDTO[]{createMockRmsResponse("1", "RMS001")});
        when(intlSoImeiService.updateById(any(IntlSoImei.class))).thenReturn(true);

        // 执行测试 - 调用目标方法
        DataSyncDataTypeEnum dataType = DataSyncDataTypeEnum.valueOf(retailSyncInfo.getDataType().toUpperCase());
        String operateType = retailSyncInfo.getOperateType();
        List<Long> dataIds = Arrays.stream(retailSyncInfo.getDataId().split(","))
                .map(Long::parseLong)
                .collect(Collectors.toList());
        retailSyncToRmsManage.handleRetailData(dataType, operateType, dataIds);

        // 验证结果 - 确保所有预期的操作都被执行
        verify(syncSoToEsProducer).sendSyncEsMsg(DataSyncDataTypeEnum.IMEI, dataIds, false);
        verify(intlSoImeiService).listByIds(dataIds);
        verify(intlSoImeiService).updateById(any(IntlSoImei.class));
    }

    /**
     * 测试处理有效的数量数据创建操作
     * 验证数量数据的正常处理流程
     */
    @Test
    void handleRetailData_ValidQtyData_ShouldProcessSuccessfully() throws Exception {
        // 准备数据 - 设置数量数据类型和创建操作
        retailSyncInfo.setDataType("qty");
        retailSyncInfo.setOperateType("create");
        
        // 模拟依赖服务的行为
        when(intlSoQtyService.listByIds(dataIds)).thenReturn(Arrays.asList(qty));
        when(intlSoOrgInfoService.getOne(any())).thenReturn(orgInfo);
        when(redisClient.get(any(RedisKey.class))).thenReturn(null);
        when(objectMapper.writeValueAsString(any())).thenReturn("{}");
        when(rmsAppliUserOauthServiceProvider.httpForObject(anyString(), anyString(), any(), anyString(), eq(RmsResponseDTO[].class)))
                .thenReturn(new RmsResponseDTO[]{createMockRmsResponse("1", "RMS001")});
        when(intlSoQtyService.updateById(any(IntlSoQty.class))).thenReturn(true);

        // 执行测试
        DataSyncDataTypeEnum dataType = DataSyncDataTypeEnum.valueOf(retailSyncInfo.getDataType().toUpperCase());
        String operateType = retailSyncInfo.getOperateType();
        List<Long> dataIds = Arrays.stream(retailSyncInfo.getDataId().split(","))
                .map(Long::parseLong)
                .collect(Collectors.toList());
        retailSyncToRmsManage.handleRetailData(dataType, operateType, dataIds);

        // 验证结果
        verify(syncSoToEsProducer).sendSyncEsMsg(DataSyncDataTypeEnum.QTY, dataIds, false);
        verify(intlSoQtyService).listByIds(dataIds);
        verify(intlSoQtyService).updateById(any(IntlSoQty.class));
    }

    /**
     * 测试数据ID为null的异常情况
     * 验证输入参数验证逻辑的正确性
     */
    @Test
    void handleRetailData_NullDataId_ShouldThrowException() {
        // 准备数据 - 设置null的dataId
        retailSyncInfo.setDataId(null);

        // 执行测试并验证异常 - 应该抛出RuntimeException
        DataSyncDataTypeEnum dataType = DataSyncDataTypeEnum.valueOf(retailSyncInfo.getDataType().toUpperCase());
        String operateType = retailSyncInfo.getOperateType();
        List<Long> dataIds = Arrays.stream(retailSyncInfo.getDataId().split(","))
                .map(Long::parseLong)
                .collect(Collectors.toList());
        RuntimeException exception = assertThrows(RuntimeException.class, 
                () -> retailSyncToRmsManage.handleRetailData(dataType, operateType, dataIds));
        assertEquals("RetailSyncInfo: dataId is null or empty", exception.getMessage());
    }

    /**
     * 测试数据ID为空的异常情况
     * 验证空字符串输入的处理逻辑
     */
    @Test
    void handleRetailData_EmptyDataId_ShouldThrowException() {
        // 准备数据 - 设置空字符串的dataId
        retailSyncInfo.setDataId("");

        // 执行测试并验证异常
        DataSyncDataTypeEnum dataType = DataSyncDataTypeEnum.valueOf(retailSyncInfo.getDataType().toUpperCase());
        String operateType = retailSyncInfo.getOperateType();
        List<Long> dataIds = Arrays.stream(retailSyncInfo.getDataId().split(","))
                .map(Long::parseLong)
                .collect(Collectors.toList());
        RuntimeException exception = assertThrows(RuntimeException.class, 
                () -> retailSyncToRmsManage.handleRetailData(dataType, operateType, dataIds));
        assertEquals("RetailSyncInfo: dataId is null or empty", exception.getMessage());
    }

    /**
     * 测试无效数据类型的异常情况
     * 验证数据类型枚举值的验证逻辑
     */
    @Test
    void handleRetailData_InvalidDataType_ShouldThrowException() {
        // 准备数据 - 设置无效的数据类型
        retailSyncInfo.setDataType("invalid_type");

        // 执行测试并验证异常
        DataSyncDataTypeEnum dataType = DataSyncDataTypeEnum.valueOf(retailSyncInfo.getDataType().toUpperCase());
        String operateType = retailSyncInfo.getOperateType();
        List<Long> dataIds = Arrays.stream(retailSyncInfo.getDataId().split(","))
                .map(Long::parseLong)
                .collect(Collectors.toList());
        RuntimeException exception = assertThrows(RuntimeException.class, 
                () -> retailSyncToRmsManage.handleRetailData(dataType, operateType, dataIds));
        assertEquals("RetailSyncInfo: dataType not found : datatype invalid_type", exception.getMessage());
    }

    /**
     * 测试无效操作类型的异常情况
     * 验证操作类型枚举值的验证逻辑
     */
    @Test
    void handleRetailData_InvalidOperateType_ShouldThrowException() {
        // 准备数据 - 设置无效的操作类型
        retailSyncInfo.setDataType("imei");
        retailSyncInfo.setOperateType("invalid_operate_type");
        
        // 模拟依赖服务的基本行为
        when(intlSoImeiService.listByIds(dataIds)).thenReturn(Arrays.asList(imei));
        when(intlSoOrgInfoService.getOne(any())).thenReturn(orgInfo);
        when(redisClient.get(any(RedisKey.class))).thenReturn(null);

        // 执行测试并验证异常
        DataSyncDataTypeEnum dataType = DataSyncDataTypeEnum.valueOf(retailSyncInfo.getDataType().toUpperCase());
        String operateType = retailSyncInfo.getOperateType();
        List<Long> dataIds = Arrays.stream(retailSyncInfo.getDataId().split(","))
                .map(Long::parseLong)
                .collect(Collectors.toList());
        RuntimeException exception = assertThrows(RuntimeException.class,
                () -> retailSyncToRmsManage.handleRetailData(dataType, operateType, dataIds));
        assertEquals("RetailSyncInfo: operateType not found : operateType invalid_operate_type", exception.getCause().getMessage());
    }

    /**
     * 测试IMEI数据重试机制
     * 验证第一次查询失败后，重试机制能够成功获取数据
     */
    @Test
    void handleRetailData_ImeiDataWithRetry_ShouldProcessSuccessfully() throws Exception {
        // 准备数据 - 第一次返回空，第二次返回数据，模拟重试场景
        retailSyncInfo.setDataType("imei");
        retailSyncInfo.setOperateType("create");
        
        // 模拟重试行为：第一次返回空列表，第二次返回有效数据
        when(intlSoImeiService.listByIds(dataIds))
                .thenReturn(Collections.emptyList())
                .thenReturn(Arrays.asList(imei));
        when(intlSoOrgInfoService.getOne(any())).thenReturn(orgInfo);
        when(redisClient.get(any(RedisKey.class))).thenReturn(null);
        when(objectMapper.writeValueAsString(any())).thenReturn("{}");
        when(rmsAppliUserOauthServiceProvider.httpForObject(anyString(), anyString(), any(), anyString(), eq(RmsResponseDTO[].class)))
                .thenReturn(new RmsResponseDTO[]{createMockRmsResponse("1", "RMS001")});
        when(intlSoImeiService.updateById(any(IntlSoImei.class))).thenReturn(true);

        // 执行测试
        DataSyncDataTypeEnum dataType = DataSyncDataTypeEnum.valueOf(retailSyncInfo.getDataType().toUpperCase());
        String operateType = retailSyncInfo.getOperateType();
        List<Long> dataIds = Arrays.stream(retailSyncInfo.getDataId().split(","))
                .map(Long::parseLong)
                .collect(Collectors.toList());
        retailSyncToRmsManage.handleRetailData(dataType, operateType, dataIds);

        // 验证结果 - 应该被调用两次，证明重试机制生效
        verify(intlSoImeiService, times(2)).listByIds(dataIds);
        verify(syncSoToEsProducer).sendSyncEsMsg(DataSyncDataTypeEnum.IMEI, dataIds, false);
    }

    /**
     * 测试IMEI数据超过最大重试次数的异常情况
     * 验证重试机制的上限控制
     */
    @Test
    void handleRetailData_ImeiDataMaxRetriesExceeded_ShouldThrowException() {
        // 准备数据 - 始终返回空，模拟持续失败的情况
        retailSyncInfo.setDataType("imei");
        retailSyncInfo.setOperateType("create");
        
        when(intlSoImeiService.listByIds(dataIds)).thenReturn(Collections.emptyList());

        // 执行测试并验证异常
        DataSyncDataTypeEnum dataType = DataSyncDataTypeEnum.valueOf(retailSyncInfo.getDataType().toUpperCase());
        String operateType = retailSyncInfo.getOperateType();
        List<Long> dataIds = Arrays.stream(retailSyncInfo.getDataId().split(","))
                .map(Long::parseLong)
                .collect(Collectors.toList());
        RuntimeException exception = assertThrows(RuntimeException.class,
                () -> retailSyncToRmsManage.handleRetailData(dataType, operateType, dataIds));
        assertEquals("IMEI not found dataIds:[1, 2]", exception.getMessage());
        
        // 验证重试次数 - 应该被调用3次（初始调用 + 2次重试）
        verify(intlSoImeiService, times(3)).listByIds(dataIds);
    }

    /**
     * 测试数量数据更新操作
     * 验证更新操作类型的处理逻辑
     */
    @Test
    void handleRetailData_QtyDataWithUpdateOperation_ShouldProcessSuccessfully() throws Exception {
        // 准备数据 - 设置数量数据类型和更新操作
        retailSyncInfo.setDataType("qty");
        retailSyncInfo.setOperateType("update");
        
        // 模拟依赖服务的行为
        when(intlSoQtyService.listByIds(dataIds)).thenReturn(Arrays.asList(qty));
        when(intlSoOrgInfoService.getOne(any())).thenReturn(orgInfo);
        when(redisClient.get(any(RedisKey.class))).thenReturn(null);
        when(objectMapper.writeValueAsString(any())).thenReturn("{}");
        when(rmsAppliUserOauthServiceProvider.httpForObject(anyString(), anyString(), any(), anyString(), eq(RmsResponseDTO[].class)))
                .thenReturn(new RmsResponseDTO[0]);

        // 执行测试
        DataSyncDataTypeEnum dataType = DataSyncDataTypeEnum.valueOf(retailSyncInfo.getDataType().toUpperCase());
        String operateType = retailSyncInfo.getOperateType();
        List<Long> dataIds = Arrays.stream(retailSyncInfo.getDataId().split(","))
                .map(Long::parseLong)
                .collect(Collectors.toList());
        retailSyncToRmsManage.handleRetailData(dataType, operateType, dataIds);

        // 验证结果
        verify(syncSoToEsProducer).sendSyncEsMsg(DataSyncDataTypeEnum.QTY, dataIds, false);
        verify(intlSoQtyService).listByIds(dataIds);
    }

    /**
     * 测试IMEI数据激活验证操作
     * 验证激活验证操作类型的处理逻辑
     */
    @Test
    void handleRetailData_ImeiDataWithActivateVerification_ShouldProcessSuccessfully() throws Exception {
        // 准备数据 - 设置IMEI数据类型和激活验证操作
        retailSyncInfo.setDataType("imei");
        retailSyncInfo.setOperateType("activate verification");
        
        // 模拟依赖服务的行为
        when(intlSoImeiService.listByIds(dataIds)).thenReturn(Arrays.asList(imei));
        when(intlSoOrgInfoService.getOne(any())).thenReturn(orgInfo);
        when(redisClient.get(any(RedisKey.class))).thenReturn(null);
        when(objectMapper.writeValueAsString(any())).thenReturn("{}");
        when(rmsAppliUserOauthServiceProvider.httpForObject(anyString(), anyString(), any(), anyString(), eq(RmsResponseDTO[].class)))
                .thenReturn(new RmsResponseDTO[0]);

        // 执行测试
        DataSyncDataTypeEnum dataType = DataSyncDataTypeEnum.valueOf(retailSyncInfo.getDataType().toUpperCase());
        String operateType = retailSyncInfo.getOperateType();
        List<Long> dataIds = Arrays.stream(retailSyncInfo.getDataId().split(","))
                .map(Long::parseLong)
                .collect(Collectors.toList());
        retailSyncToRmsManage.handleRetailData(dataType, operateType, dataIds);

        // 验证结果
        verify(syncSoToEsProducer).sendSyncEsMsg(DataSyncDataTypeEnum.IMEI, dataIds, false);
        verify(intlSoImeiService).listByIds(dataIds);
    }

    /**
     * 测试RMS返回空响应的情况
     * 验证当RMS没有返回有效数据时，不会更新RMS ID
     */
    @Test
    void handleRetailData_EmptyResponseFromRms_ShouldNotUpdateRmsIds() throws Exception {
        // 准备数据 - 设置IMEI数据类型和创建操作
        retailSyncInfo.setDataType("imei");
        retailSyncInfo.setOperateType("create");
        
        // 模拟依赖服务的行为，RMS返回空响应
        when(intlSoImeiService.listByIds(dataIds)).thenReturn(Arrays.asList(imei));
        when(intlSoOrgInfoService.getOne(any())).thenReturn(orgInfo);
        when(redisClient.get(any(RedisKey.class))).thenReturn(null);
        when(objectMapper.writeValueAsString(any())).thenReturn("{}");
        when(rmsAppliUserOauthServiceProvider.httpForObject(anyString(), anyString(), any(), anyString(), eq(RmsResponseDTO[].class)))
                .thenReturn(new RmsResponseDTO[0]);

        // 执行测试
        DataSyncDataTypeEnum dataType = DataSyncDataTypeEnum.valueOf(retailSyncInfo.getDataType().toUpperCase());
        String operateType = retailSyncInfo.getOperateType();
        List<Long> dataIds = Arrays.stream(retailSyncInfo.getDataId().split(","))
                .map(Long::parseLong)
                .collect(Collectors.toList());
        retailSyncToRmsManage.handleRetailData(dataType, operateType, dataIds);

        // 验证结果 - 不应该调用更新方法，因为没有有效的RMS响应
        verify(intlSoImeiService, never()).updateById(any(IntlSoImei.class));
    }

    /**
     * 测试RMS响应中RMS ID为null的异常情况
     * 验证RMS ID验证逻辑的正确性
     */
    @Test
    void handleRetailData_RmsResponseWithNullRmsId_ShouldThrowException() throws Exception {
        // 准备数据 - 设置IMEI数据类型和创建操作
        retailSyncInfo.setDataType("imei");
        retailSyncInfo.setOperateType("create");
        
        // 模拟依赖服务的行为，RMS返回null的RMS ID
        when(intlSoImeiService.listByIds(dataIds)).thenReturn(Arrays.asList(imei));
        when(intlSoOrgInfoService.getOne(any())).thenReturn(orgInfo);
        when(redisClient.get(any(RedisKey.class))).thenReturn(null);
        when(objectMapper.writeValueAsString(any())).thenReturn("{}");
        when(rmsAppliUserOauthServiceProvider.httpForObject(anyString(), anyString(), any(), anyString(), eq(RmsResponseDTO[].class)))
                .thenReturn(new RmsResponseDTO[]{createMockRmsResponse("1", null)});

        // 执行测试并验证异常
        DataSyncDataTypeEnum dataType = DataSyncDataTypeEnum.valueOf(retailSyncInfo.getDataType().toUpperCase());
        String operateType = retailSyncInfo.getOperateType();
        List<Long> dataIds = Arrays.stream(retailSyncInfo.getDataId().split(","))
                .map(Long::parseLong)
                .collect(Collectors.toList());
        RuntimeException exception = assertThrows(RuntimeException.class, 
                () -> retailSyncToRmsManage.handleRetailData(dataType, operateType, dataIds));
        assertEquals("RMS ID is null for retailIds: [1]", exception.getCause().getMessage());
    }

    /**
     * 测试RMS响应中RMS ID为空字符串的异常情况
     * 验证空字符串RMS ID的处理逻辑
     */
    @Test
    void handleRetailData_RmsResponseWithEmptyRmsId_ShouldThrowException() throws Exception {
        // 准备数据 - 设置IMEI数据类型和创建操作
        retailSyncInfo.setDataType("imei");
        retailSyncInfo.setOperateType("create");
        
        // 模拟依赖服务的行为，RMS返回空字符串的RMS ID
        when(intlSoImeiService.listByIds(dataIds)).thenReturn(Arrays.asList(imei));
        when(intlSoOrgInfoService.getOne(any())).thenReturn(orgInfo);
        when(redisClient.get(any(RedisKey.class))).thenReturn(null);
        when(objectMapper.writeValueAsString(any())).thenReturn("{}");
        when(rmsAppliUserOauthServiceProvider.httpForObject(anyString(), anyString(), any(), anyString(), eq(RmsResponseDTO[].class)))
                .thenReturn(new RmsResponseDTO[]{createMockRmsResponse("1", "")});

        // 执行测试并验证异常
        DataSyncDataTypeEnum dataType = DataSyncDataTypeEnum.valueOf(retailSyncInfo.getDataType().toUpperCase());
        String operateType = retailSyncInfo.getOperateType();
        List<Long> dataIds = Arrays.stream(retailSyncInfo.getDataId().split(","))
                .map(Long::parseLong)
                .collect(Collectors.toList());
        RuntimeException exception = assertThrows(RuntimeException.class, 
                () -> retailSyncToRmsManage.handleRetailData(dataType, operateType, dataIds));
        assertEquals("RMS ID is null for retailIds: [1]", exception.getCause().getMessage());
    }

    /**
     * 测试实体在映射中未找到的情况
     * 验证当RMS响应中的retailId与实体不匹配时的处理逻辑
     */
    @Test
    void handleRetailData_EntityNotFoundInMap_ShouldLogError() throws Exception {
        // 准备数据 - 设置IMEI数据类型和创建操作
        retailSyncInfo.setDataType("imei");
        retailSyncInfo.setOperateType("create");
        
        // 模拟依赖服务的行为，RMS返回不匹配的retailId
        when(intlSoImeiService.listByIds(dataIds)).thenReturn(Arrays.asList(imei));
        when(intlSoOrgInfoService.getOne(any())).thenReturn(orgInfo);
        when(redisClient.get(any(RedisKey.class))).thenReturn(null);
        when(objectMapper.writeValueAsString(any())).thenReturn("{}");
        when(rmsAppliUserOauthServiceProvider.httpForObject(anyString(), anyString(), any(), anyString(), eq(RmsResponseDTO[].class)))
                .thenReturn(new RmsResponseDTO[]{createMockRmsResponse("999", "RMS999")});

        // 执行测试
        DataSyncDataTypeEnum dataType = DataSyncDataTypeEnum.valueOf(retailSyncInfo.getDataType().toUpperCase());
        String operateType = retailSyncInfo.getOperateType();
        List<Long> dataIds = Arrays.stream(retailSyncInfo.getDataId().split(","))
                .map(Long::parseLong)
                .collect(Collectors.toList());
        retailSyncToRmsManage.handleRetailData(dataType, operateType, dataIds);

        // 验证结果 - 不应该调用更新方法，因为实体未找到
        verify(intlSoImeiService, never()).updateById(any(IntlSoImei.class));
    }

    /**
     * 测试数量更新失败的情况
     * 验证更新操作失败时的异常处理
     */
    @Test
    void handleRetailData_QtyUpdateFailed_ShouldThrowException() throws Exception {
        // 准备数据 - 设置数量数据类型和创建操作
        retailSyncInfo.setDataType("qty");
        retailSyncInfo.setOperateType("create");
        
        // 模拟依赖服务的行为，数量更新失败
        when(intlSoQtyService.listByIds(dataIds)).thenReturn(Arrays.asList(qty));
        when(intlSoOrgInfoService.getOne(any())).thenReturn(orgInfo);
        when(redisClient.get(any(RedisKey.class))).thenReturn(null);
        when(objectMapper.writeValueAsString(any())).thenReturn("{}");
        when(rmsAppliUserOauthServiceProvider.httpForObject(anyString(), anyString(), any(), anyString(), eq(RmsResponseDTO[].class)))
                .thenReturn(new RmsResponseDTO[]{createMockRmsResponse("1", "RMS001")});
        when(intlSoQtyService.updateById(any(IntlSoQty.class))).thenReturn(false);

        // 执行测试并验证异常
        DataSyncDataTypeEnum dataType = DataSyncDataTypeEnum.valueOf(retailSyncInfo.getDataType().toUpperCase());
        String operateType = retailSyncInfo.getOperateType();
        List<Long> dataIds = Arrays.stream(retailSyncInfo.getDataId().split(","))
                .map(Long::parseLong)
                .collect(Collectors.toList());
        RuntimeException exception = assertThrows(RuntimeException.class, 
                () -> retailSyncToRmsManage.handleRetailData(dataType, operateType, dataIds));
        assertEquals("updateQtyRmsId error", exception.getCause().getMessage());
    }

    /**
     * 测试JSON序列化异常的情况
     * 验证序列化失败时的异常处理
     */
    @Test
    void handleRetailData_JsonProcessingException_ShouldThrowException() throws Exception {
        // 准备数据 - 设置IMEI数据类型和创建操作
        retailSyncInfo.setDataType("imei");
        retailSyncInfo.setOperateType("create");
        
        // 模拟依赖服务的行为，JSON序列化抛出异常
        when(intlSoImeiService.listByIds(dataIds)).thenReturn(Arrays.asList(imei));
        when(intlSoOrgInfoService.getOne(any())).thenReturn(orgInfo);
        when(redisClient.get(any(RedisKey.class))).thenReturn(null);
        when(objectMapper.writeValueAsString(any())).thenThrow(new JsonProcessingException("JSON error") {});

        // 执行测试并验证异常
        DataSyncDataTypeEnum dataType = DataSyncDataTypeEnum.valueOf(retailSyncInfo.getDataType().toUpperCase());
        String operateType = retailSyncInfo.getOperateType();
        List<Long> dataIds = Arrays.stream(retailSyncInfo.getDataId().split(","))
                .map(Long::parseLong)
                .collect(Collectors.toList());

        RuntimeException exception = assertThrows(RuntimeException.class, 
                () -> retailSyncToRmsManage.handleRetailData(dataType, operateType, dataIds));
        assertEquals("Failed to serialize object to JSON", exception.getCause().getMessage());
    }

    /**
     * 测试Redis缓存命中的情况
     * 验证当Redis中有缓存数据时，不会查询数据库
     */
    @Test
    void handleRetailData_RedisCacheHit_ShouldUseCachedAreaId() throws Exception {
        // 准备数据 - 设置IMEI数据类型和创建操作
        retailSyncInfo.setDataType("imei");
        retailSyncInfo.setOperateType("create");
        
        // 模拟依赖服务的行为，Redis缓存命中
        when(intlSoImeiService.listByIds(dataIds)).thenReturn(Arrays.asList(imei));
        when(redisClient.get(any(RedisKey.class))).thenReturn("US");
        when(objectMapper.writeValueAsString(any())).thenReturn("{}");
        when(rmsAppliUserOauthServiceProvider.httpForObject(anyString(), anyString(), any(), anyString(), eq(RmsResponseDTO[].class)))
                .thenReturn(new RmsResponseDTO[]{createMockRmsResponse("1", "RMS001")});
        when(intlSoImeiService.updateById(any(IntlSoImei.class))).thenReturn(true);

        // 执行测试
        DataSyncDataTypeEnum dataType = DataSyncDataTypeEnum.valueOf(retailSyncInfo.getDataType().toUpperCase());
        String operateType = retailSyncInfo.getOperateType();
        List<Long> dataIds = Arrays.stream(retailSyncInfo.getDataId().split(","))
                .map(Long::parseLong)
                .collect(Collectors.toList());
        retailSyncToRmsManage.handleRetailData(dataType, operateType, dataIds);

        // 验证结果 - 不应该查询数据库，应该使用缓存数据
        verify(intlSoOrgInfoService, never()).getOne(any());
        verify(redisClient).get(any(RedisKey.class));
    }

    /**
     * 测试Redis缓存未命中的情况
     * 验证当Redis中没有缓存数据时，会查询数据库并缓存结果
     */
    @Test
    void handleRetailData_RedisCacheMiss_ShouldQueryDatabaseAndCache() throws Exception {
        // 准备数据 - 设置IMEI数据类型和创建操作
        retailSyncInfo.setDataType("imei");
        retailSyncInfo.setOperateType("create");
        
        // 模拟依赖服务的行为，Redis缓存未命中
        when(intlSoImeiService.listByIds(dataIds)).thenReturn(Arrays.asList(imei));
        when(redisClient.get(any(RedisKey.class))).thenReturn(null);
        when(intlSoOrgInfoService.getOne(any())).thenReturn(orgInfo);
        when(objectMapper.writeValueAsString(any())).thenReturn("{}");
        when(rmsAppliUserOauthServiceProvider.httpForObject(anyString(), anyString(), any(), anyString(), eq(RmsResponseDTO[].class)))
                .thenReturn(new RmsResponseDTO[]{createMockRmsResponse("1", "RMS001")});
        when(intlSoImeiService.updateById(any(IntlSoImei.class))).thenReturn(true);

        // 执行测试
        DataSyncDataTypeEnum dataType = DataSyncDataTypeEnum.valueOf(retailSyncInfo.getDataType().toUpperCase());
        String operateType = retailSyncInfo.getOperateType();
        List<Long> dataIds = Arrays.stream(retailSyncInfo.getDataId().split(","))
                .map(Long::parseLong)
                .collect(Collectors.toList());
        retailSyncToRmsManage.handleRetailData(dataType, operateType, dataIds);

        // 验证结果 - 应该查询数据库并缓存结果
        verify(intlSoOrgInfoService).getOne(any());
        verify(redisClient).set(any(RedisKey.class), eq("US"));
    }

    /**
     * 测试组织信息未找到的情况
     * 验证当无法获取组织信息时的异常处理
     */
    @Test
    void handleRetailData_OrgInfoNotFound_ShouldThrowException() {
        // 准备数据 - 设置IMEI数据类型和创建操作
        retailSyncInfo.setDataType("imei");
        retailSyncInfo.setOperateType("create");
        
        // 模拟依赖服务的行为，组织信息未找到
        when(intlSoImeiService.listByIds(dataIds)).thenReturn(Arrays.asList(imei));
        when(redisClient.get(any(RedisKey.class))).thenReturn(null);
        when(intlSoOrgInfoService.getOne(any())).thenReturn(null);

        // 执行测试并验证异常
        DataSyncDataTypeEnum dataType = DataSyncDataTypeEnum.valueOf(retailSyncInfo.getDataType().toUpperCase());
        String operateType = retailSyncInfo.getOperateType();
        List<Long> dataIds = Arrays.stream(retailSyncInfo.getDataId().split(","))
                .map(Long::parseLong)
                .collect(Collectors.toList());
        RuntimeException exception = assertThrows(RuntimeException.class, 
                () -> retailSyncToRmsManage.handleRetailData(dataType, operateType, dataIds));
        assertEquals("areaId is null or empty for orgInfoId: 100", exception.getMessage());
    }

    /**
     * 测试组织信息中国家代码为空字符串的情况
     * 验证空字符串国家代码的处理逻辑
     */
    @Test
    void handleRetailData_OrgInfoWithEmptyCountryCode_ShouldThrowException() {
        // 准备数据 - 设置IMEI数据类型和创建操作
        retailSyncInfo.setDataType("imei");
        retailSyncInfo.setOperateType("create");
        
        // 创建国家代码为空字符串的组织信息对象
        IntlSoOrgInfo orgInfoWithEmptyCountry = new IntlSoOrgInfo();
        orgInfoWithEmptyCountry.setId(100L);
        orgInfoWithEmptyCountry.setCountryCode("");
        
        // 模拟依赖服务的行为
        when(intlSoImeiService.listByIds(dataIds)).thenReturn(Arrays.asList(imei));
        when(redisClient.get(any(RedisKey.class))).thenReturn(null);
        when(intlSoOrgInfoService.getOne(any())).thenReturn(orgInfoWithEmptyCountry);

        // 执行测试并验证异常
        DataSyncDataTypeEnum dataType = DataSyncDataTypeEnum.valueOf(retailSyncInfo.getDataType().toUpperCase());
        String operateType = retailSyncInfo.getOperateType();
        List<Long> dataIds = Arrays.stream(retailSyncInfo.getDataId().split(","))
                .map(Long::parseLong)
                .collect(Collectors.toList());
        RuntimeException exception = assertThrows(RuntimeException.class,
                () -> retailSyncToRmsManage.handleRetailData(dataType, operateType, dataIds));
        assertEquals("areaId is null or empty for orgInfoId: 100", exception.getMessage());
    }

    /**
     * 测试组织信息中国家代码为null的情况
     * 验证null国家代码的处理逻辑
     */
    @Test
    void handleRetailData_OrgInfoWithNullCountryCode_ShouldThrowException() {
        // 准备数据 - 设置IMEI数据类型和创建操作
        retailSyncInfo.setDataType("imei");
        retailSyncInfo.setOperateType("create");
        
        // 创建国家代码为null的组织信息对象
        IntlSoOrgInfo orgInfoWithNullCountry = new IntlSoOrgInfo();
        orgInfoWithNullCountry.setId(100L);
        orgInfoWithNullCountry.setCountryCode(null);
        
        // 模拟依赖服务的行为
        when(intlSoImeiService.listByIds(dataIds)).thenReturn(Arrays.asList(imei));
        when(redisClient.get(any(RedisKey.class))).thenReturn(null);
        when(intlSoOrgInfoService.getOne(any())).thenReturn(orgInfoWithNullCountry);

        // 执行测试并验证异常
        DataSyncDataTypeEnum dataType = DataSyncDataTypeEnum.valueOf(retailSyncInfo.getDataType().toUpperCase());
        String operateType = retailSyncInfo.getOperateType();
        List<Long> dataIds = Arrays.stream(retailSyncInfo.getDataId().split(","))
                .map(Long::parseLong)
                .collect(Collectors.toList());
        RuntimeException exception = assertThrows(RuntimeException.class, 
                () -> retailSyncToRmsManage.handleRetailData(dataType, operateType, dataIds));
        assertEquals("areaId is null or empty for orgInfoId: 100", exception.getMessage());
    }

    /**
     * 测试Redis异常的情况
     * 验证当Redis出现异常时，会继续使用数据库查询
     */
    @Test
    void handleRetailData_RedisException_ShouldContinueWithDatabase() throws Exception {
        // 准备数据 - 设置IMEI数据类型和创建操作
        retailSyncInfo.setDataType("imei");
        retailSyncInfo.setOperateType("create");
        
        // 模拟依赖服务的行为，Redis抛出异常
        when(intlSoImeiService.listByIds(dataIds)).thenReturn(Arrays.asList(imei));
        when(redisClient.get(any(RedisKey.class))).thenThrow(new RuntimeException("Redis error"));
        when(intlSoOrgInfoService.getOne(any())).thenReturn(orgInfo);
        when(objectMapper.writeValueAsString(any())).thenReturn("{}");
        when(rmsAppliUserOauthServiceProvider.httpForObject(anyString(), anyString(), any(), anyString(), eq(RmsResponseDTO[].class)))
                .thenReturn(new RmsResponseDTO[]{createMockRmsResponse("1", "RMS001")});
        when(intlSoImeiService.updateById(any(IntlSoImei.class))).thenReturn(true);

        // 执行测试
        DataSyncDataTypeEnum dataType = DataSyncDataTypeEnum.valueOf(retailSyncInfo.getDataType().toUpperCase());
        String operateType = retailSyncInfo.getOperateType();
        List<Long> dataIds = Arrays.stream(retailSyncInfo.getDataId().split(","))
                .map(Long::parseLong)
                .collect(Collectors.toList());
        retailSyncToRmsManage.handleRetailData(dataType, operateType, dataIds);

        // 验证结果 - 应该查询数据库，证明Redis异常不影响正常流程
        verify(intlSoOrgInfoService).getOne(any());
    }

    /**
     * 测试Redis设置异常的情况
     * 验证当Redis设置操作失败时，不会影响主要业务流程
     */
    @Test
    void handleRetailData_RedisSetException_ShouldContinueProcessing() throws Exception {
        // 准备数据 - 设置IMEI数据类型和创建操作
        retailSyncInfo.setDataType("imei");
        retailSyncInfo.setOperateType("create");
        
        // 模拟依赖服务的行为，Redis设置操作抛出异常
        when(intlSoImeiService.listByIds(dataIds)).thenReturn(Arrays.asList(imei));
        when(redisClient.get(any(RedisKey.class))).thenReturn(null);
        when(intlSoOrgInfoService.getOne(any())).thenReturn(orgInfo);
        when(redisClient.set(any(RedisKey.class), anyString())).thenThrow(new RuntimeException("Redis set error"));
        when(objectMapper.writeValueAsString(any())).thenReturn("{}");
        when(rmsAppliUserOauthServiceProvider.httpForObject(anyString(), anyString(), any(), anyString(), eq(RmsResponseDTO[].class)))
                .thenReturn(new RmsResponseDTO[]{createMockRmsResponse("1", "RMS001")});
        when(intlSoImeiService.updateById(any(IntlSoImei.class))).thenReturn(true);

        // 执行测试 - 不应该抛出异常，Redis设置失败不影响主流程
        DataSyncDataTypeEnum dataType = DataSyncDataTypeEnum.valueOf(retailSyncInfo.getDataType().toUpperCase());
        String operateType = retailSyncInfo.getOperateType();
        List<Long> dataIds = Arrays.stream(retailSyncInfo.getDataId().split(","))
                .map(Long::parseLong)
                .collect(Collectors.toList());

        assertDoesNotThrow(() -> retailSyncToRmsManage.handleRetailData(dataType, operateType, dataIds));

        // 验证结果 - 主要业务逻辑应该正常执行
        verify(intlSoImeiService).updateById(any(IntlSoImei.class));
    }

    /**
     * 测试线程中断的情况
     * 验证重试等待过程中线程被中断时的异常处理
     */
    @Test
    void handleRetailData_ThreadInterrupted_ShouldThrowException() {
        // 准备数据 - 设置IMEI数据类型和创建操作
        retailSyncInfo.setDataType("imei");
        retailSyncInfo.setOperateType("create");
        
        when(intlSoImeiService.listByIds(dataIds)).thenReturn(Collections.emptyList());
        
        // 模拟线程中断
        Thread.currentThread().interrupt();

        // 执行测试并验证异常
        DataSyncDataTypeEnum dataType = DataSyncDataTypeEnum.valueOf(retailSyncInfo.getDataType().toUpperCase());
        String operateType = retailSyncInfo.getOperateType();
        List<Long> dataIds = Arrays.stream(retailSyncInfo.getDataId().split(","))
                .map(Long::parseLong)
                .collect(Collectors.toList());
        RuntimeException exception = assertThrows(RuntimeException.class, 
                () -> retailSyncToRmsManage.handleRetailData(dataType, operateType, dataIds));
        assertEquals("Thread interrupted during retry wait", exception.getMessage());
        
        // 恢复线程状态，避免影响后续测试
        Thread.interrupted();
    }

    /**
     * 测试批量数据超过限制的情况
     * 验证大数据量时的分批处理逻辑
     */
    @Test
    void handleRetailData_BatchSizeExceedsLimit_ShouldProcessInBatches() throws Exception {
        // 准备数据 - 创建超过BATCH_SIZE的数据（假设BATCH_SIZE=20）
        retailSyncInfo.setDataType("imei");
        retailSyncInfo.setOperateType("create");
        
        // 创建21个数据ID，超过批处理限制
        List<Long> largeDataIds = Arrays.asList(1L, 2L, 3L, 4L, 5L, 6L, 7L, 8L, 9L, 10L, 
                                               11L, 12L, 13L, 14L, 15L, 16L, 17L, 18L, 19L, 20L, 21L);
        retailSyncInfo.setDataId("1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21");
        
        // 创建对应的IMEI对象列表
        List<IntlSoImei> largeImeiList = createLargeImeiList(largeDataIds);
        
        // 模拟依赖服务的行为
        when(intlSoImeiService.listByIds(largeDataIds)).thenReturn(largeImeiList);
        when(intlSoOrgInfoService.getOne(any())).thenReturn(orgInfo);
        when(redisClient.get(any(RedisKey.class))).thenReturn(null);
        when(objectMapper.writeValueAsString(any())).thenReturn("{}");
        when(rmsAppliUserOauthServiceProvider.httpForObject(anyString(), anyString(), any(), anyString(), eq(RmsResponseDTO[].class)))
                .thenReturn(new RmsResponseDTO[0]);

        // 执行测试
        DataSyncDataTypeEnum dataType = DataSyncDataTypeEnum.valueOf(retailSyncInfo.getDataType().toUpperCase());
        String operateType = retailSyncInfo.getOperateType();
        List<Long> dataIds = Arrays.stream(retailSyncInfo.getDataId().split(","))
                .map(Long::parseLong)
                .collect(Collectors.toList());
        retailSyncToRmsManage.handleRetailData(dataType, operateType, dataIds);

        // 验证结果 - 应该被调用多次（21个数据，BATCH_SIZE=20，所以需要2次调用）
        verify(rmsAppliUserOauthServiceProvider, times(2))
                .httpForObject(anyString(), anyString(), any(), anyString(), eq(RmsResponseDTO[].class));
    }

    /**
     * 测试多个不同区域ID的情况
     * 验证不同区域的数据会被分别处理
     */
    @Test
    void handleRetailData_MultipleAreaIds_ShouldProcessEachAreaSeparately() throws Exception {
        // 准备数据 - 创建不同areaId的数据
        retailSyncInfo.setDataType("imei");
        retailSyncInfo.setOperateType("create");
        
        // 创建两个不同区域的IMEI对象
        IntlSoImei imei1 = new IntlSoImei();
        imei1.setId(1L);
        imei1.setOrgInfoId(100L); // US区域
        
        IntlSoImei imei2 = new IntlSoImei();
        imei2.setId(2L);
        imei2.setOrgInfoId(200L); // CN区域
        
        // 创建CN区域的组织信息
        IntlSoOrgInfo orgInfoCN = new IntlSoOrgInfo();
        orgInfoCN.setId(200L);
        orgInfoCN.setCountryCode("CN");
        
        // 模拟依赖服务的行为
        when(intlSoImeiService.listByIds(dataIds)).thenReturn(Arrays.asList(imei1, imei2));
        when(redisClient.get(any(RedisKey.class))).thenReturn(null);
        when(intlSoOrgInfoService.getOne(any())).thenReturn(orgInfo).thenReturn(orgInfoCN);
        when(objectMapper.writeValueAsString(any())).thenReturn("{}");
        when(rmsAppliUserOauthServiceProvider.httpForObject(anyString(), anyString(), any(), anyString(), eq(RmsResponseDTO[].class)))
                .thenReturn(new RmsResponseDTO[0]);

        // 执行测试
        DataSyncDataTypeEnum dataType = DataSyncDataTypeEnum.valueOf(retailSyncInfo.getDataType().toUpperCase());
        String operateType = retailSyncInfo.getOperateType();
        List<Long> dataIds = Arrays.stream(retailSyncInfo.getDataId().split(","))
                .map(Long::parseLong)
                .collect(Collectors.toList());
        retailSyncToRmsManage.handleRetailData(dataType, operateType, dataIds);

        // 验证结果 - 应该为每个areaId调用一次，证明不同区域被分别处理
        verify(rmsAppliUserOauthServiceProvider, times(2))
                .httpForObject(anyString(), anyString(), any(), anyString(), eq(RmsResponseDTO[].class));
    }

    /**
     * 创建模拟的RMS响应对象
     * 
     * @param retailId 零售ID
     * @param rmsId RMS ID
     * @return 模拟的RMS响应对象
     */
    private RmsResponseDTO createMockRmsResponse(String retailId, String rmsId) {
        RmsResponseDTO response = new RmsResponseDTO();
        response.setRetailId(retailId);
        response.setRmsId(rmsId);
        return response;
    }

    /**
     * 创建大量的IMEI对象列表
     * 用于测试批量处理逻辑
     * 
     * @param ids ID列表
     * @return IMEI对象列表
     */
    private List<IntlSoImei> createLargeImeiList(List<Long> ids) {
        return ids.stream().map(id -> {
            IntlSoImei imei = new IntlSoImei();
            imei.setId(id);
            imei.setOrgInfoId(100L);
            return imei;
        }).collect(java.util.stream.Collectors.toList());
    }
} 