package com.mi.info.intl.retail.so.domain.datasync;

import com.mi.info.intl.retail.so.domain.datasync.dto.DataDifferenceDto;
import com.mi.info.intl.retail.so.domain.datasync.enums.DataCompareResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import static org.mockito.ArgumentMatchers.argThat;

/**
 * RobotNotificationService 单元测试
 */
@ExtendWith(MockitoExtension.class)
class RobotNotificationServiceTest {

    @Mock
    private RestTemplate restTemplate;

    @InjectMocks
    private RobotNotificationService robotNotificationService;

    private static final String TEST_WEBHOOK_URL = "https://test-webhook-url.com";

    @BeforeEach
    void setUp() {
        // 设置测试配置
        ReflectionTestUtils.setField(robotNotificationService, "robotWebhookUrl", TEST_WEBHOOK_URL);
        ReflectionTestUtils.setField(robotNotificationService, "robotEnabled", true);
    }

    @Test
    void testSendDataDifferenceReport_WithDifferences_Success() {
        // 准备测试数据
        List<DataDifferenceDto> imeiStatistics = createTestImeiDifferences();
        List<DataDifferenceDto> qtyStatistics = createTestQtyDifferences();

        // Mock RestTemplate响应
        when(restTemplate.postForEntity(anyString(), any(HttpEntity.class), eq(String.class)))
                .thenReturn(new ResponseEntity<>("success", HttpStatus.OK));

        // 执行测试
        robotNotificationService.sendDataDifferenceReport(imeiStatistics, qtyStatistics);

        // 验证调用
        verify(restTemplate, times(1)).postForEntity(
                eq(TEST_WEBHOOK_URL),
                any(HttpEntity.class),
                eq(String.class)
        );
    }

    @Test
    void testSendDataDifferenceReport_NoDifferences_Success() {
        // 准备测试数据 - 无差异
        List<DataDifferenceDto> imeiStatistics = createEmptyDifferences();
        List<DataDifferenceDto> qtyStatistics = createEmptyDifferences();

        // Mock RestTemplate响应
        when(restTemplate.postForEntity(anyString(), any(HttpEntity.class), eq(String.class)))
                .thenReturn(new ResponseEntity<>("success", HttpStatus.OK));

        // 执行测试
        robotNotificationService.sendDataDifferenceReport(imeiStatistics, qtyStatistics);

        // 验证调用
        verify(restTemplate, times(1)).postForEntity(
                eq(TEST_WEBHOOK_URL),
                any(HttpEntity.class),
                eq(String.class)
        );
    }

    @Test
    void testSendDataDifferenceReport_RobotDisabled_NoSend() {
        // 禁用机器人通知
        ReflectionTestUtils.setField(robotNotificationService, "robotEnabled", false);

        // 准备测试数据
        List<DataDifferenceDto> imeiStatistics = createTestImeiDifferences();
        List<DataDifferenceDto> qtyStatistics = createTestQtyDifferences();

        // 执行测试
        robotNotificationService.sendDataDifferenceReport(imeiStatistics, qtyStatistics);

        // 验证未调用RestTemplate
        verify(restTemplate, never()).postForEntity(anyString(), any(HttpEntity.class), eq(String.class));
    }

    @Test
    void testSendDataDifferenceReport_EmptyWebhookUrl_NoSend() {
        // 清空webhook URL
        ReflectionTestUtils.setField(robotNotificationService, "robotWebhookUrl", "");

        // 准备测试数据
        List<DataDifferenceDto> imeiStatistics = createTestImeiDifferences();
        List<DataDifferenceDto> qtyStatistics = createTestQtyDifferences();

        // 执行测试
        robotNotificationService.sendDataDifferenceReport(imeiStatistics, qtyStatistics);

        // 验证未调用RestTemplate
        verify(restTemplate, never()).postForEntity(anyString(), any(HttpEntity.class), eq(String.class));
    }

    @Test
    void testSendDataDifferenceReport_RestTemplateException_Handled() {
        // 准备测试数据
        List<DataDifferenceDto> imeiStatistics = createTestImeiDifferences();
        List<DataDifferenceDto> qtyStatistics = createTestQtyDifferences();

        // Mock RestTemplate抛出异常
        when(restTemplate.postForEntity(anyString(), any(HttpEntity.class), eq(String.class)))
                .thenThrow(new RuntimeException("Network error"));

        // 执行测试 - 不应抛出异常
        robotNotificationService.sendDataDifferenceReport(imeiStatistics, qtyStatistics);

        // 验证调用
        verify(restTemplate, times(1)).postForEntity(
                eq(TEST_WEBHOOK_URL),
                any(HttpEntity.class),
                eq(String.class)
        );
    }

    @Test
    void testSendDataDifferenceReport_NullStatistics_Success() {
        // Mock RestTemplate响应
        when(restTemplate.postForEntity(anyString(), any(HttpEntity.class), eq(String.class)))
                .thenReturn(new ResponseEntity<>("success", HttpStatus.OK));

        // 执行测试 - 传入null
        robotNotificationService.sendDataDifferenceReport(null, null);

        // 验证调用
        verify(restTemplate, times(1)).postForEntity(
                eq(TEST_WEBHOOK_URL),
                any(HttpEntity.class),
                eq(String.class)
        );
    }

    @Test
    void testSendSimpleNotification_Success() {
        String title = "测试标题";
        String content = "测试内容";

        // Mock RestTemplate响应
        when(restTemplate.postForEntity(anyString(), any(HttpEntity.class), eq(String.class)))
                .thenReturn(new ResponseEntity<>("success", HttpStatus.OK));

        // 执行测试
        robotNotificationService.sendSimpleNotification(title, content);

        // 验证调用
        verify(restTemplate, times(1)).postForEntity(
                eq(TEST_WEBHOOK_URL),
                any(HttpEntity.class),
                eq(String.class)
        );
    }

    @Test
    void testSendSimpleNotification_RobotDisabled_NoSend() {
        // 禁用机器人通知
        ReflectionTestUtils.setField(robotNotificationService, "robotEnabled", false);

        String title = "测试标题";
        String content = "测试内容";

        // 执行测试
        robotNotificationService.sendSimpleNotification(title, content);

        // 验证未调用RestTemplate
        verify(restTemplate, never()).postForEntity(anyString(), any(HttpEntity.class), eq(String.class));
    }

    @Test
    void testSendSimpleNotification_EmptyWebhookUrl_NoSend() {
        // 清空webhook URL
        ReflectionTestUtils.setField(robotNotificationService, "robotWebhookUrl", "");

        String title = "测试标题";
        String content = "测试内容";

        // 执行测试
        robotNotificationService.sendSimpleNotification(title, content);

        // 验证未调用RestTemplate
        verify(restTemplate, never()).postForEntity(anyString(), any(HttpEntity.class), eq(String.class));
    }

    @Test
    void testSendSimpleNotification_RestTemplateException_Handled() {
        String title = "测试标题";
        String content = "测试内容";

        // Mock RestTemplate抛出异常
        when(restTemplate.postForEntity(anyString(), any(HttpEntity.class), eq(String.class)))
                .thenThrow(new RuntimeException("Network error"));

        // 执行测试 - 不应抛出异常
        robotNotificationService.sendSimpleNotification(title, content);

        // 验证调用
        verify(restTemplate, times(1)).postForEntity(
                eq(TEST_WEBHOOK_URL),
                any(HttpEntity.class),
                eq(String.class)
        );
    }

    @Test
    void testSendDataDifferenceReport_VerifyMessageFormat() {
        // 准备测试数据
        List<DataDifferenceDto> imeiStatistics = createTestImeiDifferences();
        List<DataDifferenceDto> qtyStatistics = createTestQtyDifferences();

        // Mock RestTemplate响应
        when(restTemplate.postForEntity(anyString(), any(HttpEntity.class), eq(String.class)))
                .thenReturn(new ResponseEntity<>("success", HttpStatus.OK));

        // 执行测试
        robotNotificationService.sendDataDifferenceReport(imeiStatistics, qtyStatistics);

        // 验证消息格式
        verify(restTemplate).postForEntity(
                eq(TEST_WEBHOOK_URL),
                argThat(entity -> {
                    HttpEntity<Map<String, Object>> httpEntity = (HttpEntity<Map<String, Object>>) entity;
                    Map<String, Object> body = httpEntity.getBody();
                    
                    // 验证消息类型
                    assert body != null;
                    assert "markdown".equals(body.get("msgtype"));
                    
                    // 验证markdown内容
                    Map<String, Object> markdown = (Map<String, Object>) body.get("markdown");
                    assert markdown != null;
                    assert "数据差异监控告警".equals(markdown.get("title"));
                    
                    String text = (String) markdown.get("text");
                    assert text != null;
                    assert text.contains("📊 **数据对比监控报告**");
                    assert text.contains("**IMEI差异分类统计：**");
                    assert text.contains("**QTY差异分类统计：**");
                    assert text.contains("零售系统单边有数据");
                    assert text.contains("RMS系统单边有数据");
                    assert text.contains("两边数据状态不一致");
                    
                    return true;
                }),
                eq(String.class)
        );
    }

    @Test
    void testSendDataDifferenceReport_AllZeroDifferences_ShowsNormalMessage() {
        // 准备测试数据 - 所有差异都为0
        List<DataDifferenceDto> imeiStatistics = createEmptyDifferences();
        List<DataDifferenceDto> qtyStatistics = createEmptyDifferences();

        // Mock RestTemplate响应
        when(restTemplate.postForEntity(anyString(), any(HttpEntity.class), eq(String.class)))
                .thenReturn(new ResponseEntity<>("success", HttpStatus.OK));

        // 执行测试
        robotNotificationService.sendDataDifferenceReport(imeiStatistics, qtyStatistics);

        // 验证消息包含正常信息
        verify(restTemplate).postForEntity(
                eq(TEST_WEBHOOK_URL),
                argThat(entity -> {
                    HttpEntity<Map<String, Object>> httpEntity = (HttpEntity<Map<String, Object>>) entity;
                    Map<String, Object> body = httpEntity.getBody();
                    
                    Map<String, Object> markdown = (Map<String, Object>) body.get("markdown");
                    String text = (String) markdown.get("text");
                    
                    assert text.contains("✅ **数据对比正常，未发现差异**");
                    
                    return true;
                }),
                eq(String.class)
        );
    }

    // 辅助方法：创建测试IMEI差异数据
    private List<DataDifferenceDto> createTestImeiDifferences() {
        List<DataDifferenceDto> differences = new ArrayList<>();
        
        // 零售系统单边有数据
        differences.add(new DataDifferenceDto(
                DataCompareResult.RETAIL_ONLY,
                5L,
                Arrays.asList("1001", "1002", "1003", "1004", "1005")
        ));
        
        // RMS系统单边有数据
        differences.add(new DataDifferenceDto(
                DataCompareResult.RMS_ONLY,
                3L,
                Arrays.asList("2001", "2002", "2003")
        ));
        
        // 状态不一致
        differences.add(new DataDifferenceDto(
                DataCompareResult.STATUS_INCONSISTENT,
                2L,
                Arrays.asList("3001", "3002")
        ));
        
        return differences;
    }

    // 辅助方法：创建测试QTY差异数据
    private List<DataDifferenceDto> createTestQtyDifferences() {
        List<DataDifferenceDto> differences = new ArrayList<>();
        
        // 零售系统单边有数据
        differences.add(new DataDifferenceDto(
                DataCompareResult.RETAIL_ONLY,
                2L,
                Arrays.asList("4001", "4002")
        ));
        
        // RMS系统单边有数据
        differences.add(new DataDifferenceDto(
                DataCompareResult.RMS_ONLY,
                1L,
                Arrays.asList("5001")
        ));
        
        // 状态不一致
        differences.add(new DataDifferenceDto(
                DataCompareResult.STATUS_INCONSISTENT,
                0L,
                new ArrayList<>()
        ));
        
        return differences;
    }

    // 辅助方法：创建空差异数据
    private List<DataDifferenceDto> createEmptyDifferences() {
        List<DataDifferenceDto> differences = new ArrayList<>();
        
        // 所有类别都为0
        differences.add(new DataDifferenceDto(DataCompareResult.RETAIL_ONLY, 0L, new ArrayList<>()));
        differences.add(new DataDifferenceDto(DataCompareResult.RMS_ONLY, 0L, new ArrayList<>()));
        differences.add(new DataDifferenceDto(DataCompareResult.STATUS_INCONSISTENT, 0L, new ArrayList<>()));
        
        return differences;
    }
}
