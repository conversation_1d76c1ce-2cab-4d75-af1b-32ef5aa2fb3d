package com.mi.info.intl.retail.so.domain.rule.aggregate;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import com.mi.info.intl.retail.so.domain.rule.SoRuleLoadMockDataTest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import com.google.common.collect.Lists;
import com.mi.info.intl.retail.api.country.CountryTimeZoneApiService;
import com.mi.info.intl.retail.api.country.dto.CountryDTO;
import com.mi.info.intl.retail.component.ComponentLocator;
import com.mi.info.intl.retail.component.convertor.impl.ConverterFacadeImpl;
import com.mi.info.intl.retail.enums.SwitchEnum;
import com.mi.info.intl.retail.exception.BizException;
import com.mi.info.intl.retail.exception.ErrorCodes;
import com.mi.info.intl.retail.intlretail.service.api.bpm.dto.BpmUser;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.dto.SoRuleApproveCallbackDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.dto.SoRuleDetailCreateDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.dto.SoRuleDetailModifyDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.dto.SoRuleRetailerItemDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.dto.SoRuleRetailerStatisticsDTO;
import com.mi.info.intl.retail.model.UserInfo;
import com.mi.info.intl.retail.service.DistributionLock;
import com.mi.info.intl.retail.service.DistributionLockService;
import com.mi.info.intl.retail.so.domain.rule.enums.SoRuleDetailApproveStatus;
import com.mi.info.intl.retail.so.domain.rule.enums.SoRuleEnum;
import com.mi.info.intl.retail.so.domain.rule.service.IntlSoRuleChangeLogService;
import com.mi.info.intl.retail.so.domain.rule.service.IntlSoRuleDetailService;
import com.mi.info.intl.retail.so.domain.rule.service.IntlSoRuleRetailerService;
import com.mi.info.intl.retail.so.infra.database.dataobject.rule.IntlSoRuleDetail;
import com.mi.info.intl.retail.so.infra.database.dataobject.rule.IntlSoRuleDetailLog;
import com.mi.info.intl.retail.so.infra.database.dataobject.rule.IntlSoRuleRetailer;
import com.mi.info.intl.retail.utils.AESGCMUtil;
import com.mi.info.intl.retail.utils.UserInfoUtil;

import cn.hutool.core.lang.Tuple;

/**
 * SoRuleTransactionalAggregateService单元测试
 *
 * <AUTHOR>
 * @date 2025/08/21
 */
@ExtendWith(MockitoExtension.class)
class SoRuleTransactionalAggregateServiceTest extends SoRuleLoadMockDataTest {

    @Mock
    private IntlSoRuleDetailService intlSoRuleDetailService;

    @Mock
    private IntlSoRuleRetailerService intlSoRuleRetailerService;

    @Mock
    private CountryTimeZoneApiService countryTimeZoneApiService;

    @Mock
    private IntlSoRuleChangeLogService intlSoRuleChangeLogService;

    @Mock
    private DistributionLockService distributionLockService;

    @InjectMocks
    private SoRuleTransactionalAggregateService soRuleTransactionalAggregateService;

    private UserInfo mockUserInfo;
    private CountryDTO mockCountryDTO;
    private IntlSoRuleDetail mockIntlSoRuleDetail;
    private IntlSoRuleDetailLog mockIntlSoRuleDetailLog;
    private SoRuleDetailCreateDTO mockSoRuleDetailCreateDTO;
    private SoRuleDetailModifyDTO mockSoRuleDetailModifyDTO;
    private SoRuleApproveCallbackDTO mockSoRuleApproveCallbackDTO;
    private DistributionLock distributionLock;

    @BeforeEach
    void setUp() {
        // 初始化用户信息
        mockUserInfo = getUserInfoMock();

        // 初始化国家信息
        mockCountryDTO = getCountryMock();

        // 初始化规则明细
        mockIntlSoRuleDetail = new IntlSoRuleDetail();
        mockIntlSoRuleDetail.setId(1L);
        mockIntlSoRuleDetail.setCountryCode("CN");
        mockIntlSoRuleDetail.setRegionCode("ASIA");
        mockIntlSoRuleDetail.setStatus(SoRuleDetailApproveStatus.APPROVED.getValue());
        mockIntlSoRuleDetail.setEffectiveTime(System.currentTimeMillis());

        // 初始化规则变更日志
        mockIntlSoRuleDetailLog = new IntlSoRuleDetailLog();
        mockIntlSoRuleDetailLog.setId(1L);
        mockIntlSoRuleDetailLog.setMasterId(1L);
        mockIntlSoRuleDetailLog.setCountryCode("CN");
        mockIntlSoRuleDetailLog.setStatus(SoRuleDetailApproveStatus.CREATE.getValue());

        // 初始化零售商
        SoRuleRetailerItemDTO retailer1 = new SoRuleRetailerItemDTO();
        retailer1.setRetailerCode("retailer1");
        retailer1.setRetailerName("retailer1");
        retailer1.setImeiSwitch(SwitchEnum.ON.getValue());
        retailer1.setQtySwitch(SwitchEnum.ON.getValue());

        // 初始化创建规则DTO
        mockSoRuleDetailCreateDTO = new SoRuleDetailCreateDTO();
        mockSoRuleDetailCreateDTO.setCountryCode("CN");
        mockSoRuleDetailCreateDTO.setType(1);
        mockSoRuleDetailCreateDTO.setAllAddImei(1);
        mockSoRuleDetailCreateDTO.setAllAddQty(1);
        mockSoRuleDetailCreateDTO.setEffectiveTime(System.currentTimeMillis());
        mockSoRuleDetailCreateDTO.setDefaultRetailersSwitch(Arrays.asList(1, 2));
        mockSoRuleDetailCreateDTO.setImeiRuleList(getImeiRuleMock());
        mockSoRuleDetailCreateDTO.setPhotoRuleList(getPhotoRuleMock());
        mockSoRuleDetailCreateDTO.setRetailerList(Lists.newArrayList(retailer1));

        // 初始化修改规则DTO
        mockSoRuleDetailModifyDTO = new SoRuleDetailModifyDTO();
        mockSoRuleDetailModifyDTO.setCountryCode("CN");
        mockSoRuleDetailModifyDTO.setType(2);
        mockSoRuleDetailModifyDTO.setRuleId(1L);
        mockSoRuleDetailModifyDTO.setAllAddImei(1);
        mockSoRuleDetailModifyDTO.setAllAddQty(1);
        mockSoRuleDetailModifyDTO.setEffectiveTime(System.currentTimeMillis());
        mockSoRuleDetailModifyDTO.setDefaultRetailersSwitch(Arrays.asList(1, 2));
        mockSoRuleDetailModifyDTO.setImeiRuleList(getImeiRuleMock());
        mockSoRuleDetailModifyDTO.setPhotoRuleList(getPhotoRuleMock());
        mockSoRuleDetailModifyDTO.setRetailerList(Lists.newArrayList(retailer1));
        mockSoRuleDetailModifyDTO.setApproverList(getApproverListMock());

        // 初始化审批回调DTO
        mockSoRuleApproveCallbackDTO = new SoRuleApproveCallbackDTO();
        BpmUser bpmUser = new BpmUser();
        bpmUser.setPersonId("123456");
        bpmUser.setUserName("approver");
        mockSoRuleApproveCallbackDTO.setAssignee(bpmUser);

        // Mock DistributionLock
        distributionLock = mock(DistributionLock.class);
        lenient().doNothing().when(distributionLock).close();
    }

    @Test
    @DisplayName("创建规则成功")
    void testCreateRule_Success() {

        IntlSoRuleDetail savedRuleDetail = new IntlSoRuleDetail();
        savedRuleDetail.setRegionCode("CN");
        savedRuleDetail.setStatus(SoRuleDetailApproveStatus.CREATE.getValue());

        IntlSoRuleRetailer dbRetailer1 = new IntlSoRuleRetailer();
        dbRetailer1.setRetailerCode("retailer1");
        dbRetailer1.setRetailerName("retailer1");
        dbRetailer1.setImeiSwitch(SwitchEnum.ON.getValue());
        dbRetailer1.setQtySwitch(SwitchEnum.OFF.getValue());
        List<IntlSoRuleRetailer> dbRetailers = Lists.newArrayList(dbRetailer1);

        when(countryTimeZoneApiService.getCountryInfoFromCache("CN")).thenReturn(Optional.of(mockCountryDTO));
        doNothing().when(intlSoRuleRetailerService).updateImeiAndQtySwitch(anyString(), anyInt(), any(SwitchEnum.class),
            any(SwitchEnum.class), anyString());
        when(intlSoRuleRetailerService.getRetailerCategoryByCreateType(1)).thenReturn(SoRuleEnum.MASTER);
        when(intlSoRuleRetailerService.getRetailerStatistics("CN", SoRuleEnum.MASTER.getValue(), null))
            .thenReturn(createMockRetailerStatistics());
        when(intlSoRuleDetailService.save(any(IntlSoRuleDetail.class))).thenAnswer(invocation -> {
            IntlSoRuleDetail intlSoRuleDetail = invocation.getArgument(0, IntlSoRuleDetail.class);
            intlSoRuleDetail.setId(1L);
            return true;
        });
        when(
            intlSoRuleRetailerService
                .getByCountryCodeAndRetailerCodes(mockSoRuleDetailCreateDTO.getCountryCode(),
                    SoRuleEnum.MASTER.getValue(), mockSoRuleDetailCreateDTO.getRetailerList().stream()
                        .map(SoRuleRetailerItemDTO::getRetailerCode).collect(Collectors.toList())))
            .thenReturn(dbRetailers);

        // When
        try (MockedStatic<UserInfoUtil> userInfoUtilMock = mockStatic(UserInfoUtil.class);
            MockedStatic<ComponentLocator> componentLocatorMock = mockStatic(ComponentLocator.class);
            MockedStatic<AESGCMUtil> aesGCMUtilMock = mockStatic(AESGCMUtil.class)) {
            userInfoUtilMock.when(UserInfoUtil::getUserContext).thenReturn(mockUserInfo);
            componentLocatorMock.when(ComponentLocator::getConverter).thenReturn(new ConverterFacadeImpl());
            aesGCMUtilMock.when(() -> AESGCMUtil.encryptGCM(anyString()))
                .thenAnswer(invocation -> invocation.getArgument(0));
            // Then
            Long result = soRuleTransactionalAggregateService.createRule(mockSoRuleDetailCreateDTO);
            assertEquals(1L, result);
            verify(intlSoRuleDetailService).save(any(IntlSoRuleDetail.class));
            verify(intlSoRuleRetailerService).updateRuleId("CN", SoRuleEnum.MASTER.getValue(), result, null, "123456");
        }
    }

    @Test
    @DisplayName("修改规则成功")
    void testModifyRule_Success() {
        SoRuleDetailModifyDTO modifyDTO = new SoRuleDetailModifyDTO();
        modifyDTO.setRuleId(1L);
        modifyDTO.setCountryCode("CN");
        modifyDTO.setType(2);
        modifyDTO.setAllAddImei(1);
        modifyDTO.setAllAddQty(1);
        modifyDTO.setEffectiveTime(System.currentTimeMillis());
        modifyDTO.setDefaultRetailersSwitch(Arrays.asList(1, 2));
        modifyDTO.setImeiRuleList(getImeiRuleMock());
        modifyDTO.setPhotoRuleList(getPhotoRuleMock());
        modifyDTO.setRetailerList(Lists.newArrayList());

        when(intlSoRuleChangeLogService.getDraftModifyRule("CN")).thenReturn(null);
        when(intlSoRuleDetailService.getById(1L)).thenReturn(mockIntlSoRuleDetail);
        when(countryTimeZoneApiService.getCountryInfoFromCache("CN")).thenReturn(Optional.of(mockCountryDTO));
        when(intlSoRuleRetailerService.getRetailerCategoryByCreateType(2)).thenReturn(SoRuleEnum.COPY);
        when(intlSoRuleRetailerService.getRetailerStatistics(anyString(), anyInt(), anyString()))
            .thenReturn(createMockRetailerStatistics());
        when(intlSoRuleChangeLogService.save(any(IntlSoRuleDetailLog.class))).thenAnswer(invocation -> {
            IntlSoRuleDetailLog intlSoRuleDetailLog = invocation.getArgument(0, IntlSoRuleDetailLog.class);
            intlSoRuleDetailLog.setId(1L);
            return true;
        });

        try (MockedStatic<UserInfoUtil> userInfoUtilMock = mockStatic(UserInfoUtil.class);
            MockedStatic<ComponentLocator> componentLocatorMock = mockStatic(ComponentLocator.class);
            MockedStatic<AESGCMUtil> aesGCMUtilMock = mockStatic(AESGCMUtil.class)) {
            userInfoUtilMock.when(UserInfoUtil::getUserContext).thenReturn(mockUserInfo);
            componentLocatorMock.when(ComponentLocator::getConverter).thenReturn(new ConverterFacadeImpl());
            aesGCMUtilMock.when(() -> AESGCMUtil.encryptGCM(anyString()))
                .thenAnswer(invocation -> invocation.getArgument(0));

            Long result = soRuleTransactionalAggregateService.modifyRule(mockSoRuleDetailModifyDTO);

            assertNotNull(result);
            verify(intlSoRuleChangeLogService).save(any(IntlSoRuleDetailLog.class));
            verify(intlSoRuleRetailerService).updateRuleId("CN", SoRuleEnum.COPY.getValue(), result, "123456",
                "123456");
        }
    }

    @Test
    @DisplayName("修改规则时存在草稿状态记录")
    void testModifyRule_WithDraftExists() {
        when(intlSoRuleChangeLogService.getDraftModifyRule("CN")).thenReturn(mockIntlSoRuleDetailLog);
        Long result = soRuleTransactionalAggregateService.modifyRule(mockSoRuleDetailModifyDTO);
        assertEquals(mockIntlSoRuleDetailLog.getId(), result);
        verify(intlSoRuleChangeLogService, never()).save(any(IntlSoRuleDetailLog.class));
    }

    @Test
    @DisplayName("获取主规则成功-通过规则ID")
    void testGetMasterRule_ByRuleId_Success() {
        when(intlSoRuleDetailService.getById(1L)).thenReturn(mockIntlSoRuleDetail);
        IntlSoRuleDetail result = soRuleTransactionalAggregateService.getMasterRule(1L, "CN");
        assertNotNull(result);
        assertEquals(1L, result.getId());
        verify(intlSoRuleDetailService).getById(1L);
        verify(intlSoRuleDetailService, never()).getByCountryCode(anyString());
    }

    @Test
    @DisplayName("获取主规则成功-通过国家编码")
    void testGetMasterRule_ByCountryCode_Success() {
        when(intlSoRuleDetailService.getByCountryCode("CN")).thenReturn(Optional.of(mockIntlSoRuleDetail));
        IntlSoRuleDetail result = soRuleTransactionalAggregateService.getMasterRule(null, "CN");
        assertNotNull(result);
        assertEquals(1L, result.getId());
        verify(intlSoRuleDetailService, never()).getById(anyLong());
        verify(intlSoRuleDetailService).getByCountryCode("CN");
    }

    @Test
    @DisplayName("获取主规则失败-规则不存在")
    void testGetMasterRule_NotExists() {
        when(intlSoRuleDetailService.getById(1L)).thenReturn(null);
        BizException exception =
            assertThrows(BizException.class, () -> soRuleTransactionalAggregateService.getMasterRule(1L, "CN"));
        assertEquals(ErrorCodes.SO_RULE_NOT_EXIST, exception.getErrorCode());
    }

    @Test
    @DisplayName("发起审批流程成功")
    void testApproveRuleLaunch_Success() {
        // Given
        when(intlSoRuleDetailService.getById(1L)).thenReturn(mockIntlSoRuleDetail);
        when(intlSoRuleChangeLogService.updateById(any(IntlSoRuleDetailLog.class))).thenAnswer(invocation -> {
            IntlSoRuleDetailLog intlSoRuleDetailLog = invocation.getArgument(0);
            assertEquals(intlSoRuleDetailLog.getStatus(), SoRuleDetailApproveStatus.PENDING.getValue());
            return true;
        });
        when(intlSoRuleDetailService.updateById(any(IntlSoRuleDetail.class))).thenAnswer(invocation -> {
            IntlSoRuleDetail intlSoRuleDetail = invocation.getArgument(0);
            assertEquals(intlSoRuleDetail.getStatus(), SoRuleDetailApproveStatus.PENDING.getValue());
            return true;
        });

        // When
        soRuleTransactionalAggregateService.approveRuleLaunch(mockIntlSoRuleDetailLog, "123456");

        // Then
        verify(intlSoRuleChangeLogService).updateById(any(IntlSoRuleDetailLog.class));
        verify(intlSoRuleDetailService).updateById(any(IntlSoRuleDetail.class));
    }

    @Test
    @DisplayName("审批完成回调成功")
    void testApproveRuleCompleted_Success() {
        // Given
        List<IntlSoRuleRetailer> ruleRetailerCopyList = createMockRuleRetailerCopyList();
        List<IntlSoRuleRetailer> ruleRetailerList = createMockRuleRetailerList();
        SoRuleRetailerStatisticsDTO retailerStatistics = createMockRetailerStatistics();

        when(intlSoRuleRetailerService.getByCountryCodeAndCategoryWithRuleId(anyLong(), anyString(), anyInt()))
            .thenReturn(ruleRetailerCopyList);
        when(intlSoRuleRetailerService.getByCountryCodeAndRetailerCodes(anyString(), anyInt(), anyList()))
            .thenReturn(ruleRetailerList);
        when(intlSoRuleRetailerService.getRetailerStatistics("CN", SoRuleEnum.MASTER.getValue(), null))
            .thenReturn(retailerStatistics);
        when(intlSoRuleChangeLogService.updateById(any(IntlSoRuleDetailLog.class))).thenAnswer(invocationOnMock -> {
            IntlSoRuleDetailLog intlSoRuleDetailLog = invocationOnMock.getArgument(0);
            assertEquals(intlSoRuleDetailLog.getStatus(), SoRuleDetailApproveStatus.APPROVED.getValue());
            return true;
        });

        when(intlSoRuleRetailerService.updateBatchById(anyList())).thenAnswer(invocation -> {
            List<IntlSoRuleRetailer> ruleRetailerList1 = invocation.getArgument(0);
            ruleRetailerList1.forEach(ruleRetailer -> {
                // 校验imei、qty是否更新成功
                assertEquals(ruleRetailer.getImeiSwitch(), SwitchEnum.ON.getValue());
                assertEquals(ruleRetailer.getQtySwitch(), SwitchEnum.ON.getValue());
            });
            return true;
        });
        when(intlSoRuleDetailService.updateById(any(IntlSoRuleDetail.class))).thenAnswer(invocation -> {
            IntlSoRuleDetail intlSoRuleDetail = invocation.getArgument(0);
            assertEquals(intlSoRuleDetail.getStatus(), SoRuleDetailApproveStatus.APPROVED.getValue());
            assertEquals(intlSoRuleDetail.getNoRuleRetailersCount(), 0);
            assertEquals(intlSoRuleDetail.getImeiRetailersCount(), 1);
            assertEquals(intlSoRuleDetail.getQtyRetailersCount(), 1);
            assertEquals(intlSoRuleDetail.getImeiRetailersCount(), 1);
            return true;
        });

        // When
        try (MockedStatic<UserInfoUtil> userInfoUtilMock = mockStatic(UserInfoUtil.class);
            MockedStatic<AESGCMUtil> aesGCMUtilMock = mockStatic(AESGCMUtil.class)) {
            userInfoUtilMock.when(UserInfoUtil::getUserContext).thenReturn(mockUserInfo);
            aesGCMUtilMock.when(() -> AESGCMUtil.encryptGCM(anyString()))
                .thenAnswer(invocation -> invocation.getArgument(0));

            soRuleTransactionalAggregateService.approveRuleCompleted(mockSoRuleApproveCallbackDTO,
                mockIntlSoRuleDetailLog, mockIntlSoRuleDetail);
        }

        // Then
        verify(intlSoRuleChangeLogService).updateById(any(IntlSoRuleDetailLog.class));
        verify(intlSoRuleRetailerService).updateBatchById(anyList());
        verify(intlSoRuleDetailService).updateById(any(IntlSoRuleDetail.class));
    }

    @Test
    @DisplayName("审批拒绝回调成功")
    void testApproveRuleRejected_Success() {
        // Given
        when(intlSoRuleChangeLogService.updateById(any(IntlSoRuleDetailLog.class))).thenAnswer(invocation -> {
            IntlSoRuleDetailLog intlSoRuleDetailLog = invocation.getArgument(0);
            assertEquals(intlSoRuleDetailLog.getStatus(), SoRuleDetailApproveStatus.REJECTED.getValue());
            return true;
        });
        when(intlSoRuleDetailService.updateById(any(IntlSoRuleDetail.class))).thenAnswer(invocation -> {
            // 回退主规则状态为审批通过
            IntlSoRuleDetail intlSoRuleDetail = invocation.getArgument(0);
            assertEquals(intlSoRuleDetail.getStatus(), SoRuleDetailApproveStatus.APPROVED.getValue());
            return true;
        });

        // When
        soRuleTransactionalAggregateService.approveRuleRejected(mockSoRuleApproveCallbackDTO, mockIntlSoRuleDetailLog,
            mockIntlSoRuleDetail);

        // Then
        verify(intlSoRuleChangeLogService).updateById(any(IntlSoRuleDetailLog.class));
        verify(intlSoRuleDetailService).updateById(any(IntlSoRuleDetail.class));
    }

    @Test
    @DisplayName("审批撤回回调成功")
    void testApproveRuleRecalled_Success() {
        // Given
        when(intlSoRuleChangeLogService.updateById(any(IntlSoRuleDetailLog.class))).thenAnswer(invocation -> {
            IntlSoRuleDetailLog intlSoRuleDetailLog = invocation.getArgument(0);
            assertEquals(intlSoRuleDetailLog.getStatus(), SoRuleDetailApproveStatus.RECALLED.getValue());
            return true;
        });
        when(intlSoRuleDetailService.updateById(any(IntlSoRuleDetail.class))).thenAnswer(invocation -> {
            // 回退主规则状态为审批通过
            IntlSoRuleDetail intlSoRuleDetail = invocation.getArgument(0);
            assertEquals(intlSoRuleDetail.getStatus(), SoRuleDetailApproveStatus.APPROVED.getValue());
            return true;
        });

        // When
        soRuleTransactionalAggregateService.approveRuleRecalled(mockSoRuleApproveCallbackDTO, mockIntlSoRuleDetailLog,
            mockIntlSoRuleDetail);

        // Then
        verify(intlSoRuleChangeLogService).updateById(any(IntlSoRuleDetailLog.class));
        verify(intlSoRuleDetailService).updateById(any(IntlSoRuleDetail.class));
    }

    @Test
    @DisplayName("中间节点审批通过成功")
    void testApproveRuleAgree_Success() {
        // Given
        when(intlSoRuleChangeLogService.updateById(any(IntlSoRuleDetailLog.class))).thenAnswer(invocation -> {
            IntlSoRuleDetailLog intlSoRuleDetailLog = invocation.getArgument(0);
            assertEquals(intlSoRuleDetailLog.getStatus(), SoRuleDetailApproveStatus.PENDING.getValue());
            return true;
        });
        when(intlSoRuleDetailService.updateById(any(IntlSoRuleDetail.class))).thenAnswer(invocation -> {
            IntlSoRuleDetail intlSoRuleDetail = invocation.getArgument(0);
            assertEquals(intlSoRuleDetail.getStatus(), SoRuleDetailApproveStatus.PENDING.getValue());
            return true;
        });

        // When
        soRuleTransactionalAggregateService.approveRuleAgree(mockSoRuleApproveCallbackDTO, mockIntlSoRuleDetailLog,
            mockIntlSoRuleDetail);

        // Then
        verify(intlSoRuleChangeLogService).updateById(any(IntlSoRuleDetailLog.class));
        verify(intlSoRuleDetailService).updateById(any(IntlSoRuleDetail.class));
    }

    @Test
    @DisplayName("创建规则场景处理零售商规则-全量开关")
    void testDoCreateRuleWithRetailers_AllSwitch() {
        // Given
        mockSoRuleDetailCreateDTO.setAllAddImei(1);
        mockSoRuleDetailCreateDTO.setAllAddQty(1);
        doNothing().when(intlSoRuleRetailerService).updateImeiAndQtySwitch(anyString(), anyInt(), any(SwitchEnum.class),
            any(SwitchEnum.class), anyString());

        // When
        soRuleTransactionalAggregateService.doCreateRuleWithRetailers(mockSoRuleDetailCreateDTO, mockUserInfo);

        // Then
        verify(intlSoRuleRetailerService).updateImeiAndQtySwitch("CN", SoRuleEnum.MASTER.getValue(), SwitchEnum.ON,
            SwitchEnum.ON, "123456");
        verify(intlSoRuleRetailerService).updateBatchById(anyList());
    }

    @Test
    @DisplayName("创建规则场景处理零售商规则-单个零售商")
    void testDoCreateRuleWithRetailers_IndividualRetailers() {
        // Given
        List<SoRuleRetailerItemDTO> retailerList = createMockRetailerList();
        mockSoRuleDetailCreateDTO.setRetailerList(retailerList);
        List<IntlSoRuleRetailer> dbRetailerList = createMockDbRetailerList();
        when(intlSoRuleRetailerService.getByCountryCodeAndRetailerCodes(anyString(), anyInt(), anyList()))
            .thenReturn(dbRetailerList);
        when(intlSoRuleRetailerService.updateBatchById(anyList())).thenReturn(true);

        // When
        soRuleTransactionalAggregateService.doCreateRuleWithRetailers(mockSoRuleDetailCreateDTO, mockUserInfo);

        // Then
        verify(intlSoRuleRetailerService).updateBatchById(anyList());
    }

    @Test
    @DisplayName("修改规则场景处理零售商规则")
    void testDoModifyRuleWithRetailers_Success() {
        // Given
        List<SoRuleRetailerItemDTO> retailerList = createMockRetailerList();
        mockSoRuleDetailModifyDTO.getRetailerList().addAll(retailerList);
        List<IntlSoRuleRetailer> dbRetailerList = createMockDbRetailerList();
        when(intlSoRuleRetailerService.getCurrentInitRetailers(anyString(), anyList())).thenReturn(dbRetailerList);
        when(intlSoRuleRetailerService.updateBatchById(anyList())).thenReturn(true);
        when(intlSoRuleRetailerService.saveBatch(anyList())).thenReturn(true);
        when(distributionLockService.tryLock(anyString(), anyString())).thenReturn(distributionLock);

        // When
        soRuleTransactionalAggregateService.doModifyRuleWithRetailers(mockSoRuleDetailModifyDTO, mockUserInfo);

        // Then
        verify(intlSoRuleRetailerService).updateBatchById(anyList());
        verify(intlSoRuleRetailerService).saveBatch(anyList());
    }

    @Test
    @DisplayName("获取IMEI和QTY开关值-全量开启")
    void testGetImeiAndQtySwitch_AllOn() {
        Tuple result = soRuleTransactionalAggregateService.getImeiAndQtySwitch(1, 1);
        assertEquals(SwitchEnum.ON, result.get(0));
        assertEquals(SwitchEnum.ON, result.get(1));
    }

    @Test
    @DisplayName("获取IMEI和QTY开关值-全量关闭")
    void testGetImeiAndQtySwitch_AllOff() {
        Tuple result = soRuleTransactionalAggregateService.getImeiAndQtySwitch(2, 2);
        assertEquals(SwitchEnum.OFF, result.get(0));
        assertEquals(SwitchEnum.OFF, result.get(1));
    }

    @Test
    @DisplayName("获取IMEI和QTY开关值-部分开关")
    void testGetImeiAndQtySwitch_Partial() {
        Tuple result = soRuleTransactionalAggregateService.getImeiAndQtySwitch(1, null);
        assertEquals(SwitchEnum.ON, result.get(0));
        assertNull(result.get(1));
    }

    // 私有辅助方法
    private SoRuleRetailerStatisticsDTO createMockRetailerStatistics() {
        SoRuleRetailerStatisticsDTO statistics = new SoRuleRetailerStatisticsDTO();
        statistics.setTotalRetailersCount(1);
        statistics.setNoRuleRetailersCount(0);
        statistics.setImeiRetailersCount(1);
        statistics.setQtyRetailersCount(1);
        return statistics;
    }

    private List<IntlSoRuleRetailer> createMockRuleRetailerCopyList() {
        IntlSoRuleRetailer retailer = new IntlSoRuleRetailer();
        retailer.setRetailerCode("RETAILER001");
        retailer.setImeiSwitch(SwitchEnum.ON.getValue());
        retailer.setQtySwitch(SwitchEnum.ON.getValue());
        retailer.setCategory(SoRuleEnum.COPY.getValue());
        return Lists.newArrayList(retailer);
    }

    private List<IntlSoRuleRetailer> createMockRuleRetailerList() {
        IntlSoRuleRetailer retailer = new IntlSoRuleRetailer();
        retailer.setRetailerCode("RETAILER001");
        retailer.setImeiSwitch(SwitchEnum.OFF.getValue());
        retailer.setQtySwitch(SwitchEnum.OFF.getValue());
        retailer.setCategory(SoRuleEnum.MASTER.getValue());
        return Lists.newArrayList(retailer);
    }

    private List<SoRuleRetailerItemDTO> createMockRetailerList() {
        SoRuleRetailerItemDTO retailer = new SoRuleRetailerItemDTO();
        retailer.setRetailerCode("RETAILER001");
        retailer.setRetailerName("Test Retailer");
        retailer.setImeiSwitch(SwitchEnum.ON.getValue());
        retailer.setQtySwitch(SwitchEnum.OFF.getValue());
        return Lists.newArrayList(retailer);
    }

    private List<IntlSoRuleRetailer> createMockDbRetailerList() {
        IntlSoRuleRetailer retailer = new IntlSoRuleRetailer();
        retailer.setRetailerCode("RETAILER001");
        retailer.setImeiSwitch(SwitchEnum.OFF.getValue());
        retailer.setQtySwitch(SwitchEnum.OFF.getValue());
        return Lists.newArrayList(retailer);
    }

}