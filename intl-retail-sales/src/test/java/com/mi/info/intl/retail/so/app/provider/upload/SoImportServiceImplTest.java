package com.mi.info.intl.retail.so.app.provider.upload;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.GetImportLogListRequest;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.GetImportLogListResponse;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlImportLog;
import com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlImportLogMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * SoImportServiceImpl单元测试
 *
 * <AUTHOR> Assistant
 * @date 2025/09/29
 */
@ExtendWith(MockitoExtension.class)
public class SoImportServiceImplTest {

    @InjectMocks
    private SoImportServiceImpl soImportService;

    @Mock
    private IntlImportLogMapper intlImportLogMapper;

    private GetImportLogListRequest validRequest;
    private IntlImportLog testLog;

    @BeforeEach
    public void setUp() {
        setupValidRequest();
        setupTestLog();
    }

    private void setupValidRequest() {
        validRequest = new GetImportLogListRequest();
        validRequest.setCountryCode("CN");
        validRequest.setUserId("test-user-id");
        validRequest.setMiId(123456789L);
        validRequest.setUserTitle(102L);
        validRequest.setPageNum(1);
        validRequest.setPageSize(20);
    }

    private void setupTestLog() {
        testLog = new IntlImportLog();
        testLog.setId(1L);
        testLog.setTaskName("测试任务");
        testLog.setDataSource(1);
        testLog.setType(1);
        testLog.setFileName("test.xlsx");
        testLog.setStatus(1);
        testLog.setTotalCount(100);
        testLog.setFinishCount(100);
        testLog.setImportDuration(1);
        testLog.setOperator("测试用户");
        testLog.setCreatedAt(System.currentTimeMillis());
        testLog.setActionType(1);
        testLog.setSourceFileUrl("http://test.com/source.xlsx");
        testLog.setResultFileUrl("http://test.com/result.xlsx");
    }

    @Test
    @DisplayName("查询导入日志列表 - 成功")
    public void getImportLogList_Success() {
        // 准备数据
        Page<IntlImportLog> mockPage = new Page<>(1, 20);
        mockPage.setTotal(1);
        mockPage.setRecords(Arrays.asList(testLog));

        // mock
        when(intlImportLogMapper.selectImportLogList(any(Page.class), any(), any(), any(), any(), any(), any(), any(), any()))
                .thenReturn(mockPage);

        // 执行测试
        CommonApiResponse<GetImportLogListResponse> result = soImportService.getImportLogList(validRequest);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getCode());
        assertNotNull(result.getData());
        assertEquals(1, result.getData().getTotal());
        assertEquals(1, result.getData().getCurrentPage());
        assertEquals(20, result.getData().getPageSize());
        assertEquals(1, result.getData().getRecords().size());

        GetImportLogListResponse.ImportLogRecord record = result.getData().getRecords().get(0);
        assertEquals("测试任务", record.getTaskName());
        assertEquals("1", record.getDataSource());
        assertEquals(Integer.valueOf(1), record.getImportType());
        assertEquals("test.xlsx", record.getFileName());
        assertEquals(Integer.valueOf(1), record.getStatus());
        assertEquals(Integer.valueOf(100), record.getImportProgress());

        // 验证mock调用
        verify(intlImportLogMapper).selectImportLogList(any(Page.class), any(), any(), any(), any(), any(), any(), any(), any());
    }

    @Test
    @DisplayName("查询导入日志列表 - 请求参数为空")
    public void getImportLogList_NullRequest() {
        // 执行测试
        CommonApiResponse<GetImportLogListResponse> result = soImportService.getImportLogList(null);

        // 验证结果
        assertNotNull(result);
        assertEquals(400, result.getCode());
        assertEquals("请求参数不能为空", result.getMessage());
        assertNull(result.getData());

        // 验证没有调用mapper
        verify(intlImportLogMapper, never()).selectImportLogList(any(), any(), any(), any(), any(), any(), any(), any(), any());
    }

    @Test
    @DisplayName("查询导入日志列表 - 国家代码为空")
    public void getImportLogList_EmptyCountryCode() {
        // 准备数据
        validRequest.setCountryCode("");

        // 执行测试
        CommonApiResponse<GetImportLogListResponse> result = soImportService.getImportLogList(validRequest);

        // 验证结果
        assertNotNull(result);
        assertEquals(400, result.getCode());
        assertEquals("国家代码不能为空", result.getMessage());
        assertNull(result.getData());

        // 验证没有调用mapper
        verify(intlImportLogMapper, never()).selectImportLogList(any(), any(), any(), any(), any(), any(), any(), any(), any());
    }

    @Test
    @DisplayName("查询导入日志列表 - 用户ID为空")
    public void getImportLogList_EmptyUserId() {
        // 准备数据
        validRequest.setUserId(null);

        // 执行测试
        CommonApiResponse<GetImportLogListResponse> result = soImportService.getImportLogList(validRequest);

        // 验证结果
        assertNotNull(result);
        assertEquals(400, result.getCode());
        assertEquals("用户ID不能为空", result.getMessage());
        assertNull(result.getData());

        // 验证没有调用mapper
        verify(intlImportLogMapper, never()).selectImportLogList(any(), any(), any(), any(), any(), any(), any(), any(), any());
    }

    @Test
    @DisplayName("查询导入日志列表 - miId为空")
    public void getImportLogList_NullMiId() {
        // 准备数据
        validRequest.setMiId(null);

        // 执行测试
        CommonApiResponse<GetImportLogListResponse> result = soImportService.getImportLogList(validRequest);

        // 验证结果
        assertNotNull(result);
        assertEquals(400, result.getCode());
        assertEquals("用户miId不能为空", result.getMessage());
        assertNull(result.getData());

        // 验证没有调用mapper
        verify(intlImportLogMapper, never()).selectImportLogList(any(), any(), any(), any(), any(), any(), any(), any(), any());
    }

    @Test
    @DisplayName("查询导入日志列表 - 用户职位为空")
    public void getImportLogList_NullUserTitle() {
        // 准备数据
        validRequest.setUserTitle(null);

        // 执行测试
        CommonApiResponse<GetImportLogListResponse> result = soImportService.getImportLogList(validRequest);

        // 验证结果
        assertNotNull(result);
        assertEquals(400, result.getCode());
        assertEquals("用户职位不能为空", result.getMessage());
        assertNull(result.getData());

        // 验证没有调用mapper
        verify(intlImportLogMapper, never()).selectImportLogList(any(), any(), any(), any(), any(), any(), any(), any(), any());
    }

    @Test
    @DisplayName("查询导入日志列表 - 处理中状态进度计算")
    public void getImportLogList_ProcessingStatus() {
        // 准备数据 - 处理中状态
        testLog.setStatus(0);
        testLog.setTotalCount(100);
        testLog.setFinishCount(50);

        Page<IntlImportLog> mockPage = new Page<>(1, 20);
        mockPage.setTotal(1);
        mockPage.setRecords(Arrays.asList(testLog));

        // mock
        when(intlImportLogMapper.selectImportLogList(any(Page.class), any(), any(), any(), any(), any(), any(), any(), any()))
                .thenReturn(mockPage);

        // 执行测试
        CommonApiResponse<GetImportLogListResponse> result = soImportService.getImportLogList(validRequest);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getCode());
        GetImportLogListResponse.ImportLogRecord record = result.getData().getRecords().get(0);
        assertEquals(Integer.valueOf(50), record.getImportProgress()); // 50/100 = 50%
        assertEquals(testLog.getSourceFileUrl(), record.getFileUrl());
    }

    @Test
    @DisplayName("查询导入日志列表 - 失败状态文件链接")
    public void getImportLogList_FailedStatus() {
        // 准备数据 - 失败状态
        testLog.setStatus(2);

        Page<IntlImportLog> mockPage = new Page<>(1, 20);
        mockPage.setTotal(1);
        mockPage.setRecords(Arrays.asList(testLog));

        // mock
        when(intlImportLogMapper.selectImportLogList(any(Page.class), any(), any(), any(), any(), any(), any(), any(), any()))
                .thenReturn(mockPage);

        // 执行测试
        CommonApiResponse<GetImportLogListResponse> result = soImportService.getImportLogList(validRequest);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getCode());
        GetImportLogListResponse.ImportLogRecord record = result.getData().getRecords().get(0);
        assertEquals(Integer.valueOf(100), record.getImportProgress());
        assertEquals(testLog.getResultFileUrl(), record.getFileUrl()); // 失败时使用结果文件链接
    }

    @Test
    @DisplayName("查询导入日志列表 - 带过滤条件")
    public void getImportLogList_WithFilters() {
        // 准备数据
        validRequest.setTaskName("销售数据");
        validRequest.setDataSource(Arrays.asList("SalesApp"));
        validRequest.setStatus(Arrays.asList(1));
        validRequest.setImportDuration(Arrays.asList(0, 1));
        validRequest.setImportTypes(Arrays.asList(1));
        validRequest.setOperationStartTime(1704067200000L);
        validRequest.setOperationEndTime(1706745599000L);

        Page<IntlImportLog> mockPage = new Page<>(1, 20);
        mockPage.setTotal(0);
        mockPage.setRecords(new ArrayList<>());

        // mock
        when(intlImportLogMapper.selectImportLogList(any(Page.class), eq("销售数据"), eq(Arrays.asList("SalesApp")), 
                any(), eq(Arrays.asList(1)), eq(Arrays.asList(0, 1)), eq(1704067200000L), eq(1706745599000L), eq(Arrays.asList(1))))
                .thenReturn(mockPage);

        // 执行测试
        CommonApiResponse<GetImportLogListResponse> result = soImportService.getImportLogList(validRequest);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getCode());
        assertEquals(0, result.getData().getTotal());

        // 验证mock调用
        verify(intlImportLogMapper).selectImportLogList(any(Page.class), eq("销售数据"), eq(Arrays.asList("SalesApp")), 
                any(), eq(Arrays.asList(1)), eq(Arrays.asList(0, 1)), eq(1704067200000L), eq(1706745599000L), eq(Arrays.asList(1)));
    }

    @Test
    @DisplayName("查询导入日志列表 - 异常处理")
    public void getImportLogList_Exception() {
        // mock异常
        when(intlImportLogMapper.selectImportLogList(any(Page.class), any(), any(), any(), any(), any(), any(), any(), any()))
                .thenThrow(new RuntimeException("数据库异常"));

        // 执行测试
        CommonApiResponse<GetImportLogListResponse> result = soImportService.getImportLogList(validRequest);

        // 验证结果
        assertNotNull(result);
        assertEquals(500, result.getCode());
        assertTrue(result.getMessage().contains("查询失败"));
        assertNull(result.getData());
    }
}
