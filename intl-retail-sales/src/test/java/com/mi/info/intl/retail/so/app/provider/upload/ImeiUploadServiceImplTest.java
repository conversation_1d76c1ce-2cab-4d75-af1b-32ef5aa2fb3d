package com.mi.info.intl.retail.so.app.provider.upload;

import com.google.zxing.Result;
import com.mi.info.intl.retail.api.fieldforce.user.UserApiService;
import com.mi.info.intl.retail.api.fieldforce.user.dto.UserInfoDTO;
import com.mi.info.intl.retail.api.file.FileUploadApiService;
import com.mi.info.intl.retail.api.front.position.IntlPositionApiService;
import com.mi.info.intl.retail.api.front.position.dto.PositionStoreInfoDTO;
import com.mi.info.intl.retail.api.front.store.RmsStoreService;
import com.mi.info.intl.retail.api.front.store.StoreChangeLogApiService;
import com.mi.info.intl.retail.api.product.ProductApiService;
import com.mi.info.intl.retail.api.product.dto.ProductInfoDTO;
import com.mi.info.intl.retail.api.task.TaskActionService;
import com.mi.info.intl.retail.enums.SerialNumberType;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.*;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.so.app.mq.RetailSyncToRmsProducer;
import com.mi.info.intl.retail.so.app.provider.enums.ReportingTypeEnum;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlRmsRrp;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoImei;
import com.mi.info.intl.retail.so.domain.upload.service.IntlRmsRrpService;
import com.mi.info.intl.retail.so.domain.upload.service.IntlSoImeiService;
import com.mi.info.intl.retail.so.domain.upload.service.IntlSoOrgInfoService;
import com.mi.info.intl.retail.so.domain.upload.service.IntlSoUserInfoService;
import com.mi.info.intl.retail.so.infra.database.dataobject.rule.IntlSoRuleDetail;
import com.mi.info.intl.retail.so.infra.database.mapper.rule.IntlSoRuleDetailMapper;
import com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlRmsRrpMapper;
import com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlSoImeiMapper;
import com.mi.info.intl.retail.utils.BarcodeScannerUtil;
import com.mi.info.intl.retail.utils.SnImeiValidateUtil;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Field;
import java.math.BigInteger;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * ImeiUploadServiceImpl 单元测试类
 *
 * <AUTHOR>
 * @date 2025/7/28
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("IMEI上传服务测试")
class ImeiUploadServiceImplTest {

    @Mock
    private IntlSoRuleDetailMapper intlSoRuleDetailMapper;

    @Mock
    private IntlSoOrgInfoService intlSoOrgInfoService;

    @Mock
    private IntlSoUserInfoService intlSoUserInfoService;

    @Mock
    private StoreChangeLogApiService storeChangeLogApiService;

    @Mock
    private IntlSoImeiService intlSoImeiService;

    @Mock
    private ProductApiService productApiService;

    @Mock
    private FileUploadApiService fileUploadApiService;

    @Mock
    private IntlPositionApiService intlPositionApiService;

    @Mock
    private IntlRmsRrpMapper intlRmsRrpMapper;

    @Mock
    private UserApiService userApiService;

    @Mock
    private RmsStoreService rmsStoreService;

    @Mock
    private IntlSoImeiMapper intlSoImeiMapper;

    @Mock
    private RocketMQTemplate rocketMQTemplate;

    @Mock
    private IntlRmsRrpService intlRmsRrpService;

    @Mock
    private ImeiReportVerifyServiceImpl imeiReportVerifyServiceImpl;

    @Mock
    private TaskActionService taskActionService;

    @Mock
    private RetailSyncToRmsProducer retailSyncToRmsProducer;

    @InjectMocks
    private ImeiUploadServiceImpl imeiUploadService;

    @BeforeEach
    void setUp() {
        // 初始化测试环境
        reset(intlSoRuleDetailMapper, intlSoOrgInfoService, intlSoUserInfoService,
                storeChangeLogApiService, intlSoImeiService, productApiService,
                fileUploadApiService, intlPositionApiService, intlRmsRrpMapper,
                userApiService, rmsStoreService, intlSoImeiMapper, rocketMQTemplate,
                intlRmsRrpService, imeiReportVerifyServiceImpl, taskActionService,
                retailSyncToRmsProducer);
    }

    @Test
    @DisplayName("submitImei 成功提交IMEI数据")
    void submitImei_success() {
        // 准备测试数据
        SubmitImeiReq request = createValidSubmitImeiReq();
        IntlSoRuleDetail ruleDetail = createMockRuleDetail();
        PositionStoreInfoDTO positionStoreInfo = createMockPositionStoreInfo();
        UserInfoDTO userInfo = createMockUserInfo();
        ProductInfoDTO productInfo = createMockProductInfo();
        IntlRmsRrp rrp = createMockRrp();

        // Mock 依赖服务
        when(intlSoRuleDetailMapper.selectById(1L)).thenReturn(ruleDetail);
        when(intlPositionApiService.queryPositionWithStoreByCode("POS001"))
                .thenReturn(Optional.of(positionStoreInfo));
        when(userApiService.queryUserByMiId(12345L))
                .thenReturn(Optional.of(userInfo));
        when(productApiService.queryProductsByGoodIds(anyList()))
                .thenReturn(Collections.singletonMap(1001L, productInfo));
        when(intlSoUserInfoService.createUserInfo(any(UserInfoDTO.class), anyLong()))
                .thenReturn(1L);
        when(storeChangeLogApiService.findAllByStoreCode("STORE001"))
                .thenReturn(new ArrayList<>());
        when(intlSoOrgInfoService.createOrgInfoWithStoreLogs(any(), anyString(), anyList(), anyLong()))
                .thenReturn(1L);
        when(intlRmsRrpService.getRrpFromCache(any(), anyString(), anyLong(), anyString()))
                .thenReturn(rrp);
        when(intlSoImeiService.saveBatch(anyList())).thenAnswer(invocation -> {
            List<IntlSoImei> imeiList = invocation.getArgument(0);
            // 模拟保存后设置ID，这样createPhotoData方法就能获取到ID
            for (int i = 0; i < imeiList.size(); i++) {
                imeiList.get(i).setId((long) (i + 1));
            }
            return true;
        });
        when(fileUploadApiService.createPhotoData(anyList())).thenReturn(1);
        doNothing().when(taskActionService).taskCompleted(any());
        doNothing().when(retailSyncToRmsProducer).sendSyncRmsMsg(any());

        // 执行测试
        CommonApiResponse<Object> response = imeiUploadService.submitImei(request);

        // 验证结果
        assertNotNull(response);
        assertEquals(0, response.getCode()); // 成功时code为0
        
        // 验证各个依赖服务被正确调用
        verify(intlSoRuleDetailMapper).selectById(1L);
        verify(intlPositionApiService).queryPositionWithStoreByCode("POS001");
        verify(userApiService).queryUserByMiId(12345L);
        verify(productApiService).queryProductsByGoodIds(anyList());
        verify(intlSoUserInfoService).createUserInfo(any(UserInfoDTO.class), anyLong());
        verify(intlSoImeiService).saveBatch(anyList());
        verify(fileUploadApiService).createPhotoData(anyList());
        verify(taskActionService).taskCompleted(any());
        verify(retailSyncToRmsProducer).sendSyncRmsMsg(any());
    }

    @Test
    @DisplayName("submitImei 请求参数为空")
    void submitImei_nullRequest() {
        // 执行测试
        CommonApiResponse<Object> response = imeiUploadService.submitImei(null);

        // 验证结果
        assertNotNull(response);
        assertEquals(400, response.getCode());
        assertEquals("请求参数不能为空", response.getMessage());
    }

    @Test
    @DisplayName("imeiBarcodeRead 成功识别条形码")
    void imeiBarcodeRead_success() {
        // 准备测试数据
        ImeiBarcodeReadReq request = new ImeiBarcodeReadReq();
        request.setImage("base64encodedimage");
        request.setCountryCode("CN");
        request.setMiId("12345");
        request.setUserTitle(500900001);
        
        // Mock 条形码解析结果
        Result mockResult = mock(Result.class);
        when(mockResult.getText()).thenReturn("123456789012345");
        
        // Mock IMEI上报校验结果
        CommonApiResponse<Object> mockVerifyResponse = new CommonApiResponse<>(null);
        // 成功时不设置code
        
        // 使用MockedStatic模拟BarcodeScannerUtil
        try (MockedStatic<BarcodeScannerUtil> mockedUtil = mockStatic(BarcodeScannerUtil.class);
             MockedStatic<SnImeiValidateUtil> mockedValidator = mockStatic(SnImeiValidateUtil.class)) {
            mockedUtil.when(() -> BarcodeScannerUtil.decodeBarcode(anyString()))
                    .thenReturn(mockResult);
            
            mockedValidator.when(() -> SnImeiValidateUtil.getSerialNumberType("123456789012345"))
                    .thenReturn(SerialNumberType.IMEI);
            
            when(imeiReportVerifyServiceImpl.imeiReportVerify(any()))
                    .thenReturn(mockVerifyResponse);

            // 执行测试
            CommonApiResponse<Object> response = imeiUploadService.imeiBarcodeRead(request);

            // 验证结果
            assertNotNull(response);
            assertEquals(0, response.getCode()); // 成功时code为0
            
            // 验证IMEI校验被调用
            verify(imeiReportVerifyServiceImpl).imeiReportVerify(any(ImeiReportVerifyRequest.class));
        }
    }

    @Test
    @DisplayName("imeiBarcodeRead 图片数据为空")
    void imeiBarcodeRead_emptyImage() {
        // 准备测试数据
        ImeiBarcodeReadReq request = new ImeiBarcodeReadReq();
        request.setImage(""); // 空字符串
        request.setCountryCode("CN");
        request.setMiId("12345");
        request.setUserTitle(500900001);

        // 执行测试
        CommonApiResponse<Object> response = imeiUploadService.imeiBarcodeRead(request);

        // 验证结果
        assertNotNull(response);
        assertEquals(400, response.getCode());
        assertEquals("Image data cannot be empty", response.getMessage());
    }

    @Test
    @DisplayName("queryImeiListByPage 成功查询IMEI列表")
    void queryImeiListByPage_success() {
        // 准备测试数据 - 使用促销员职位避免调用getUserStoreCodesByMiId
        ImeiListQueryReq request = new ImeiListQueryReq();
        request.setCountryCode("CN");
        request.setUserId("user123");
        request.setMiId(12345L);
        request.setUserTitle(500900001L); // 促销员职位
        request.setPageIndex(1);
        request.setPageSize(10);
        // 关键：不设置时间过滤参数，避免触发时间处理逻辑
        request.setDateFilterType(null);
        request.setYear(null);
        request.setMonth(null);
        request.setStartTime(null);
        request.setEndTime(null);
        
        // Mock返回空列表，避免数据处理逻辑
        // 注意：参数要与实际调用时的参数匹配
        when(intlSoImeiMapper.queryImeiDetailList(
                eq("CN"),           // countryCode
                eq(12345L),         // miId (促销员职位使用)
                isNull(),           // userStoreList (促销员职位为null)
                isNull(),           // search
                isNull(),           // reportingType
                isNull(),           // storeCode
                isNull(),           // productLine
                isNull(),           // storeType
                isNull(),           // channelType
                isNull(),           // verifyResult
                isNull(),           // startTime
                isNull(),           // endTime
                eq(0),              // offset
                eq(11)              // pageSize + 1
        )).thenReturn(new ArrayList<>());
        
        // 执行测试
        CommonApiResponse<ImeiListQueryResp> response = imeiUploadService.queryImeiListByPage(request);
        
        // 验证结果
        assertNotNull(response);
        assertEquals(0, response.getCode()); // 成功时code为0
        assertNotNull(response.getData());
        assertNotNull(response.getData().getDetailList());
        assertTrue(response.getData().getDetailList().isEmpty());
        assertFalse(response.getData().getMoreRecords());
        
        // 验证依赖服务被调用
        verify(intlSoImeiMapper).queryImeiDetailList(
                eq("CN"),
                eq(12345L),
                isNull(),
                isNull(),
                isNull(),
                isNull(),
                isNull(),
                isNull(),
                isNull(),
                isNull(),
                isNull(),
                isNull(),
                eq(0),
                eq(11)
        );
    }

    @Test
    @DisplayName("queryImeiListByPage 必填参数为空")
    void queryImeiListByPage_missingRequiredParams() {
        // 准备测试数据 - 缺少必填参数
        ImeiListQueryReq request = new ImeiListQueryReq();
        request.setCountryCode(""); // 空字符串
        
        // 执行测试
        CommonApiResponse<ImeiListQueryResp> response = imeiUploadService.queryImeiListByPage(request);
        
        // 验证结果
        assertNotNull(response);
        assertEquals(400, response.getCode());
        assertEquals("必填参数不能为空", response.getMessage());
    }

    @Test
    @DisplayName("queryImeiDetail 成功查询IMEI明细")
    void queryImeiDetail_success() {
        // 准备测试数据
        ImeiDetailQueryReq request = createValidImeiDetailQueryReq();
        
        // Mock 依赖服务
        when(intlSoImeiMapper.queryImeiDetailById("123"))
                .thenReturn(createMockImeiDetailByIdData());
        
        // 执行测试
        CommonApiResponse<ImeiDetailQueryResp> response = imeiUploadService.queryImeiDetail(request);
        
        // 验证结果
        assertNotNull(response);
        assertEquals(0, response.getCode()); // 成功时code为0
        assertNotNull(response.getData());
        
        // 验证依赖服务被调用
        verify(intlSoImeiMapper).queryImeiDetailById("123");
    }

    @Test
    @DisplayName("queryImeiDetail 必填参数为空")
    void queryImeiDetail_missingRequiredParams() {
        // 准备测试数据 - 缺少必填参数
        ImeiDetailQueryReq request = new ImeiDetailQueryReq();
        request.setImeiId(""); // 空字符串
        
        // 执行测试
        CommonApiResponse<ImeiDetailQueryResp> response = imeiUploadService.queryImeiDetail(request);
        
        // 验证结果
        assertNotNull(response);
        assertEquals(400, response.getCode());
        assertEquals("必填参数不能为空", response.getMessage());
    }

    @Test
    @DisplayName("queryImeiDetail IMEI明细不存在")
    void queryImeiDetail_notFound() {
        // 准备测试数据
        ImeiDetailQueryReq request = createValidImeiDetailQueryReq();
        
        // Mock 依赖服务 - 返回空列表
        when(intlSoImeiMapper.queryImeiDetailById("123"))
                .thenReturn(new ArrayList<>());
        
        // 执行测试
        CommonApiResponse<ImeiDetailQueryResp> response = imeiUploadService.queryImeiDetail(request);
        
        // 验证结果
        assertNotNull(response);
        assertEquals(404, response.getCode());
        assertEquals("IMEI明细不存在", response.getMessage());
    }

    @Test
    @DisplayName("queryImeiSummary 成功查询IMEI汇总")
    void queryImeiSummary_success() {
        // 准备测试数据 - 使用促销员职位避免调用getUserStoreCodesByMiId
        ImeiSummaryQueryReq request = new ImeiSummaryQueryReq();
        request.setCountryCode("CN");
        request.setUserId("user123");
        request.setMiId(12345L);
        request.setUserTitle(500900001L); // 促销员职位
        // 关键：不设置时间过滤参数，避免触发时间处理逻辑
        request.setDateFilterType(null);
        request.setYear(null);
        request.setMonth(null);
        request.setStartTime(null);
        request.setEndTime(null);
        request.setSearch(null);
        request.setStoreCode(null);
        request.setProductLine(null);
        request.setStoreType(null);
        request.setChannelType(null);
        request.setVerifyResult(null);
        
        // Mock依赖服务，参数要与实际调用时的参数匹配
        when(intlSoImeiMapper.queryImeiSummary(
                eq("CN"),           // countryCode
                eq(12345L),         // miId (促销员职位使用)
                isNull(),           // userStoreList (促销员职位为null)
                isNull(),           // search
                isNull(),           // storeCode
                isNull(),           // productLine
                isNull(),           // storeType
                isNull(),           // channelType
                isNull(),           // verifyResult
                isNull(),           // startTime
                isNull()            // endTime
        )).thenReturn(createMockImeiSummaryData());
        
        // 执行测试
        CommonApiResponse<ImeiSummaryQueryResp> response = imeiUploadService.queryImeiSummary(request);
        
        // 验证结果
        assertNotNull(response);
        assertEquals(0, response.getCode()); // 成功时code为0
        assertNotNull(response.getData());
        assertEquals(10, response.getData().getTotalCount());
        assertEquals(8, response.getData().getSuccessCount());
        assertEquals(2, response.getData().getFailedCount());
        assertEquals(0, response.getData().getVerifyingCount());
        
        // 验证依赖服务被调用
        verify(intlSoImeiMapper).queryImeiSummary(
                eq("CN"),
                eq(12345L),
                isNull(),
                isNull(),
                isNull(),
                isNull(),
                isNull(),
                isNull(),
                isNull(),
                isNull(),
                isNull()
        );
    }

    @Test
    @DisplayName("queryImeiSummary 必填参数为空")
    void queryImeiSummary_missingRequiredParams() {
        // 准备测试数据 - 缺少必填参数
        ImeiSummaryQueryReq request = new ImeiSummaryQueryReq();
        request.setCountryCode(""); // 空字符串
        
        // 执行测试
        CommonApiResponse<ImeiSummaryQueryResp> response = imeiUploadService.queryImeiSummary(request);
        
        // 验证结果
        assertNotNull(response);
        assertEquals(400, response.getCode());
        assertEquals("必填参数不能为空", response.getMessage());
    }

    @Test
    @DisplayName("submitImei 依赖服务异常")
    void submitImei_serviceException() {
        // 准备测试数据
        SubmitImeiReq request = createValidSubmitImeiReq();
        IntlSoRuleDetail ruleDetail = createMockRuleDetail();
        PositionStoreInfoDTO positionStoreInfo = createMockPositionStoreInfo();
        UserInfoDTO userInfo = createMockUserInfo();
        ProductInfoDTO productInfo = createMockProductInfo();
        
        // Mock 前几个步骤成功，在createImeiData阶段抛出异常
        when(intlSoRuleDetailMapper.selectById(1L)).thenReturn(ruleDetail);
        when(intlPositionApiService.queryPositionWithStoreByCode("POS001"))
                .thenReturn(Optional.of(positionStoreInfo));
        when(userApiService.queryUserByMiId(12345L))
                .thenReturn(Optional.of(userInfo));
        when(productApiService.queryProductsByGoodIds(anyList()))
                .thenReturn(Collections.singletonMap(1001L, productInfo));
        // 在创建用户信息时抛出异常
        when(intlSoUserInfoService.createUserInfo(any(UserInfoDTO.class), anyLong()))
                .thenThrow(new RuntimeException("User service error"));
        
        // 执行测试
        CommonApiResponse<Object> response = imeiUploadService.submitImei(request);
        
        // 验证结果
        assertNotNull(response);
        assertEquals(500, response.getCode());
        assertTrue(response.getMessage().contains("Internal error"));
    }

    @Test
    @DisplayName("submitImei SO上报规则不存在")
    void submitImei_ruleNotFound() {
        // 准备测试数据
        SubmitImeiReq request = createValidSubmitImeiReq();
        
        // Mock 依赖服务 - 规则不存在
        when(intlSoRuleDetailMapper.selectById(1L)).thenReturn(null);
        
        // 执行测试
        CommonApiResponse<Object> response = imeiUploadService.submitImei(request);
        
        // 验证结果
        assertNotNull(response);
        assertEquals(400, response.getCode());
        assertEquals("SO上报规则不存在", response.getMessage());
    }

    @Test
    @DisplayName("imeiBarcodeRead 条形码识别失败")
    void imeiBarcodeRead_decodeFailure() {
        // 准备测试数据
        ImeiBarcodeReadReq request = new ImeiBarcodeReadReq();
        request.setImage("invalidbase64image");
        request.setCountryCode("CN");
        request.setMiId("12345");
        request.setUserTitle(500900001);
        
        // 使用MockedStatic模拟条形码解析失败
        try (MockedStatic<BarcodeScannerUtil> mockedUtil = mockStatic(BarcodeScannerUtil.class)) {
            mockedUtil.when(() -> BarcodeScannerUtil.decodeBarcode(anyString()))
                    .thenThrow(new RuntimeException("Barcode decode failed"));
            
            // 执行测试
            CommonApiResponse<Object> response = imeiUploadService.imeiBarcodeRead(request);
            
            // 验证结果
            assertNotNull(response);
            assertEquals(400, response.getCode());
            assertEquals("Failed to parse barcode", response.getMessage());
        }
    }

    // 辅助方法：创建测试用的 SubmitImeiReq
    private SubmitImeiReq createValidSubmitImeiReq() {
        SubmitImeiReq request = new SubmitImeiReq();
        request.setRuleId(1L);
        request.setCountryCode("CN");
        request.setPositionCode("POS001");
        request.setStoreCode("STORE001");
        request.setUserId("user123");
        request.setMiId(12345L);
        request.setUserTitle(500900001L);
        request.setBatchId(123L);
        request.setBatchIdStr("batchStr123");

        // 创建明细数据
        ImeiDetailDto detail = new ImeiDetailDto();
        detail.setImei("123456789012345");
        detail.setSn("SN12345");
        detail.setProductId(1001L);
        detail.setProductCode("PRODUCT001");
        detail.setSalesTime(System.currentTimeMillis());
        detail.setDetailId("detail001");
        detail.setReportingType(ReportingTypeEnum.APP.getValue());
        detail.setIsHashCountry(0);
        detail.setNote("测试备注");
        detail.setSalesChannel(1L);

        request.setDetailList(Arrays.asList(detail));

        // 创建图片数据
        PhotoDto photo = new PhotoDto();
        photo.setDetailId("detail001");
        photo.setUrl("http://example.com/photo.jpg");
        photo.setUploadTime(System.currentTimeMillis());

        request.setPhotoList(Arrays.asList(photo));

        return request;
    }

    // 辅助方法：创建测试用的 IntlSoRuleDetail
    private IntlSoRuleDetail createMockRuleDetail() {
        IntlSoRuleDetail ruleDetail = new IntlSoRuleDetail();
        ruleDetail.setId(1L);
        ruleDetail.setImeiRuleList("[{\"userTitleList\":[{\"userTitleId\":\"500900001\"}],\"productLineList\":[{\"productLineId\":\"1\"}],\"before\":10,\"after\":20}]");
        return ruleDetail;
    }

    // 辅助方法：创建测试用的 PositionStoreInfoDTO
    private PositionStoreInfoDTO createMockPositionStoreInfo() {
        PositionStoreInfoDTO dto = new PositionStoreInfoDTO();
        dto.setPositionCode("POS001");
        dto.setStoreCode("STORE001");
        dto.setSupplierCode("SUP001");
        return dto;
    }

    // 辅助方法：创建测试用的 UserInfoDTO
    private UserInfoDTO createMockUserInfo() {
        UserInfoDTO dto = new UserInfoDTO();
        dto.setMiId(12345L);
        dto.setEnglishName("TestUser");
        return dto;
    }

    // 辅助方法：创建测试用的 ProductInfoDTO
    private ProductInfoDTO createMockProductInfo() {
        ProductInfoDTO dto = new ProductInfoDTO();
        dto.setGoodsId("PRODUCT001");
        dto.setName("Test Product");
        dto.setShortName("TestP");
        dto.setProductLineCode(1L);
        return dto;
    }

    // 辅助方法：创建测试用的 IntlRmsRrp
    private IntlRmsRrp createMockRrp() {
        IntlRmsRrp rrp = new IntlRmsRrp();
        rrp.setCurrency("CNY");
        rrp.setRrpCode("RRP001");
        rrp.setRrp(new java.math.BigDecimal("999.99"));
        return rrp;
    }

    // 辅助方法：创建测试用的 ImeiListQueryReq
    private ImeiListQueryReq createValidImeiListQueryReq() {
        ImeiListQueryReq request = new ImeiListQueryReq();
        request.setCountryCode("CN");
        request.setUserId("user123");
        request.setMiId(12345L);
        request.setUserTitle(500900001L); // 促销员职位，避免调用门店查询
        request.setPageIndex(1);
        request.setPageSize(10);
        return request;
    }

    // 辅助方法：创建测试用的 ImeiDetailQueryReq
    private ImeiDetailQueryReq createValidImeiDetailQueryReq() {
        ImeiDetailQueryReq request = new ImeiDetailQueryReq();
        request.setImeiId("123");
        request.setUserId("user123");
        request.setMiId(12345L);
        request.setUserTitle(500900001L);
        return request;
    }

    // 辅助方法：创建测试用的 ImeiSummaryQueryReq
    private ImeiSummaryQueryReq createValidImeiSummaryQueryReq() {
        ImeiSummaryQueryReq request = new ImeiSummaryQueryReq();
        request.setCountryCode("CN");
        request.setUserId("user123");
        request.setMiId(12345L);
        request.setUserTitle(500900001L); // 促销员职位，避免调用门店查询
        return request;
    }

    // 辅助方法：创建模拟IMEI明细数据
    private List<Map<String, Object>> createMockImeiDetailData() {
        Map<String, Object> item = new HashMap<>();
        item.put("id", new BigInteger("1"));
        item.put("productName", "Test Product");
        item.put("imei1Mask", "123***789");
        item.put("imei2Mask", "456***012");
        item.put("snMask", "SN***45");
        item.put("note", "测试备注");
        item.put("storeName", "Test Store");
        item.put("verifyResult", 100000002);
        item.put("verifyResultDetail", "校验成功");
        item.put("reportingType", 100000001);
        item.put("sales_time", System.currentTimeMillis());
        item.put("country_code", "CN");
        item.put("url", "http://example.com/photo.jpg");
        
        return Arrays.asList(item);
    }

    // 辅助方法：创建模拟IMEI明细查询数据
    private List<Map<String, Object>> createMockImeiDetailByIdData() {
        Map<String, Object> item = new HashMap<>();
        item.put("id", new BigInteger("123"));
        item.put("productName", "Test Product");
        item.put("imei1Mask", "123***789");
        item.put("imei2Mask", "456***012");
        item.put("snMask", "SN***45");
        item.put("note", "测试备注");
        item.put("storeName", "Test Store");
        item.put("createdBy", "TestUser");
        item.put("createdOn", System.currentTimeMillis());
        item.put("countryCode", "CN");
        item.put("verifyResult", 100000002);
        item.put("reportingType", 100000001);
        item.put("salesTime", System.currentTimeMillis());
        
        return Arrays.asList(item);
    }

    // 辅助方法：创建模拟IMEI汇总数据
    private List<Map<String, Object>> createMockImeiSummaryData() {
        List<Map<String, Object>> summaryData = new ArrayList<>();
        
        // 成功的数据
        Map<String, Object> successItem = new HashMap<>();
        successItem.put("verification_result", 100000002); // Successfully
        successItem.put("count", 8);
        summaryData.add(successItem);
        
        // 失败的数据
        Map<String, Object> failedItem = new HashMap<>();
        failedItem.put("verification_result", 100000001); // Failed
        failedItem.put("count", 2);
        summaryData.add(failedItem);
        
        return summaryData;
    }

    // 辅助方法：通过反射设置私有字段
    private void setField(Object target, String fieldName, Object value) throws Exception {
        Field field = target.getClass().getDeclaredField(fieldName);
        field.setAccessible(true);
        field.set(target, value);
    }

    // 辅助方法：通过反射调用私有方法
    private Object invokePrivateMethod(Object target, String methodName, Class<?>[] parameterTypes, Object[] parameters) throws Exception {
        return target.getClass().getDeclaredMethod(methodName, parameterTypes).invoke(target, parameters);
    }
}