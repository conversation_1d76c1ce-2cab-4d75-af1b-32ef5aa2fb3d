package com.mi.info.intl.retail.so.app.provider.upload;

import com.mi.info.intl.retail.api.front.dto.RmsStoreInfoDto;
import com.mi.info.intl.retail.api.front.position.IntlPositionApiService;
import com.mi.info.intl.retail.api.front.dto.RmsPositionInfoRes;
import com.mi.info.intl.retail.api.front.position.dto.StoreValidationInfoDTO;
import com.mi.info.intl.retail.core.utils.EasyExcelUtil;
import com.mi.info.intl.retail.intlretail.service.api.fds.FdsService;
import com.mi.info.intl.retail.intlretail.service.api.fds.dto.FdsUploadResult;
import com.mi.info.intl.retail.intlretail.service.api.so.ImeiReportVerifyService;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.*;
import com.mi.info.intl.retail.intlretail.service.api.so.upload.ImeiUploadService;
import com.mi.info.intl.retail.intlretail.service.api.so.upload.dto.ImeiImportRequest;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.so.app.provider.upload.dto.ImeiImportExcelData;
import com.mi.info.intl.retail.so.domain.rule.bean.GetRetailerSoRuleResp;
import com.mi.info.intl.retail.so.domain.rule.service.IntlSoRuleRetailerService;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlImportLog;
import com.mi.info.intl.retail.so.domain.upload.service.IntlImportLogService;
import com.mi.info.intl.retail.so.util.ExcelFileUtil;
import com.mi.info.intl.retail.so.util.SalesTimeValidUtil;
import com.mi.info.intl.retail.so.util.dto.ExcelFileResource;
import com.mi.info.intl.retail.utils.intl.IntlTimeUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * ImeiImportServiceImpl 单元测试类
 *
 * <AUTHOR>
 * @date 2025/9/2
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("IMEI数据导入服务测试")
class ImeiImportServiceImplTest {

    @Mock
    private IntlImportLogService intlImportLogService;

    @Mock
    private EasyExcelUtil easyExcelUtil;

    @Mock
    private FdsService fdsService;

    @Mock
    private IntlSoRuleRetailerService intlSoRuleRetailerService;

    @Mock
    private ImeiReportVerifyService imeiReportVerifyService;

    @Mock
    private ImeiUploadService imeiUploadService;

    @Mock
    private IntlPositionApiService intlPositionApiService;

    @InjectMocks
    private ImeiImportServiceImpl imeiImportService;

    @BeforeEach
    void setUp() {
        // 重置所有Mock对象
        reset(intlImportLogService, easyExcelUtil, fdsService, intlSoRuleRetailerService,
                imeiReportVerifyService, imeiUploadService, intlPositionApiService);
    }

    /**
     * 创建有效的导入请求
     */
    private ImeiImportRequest createValidImportRequest() {
        ImeiImportRequest request = new ImeiImportRequest();
        request.setImportLogId(1L);
        request.setMiId(12345L);
        request.setSourceFileUrl("http://example.com/test.xlsx");
        return request;
    }

    /**
     * 创建模拟的导入日志
     */
    private IntlImportLog createMockImportLog() {
        IntlImportLog importLog = new IntlImportLog();
        importLog.setId(1L);
        importLog.setSourceFileUrl("http://example.com/test.xlsx");
        importLog.setStatus(0);
        return importLog;
    }

    /**
     * 创建有效的Excel数据列表
     */
    private List<ImeiImportExcelData> createValidExcelDataList() {
        List<ImeiImportExcelData> dataList = new ArrayList<>();
        
        ImeiImportExcelData data1 = new ImeiImportExcelData();
        data1.setStoreCode("STORE001");
        data1.setSalesTime("2025-09-02 10:00:00");
        data1.setImei("123456789012345");
        data1.setSn("SN001");
        data1.setRemark("Test remark");
        dataList.add(data1);

        ImeiImportExcelData data2 = new ImeiImportExcelData();
        data2.setStoreCode("STORE001");
        data2.setSalesTime("2025-09-02 11:00:00");
        data2.setImei("123456789012346");
        data2.setSn("SN002");
        data2.setRemark("Test remark 2");
        dataList.add(data2);

        return dataList;
    }

    /**
     * 创建门店校验信息
     */
    private StoreValidationInfoDTO createMockStoreValidationInfo() {
        StoreValidationInfoDTO storeInfo = new StoreValidationInfoDTO();
        storeInfo.setStoreCode("STORE001");
        storeInfo.setCountryShortcode("CN");
        storeInfo.setMiId(12345L);
        storeInfo.setJobId(1);
        storeInfo.setRmsUserid("rms123");
        storeInfo.setStoreCreatedOn("2025-01-01T00:00:00Z");
        
        RmsPositionInfoRes bestPosition = new RmsPositionInfoRes();
        bestPosition.setCode("POS001");
        storeInfo.setBestPosition(bestPosition);
        
        return storeInfo;
    }

    /**
     * 创建SO规则响应
     */
    private GetRetailerSoRuleResp createMockSoRuleResp() {
        GetRetailerSoRuleResp ruleResp = new GetRetailerSoRuleResp();
        ruleResp.setRuleId(1L);
        ruleResp.setEnableImei(1);
        return ruleResp;
    }

    /**
     * 创建IMEI校验响应
     */
    private List<ImeiReportVerifyResponse> createMockVerifyResponse() {
        List<ImeiReportVerifyResponse> responseList = new ArrayList<>();
        
        ImeiReportVerifyResponse response1 = new ImeiReportVerifyResponse();
        response1.setVerifyResult(0); // 成功
        response1.setImei1("123456789012345");
        response1.setSn("SN001");
        response1.setProductId(1001L);
        response1.setProductCode("PROD001");
        response1.setSnHash("hash001");
        response1.setIsHashCountry(1);
        responseList.add(response1);

        ImeiReportVerifyResponse response2 = new ImeiReportVerifyResponse();
        response2.setVerifyResult(0); // 成功
        response2.setImei1("123456789012346");
        response2.setSn("SN002");
        response2.setProductId(1002L);
        response2.setProductCode("PROD002");
        response2.setSnHash("hash002");
        response2.setIsHashCountry(1);
        responseList.add(response2);

        return responseList;
    }

    @Test
    @DisplayName("importImeiData 成功导入IMEI数据")
    void importImeiData_success() throws IOException {
        // 准备测试数据
        ImeiImportRequest request = createValidImportRequest();
        IntlImportLog importLog = createMockImportLog();
        List<ImeiImportExcelData> excelDataList = createValidExcelDataList();
        StoreValidationInfoDTO storeValidationInfo = createMockStoreValidationInfo();
        GetRetailerSoRuleResp soRuleResp = createMockSoRuleResp();
        List<ImeiReportVerifyResponse> verifyResponseList = createMockVerifyResponse();
        FdsUploadResult fdsUploadResult = new FdsUploadResult();
        fdsUploadResult.setUrl("http://example.com/result.xlsx");

        // Mock文件操作的静态方法
        try (MockedStatic<ExcelFileUtil> excelFileUtilMock = mockStatic(ExcelFileUtil.class);
             MockedStatic<SalesTimeValidUtil> salesTimeValidUtilMock = mockStatic(SalesTimeValidUtil.class);
             MockedStatic<IntlTimeUtil> intlTimeUtilMock = mockStatic(IntlTimeUtil.class);
             MockedStatic<Files> filesMock = mockStatic(Files.class)) {

            // Mock文件下载和表头校验
            ExcelFileResource mockFileResource = mock(ExcelFileResource.class);
            File mockFile = mock(File.class);
            InputStream mockInputStream = mock(InputStream.class);
            when(mockFileResource.getFile()).thenReturn(mockFile);
            when(mockFile.toPath()).thenReturn(mock(java.nio.file.Path.class));
            filesMock.when(() -> Files.newInputStream(any(java.nio.file.Path.class))).thenReturn(mockInputStream);
            excelFileUtilMock.when(() -> ExcelFileUtil.downloadFileFromUrl(anyString()))
                    .thenReturn(mockFileResource);
            excelFileUtilMock.when(() -> ExcelFileUtil.validateExcelHeaders(any(File.class), any(String[].class)))
                    .thenReturn(true);

            // Mock Excel数据解析 - 模拟向listener的dataList中添加数据
            doAnswer(invocation -> {
                Object listener = invocation.getArgument(2);
                try {
                    java.lang.reflect.Field dataListField = listener.getClass().getDeclaredField("dataList");
                    dataListField.setAccessible(true);
                    @SuppressWarnings("unchecked")
                    List<ImeiImportExcelData> dataList = (List<ImeiImportExcelData>) dataListField.get(listener);
                    dataList.addAll(excelDataList);
                } catch (Exception e) {
                    // 如果反射失败，可能不会触发数据校验逻辑
                }
                return null;
            }).when(easyExcelUtil).readFromStream(any(InputStream.class), eq(ImeiImportExcelData.class), any(), eq(1));

            // Mock时间校验
            intlTimeUtilMock.when(() -> IntlTimeUtil.parseLocalTimeToTimestamp(anyString(), anyString()))
                    .thenReturn(System.currentTimeMillis());
            salesTimeValidUtilMock.when(() -> SalesTimeValidUtil.validateSalesTime(anyString(), anyString(), any()))
                    .thenReturn(null); // 无错误

            // Mock门店校验
            when(intlPositionApiService.getStoreValidationInfo(eq("STORE001"), eq(12345L)))
                    .thenReturn(Optional.of(storeValidationInfo));

            // Mock SO规则查询
            when(intlSoRuleRetailerService.getRetailerSoRule(any()))
                    .thenReturn(soRuleResp);

            // Mock IMEI校验
            CommonApiResponse<Object> verifyResponse = new CommonApiResponse<Object>(0, "success", verifyResponseList);
            when(imeiReportVerifyService.imeiReportVerify(any()))
                    .thenReturn(verifyResponse);

            // Mock IMEI提交
            CommonApiResponse<Object> submitResponse = new CommonApiResponse<Object>(0, "success", null);
            when(imeiUploadService.submitImei(any()))
                    .thenReturn(submitResponse);

            // Mock导入日志更新
            when(intlImportLogService.updateById(any())).thenReturn(true);

            // 执行测试
            CommonApiResponse<ImportDataResponse> response = imeiImportService.importImeiData(request);

            // 验证结果
            assertNotNull(response);
            assertEquals(0, response.getCode());
            assertNotNull(response.getData());
            assertEquals(1L, response.getData().getImportLogId());
            assertEquals(1, response.getData().getStatus());
            assertEquals(2, response.getData().getTotalCount());
            assertEquals(0, response.getData().getFailedCount());

            // 验证依赖服务调用
            verify(intlPositionApiService).getStoreValidationInfo(eq("STORE001"), eq(12345L));
            verify(intlSoRuleRetailerService).getRetailerSoRule(any());
            verify(imeiReportVerifyService).imeiReportVerify(any());
            verify(imeiUploadService).submitImei(any());
            verify(intlImportLogService, times(1)).updateById(any());
        }
    }

    @Test
    @DisplayName("importImeiData 参数校验失败 - importLogId为空")
    void importImeiData_nullImportLogId() {
        // 准备测试数据
        ImeiImportRequest request = new ImeiImportRequest();
        request.setImportLogId(null);
        request.setMiId(12345L);

        // 执行测试
        CommonApiResponse<ImportDataResponse> response = imeiImportService.importImeiData(request);

        // 验证结果
        assertNotNull(response);
        assertEquals(400, response.getCode());
        assertEquals("importLogId and miId are required", response.getMessage());
        assertNull(response.getData());

        // 验证无额外服务调用
        verifyNoInteractions(intlImportLogService, easyExcelUtil, fdsService,
                intlSoRuleRetailerService, imeiReportVerifyService, imeiUploadService, intlPositionApiService);
    }

    @Test
    @DisplayName("importImeiData 参数校验失败 - miId为空")
    void importImeiData_nullMiId() {
        // 准备测试数据
        ImeiImportRequest request = new ImeiImportRequest();
        request.setImportLogId(1L);
        request.setMiId(null);

        // 执行测试
        CommonApiResponse<ImportDataResponse> response = imeiImportService.importImeiData(request);

        // 验证结果
        assertNotNull(response);
        assertEquals(400, response.getCode());
        assertEquals("importLogId and miId are required", response.getMessage());
        assertNull(response.getData());

        // 验证无额外服务调用
        verifyNoInteractions(intlImportLogService, easyExcelUtil, fdsService,
                intlSoRuleRetailerService, imeiReportVerifyService, imeiUploadService, intlPositionApiService);
    }

    @Test
    @DisplayName("importImeiData 源文件URL为空")
    void importImeiData_emptySourceFileUrl() {
        // 准备测试数据
        ImeiImportRequest request = new ImeiImportRequest();
        request.setImportLogId(1L);
        request.setMiId(12345L);
        request.setSourceFileUrl("");

        // Mock导入日志查询
        IntlImportLog importLog = new IntlImportLog();
        importLog.setId(1L);
        importLog.setSourceFileUrl(null); // 源文件URL也为空
        when(intlImportLogService.getById(1L)).thenReturn(importLog);

        // 执行测试
        CommonApiResponse<ImportDataResponse> response = imeiImportService.importImeiData(request);

        // 验证结果
        assertNotNull(response);
        assertEquals(400, response.getCode());
        assertEquals("Source file URL is required", response.getMessage());
        assertNull(response.getData());

        // 验证服务调用
        verify(intlImportLogService).getById(1L);
        verifyNoInteractions(easyExcelUtil, fdsService, intlSoRuleRetailerService,
                imeiReportVerifyService, imeiUploadService, intlPositionApiService);
    }

    @Test
    @DisplayName("importImeiData 表头校验失败")
    void importImeiData_headerValidationFailed() throws IOException {
        // 准备测试数据
        ImeiImportRequest request = createValidImportRequest();

        // Mock文件操作的静态方法
        try (MockedStatic<ExcelFileUtil> excelFileUtilMock = mockStatic(ExcelFileUtil.class)) {
            // Mock文件下载
            ExcelFileResource mockFileResource = mock(ExcelFileResource.class);
            File mockFile = mock(File.class);
            when(mockFileResource.getFile()).thenReturn(mockFile);
            excelFileUtilMock.when(() -> ExcelFileUtil.downloadFileFromUrl(anyString()))
                    .thenReturn(mockFileResource);

            // Mock表头校验失败
            excelFileUtilMock.when(() -> ExcelFileUtil.validateExcelHeaders(any(File.class), any(String[].class)))
                    .thenReturn(false);

            // Mock导入日志更新
            when(intlImportLogService.updateById(any())).thenReturn(true);

            // 执行测试
            CommonApiResponse<ImportDataResponse> response = imeiImportService.importImeiData(request);

            // 验证结果
            assertNotNull(response);
            assertEquals(0, response.getCode());
            assertNotNull(response.getData());
            assertEquals(1L, response.getData().getImportLogId());
            assertEquals(2, response.getData().getStatus());
            assertEquals("Please use the template to upload.", response.getData().getErrorMsg());

            // 验证服务调用
            verify(intlImportLogService).updateById(any());
            verifyNoInteractions(easyExcelUtil, fdsService, intlSoRuleRetailerService,
                    imeiReportVerifyService, imeiUploadService, intlPositionApiService);
        }
    }

    @Test
    @DisplayName("importImeiData 数据量超过限制")
    void importImeiData_exceedMaxRows() throws IOException {
        // 准备测试数据
        ImeiImportRequest request = createValidImportRequest();

        // Mock文件操作的静态方法
        try (MockedStatic<ExcelFileUtil> excelFileUtilMock = mockStatic(ExcelFileUtil.class);
             MockedStatic<Files> filesMock = mockStatic(Files.class)) {
            // Mock文件下载和表头校验
            ExcelFileResource mockFileResource = mock(ExcelFileResource.class);
            File mockFile = mock(File.class);
            InputStream mockInputStream = mock(InputStream.class);
            when(mockFileResource.getFile()).thenReturn(mockFile);
            when(mockFile.toPath()).thenReturn(mock(java.nio.file.Path.class));
            filesMock.when(() -> Files.newInputStream(any(java.nio.file.Path.class))).thenReturn(mockInputStream);
            excelFileUtilMock.when(() -> ExcelFileUtil.downloadFileFromUrl(anyString()))
                    .thenReturn(mockFileResource);
            excelFileUtilMock.when(() -> ExcelFileUtil.validateExcelHeaders(any(File.class), any(String[].class)))
                    .thenReturn(true);

            // Mock Excel数据解析，模拟返回超过5000条数据
            // 这里我们直接模拟 easyExcelUtil.readFromStream 方法的行为
            // 在实际情况下，这个方法会通过 listener 来填充数据列表
            doAnswer(invocation -> {
                // 获取 listener 参数，这是一个 ImeiImportExcelListener 实例
                Object listener = invocation.getArgument(2);
                
                // 直接通过反射获取 dataList 字段并添加数据
                try {
                    java.lang.reflect.Field dataListField = listener.getClass().getDeclaredField("dataList");
                    dataListField.setAccessible(true);
                    @SuppressWarnings("unchecked")
                    List<ImeiImportExcelData> dataList = (List<ImeiImportExcelData>) dataListField.get(listener);
                    
                    // 添加超过5000条数据
                    List<ImeiImportExcelData> largeDataList = createLargeExcelDataList();
                    dataList.addAll(largeDataList);
                    
                } catch (Exception e) {
                    // 如果反射失败，这个测试可能不会触发数据量超限检查
                    // 但至少不会抛出异常
                }
                return null;
            }).when(easyExcelUtil).readFromStream(any(InputStream.class), eq(ImeiImportExcelData.class), any(), eq(1));

            // Mock导入日志更新（数据量超限时只会调用这个方法）
            when(intlImportLogService.updateById(any())).thenReturn(true);

            // 执行测试
            CommonApiResponse<ImportDataResponse> response = imeiImportService.importImeiData(request);

            // 验证结果
            assertNotNull(response);
            assertEquals(0, response.getCode());
            assertNotNull(response.getData());
            assertEquals(1L, response.getData().getImportLogId());
            assertEquals(2, response.getData().getStatus());
            // 如果数据量超限检查生效，应该返回超限错误信息
            assertTrue(response.getData().getErrorMsg().contains("maximum") || 
                      response.getData().getErrorMsg().contains("5,000") ||
                      response.getData().getErrorMsg().equals("A maximum of 5,000 pieces of data are allowed to be uploaded."));
            
            // 验证服务调用（只有updateById会被调用）
            verify(intlImportLogService).updateById(any());
        }
    }

    /**
     * 创建数据量超限的测试数据
     */
    private List<ImeiImportExcelData> createLargeExcelDataList() {
        List<ImeiImportExcelData> dataList = new ArrayList<>();
        for (int i = 0; i < 5001; i++) {
            ImeiImportExcelData data = new ImeiImportExcelData();
            data.setStoreCode("STORE001");
            data.setSalesTime("2025-09-02 10:00:00");
            data.setImei("123456789012" + String.format("%03d", i));
            data.setSn("SN" + String.format("%03d", i));
            data.setRemark("Test remark " + i);
            dataList.add(data);
        }
        return dataList;
    }

    @Test
    @DisplayName("importImeiData 数据校验失败 - 必填字段缺失")
    void importImeiData_mandatoryFieldsMissing() throws IOException {
        // 准备测试数据 - 缺少必填字段
        ImeiImportRequest request = createValidImportRequest();
        List<ImeiImportExcelData> invalidDataList = createInvalidExcelDataList();

        // Mock文件操作的静态方法
        try (MockedStatic<ExcelFileUtil> excelFileUtilMock = mockStatic(ExcelFileUtil.class);
             MockedStatic<IntlTimeUtil> intlTimeUtilMock = mockStatic(IntlTimeUtil.class);
             MockedStatic<Files> filesMock = mockStatic(Files.class)) {

            // Mock文件下载和表头校验
            ExcelFileResource mockFileResource = mock(ExcelFileResource.class);
            File mockFile = mock(File.class);
            InputStream mockInputStream = mock(InputStream.class);
            when(mockFileResource.getFile()).thenReturn(mockFile);
            when(mockFile.toPath()).thenReturn(mock(java.nio.file.Path.class));
            filesMock.when(() -> Files.newInputStream(any(java.nio.file.Path.class))).thenReturn(mockInputStream);
            excelFileUtilMock.when(() -> ExcelFileUtil.downloadFileFromUrl(anyString()))
                    .thenReturn(mockFileResource);
            excelFileUtilMock.when(() -> ExcelFileUtil.validateExcelHeaders(any(File.class), any(String[].class)))
                    .thenReturn(true);

            // Mock Excel数据解析 - 模拟解析出无效数据
            doAnswer(invocation -> {
                Object listener = invocation.getArgument(2);
                try {
                    java.lang.reflect.Field dataListField = listener.getClass().getDeclaredField("dataList");
                    dataListField.setAccessible(true);
                    @SuppressWarnings("unchecked")
                    List<ImeiImportExcelData> dataList = (List<ImeiImportExcelData>) dataListField.get(listener);
                    dataList.addAll(invalidDataList);
                } catch (Exception e) {
                    // 如果反射失败，可能不会触发数据校验逻辑
                }
                return null;
            }).when(easyExcelUtil).readFromStream(any(InputStream.class), eq(ImeiImportExcelData.class), any(), eq(1));

            // Mock时间校验失败
            intlTimeUtilMock.when(() -> IntlTimeUtil.parseLocalTimeToTimestamp(anyString(), anyString()))
                    .thenReturn(null); // 模拟时间格式错误

            // Mock错误文件生成
            FdsUploadResult fdsUploadResult = new FdsUploadResult();
            fdsUploadResult.setUrl("http://example.com/error.xlsx");
            ExcelFileResource mockErrorFileResource = mock(ExcelFileResource.class);
            File mockErrorFile = mock(File.class);
            when(mockErrorFileResource.getFile()).thenReturn(mockErrorFile);
            excelFileUtilMock.when(() -> ExcelFileUtil.generateExcelFile(anyList(), eq(ImeiImportExcelData.class), anyString()))
                    .thenReturn(mockErrorFileResource);
            when(fdsService.upload(anyString(), any(File.class), eq(true)))
                    .thenReturn(fdsUploadResult);

            // Mock导入日志更新
            when(intlImportLogService.updateById(any())).thenReturn(true);

            // 执行测试
            CommonApiResponse<ImportDataResponse> response = imeiImportService.importImeiData(request);

            // 验证结果
            assertNotNull(response);
            assertEquals(0, response.getCode());
            assertNotNull(response.getData());
            assertEquals(1L, response.getData().getImportLogId());
            assertEquals(2, response.getData().getStatus());
            assertEquals("Validation failed.", response.getData().getErrorMsg());
            
            // 验证服务调用
            verify(intlImportLogService, atLeast(1)).updateById(any());
            verify(fdsService).upload(anyString(), any(File.class), eq(true));
        }
    }

    @Test
    @DisplayName("importImeiData 数据校验失败 - 门店不存在")
    void importImeiData_storeNotFound() throws IOException {
        // 准备测试数据
        ImeiImportRequest request = createValidImportRequest();
        List<ImeiImportExcelData> excelDataList = createValidExcelDataList();

        // Mock文件操作的静态方法
        try (MockedStatic<ExcelFileUtil> excelFileUtilMock = mockStatic(ExcelFileUtil.class);
             MockedStatic<SalesTimeValidUtil> salesTimeValidUtilMock = mockStatic(SalesTimeValidUtil.class);
             MockedStatic<IntlTimeUtil> intlTimeUtilMock = mockStatic(IntlTimeUtil.class);
             MockedStatic<Files> filesMock = mockStatic(Files.class)) {

            // Mock文件下载和表头校验
            ExcelFileResource mockFileResource = mock(ExcelFileResource.class);
            File mockFile = mock(File.class);
            InputStream mockInputStream = mock(InputStream.class);
            when(mockFileResource.getFile()).thenReturn(mockFile);
            when(mockFile.toPath()).thenReturn(mock(java.nio.file.Path.class));
            filesMock.when(() -> Files.newInputStream(any(java.nio.file.Path.class))).thenReturn(mockInputStream);
            excelFileUtilMock.when(() -> ExcelFileUtil.downloadFileFromUrl(anyString()))
                    .thenReturn(mockFileResource);
            excelFileUtilMock.when(() -> ExcelFileUtil.validateExcelHeaders(any(File.class), any(String[].class)))
                    .thenReturn(true);

            // Mock Excel数据解析 - 模拟解析出有效数据
            doAnswer(invocation -> {
                Object listener = invocation.getArgument(2);
                try {
                    java.lang.reflect.Field dataListField = listener.getClass().getDeclaredField("dataList");
                    dataListField.setAccessible(true);
                    @SuppressWarnings("unchecked")
                    List<ImeiImportExcelData> dataList = (List<ImeiImportExcelData>) dataListField.get(listener);
                    dataList.addAll(excelDataList);
                } catch (Exception e) {
                    // 如果反射失败，可能不会触发数据校验逻辑
                }
                return null;
            }).when(easyExcelUtil).readFromStream(any(InputStream.class), eq(ImeiImportExcelData.class), any(), eq(1));

            // Mock时间校验
            intlTimeUtilMock.when(() -> IntlTimeUtil.parseLocalTimeToTimestamp(anyString(), anyString()))
                    .thenReturn(System.currentTimeMillis());
            salesTimeValidUtilMock.when(() -> SalesTimeValidUtil.validateSalesTime(anyString(), anyString(), any()))
                    .thenReturn(null);

            // Mock门店校验失败 - 门店不存在
            when(intlPositionApiService.getStoreValidationInfo(eq("STORE001"), eq(12345L)))
                    .thenReturn(Optional.empty());
            when(intlPositionApiService.getStoreByCode(eq("STORE001")))
                    .thenReturn(null); // 门店不存在

            // Mock错误文件生成
            FdsUploadResult fdsUploadResult = new FdsUploadResult();
            fdsUploadResult.setUrl("http://example.com/error.xlsx");
            ExcelFileResource mockErrorFileResource = mock(ExcelFileResource.class);
            File mockErrorFile = mock(File.class);
            when(mockErrorFileResource.getFile()).thenReturn(mockErrorFile);
            excelFileUtilMock.when(() -> ExcelFileUtil.generateExcelFile(anyList(), eq(ImeiImportExcelData.class), anyString()))
                    .thenReturn(mockErrorFileResource);
            when(fdsService.upload(anyString(), any(File.class), eq(true)))
                    .thenReturn(fdsUploadResult);

            // Mock导入日志更新
            when(intlImportLogService.updateById(any())).thenReturn(true);

            // 执行测试
            CommonApiResponse<ImportDataResponse> response = imeiImportService.importImeiData(request);

            // 验证结果
            assertNotNull(response);
            assertEquals(0, response.getCode());
            assertNotNull(response.getData());
            assertEquals(1L, response.getData().getImportLogId());
            assertEquals(2, response.getData().getStatus());
            assertEquals("Validation failed.", response.getData().getErrorMsg());
            
            // 验证服务调用
            verify(intlPositionApiService).getStoreValidationInfo(eq("STORE001"), eq(12345L));
            verify(intlPositionApiService).getStoreByCode(eq("STORE001"));
            verify(fdsService).upload(anyString(), any(File.class), eq(true));
            verify(intlImportLogService, atLeast(1)).updateById(any());
        }
    }

    /**
     * 创建无效的Excel数据列表（缺少必填字段）
     */
    private List<ImeiImportExcelData> createInvalidExcelDataList() {
        List<ImeiImportExcelData> dataList = new ArrayList<>();
        
        // 缺少门店编码
        ImeiImportExcelData data1 = new ImeiImportExcelData();
        data1.setStoreCode(""); // 空的门店编码
        data1.setSalesTime("2025-09-02 10:00:00");
        data1.setImei("123456789012345");
        data1.setSn("SN001");
        dataList.add(data1);

        // 缺少销售时间
        ImeiImportExcelData data2 = new ImeiImportExcelData();
        data2.setStoreCode("STORE001");
        data2.setSalesTime(""); // 空的销售时间
        data2.setImei("123456789012346");
        data2.setSn("SN002");
        dataList.add(data2);

        // IMEI和SN都缺少
        ImeiImportExcelData data3 = new ImeiImportExcelData();
        data3.setStoreCode("STORE001");
        data3.setSalesTime("2025-09-02 12:00:00");
        data3.setImei(""); // 空的IMEI
        data3.setSn(""); // 空的SN
        dataList.add(data3);

        return dataList;
    }

    @Test
    @DisplayName("importImeiData IMEI创建失败")
    void importImeiData_imeiCreationFailed() throws IOException {
        // 准备测试数据
        ImeiImportRequest request = createValidImportRequest();
        List<ImeiImportExcelData> excelDataList = createValidExcelDataList();
        StoreValidationInfoDTO storeValidationInfo = createMockStoreValidationInfo();
        GetRetailerSoRuleResp soRuleResp = createMockSoRuleResp();
        List<ImeiReportVerifyResponse> verifyResponseList = createMockVerifyResponse();

        // Mock文件操作的静态方法
        try (MockedStatic<ExcelFileUtil> excelFileUtilMock = mockStatic(ExcelFileUtil.class);
             MockedStatic<SalesTimeValidUtil> salesTimeValidUtilMock = mockStatic(SalesTimeValidUtil.class);
             MockedStatic<IntlTimeUtil> intlTimeUtilMock = mockStatic(IntlTimeUtil.class);
             MockedStatic<Files> filesMock = mockStatic(Files.class)) {

            // Mock文件下载和表头校验
            ExcelFileResource mockFileResource = mock(ExcelFileResource.class);
            File mockFile = mock(File.class);
            InputStream mockInputStream = mock(InputStream.class);
            when(mockFileResource.getFile()).thenReturn(mockFile);
            when(mockFile.toPath()).thenReturn(mock(java.nio.file.Path.class));
            filesMock.when(() -> Files.newInputStream(any(java.nio.file.Path.class))).thenReturn(mockInputStream);
            excelFileUtilMock.when(() -> ExcelFileUtil.downloadFileFromUrl(anyString()))
                    .thenReturn(mockFileResource);
            excelFileUtilMock.when(() -> ExcelFileUtil.validateExcelHeaders(any(File.class), any(String[].class)))
                    .thenReturn(true);

            // Mock Excel数据解析 - 模拟向listener的dataList中添加数据
            doAnswer(invocation -> {
                Object listener = invocation.getArgument(2);
                try {
                    java.lang.reflect.Field dataListField = listener.getClass().getDeclaredField("dataList");
                    dataListField.setAccessible(true);
                    @SuppressWarnings("unchecked")
                    List<ImeiImportExcelData> dataList = (List<ImeiImportExcelData>) dataListField.get(listener);
                    dataList.addAll(excelDataList);
                } catch (Exception e) {
                    // 如果反射失败，可能不会触发数据校验逻辑
                }
                return null;
            }).when(easyExcelUtil).readFromStream(any(InputStream.class), eq(ImeiImportExcelData.class), any(), eq(1));

            // Mock时间校验
            intlTimeUtilMock.when(() -> IntlTimeUtil.parseLocalTimeToTimestamp(anyString(), anyString()))
                    .thenReturn(System.currentTimeMillis());
            salesTimeValidUtilMock.when(() -> SalesTimeValidUtil.validateSalesTime(anyString(), anyString(), any()))
                    .thenReturn(null);

            // Mock门店校验
            when(intlPositionApiService.getStoreValidationInfo(eq("STORE001"), eq(12345L)))
                    .thenReturn(Optional.of(storeValidationInfo));

            // Mock SO规则查询
            when(intlSoRuleRetailerService.getRetailerSoRule(any()))
                    .thenReturn(soRuleResp);

            // Mock IMEI校验
            CommonApiResponse<Object> verifyResponse = new CommonApiResponse<Object>(0, "success", verifyResponseList);
            verifyResponse.setCode(0);
            verifyResponse.setData(verifyResponseList);
            when(imeiReportVerifyService.imeiReportVerify(any()))
                    .thenReturn(verifyResponse);

            // Mock IMEI提交失败
            CommonApiResponse<Object> submitResponse = new CommonApiResponse<Object>(500, "IMEI creation failed", null);
            when(imeiUploadService.submitImei(any()))
                    .thenReturn(submitResponse);

            // Mock导入日志更新
            when(intlImportLogService.updateById(any())).thenReturn(true);

            // 执行测试
            CommonApiResponse<ImportDataResponse> response = imeiImportService.importImeiData(request);

            // 验证结果
            assertNotNull(response);
            assertEquals(0, response.getCode());
            assertNotNull(response.getData());
            assertEquals(1L, response.getData().getImportLogId());
            assertEquals(2, response.getData().getStatus());
            assertEquals("IMEI creation failed", response.getData().getErrorMsg());

            // 验证依赖服务调用
            verify(intlPositionApiService).getStoreValidationInfo(eq("STORE001"), eq(12345L));
            verify(intlSoRuleRetailerService).getRetailerSoRule(any());
            verify(imeiReportVerifyService).imeiReportVerify(any());
            verify(imeiUploadService).submitImei(any());
            verify(intlImportLogService, times(1)).updateById(any());
        }
    }

    @Test
    @DisplayName("importImeiData 系统异常")
    void importImeiData_systemException() throws IOException {
        // 准备测试数据
        ImeiImportRequest request = createValidImportRequest();

        // Mock文件操作抛出异常
        try (MockedStatic<ExcelFileUtil> excelFileUtilMock = mockStatic(ExcelFileUtil.class)) {
            // Mock文件下载抛出异常
            excelFileUtilMock.when(() -> ExcelFileUtil.downloadFileFromUrl(anyString()))
                    .thenThrow(new IOException("File download failed"));

            // Mock导入日志更新
            when(intlImportLogService.updateById(any())).thenReturn(true);

            // 执行测试
            CommonApiResponse<ImportDataResponse> response = imeiImportService.importImeiData(request);

            // 验证结果
            assertNotNull(response);
            assertEquals(500, response.getCode());
            assertTrue(response.getMessage().contains("System error"));
            assertTrue(response.getMessage().contains("File download failed"));
            assertNull(response.getData());

            // 验证服务调用
            verify(intlImportLogService).updateById(any());
        }
    }

    @Test
    @DisplayName("importImeiData 数据库异常")
    void importImeiData_databaseException() {
        // 准备测试数据 - 不设置 sourceFileUrl，让其去查询数据库
        ImeiImportRequest request = new ImeiImportRequest();
        request.setImportLogId(1L);
        request.setMiId(12345L);
        // 不设置 sourceFileUrl，这样会调用 getById 方法
        
        // Mock导入日志查询抛出异常
        when(intlImportLogService.getById(1L))
                .thenThrow(new RuntimeException("Database connection failed"));

        // Mock导入日志更新
        when(intlImportLogService.updateById(any())).thenReturn(true);

        // 执行测试
        CommonApiResponse<ImportDataResponse> response = imeiImportService.importImeiData(request);

        // 验证结果
        assertNotNull(response);
        assertEquals(500, response.getCode());
        assertTrue(response.getMessage().contains("System error"));
        assertTrue(response.getMessage().contains("Database connection failed"));
        assertNull(response.getData());

        // 验证服务调用
        verify(intlImportLogService).getById(1L);
        verify(intlImportLogService).updateById(any());
    }

    @Test
    @DisplayName("importImeiData IMEI校验失败")
    void importImeiData_imeiVerificationFailed() throws IOException {
        // 准备测试数据
        ImeiImportRequest request = createValidImportRequest();
        List<ImeiImportExcelData> excelDataList = createValidExcelDataList();
        StoreValidationInfoDTO storeValidationInfo = createMockStoreValidationInfo();
        GetRetailerSoRuleResp soRuleResp = createMockSoRuleResp();
        
        // 创建校验失败的响应
        List<ImeiReportVerifyResponse> verifyResponseList = new ArrayList<>();
        ImeiReportVerifyResponse failedResponse = new ImeiReportVerifyResponse();
        failedResponse.setVerifyResult(1); // 产品校验失败
        verifyResponseList.add(failedResponse);

        // Mock文件操作的静态方法
        try (MockedStatic<ExcelFileUtil> excelFileUtilMock = mockStatic(ExcelFileUtil.class);
             MockedStatic<SalesTimeValidUtil> salesTimeValidUtilMock = mockStatic(SalesTimeValidUtil.class);
             MockedStatic<IntlTimeUtil> intlTimeUtilMock = mockStatic(IntlTimeUtil.class);
             MockedStatic<Files> filesMock = mockStatic(Files.class)) {

            // Mock文件下载和表头校验
            ExcelFileResource mockFileResource = mock(ExcelFileResource.class);
            File mockFile = mock(File.class);
            InputStream mockInputStream = mock(InputStream.class);
            when(mockFileResource.getFile()).thenReturn(mockFile);
            when(mockFile.toPath()).thenReturn(mock(java.nio.file.Path.class));
            filesMock.when(() -> Files.newInputStream(any(java.nio.file.Path.class))).thenReturn(mockInputStream);
            excelFileUtilMock.when(() -> ExcelFileUtil.downloadFileFromUrl(anyString()))
                    .thenReturn(mockFileResource);
            excelFileUtilMock.when(() -> ExcelFileUtil.validateExcelHeaders(any(File.class), any(String[].class)))
                    .thenReturn(true);

            // Mock Excel数据解析 - 模拟解析出有效数据
            doAnswer(invocation -> {
                Object listener = invocation.getArgument(2);
                try {
                    java.lang.reflect.Field dataListField = listener.getClass().getDeclaredField("dataList");
                    dataListField.setAccessible(true);
                    @SuppressWarnings("unchecked")
                    List<ImeiImportExcelData> dataList = (List<ImeiImportExcelData>) dataListField.get(listener);
                    dataList.addAll(excelDataList);
                } catch (Exception e) {
                    // 如果反射失败，可能不会触发数据校验逻辑
                }
                return null;
            }).when(easyExcelUtil).readFromStream(any(InputStream.class), eq(ImeiImportExcelData.class), any(), eq(1));

            // Mock时间校验
            intlTimeUtilMock.when(() -> IntlTimeUtil.parseLocalTimeToTimestamp(anyString(), anyString()))
                    .thenReturn(System.currentTimeMillis());
            salesTimeValidUtilMock.when(() -> SalesTimeValidUtil.validateSalesTime(anyString(), anyString(), any()))
                    .thenReturn(null);

            // Mock门店校验
            when(intlPositionApiService.getStoreValidationInfo(eq("STORE001"), eq(12345L)))
                    .thenReturn(Optional.of(storeValidationInfo));

            // Mock SO规则查询
            when(intlSoRuleRetailerService.getRetailerSoRule(any()))
                    .thenReturn(soRuleResp);

            // Mock IMEI校验失败
            CommonApiResponse<Object> verifyResponse = new CommonApiResponse<Object>(0, "success", verifyResponseList);
            when(imeiReportVerifyService.imeiReportVerify(any()))
                    .thenReturn(verifyResponse);

            // Mock错误文件生成
            FdsUploadResult fdsUploadResult = new FdsUploadResult();
            fdsUploadResult.setUrl("http://example.com/error.xlsx");
            ExcelFileResource mockErrorFileResource = mock(ExcelFileResource.class);
            File mockErrorFile = mock(File.class);
            when(mockErrorFileResource.getFile()).thenReturn(mockErrorFile);
            excelFileUtilMock.when(() -> ExcelFileUtil.generateExcelFile(anyList(), eq(ImeiImportExcelData.class), anyString()))
                    .thenReturn(mockErrorFileResource);
            when(fdsService.upload(anyString(), any(File.class), eq(true)))
                    .thenReturn(fdsUploadResult);

            // Mock导入日志更新
            when(intlImportLogService.updateById(any())).thenReturn(true);

            // 执行测试
            CommonApiResponse<ImportDataResponse> response = imeiImportService.importImeiData(request);

            // 验证结果
            assertNotNull(response);
            assertEquals(0, response.getCode());
            assertNotNull(response.getData());
            assertEquals(1L, response.getData().getImportLogId());
            assertEquals(2, response.getData().getStatus());
            assertEquals("Validation failed.", response.getData().getErrorMsg());

            // 验证依赖服务调用
            verify(intlPositionApiService).getStoreValidationInfo(eq("STORE001"), eq(12345L));
            verify(intlSoRuleRetailerService).getRetailerSoRule(any());
            verify(imeiReportVerifyService).imeiReportVerify(any());
            verify(fdsService).upload(anyString(), any(File.class), eq(true));
            verify(intlImportLogService, times(1)).updateById(any());
        }
    }

    @Test
    @DisplayName("importImeiData 门店未开放")
    void importImeiData_storeNotOpening() throws IOException {
        // 准备测试数据
        ImeiImportRequest request = createValidImportRequest();
        List<ImeiImportExcelData> excelDataList = createValidExcelDataList();
        
        // 创建未开放的门店
        RmsStoreInfoDto store = new RmsStoreInfoDto();
        store.setCode("STORE001");
        store.setOperationStatus(0); // 非开放状态

        // Mock文件操作的静态方法
        try (MockedStatic<ExcelFileUtil> excelFileUtilMock = mockStatic(ExcelFileUtil.class);
             MockedStatic<SalesTimeValidUtil> salesTimeValidUtilMock = mockStatic(SalesTimeValidUtil.class);
             MockedStatic<IntlTimeUtil> intlTimeUtilMock = mockStatic(IntlTimeUtil.class);
             MockedStatic<Files> filesMock = mockStatic(Files.class)) {

            // Mock文件下载和表头校验
            ExcelFileResource mockFileResource = mock(ExcelFileResource.class);
            File mockFile = mock(File.class);
            InputStream mockInputStream = mock(InputStream.class);
            when(mockFileResource.getFile()).thenReturn(mockFile);
            when(mockFile.toPath()).thenReturn(mock(java.nio.file.Path.class));
            filesMock.when(() -> Files.newInputStream(any(java.nio.file.Path.class))).thenReturn(mockInputStream);
            excelFileUtilMock.when(() -> ExcelFileUtil.downloadFileFromUrl(anyString()))
                    .thenReturn(mockFileResource);
            excelFileUtilMock.when(() -> ExcelFileUtil.validateExcelHeaders(any(File.class), any(String[].class)))
                    .thenReturn(true);

            // Mock Excel数据解析 - 模拟解析出有效数据
            doAnswer(invocation -> {
                Object listener = invocation.getArgument(2);
                try {
                    java.lang.reflect.Field dataListField = listener.getClass().getDeclaredField("dataList");
                    dataListField.setAccessible(true);
                    @SuppressWarnings("unchecked")
                    List<ImeiImportExcelData> dataList = (List<ImeiImportExcelData>) dataListField.get(listener);
                    dataList.addAll(excelDataList);
                } catch (Exception e) {
                    // 如果反射失败，可能不会触发数据校验逻辑
                }
                return null;
            }).when(easyExcelUtil).readFromStream(any(InputStream.class), eq(ImeiImportExcelData.class), any(), eq(1));

            // Mock时间校验
            intlTimeUtilMock.when(() -> IntlTimeUtil.parseLocalTimeToTimestamp(anyString(), anyString()))
                    .thenReturn(System.currentTimeMillis());
            salesTimeValidUtilMock.when(() -> SalesTimeValidUtil.validateSalesTime(anyString(), anyString(), any()))
                    .thenReturn(null);

            // Mock门店校验失败 - 门店未开放
            when(intlPositionApiService.getStoreValidationInfo(eq("STORE001"), eq(12345L)))
                    .thenReturn(Optional.empty());
            when(intlPositionApiService.getStoreByCode(eq("STORE001")))
                    .thenReturn(store); // 门店存在但未开放

            // Mock错误文件生成
            FdsUploadResult fdsUploadResult = new FdsUploadResult();
            fdsUploadResult.setUrl("http://example.com/error.xlsx");
            ExcelFileResource mockErrorFileResource = mock(ExcelFileResource.class);
            File mockErrorFile = mock(File.class);
            when(mockErrorFileResource.getFile()).thenReturn(mockErrorFile);
            excelFileUtilMock.when(() -> ExcelFileUtil.generateExcelFile(anyList(), eq(ImeiImportExcelData.class), anyString()))
                    .thenReturn(mockErrorFileResource);
            when(fdsService.upload(anyString(), any(File.class), eq(true)))
                    .thenReturn(fdsUploadResult);

            // Mock导入日志更新
            when(intlImportLogService.updateById(any())).thenReturn(true);

            // 执行测试
            CommonApiResponse<ImportDataResponse> response = imeiImportService.importImeiData(request);

            // 验证结果
            assertNotNull(response);
            assertEquals(0, response.getCode());
            assertNotNull(response.getData());
            assertEquals(1L, response.getData().getImportLogId());
            assertEquals(2, response.getData().getStatus());
            assertEquals("Validation failed.", response.getData().getErrorMsg());
            
            // 验证服务调用
            verify(intlPositionApiService).getStoreValidationInfo(eq("STORE001"), eq(12345L));
            verify(intlPositionApiService).getStoreByCode(eq("STORE001"));
            verify(fdsService).upload(anyString(), any(File.class), eq(true));
            verify(intlImportLogService, atLeast(1)).updateById(any());
        }
    }
}