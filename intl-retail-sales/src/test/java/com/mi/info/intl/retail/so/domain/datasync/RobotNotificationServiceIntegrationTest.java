package com.mi.info.intl.retail.so.domain.datasync;

import com.mi.info.intl.retail.so.domain.datasync.dto.DataDifferenceDto;
import com.mi.info.intl.retail.so.domain.datasync.enums.DataCompareResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * RobotNotificationService 集成测试
 * 测试消息构建逻辑，不依赖外部服务
 */
class RobotNotificationServiceIntegrationTest {

    private RobotNotificationService robotNotificationService;

    @BeforeEach
    void setUp() {
        robotNotificationService = new RobotNotificationService();
        // 设置测试配置
        ReflectionTestUtils.setField(robotNotificationService, "robotWebhookUrl", "https://test-webhook-url.com");
        ReflectionTestUtils.setField(robotNotificationService, "robotEnabled", true);
    }

    @Test
    void testBuildComparisonReportMessage_WithDifferences() {
        // 准备测试数据
        List<DataDifferenceDto> imeiStatistics = createTestImeiDifferences();
        List<DataDifferenceDto> qtyStatistics = createTestQtyDifferences();

        // 通过反射调用私有方法
        String message = (String) ReflectionTestUtils.invokeMethod(
                robotNotificationService, 
                "buildComparisonReportMessage", 
                imeiStatistics, 
                qtyStatistics
        );

        // 验证消息内容
        assertNotNull(message);
        assertTrue(message.contains("📊 **数据对比监控报告**"));
        assertTrue(message.contains("**IMEI差异分类统计：**"));
        assertTrue(message.contains("**QTY差异分类统计：**"));
        assertTrue(message.contains("零售系统单边有数据"));
        assertTrue(message.contains("RMS系统单边有数据"));
        assertTrue(message.contains("两边数据状态不一致"));
        assertTrue(message.contains("**处理建议：**"));
        
        // 验证不包含正常信息（因为有差异）
        assertFalse(message.contains("✅ **数据对比正常，未发现差异**"));
    }

    @Test
    void testBuildComparisonReportMessage_NoDifferences() {
        // 准备测试数据 - 无差异
        List<DataDifferenceDto> imeiStatistics = createEmptyDifferences();
        List<DataDifferenceDto> qtyStatistics = createEmptyDifferences();

        // 通过反射调用私有方法
        String message = (String) ReflectionTestUtils.invokeMethod(
                robotNotificationService, 
                "buildComparisonReportMessage", 
                imeiStatistics, 
                qtyStatistics
        );

        // 验证消息内容
        assertNotNull(message);
        assertTrue(message.contains("📊 **数据对比监控报告**"));
        assertTrue(message.contains("**IMEI差异分类统计：**"));
        assertTrue(message.contains("**QTY差异分类统计：**"));
        assertTrue(message.contains("✅ **数据对比正常，未发现差异**"));
        assertTrue(message.contains("**处理建议：**"));
    }

    @Test
    void testBuildComparisonReportMessage_NullInput() {
        // 通过反射调用私有方法
        String message = (String) ReflectionTestUtils.invokeMethod(
                robotNotificationService, 
                "buildComparisonReportMessage", 
                (List<DataDifferenceDto>) null, 
                (List<DataDifferenceDto>) null
        );

        // 验证消息内容
        assertNotNull(message);
        assertTrue(message.contains("📊 **数据对比监控报告**"));
        assertTrue(message.contains("**IMEI差异分类统计：**"));
        assertTrue(message.contains("**QTY差异分类统计：**"));
        assertTrue(message.contains("✅ **数据对比正常，未发现差异**"));
    }

    @Test
    void testBuildCategoryTable_WithData() {
        // 准备测试数据
        List<DataDifferenceDto> differences = createTestImeiDifferences();

        // 通过反射调用私有方法
        String table = (String) ReflectionTestUtils.invokeMethod(
                robotNotificationService, 
                "buildCategoryTable", 
                differences
        );

        // 验证表格内容
        assertNotNull(table);
        assertTrue(table.contains("| 差异类型 | 数量 | 数据ID列表 |"));
        assertTrue(table.contains("|----------|------|------------|"));
        assertTrue(table.contains("| 零售系统单边有数据 | 5 | 1001, 1002, 1003, 1004, 1005 |"));
        assertTrue(table.contains("| RMS系统单边有数据 | 3 | 2001, 2002, 2003 |"));
        assertTrue(table.contains("| 两边数据状态不一致 | 2 | 3001, 3002 |"));
    }

    @Test
    void testBuildCategoryTable_EmptyData() {
        // 准备测试数据
        List<DataDifferenceDto> differences = createEmptyDifferences();

        // 通过反射调用私有方法
        String table = (String) ReflectionTestUtils.invokeMethod(
                robotNotificationService, 
                "buildCategoryTable", 
                differences
        );

        // 验证表格内容
        assertNotNull(table);
        assertTrue(table.contains("| 差异类型 | 数量 | 数据ID列表 |"));
        assertTrue(table.contains("|----------|------|------------|"));
        assertTrue(table.contains("| 零售系统单边有数据 | 0 | 无 |"));
        assertTrue(table.contains("| RMS系统单边有数据 | 0 | 无 |"));
        assertTrue(table.contains("| 两边数据状态不一致 | 0 | 无 |"));
    }

    @Test
    void testBuildCategoryTable_NullInput() {
        // 通过反射调用私有方法
        String table = (String) ReflectionTestUtils.invokeMethod(
                robotNotificationService, 
                "buildCategoryTable", 
                (List<DataDifferenceDto>) null
        );

        // 验证表格内容
        assertNotNull(table);
        assertTrue(table.contains("| 差异类型 | 数量 | 数据ID列表 |"));
        assertTrue(table.contains("|----------|------|------------|"));
        assertTrue(table.contains("| 零售系统单边有数据 | 0 | 无 |"));
        assertTrue(table.contains("| RMS系统单边有数据 | 0 | 无 |"));
        assertTrue(table.contains("| 两边数据状态不一致 | 0 | 无 |"));
    }

    @Test
    void testIsAllZero_WithDifferences() {
        // 准备测试数据
        List<DataDifferenceDto> differences = createTestImeiDifferences();

        // 通过反射调用私有方法
        Boolean result = (Boolean) ReflectionTestUtils.invokeMethod(
                robotNotificationService, 
                "isAllZero", 
                differences
        );

        // 验证结果
        assertFalse(result);
    }

    @Test
    void testIsAllZero_NoDifferences() {
        // 准备测试数据
        List<DataDifferenceDto> differences = createEmptyDifferences();

        // 通过反射调用私有方法
        Boolean result = (Boolean) ReflectionTestUtils.invokeMethod(
                robotNotificationService, 
                "isAllZero", 
                differences
        );

        // 验证结果
        assertTrue(result);
    }

    @Test
    void testIsAllZero_NullInput() {
        // 通过反射调用私有方法
        Boolean result = (Boolean) ReflectionTestUtils.invokeMethod(
                robotNotificationService, 
                "isAllZero", 
                (List<DataDifferenceDto>) null
        );

        // 验证结果
        assertTrue(result);
    }

    @Test
    void testIsAllZero_EmptyList() {
        // 准备测试数据
        List<DataDifferenceDto> differences = new ArrayList<>();

        // 通过反射调用私有方法
        Boolean result = (Boolean) ReflectionTestUtils.invokeMethod(
                robotNotificationService, 
                "isAllZero", 
                differences
        );

        // 验证结果
        assertTrue(result);
    }

    @Test
    void testIsAllZero_PartialZero() {
        // 准备测试数据 - 部分为0
        List<DataDifferenceDto> differences = new ArrayList<>();
        differences.add(new DataDifferenceDto(DataCompareResult.RETAIL_ONLY, 0L, new ArrayList<>()));
        differences.add(new DataDifferenceDto(DataCompareResult.RMS_ONLY, 5L, Arrays.asList("1001", "1002")));
        differences.add(new DataDifferenceDto(DataCompareResult.STATUS_INCONSISTENT, 0L, new ArrayList<>()));

        // 通过反射调用私有方法
        Boolean result = (Boolean) ReflectionTestUtils.invokeMethod(
                robotNotificationService, 
                "isAllZero", 
                differences
        );

        // 验证结果
        assertFalse(result);
    }

    // 辅助方法：创建测试IMEI差异数据
    private List<DataDifferenceDto> createTestImeiDifferences() {
        List<DataDifferenceDto> differences = new ArrayList<>();
        
        // 零售系统单边有数据
        differences.add(new DataDifferenceDto(
                DataCompareResult.RETAIL_ONLY,
                5L,
                Arrays.asList("1001", "1002", "1003", "1004", "1005")
        ));
        
        // RMS系统单边有数据
        differences.add(new DataDifferenceDto(
                DataCompareResult.RMS_ONLY,
                3L,
                Arrays.asList("2001", "2002", "2003")
        ));
        
        // 状态不一致
        differences.add(new DataDifferenceDto(
                DataCompareResult.STATUS_INCONSISTENT,
                2L,
                Arrays.asList("3001", "3002")
        ));
        
        return differences;
    }

    // 辅助方法：创建测试QTY差异数据
    private List<DataDifferenceDto> createTestQtyDifferences() {
        List<DataDifferenceDto> differences = new ArrayList<>();
        
        // 零售系统单边有数据
        differences.add(new DataDifferenceDto(
                DataCompareResult.RETAIL_ONLY,
                2L,
                Arrays.asList("4001", "4002")
        ));
        
        // RMS系统单边有数据
        differences.add(new DataDifferenceDto(
                DataCompareResult.RMS_ONLY,
                1L,
                Arrays.asList("5001")
        ));
        
        // 状态不一致
        differences.add(new DataDifferenceDto(
                DataCompareResult.STATUS_INCONSISTENT,
                0L,
                new ArrayList<>()
        ));
        
        return differences;
    }

    // 辅助方法：创建空差异数据
    private List<DataDifferenceDto> createEmptyDifferences() {
        List<DataDifferenceDto> differences = new ArrayList<>();
        
        // 所有类别都为0
        differences.add(new DataDifferenceDto(DataCompareResult.RETAIL_ONLY, 0L, new ArrayList<>()));
        differences.add(new DataDifferenceDto(DataCompareResult.RMS_ONLY, 0L, new ArrayList<>()));
        differences.add(new DataDifferenceDto(DataCompareResult.STATUS_INCONSISTENT, 0L, new ArrayList<>()));
        
        return differences;
    }
}
