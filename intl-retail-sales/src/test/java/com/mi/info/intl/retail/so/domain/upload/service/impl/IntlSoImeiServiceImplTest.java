package com.mi.info.intl.retail.so.domain.upload.service.impl;

import com.mi.info.intl.retail.api.fieldforce.retailer.IntlRetailerApiService;
import com.mi.info.intl.retail.api.fieldforce.retailer.dto.IntlRetailerDTO;
import com.mi.info.intl.retail.api.fieldforce.user.UserApiService;
import com.mi.info.intl.retail.api.front.dto.RmsPositionInfoRes;
import com.mi.info.intl.retail.api.front.dto.RmsStoreInfoDto;
import com.mi.info.intl.retail.api.front.position.IntlPositionApiService;
import com.mi.info.intl.retail.api.front.position.dto.IntlPositionDTO;
import com.mi.info.intl.retail.api.front.store.RmsStoreService;
import com.mi.info.intl.retail.component.ComponentLocator;
import com.mi.info.intl.retail.so.app.mq.SyncSoToEsProducer;
import com.mi.info.intl.retail.so.app.mq.dto.RmsSyncImeiData;
import com.mi.info.intl.retail.so.app.provider.enums.DataFromEnum;
import com.mi.info.intl.retail.so.domain.datasync.entity.IntlDatasyncLog;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoImei;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoOrgInfo;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoUserInfo;
import com.mi.info.intl.retail.so.infra.database.mapper.datasync.IntlDatasyncLogMapper;
import com.mi.info.intl.retail.so.infra.database.mapper.datasync.RmsStockDataSyncMapper;
import com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlSoImeiMapper;
import com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlSoOrgInfoMapper;
import com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlSoUserInfoMapper;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.fail;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;

import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(MockitoExtension.class)
class IntlSoImeiServiceImplTest {

    @Mock
    private UserApiService userApiService;
    @Mock
    private IntlDatasyncLogMapper intlDatasyncLogMapper;
    @Mock
    private IntlSoOrgInfoMapper intlSoOrgInfoMapper;
    @Mock
    private IntlPositionApiService positionApiService;
    @Mock
    private IntlSoUserInfoMapper intlSoUserInfoMapper;
    @Mock
    private RmsStoreService rmsStoreService;
    @Mock
    private IntlRetailerApiService retailerApiService;
    @Mock
    private RmsStockDataSyncMapper rmsStockDataSyncMapper;
    @Mock
    private SyncSoToEsProducer syncSoToEsProducer;
    @Mock
    private IntlSoImeiMapper intlSoImeiMapper;

    private ThreadPoolTaskExecutor executor;

    @InjectMocks
    private IntlSoImeiServiceImpl service;

    @BeforeEach
    void setUp() throws Exception {
        setField(service, "baseMapper", intlSoImeiMapper);
        executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(1);
        executor.setMaxPoolSize(1);
        executor.initialize();
        setField(service, "threadPoolTaskExecutor", executor);
    }

    @Test
    @DisplayName("doImeiSave 正常路径：插入主表与日志")
    void testDoImeiSave_success() throws Exception {
        RmsSyncImeiData data = buildImeiData();
        data.setIsStockData(0);

        when(rmsStoreService.getStoreInfoByStoreCode(anyString())).thenReturn(Optional.of(new RmsStoreInfoDto()));
        when(rmsStoreService.getPositionIfoByPositionCode(anyString())).thenReturn(
                Optional.of(new RmsPositionInfoRes()));
        when(retailerApiService.getRetailerByRetailerCode(any(IntlPositionDTO.class))).thenReturn(
                Optional.of(new IntlRetailerDTO()));
        when(userApiService.getUserListByMiIds(anyList())).thenReturn(Optional.empty());

        try (MockedStatic<ComponentLocator> mockedStatic = mockStatic(ComponentLocator.class)) {
            Class<?> converterType = Class.forName("com.mi.info.intl.retail.component.convertor.ConverterFacade");
            Object converter = mock(converterType);
            mockedStatic.when(ComponentLocator::getConverter).thenReturn(converter);

            doAnswer(invocation -> {
                IntlSoUserInfo u = invocation.getArgument(0);
                setId(u, 11L);
                return 1;
            })
                    .when(intlSoUserInfoMapper).insert(any(IntlSoUserInfo.class));
            doAnswer(invocation -> {
                IntlSoOrgInfo o = invocation.getArgument(0);
                setId(o, 22L);
                return 1;
            })
                    .when(intlSoOrgInfoMapper).insert(any(IntlSoOrgInfo.class));
            doAnswer(invocation -> {
                IntlSoImei imei = invocation.getArgument(0);
                setId(imei, 33L);
                return 1;
            })
                    .when(intlSoImeiMapper).insert(any(IntlSoImei.class));
            when(intlDatasyncLogMapper.insert(any(IntlDatasyncLog.class))).thenReturn(1);

            service.doImeiSave(data);

            verify(intlSoUserInfoMapper, times(1)).insert(any(IntlSoUserInfo.class));
            verify(intlSoOrgInfoMapper, times(1)).insert(any(IntlSoOrgInfo.class));
            verify(intlSoImeiMapper, times(1)).insert(any(IntlSoImei.class));
            verify(intlDatasyncLogMapper, times(1)).insert(any(IntlDatasyncLog.class));
            verify(rmsStockDataSyncMapper, never()).updateImeiSyncStatus(any());
        } catch (ClassNotFoundException e) {
            fail("ConverterFacade class not found: " + e.getMessage());
        }
    }

    @Test
    @DisplayName("doImeiBatchSave：rmsId 为空直接走主流程，批量插入与日志")
    void testDoImeiBatchSave_basic() throws Exception {
        RmsSyncImeiData d1 = buildImeiData();
        d1.setRmsId(null);
        d1.setIsStockData(0);
        RmsSyncImeiData d2 = buildImeiData();
        d2.setRmsId(null);
        d2.setIsStockData(0);
        List<RmsSyncImeiData> list = Arrays.asList(d1, d2);

        org.mockito.Mockito.lenient().when(rmsStoreService.getStoreInfoByStoreCode(anyString()))
                .thenReturn(Optional.empty());
        org.mockito.Mockito.lenient().when(rmsStoreService.getPositionIfoByPositionCode(anyString()))
                .thenReturn(Optional.empty());
        org.mockito.Mockito.lenient().when(retailerApiService.getRetailerByRetailerCode(any(IntlPositionDTO.class)))
                .thenReturn(Optional.empty());
        when(userApiService.getUserListByMiIds(anyList())).thenReturn(Optional.empty());

        try (MockedStatic<ComponentLocator> mockedStatic = mockStatic(ComponentLocator.class)) {
            Class<?> converterType = Class.forName("com.mi.info.intl.retail.component.convertor.ConverterFacade");
            Object converter = mock(converterType);
            mockedStatic.when(ComponentLocator::getConverter).thenReturn(converter);

            // 批量查询使用 Mockito.lenient
            org.mockito.Mockito.lenient().when(rmsStoreService.batchGetStoreInfoByStoreCode(anyList()))
                    .thenReturn(new HashMap<>());
            org.mockito.Mockito.lenient().when(positionApiService.getPositionInfoByPositionCodes(anyList()))
                    .thenReturn(new HashMap<>());
            org.mockito.Mockito.lenient().when(retailerApiService.getRetailersByRetailerCodes(anyList()))
                    .thenReturn(new HashMap<>());

            doNothing().when(intlSoImeiMapper).batchInsert(anyList());
            doNothing().when(intlDatasyncLogMapper).batchInsert(anyList());

            service.doImeiBatchSave(list);

            verify(intlSoImeiMapper, times(1)).batchInsert(anyList());
            verify(intlDatasyncLogMapper, times(1)).batchInsert(anyList());
            verify(rmsStockDataSyncMapper, never()).updateImeiSyncStatus(any());
        } catch (ClassNotFoundException e) {
            fail("ConverterFacade class not found: " + e.getMessage());
        }
    }

    @Test
    @DisplayName("doImeiSave 存量分支：更新存量同步状态")
    void testDoImeiSave_stockData_updatesStatus() throws Exception {
        RmsSyncImeiData data = buildImeiData();
        data.setIsStockData(1);
        data.setIdNew(999L);

        when(rmsStoreService.getStoreInfoByStoreCode(anyString())).thenReturn(Optional.empty());
        when(rmsStoreService.getPositionIfoByPositionCode(anyString())).thenReturn(Optional.empty());
        when(retailerApiService.getRetailerByRetailerCode(any(IntlPositionDTO.class))).thenReturn(Optional.empty());
        when(userApiService.getUserListByMiIds(anyList())).thenReturn(Optional.empty());

        try (MockedStatic<ComponentLocator> mockedStatic = mockStatic(ComponentLocator.class)) {
            Class<?> converterType = Class.forName("com.mi.info.intl.retail.component.convertor.ConverterFacade");
            Object converter = mock(converterType);
            mockedStatic.when(ComponentLocator::getConverter).thenReturn(converter);

            doAnswer(invocation -> {
                IntlSoUserInfo u = invocation.getArgument(0);
                setId(u, 11L);
                return 1;
            })
                    .when(intlSoUserInfoMapper).insert(any(IntlSoUserInfo.class));
            doAnswer(invocation -> {
                IntlSoOrgInfo o = invocation.getArgument(0);
                setId(o, 22L);
                return 1;
            })
                    .when(intlSoOrgInfoMapper).insert(any(IntlSoOrgInfo.class));
            doAnswer(invocation -> {
                IntlSoImei imei = invocation.getArgument(0);
                setId(imei, 33L);
                return 1;
            })
                    .when(intlSoImeiMapper).insert(any(IntlSoImei.class));
            when(intlDatasyncLogMapper.insert(any(IntlDatasyncLog.class))).thenReturn(1);

            service.doImeiSave(data);

            verify(rmsStockDataSyncMapper, times(1)).updateImeiSyncStatus(any());
        } catch (ClassNotFoundException e) {
            fail("ConverterFacade class not found: " + e.getMessage());
        }
    }

    @Test
    @DisplayName("doImeiBatchSave 空列表：不进行任何数据库操作")
    void testDoImeiBatchSave_empty_noop() {
        service.doImeiBatchSave(Collections.emptyList());
        verify(intlSoImeiMapper, never()).batchInsert(anyList());
        verify(intlDatasyncLogMapper, never()).batchInsert(anyList());
        verify(rmsStockDataSyncMapper, never()).updateImeiSyncStatus(any());
    }

    @Test
    @DisplayName("batchGetImeiByRmsId 传 retailIds：调用 selectByRetailerIds")
    void testBatchGetImeiByRmsId_retailIds() {
        when(intlSoImeiMapper.selectByRetailerIds(anyList())).thenReturn(Collections.emptyList());
        List<IntlSoImei> res = service.batchGetImeiByRmsId(Arrays.asList(1L, 2L), null);
        assertNotNull(res);
        verify(intlSoImeiMapper, times(1)).selectByRetailerIds(eq(Arrays.asList(1L, 2L)));
        verify(intlSoImeiMapper, never()).selectByRmsIds(anyList());
    }

    @Test
    @DisplayName("batchGetImeiByRmsId 传 rmsIds：调用 selectByRmsIds")
    void testBatchGetImeiByRmsId_rmsIds() {
        when(intlSoImeiMapper.selectByRmsIds(anyList())).thenReturn(Collections.emptyList());
        List<IntlSoImei> res = service.batchGetImeiByRmsId(null, Arrays.asList("A", "B"));
        assertNotNull(res);
        verify(intlSoImeiMapper, times(1)).selectByRmsIds(eq(Arrays.asList("A", "B")));
        verify(intlSoImeiMapper, never()).selectByRetailerIds(anyList());
    }

    @Test
    @DisplayName("batchGetImeiByRmsId 入参为空：返回空列表")
    void testBatchGetImeiByRmsId_emptyParams() {
        List<IntlSoImei> res = service.batchGetImeiByRmsId(Collections.emptyList(), Collections.emptyList());
        assertNotNull(res);
        assertTrue(res.isEmpty());
        verify(intlSoImeiMapper, never()).selectByRetailerIds(anyList());
        verify(intlSoImeiMapper, never()).selectByRmsIds(anyList());
    }

    @Test
    @DisplayName("getNeedVerifyImeiList 非法参数：直接返回空")
    void testGetNeedVerifyImeiList_invalid() {
        assertTrue(service.getNeedVerifyImeiList(null, 1, DataFromEnum.MIRETAIL.getValue(), 10).isEmpty());
        assertTrue(service.getNeedVerifyImeiList(1, null, DataFromEnum.MIRETAIL.getValue(), 10).isEmpty());
        assertTrue(service.getNeedVerifyImeiList(1, 1, DataFromEnum.MIRETAIL.getValue(), null).isEmpty());
        assertTrue(service.getNeedVerifyImeiList(1, 1, DataFromEnum.MIRETAIL.getValue(), 0).isEmpty());
        assertTrue(service.getNeedVerifyImeiList(1, 1, null, 1).isEmpty());
    }

    @Test
    @DisplayName("batchSoftUpdateById 空列表：返回0")
    void testBatchSoftUpdateById_empty() {
        int res = service.batchSoftUpdateById(Collections.emptyList());
        assertEquals(0, res);
        verify(intlSoImeiMapper, never()).batchUpdateById(anyList());
    }

    @Test
    @DisplayName("batchSoftUpdateById 有数据：调用mapper并返回结果")
    void testBatchSoftUpdateById_update() {
        when(intlSoImeiMapper.batchUpdateById(anyList())).thenReturn(2);
        int res = service.batchSoftUpdateById(Arrays.asList(new IntlSoImei(), new IntlSoImei()));
        assertEquals(2, res);
        verify(intlSoImeiMapper, times(1)).batchUpdateById(anyList());
    }

    private RmsSyncImeiData buildImeiData() {
        RmsSyncImeiData data = new RmsSyncImeiData();
        data.setRmsId("RMS001");
        data.setRetailId("1001");
        data.setStoreCodeRMS("STORE1");
        data.setPositionCodeRMS("POS1");
        data.setRetailerCode("RET1");
        data.setCreatedbyMiid("101");
        data.setSalesManMiid("202");
        data.setReportType(1);
        data.setVerifyResult(1);
        data.setModifiedon(System.currentTimeMillis() + 1000);
        data.setCreatedTime(System.currentTimeMillis());
        return data;
    }

    private static void setField(Object target, String name, Object value) {
        Field f = ReflectionUtils.findField(target.getClass(), name);
        assertNotNull(f, "field not found: " + name);
        ReflectionUtils.makeAccessible(f);
        ReflectionUtils.setField(f, target, value);
    }

    private static void setId(Object entity, Long id) throws Exception {
        Field f = ReflectionUtils.findField(entity.getClass(), "id");
        if (f != null) {
            ReflectionUtils.makeAccessible(f);
            ReflectionUtils.setField(f, entity, id);
        }
    }
} 