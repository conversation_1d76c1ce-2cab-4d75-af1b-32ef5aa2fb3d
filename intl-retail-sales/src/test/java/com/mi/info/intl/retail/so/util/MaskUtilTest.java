package com.mi.info.intl.retail.so.util;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * MaskUtil 单元测试
 *
 * <AUTHOR>
 * @date 2025/7/28
 */
class MaskUtilTest {

    @Test
    void testMaskString_NormalString() {
        // 测试正常字符串掩码
        String input = "861234567890123";
        String expected = "86123*****90123";
        String result = MaskUtil.maskString(input);
        assertEquals(expected, result);
    }

    @Test
    void testMaskString_LongString() {
        // 测试长字符串掩码
        String input = "1234567890123456789012345";
        String expected = "12345**********12345";
        String result = MaskUtil.maskString(input);
        assertEquals(expected, result);
    }

    @Test
    void testMaskString_ExactlyTenCharacters() {
        // 测试恰好10个字符的字符串
        String input = "1234567890";
        String result = MaskUtil.maskString(input);
        assertEquals(input, result); // 应该不进行掩码处理
    }

    @Test
    void testMaskString_LessThanTenCharacters() {
        // 测试少于10个字符的字符串
        String input = "123456789";
        String result = MaskUtil.maskString(input);
        assertEquals(input, result); // 应该不进行掩码处理
    }

    @Test
    void testMaskString_ElevenCharacters() {
        // 测试11个字符的字符串
        String input = "12345678901";
        String expected = "12345*78901";
        String result = MaskUtil.maskString(input);
        assertEquals(expected, result);
    }

    @Test
    void testMaskString_EmptyString() {
        // 测试空字符串
        String input = "";
        String result = MaskUtil.maskString(input);
        assertEquals(input, result);
    }

    @Test
    void testMaskString_NullString() {
        // 测试null字符串
        String input = null;
        String result = MaskUtil.maskString(input);
        assertNull(result);
    }

    @Test
    void testMaskString_BlankString() {
        // 测试空白字符串
        String input = "   ";
        String result = MaskUtil.maskString(input);
        assertEquals(input, result); // 空白字符串应该不进行掩码处理
    }

    @Test
    void testMaskString_SingleCharacter() {
        // 测试单个字符
        String input = "A";
        String result = MaskUtil.maskString(input);
        assertEquals(input, result);
    }

    @Test
    void testMaskString_SpecialCharacters() {
        // 测试包含特殊字符的字符串
        String input = "ABC!@#$%^&*()DEF";
        String expected = "ABC!@*****&*()DEF";
        String result = MaskUtil.maskString(input);
        assertEquals(expected, result);
    }

    @Test
    void testMaskString_ChineseCharacters() {
        // 测试包含中文字符的字符串
        String input = "测试字符串掩码处理功能测试";
        String expected = "测试字符串*****能测试";
        String result = MaskUtil.maskString(input);
        assertEquals(expected, result);
    }

    @Test
    void testMaskString_MixedCharacters() {
        // 测试混合字符类型
        String input = "Test123测试456";
        String expected = "Test1*****56";
        String result = MaskUtil.maskString(input);
        assertEquals(expected, result);
    }

    @Test
    void testMaskString_NumbersOnly() {
        // 测试纯数字字符串
        String input = "12345678901234567890";
        String expected = "12345**********67890";
        String result = MaskUtil.maskString(input);
        assertEquals(expected, result);
    }

    @Test
    void testMaskString_LettersOnly() {
        // 测试纯字母字符串
        String input = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        String expected = "ABCDE**********VWXYZ";
        String result = MaskUtil.maskString(input);
        assertEquals(expected, result);
    }
}
