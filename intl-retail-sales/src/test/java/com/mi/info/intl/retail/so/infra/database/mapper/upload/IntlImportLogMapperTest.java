package com.mi.info.intl.retail.so.infra.database.mapper.upload;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlImportLog;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * IntlImportLogMapper单元测试
 *
 * <AUTHOR> Assistant
 * @date 2025/09/29
 */
@ExtendWith(MockitoExtension.class)
public class IntlImportLogMapperTest {

    @Mock
    private IntlImportLogMapper intlImportLogMapper;

    private Page<IntlImportLog> testPage;
    private IntlImportLog testLog;

    @BeforeEach
    public void setUp() {
        setupTestData();
    }

    private void setupTestData() {
        testPage = new Page<>(1, 20);
        
        testLog = new IntlImportLog();
        testLog.setId(1L);
        testLog.setTaskName("测试任务");
        testLog.setDataSource(1);
        testLog.setType(1);
        testLog.setFileName("test.xlsx");
        testLog.setStatus(1);
        testLog.setTotalCount(100);
        testLog.setFinishCount(100);
        testLog.setImportDuration(1);
        testLog.setOperator("测试用户");
        testLog.setCreatedAt(System.currentTimeMillis());
        testLog.setActionType(1);
        testLog.setSourceFileUrl("http://test.com/source.xlsx");
        testLog.setResultFileUrl("http://test.com/result.xlsx");
    }

    @Test
    @DisplayName("分页查询导入日志列表 - 无过滤条件")
    public void selectImportLogList_NoFilters() {
        // 准备数据
        testPage.setTotal(1);
        testPage.setRecords(Arrays.asList(testLog));

        // mock
        when(intlImportLogMapper.selectImportLogList(any(Page.class), isNull(), isNull(), isNull(), 
                isNull(), isNull(), isNull(), isNull(), isNull()))
                .thenReturn(testPage);

        // 执行测试
        Page<IntlImportLog> result = intlImportLogMapper.selectImportLogList(
                new Page<>(1, 20), null, null, null, null, null, null, null, null);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getTotal());
        assertEquals(1, result.getRecords().size());
        assertEquals("测试任务", result.getRecords().get(0).getTaskName());

        // 验证mock调用
        verify(intlImportLogMapper).selectImportLogList(any(Page.class), isNull(), isNull(), isNull(), 
                isNull(), isNull(), isNull(), isNull(), isNull());
    }

    @Test
    @DisplayName("分页查询导入日志列表 - 按任务名称模糊查询")
    public void selectImportLogList_ByTaskName() {
        // 准备数据
        String taskName = "销售";
        testPage.setTotal(1);
        testPage.setRecords(Arrays.asList(testLog));

        // mock
        when(intlImportLogMapper.selectImportLogList(any(Page.class), eq(taskName), isNull(), isNull(), 
                isNull(), isNull(), isNull(), isNull(), isNull()))
                .thenReturn(testPage);

        // 执行测试
        Page<IntlImportLog> result = intlImportLogMapper.selectImportLogList(
                new Page<>(1, 20), taskName, null, null, null, null, null, null, null);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getTotal());
        assertEquals(1, result.getRecords().size());

        // 验证mock调用
        verify(intlImportLogMapper).selectImportLogList(any(Page.class), eq(taskName), isNull(), isNull(), 
                isNull(), isNull(), isNull(), isNull(), isNull());
    }

    @Test
    @DisplayName("分页查询导入日志列表 - 按数据源过滤")
    public void selectImportLogList_ByDataSource() {
        // 准备数据
        List<String> dataSource = Arrays.asList("SalesApp", "InventorySys");
        testPage.setTotal(1);
        testPage.setRecords(Arrays.asList(testLog));

        // mock
        when(intlImportLogMapper.selectImportLogList(any(Page.class), isNull(), eq(dataSource), isNull(), 
                isNull(), isNull(), isNull(), isNull(), isNull()))
                .thenReturn(testPage);

        // 执行测试
        Page<IntlImportLog> result = intlImportLogMapper.selectImportLogList(
                new Page<>(1, 20), null, dataSource, null, null, null, null, null, null);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getTotal());

        // 验证mock调用
        verify(intlImportLogMapper).selectImportLogList(any(Page.class), isNull(), eq(dataSource), isNull(), 
                isNull(), isNull(), isNull(), isNull(), isNull());
    }

    @Test
    @DisplayName("分页查询导入日志列表 - 按状态过滤")
    public void selectImportLogList_ByStatus() {
        // 准备数据
        List<Integer> status = Arrays.asList(1, 2);
        testPage.setTotal(1);
        testPage.setRecords(Arrays.asList(testLog));

        // mock
        when(intlImportLogMapper.selectImportLogList(any(Page.class), isNull(), isNull(), isNull(), 
                eq(status), isNull(), isNull(), isNull(), isNull()))
                .thenReturn(testPage);

        // 执行测试
        Page<IntlImportLog> result = intlImportLogMapper.selectImportLogList(
                new Page<>(1, 20), null, null, null, status, null, null, null, null);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getTotal());

        // 验证mock调用
        verify(intlImportLogMapper).selectImportLogList(any(Page.class), isNull(), isNull(), isNull(), 
                eq(status), isNull(), isNull(), isNull(), isNull());
    }

    @Test
    @DisplayName("分页查询导入日志列表 - 按导入耗时过滤")
    public void selectImportLogList_ByImportDuration() {
        // 准备数据
        List<Integer> importDuration = Arrays.asList(0, 1, 2);
        testPage.setTotal(1);
        testPage.setRecords(Arrays.asList(testLog));

        // mock
        when(intlImportLogMapper.selectImportLogList(any(Page.class), isNull(), isNull(), isNull(), 
                isNull(), eq(importDuration), isNull(), isNull(), isNull()))
                .thenReturn(testPage);

        // 执行测试
        Page<IntlImportLog> result = intlImportLogMapper.selectImportLogList(
                new Page<>(1, 20), null, null, null, null, importDuration, null, null, null);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getTotal());

        // 验证mock调用
        verify(intlImportLogMapper).selectImportLogList(any(Page.class), isNull(), isNull(), isNull(), 
                isNull(), eq(importDuration), isNull(), isNull(), isNull());
    }

    @Test
    @DisplayName("分页查询导入日志列表 - 按时间范围过滤")
    public void selectImportLogList_ByTimeRange() {
        // 准备数据
        Long startTime = 1704067200000L;
        Long endTime = 1706745599000L;
        testPage.setTotal(1);
        testPage.setRecords(Arrays.asList(testLog));

        // mock
        when(intlImportLogMapper.selectImportLogList(any(Page.class), isNull(), isNull(), isNull(), 
                isNull(), isNull(), eq(startTime), eq(endTime), isNull()))
                .thenReturn(testPage);

        // 执行测试
        Page<IntlImportLog> result = intlImportLogMapper.selectImportLogList(
                new Page<>(1, 20), null, null, null, null, null, startTime, endTime, null);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getTotal());

        // 验证mock调用
        verify(intlImportLogMapper).selectImportLogList(any(Page.class), isNull(), isNull(), isNull(), 
                isNull(), isNull(), eq(startTime), eq(endTime), isNull());
    }

    @Test
    @DisplayName("分页查询导入日志列表 - 按导入类型过滤")
    public void selectImportLogList_ByImportTypes() {
        // 准备数据
        List<Integer> importTypes = Arrays.asList(1, 2);
        testPage.setTotal(1);
        testPage.setRecords(Arrays.asList(testLog));

        // mock
        when(intlImportLogMapper.selectImportLogList(any(Page.class), isNull(), isNull(), isNull(), 
                isNull(), isNull(), isNull(), isNull(), eq(importTypes)))
                .thenReturn(testPage);

        // 执行测试
        Page<IntlImportLog> result = intlImportLogMapper.selectImportLogList(
                new Page<>(1, 20), null, null, null, null, null, null, null, importTypes);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getTotal());

        // 验证mock调用
        verify(intlImportLogMapper).selectImportLogList(any(Page.class), isNull(), isNull(), isNull(), 
                isNull(), isNull(), isNull(), isNull(), eq(importTypes));
    }

    @Test
    @DisplayName("分页查询导入日志列表 - 按日志ID查询")
    public void selectImportLogList_ByLogId() {
        // 准备数据
        Long logId = 123L;
        testPage.setTotal(1);
        testPage.setRecords(Arrays.asList(testLog));

        // mock
        when(intlImportLogMapper.selectImportLogList(any(Page.class), isNull(), isNull(), eq(logId), 
                isNull(), isNull(), isNull(), isNull(), isNull()))
                .thenReturn(testPage);

        // 执行测试
        Page<IntlImportLog> result = intlImportLogMapper.selectImportLogList(
                new Page<>(1, 20), null, null, logId, null, null, null, null, null);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getTotal());

        // 验证mock调用
        verify(intlImportLogMapper).selectImportLogList(any(Page.class), isNull(), isNull(), eq(logId), 
                isNull(), isNull(), isNull(), isNull(), isNull());
    }

    @Test
    @DisplayName("分页查询导入日志列表 - 组合条件查询")
    public void selectImportLogList_CombinedFilters() {
        // 准备数据
        String taskName = "销售数据";
        List<String> dataSource = Arrays.asList("SalesApp");
        List<Integer> status = Arrays.asList(1);
        List<Integer> importDuration = Arrays.asList(0, 1);
        Long startTime = 1704067200000L;
        Long endTime = 1706745599000L;
        List<Integer> importTypes = Arrays.asList(1);
        
        testPage.setTotal(1);
        testPage.setRecords(Arrays.asList(testLog));

        // mock
        when(intlImportLogMapper.selectImportLogList(any(Page.class), eq(taskName), eq(dataSource), isNull(), 
                eq(status), eq(importDuration), eq(startTime), eq(endTime), eq(importTypes)))
                .thenReturn(testPage);

        // 执行测试
        Page<IntlImportLog> result = intlImportLogMapper.selectImportLogList(
                new Page<>(1, 20), taskName, dataSource, null, status, importDuration, startTime, endTime, importTypes);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getTotal());

        // 验证mock调用
        verify(intlImportLogMapper).selectImportLogList(any(Page.class), eq(taskName), eq(dataSource), isNull(), 
                eq(status), eq(importDuration), eq(startTime), eq(endTime), eq(importTypes));
    }

    @Test
    @DisplayName("分页查询导入日志列表 - 空结果")
    public void selectImportLogList_EmptyResult() {
        // 准备数据
        testPage.setTotal(0);
        testPage.setRecords(Collections.emptyList());

        // mock
        when(intlImportLogMapper.selectImportLogList(any(Page.class), any(), any(), any(), 
                any(), any(), any(), any(), any()))
                .thenReturn(testPage);

        // 执行测试
        Page<IntlImportLog> result = intlImportLogMapper.selectImportLogList(
                new Page<>(1, 20), "不存在的任务", null, null, null, null, null, null, null);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getTotal());
        assertTrue(result.getRecords().isEmpty());

        // 验证mock调用
        verify(intlImportLogMapper).selectImportLogList(any(Page.class), any(), any(), any(), 
                any(), any(), any(), any(), any());
    }
}
