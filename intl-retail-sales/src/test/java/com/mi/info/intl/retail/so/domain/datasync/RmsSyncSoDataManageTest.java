package com.mi.info.intl.retail.so.domain.datasync;

import com.alibaba.fastjson.JSONObject;
import com.mi.info.intl.retail.exception.BizException;
import com.mi.info.intl.retail.service.DistributionLock;
import com.mi.info.intl.retail.service.DistributionLockService;
import com.mi.info.intl.retail.so.app.mq.SyncSoToEsProducer;
import com.mi.info.intl.retail.so.app.mq.dto.RmsSyncImeiData;
import com.mi.info.intl.retail.so.app.mq.dto.RmsSyncQtyData;
import com.mi.info.intl.retail.so.app.mq.dto.RmsSyncSoDataReqDto;
import com.mi.info.intl.retail.so.domain.datasync.enums.DataFromEnum;
import com.mi.info.intl.retail.so.domain.datasync.enums.DataSyncDataTypeEnum;
import com.mi.info.intl.retail.so.domain.datasync.enums.DataSyncOperateTypeTypeEnum;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoImei;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoQty;
import com.mi.info.intl.retail.so.domain.upload.service.IntlSoImeiService;
import com.mi.info.intl.retail.so.domain.upload.service.IntlSoQtyService;
import com.xiaomi.cnzone.commons.exception.CommonBusinessException;
import static java.util.Collections.emptyList;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * RmsSyncSoDataManage 单元测试
 */
@ExtendWith(MockitoExtension.class)
class RmsSyncSoDataManageTest {

    @Mock
    private DistributionLockService distributionLockService;

    @Mock
    private IntlSoImeiService intlSoImeiService;

    @Mock
    private IntlSoQtyService qtyService;

    @Mock
    private SyncSoToEsProducer syncSoToEsProducer;

    @Mock
    private DistributionLock distributionLock;

    @InjectMocks
    private RmsSyncSoDataManage rmsSyncSoDataManage;

    private RmsSyncSoDataReqDto imeiRequest;
    private RmsSyncSoDataReqDto qtyRequest;
    private RmsSyncImeiData imeiData;
    private RmsSyncQtyData qtyData;

    @BeforeEach
    void setUp() {
        // 初始化IMEI测试数据
        imeiData = new RmsSyncImeiData();
        imeiData.setId(1L);
        imeiData.setRmsId("RMS001");
        imeiData.setRetailId("1001");
        imeiData.setDataFrom(DataFromEnum.RMS.getCode());

        imeiRequest = new RmsSyncSoDataReqDto();
        imeiRequest.setType(DataSyncDataTypeEnum.IMEI.getMessage());
        imeiRequest.setOperateType(DataSyncOperateTypeTypeEnum.REPORT_VERIFICATION.getMessage());
        imeiRequest.setData(JSONObject.parseObject(JSONObject.toJSONString(imeiData)));
        imeiRequest.setFields(Arrays.asList("status", "remark"));

        // 初始化QTY测试数据
        qtyData = new RmsSyncQtyData();
        qtyData.setId(1L);
        qtyData.setRmsId("RMS001");

        qtyRequest = new RmsSyncSoDataReqDto();
        qtyRequest.setType(DataSyncDataTypeEnum.QTY.getMessage());
        qtyRequest.setOperateType(DataSyncOperateTypeTypeEnum.CREATE.getMessage());
        qtyRequest.setData(JSONObject.parseObject(JSONObject.toJSONString(qtyData)));
    }

    @Test
    @DisplayName("保存IMEI数据-新增场景")
    void saveDb_ImeiCreate_Success() throws Exception {
        // 准备测试数据
        imeiRequest.setOperateType(DataSyncOperateTypeTypeEnum.CREATE.getMessage());

        // Mock锁服务
        when(distributionLockService.tryLock(anyString(), anyString())).thenReturn(distributionLock);

        // Mock查询IMEI不存在
        when(intlSoImeiService.checkImeiExist(any(), any())).thenReturn(null);

        // Mock保存IMEI
        doNothing().when(intlSoImeiService).doImeiSave(any(RmsSyncImeiData.class));

        // Mock发送ES消息
        doNothing().when(syncSoToEsProducer).sendSyncEsMsg(any(), any());

        // 执行测试
        rmsSyncSoDataManage.saveDb(imeiRequest);

        // 验证调用
        verify(distributionLockService).tryLock(anyString(), eq("RMS001"));
        verify(intlSoImeiService).checkImeiExist(null, "RMS001");
        verify(intlSoImeiService).doImeiSave(any(RmsSyncImeiData.class));
        verify(syncSoToEsProducer).sendSyncEsMsg(eq(DataSyncDataTypeEnum.IMEI), eq(1L));
    }

    @Test
    @DisplayName("保存IMEI数据-更新场景")
    void saveDb_ImeiUpdate_Success() throws Exception {
        // 准备测试数据
        IntlSoImei existingImei = new IntlSoImei();
        existingImei.setId(1L);
        existingImei.setRmsId("RMS001");

        // Mock锁服务
        when(distributionLockService.tryLock(anyString(), anyString())).thenReturn(distributionLock);

        // Mock查询IMEI存在
        when(intlSoImeiService.checkImeiExist(any(), any())).thenReturn(existingImei);

        // Mock更新IMEI
        doNothing().when(intlSoImeiService).doImeiUpdate(anyString(), anyList(), any(RmsSyncImeiData.class));

        // Mock发送ES消息
        doNothing().when(syncSoToEsProducer).sendSyncEsMsg(any(), any());

        // 执行测试
        rmsSyncSoDataManage.saveDb(imeiRequest);

        // 验证调用
        verify(intlSoImeiService).doImeiUpdate(
                eq(DataSyncOperateTypeTypeEnum.REPORT_VERIFICATION.getMessage()),
                eq(Arrays.asList("status", "remark")),
                any(RmsSyncImeiData.class)
        );
    }

    @Test
    @DisplayName("保存IMEI数据-已存在但操作类型为CREATE")
    void saveDb_ImeiExistsButCreate_NoAction() throws Exception {
        // 准备测试数据
        imeiRequest.setOperateType(DataSyncOperateTypeTypeEnum.CREATE.getMessage());
        IntlSoImei existingImei = new IntlSoImei();
        existingImei.setId(1L);

        // Mock锁服务
        when(distributionLockService.tryLock(anyString(), anyString())).thenReturn(distributionLock);

        // Mock查询IMEI存在
        when(intlSoImeiService.checkImeiExist(any(), any())).thenReturn(existingImei);

        // Mock发送ES消息
        doNothing().when(syncSoToEsProducer).sendSyncEsMsg(any(), any());

        // 执行测试
        rmsSyncSoDataManage.saveDb(imeiRequest);

        // 验证不调用更新方法
        verify(intlSoImeiService, never()).doImeiUpdate(anyString(), anyList(), any(RmsSyncImeiData.class));
        verify(intlSoImeiService, never()).doImeiSave(any(RmsSyncImeiData.class));
    }

    @Test
    @DisplayName("保存QTY数据-新增场景")
    void saveDb_QtyCreate_Success() throws Exception {
        // 准备测试数据
        qtyRequest.setOperateType(DataSyncOperateTypeTypeEnum.CREATE.getMessage());

        // Mock锁服务
        when(distributionLockService.tryLock(anyString(), anyString())).thenReturn(distributionLock);

        // Mock保存QTY
        doNothing().when(qtyService).doQtySave(any(RmsSyncQtyData.class));

        // Mock发送ES消息
        doNothing().when(syncSoToEsProducer).sendSyncEsMsg(any(), any());

        // 执行测试
        rmsSyncSoDataManage.saveDb(qtyRequest);

        // 验证调用
        verify(qtyService).doQtySave(any(RmsSyncQtyData.class));
        verify(syncSoToEsProducer).sendSyncEsMsg(eq(DataSyncDataTypeEnum.QTY), eq(1L));
    }

    @Test
    @DisplayName("保存QTY数据-更新场景")
    void saveDb_QtyUpdate_Success() throws Exception {
        // 准备测试数据
        qtyRequest.setOperateType(DataSyncOperateTypeTypeEnum.UPDATE.getMessage());

        // Mock锁服务
        when(distributionLockService.tryLock(anyString(), anyString())).thenReturn(distributionLock);

        // Mock更新QTY
        doNothing().when(qtyService).doQtyUpdate(any(RmsSyncQtyData.class));

        // Mock发送ES消息
        doNothing().when(syncSoToEsProducer).sendSyncEsMsg(any(), any());

        // 执行测试
        rmsSyncSoDataManage.saveDb(qtyRequest);

        // 验证调用
        verify(qtyService).doQtyUpdate(any(RmsSyncQtyData.class));
    }

    @Test
    @DisplayName("保存数据-参数为空异常")
    void saveDb_NullParameters_ThrowsException() {
        // 准备空参数
        RmsSyncSoDataReqDto nullRequest = new RmsSyncSoDataReqDto();
        nullRequest.setType(null);
        nullRequest.setOperateType(null);
        nullRequest.setData(null);

        // 执行测试并验证异常
        assertThrows(IllegalArgumentException.class, () -> rmsSyncSoDataManage.saveDb(nullRequest));
    }

    @Test
    @DisplayName("保存数据-不支持的数据类型")
    void saveDb_UnsupportedDataType_ThrowsException() {
        // 准备不支持的数据类型
        RmsSyncSoDataReqDto invalidRequest = new RmsSyncSoDataReqDto();
        invalidRequest.setType("INVALID_TYPE");
        invalidRequest.setOperateType("CREATE");
        invalidRequest.setData(new JSONObject());

        // 执行测试并验证异常
        assertThrows(Exception.class, () -> rmsSyncSoDataManage.saveDb(invalidRequest));
    }

    @Test
    @DisplayName("保存数据-不支持的操作类型")
    void saveDb_UnsupportedOperateType_ThrowsException() {
        // 准备不支持的操作类型
        imeiRequest.setOperateType("INVALID_OPERATE");

        // Mock锁服务
        when(distributionLockService.tryLock(anyString(), anyString())).thenReturn(distributionLock);

        // Mock查询IMEI存在
        IntlSoImei existingImei = new IntlSoImei();
        existingImei.setId(1L);
        when(intlSoImeiService.checkImeiExist(any(), any())).thenReturn(existingImei);

        // 执行测试并验证异常
        assertThrows(Exception.class, () -> rmsSyncSoDataManage.saveDb(imeiRequest));
    }

    @Test
    @DisplayName("批量保存IMEI数据")
    void saveBatchDb_ImeiBatch_Success() {
        // 准备批量测试数据
        List<RmsSyncSoDataReqDto> batchRequests = Arrays.asList(imeiRequest, imeiRequest);

        // Mock批量查询IMEI（第一次用于存在性检查，第二次用于获取新保存的ID）
        List<IntlSoImei> existingImeis = new ArrayList<>();
        List<IntlSoImei> newImeis = Arrays.asList(createIntlSoImei(1L), createIntlSoImei(2L));
        when(intlSoImeiService.batchGetImeiByRmsId(isNull(), eq(Arrays.asList("RMS001", "RMS001"))))
                .thenReturn(existingImeis, newImeis);

        // Mock批量保存IMEI
        doNothing().when(intlSoImeiService).doImeiBatchSave(anyList());

        // Mock发送ES消息
        doNothing().when(syncSoToEsProducer).sendSyncEsMsg(any(), anyList(), anyBoolean());

        // 执行测试
        rmsSyncSoDataManage.saveBatchDb(batchRequests);

        // 验证调用
        verify(intlSoImeiService).doImeiBatchSave(anyList());
        verify(syncSoToEsProducer).sendSyncEsMsg(eq(DataSyncDataTypeEnum.IMEI), anyList(), eq(false));
    }

    @Test
    @DisplayName("批量保存QTY数据")
    void saveBatchDb_QtyBatch_Success() {
        // 准备批量测试数据
        List<RmsSyncSoDataReqDto> batchRequests = Arrays.asList(qtyRequest, qtyRequest);

                // Mock批量查询QTY（第一次用于存在性检查，第二次用于获取新保存的ID）
        List<IntlSoQty> existingQties = new ArrayList<>();
        List<IntlSoQty> newQties = Arrays.asList(createIntlSoQty(1L), createIntlSoQty(2L));
        when(qtyService.batchGetQtyByRmsId(anyList())).thenReturn(existingQties, newQties);
        
        // Mock批量保存QTY
        doNothing().when(qtyService).doQtyBatchSave(anyList());
        
        // Mock发送ES消息
        doNothing().when(syncSoToEsProducer).sendSyncEsMsg(any(), anyList(), anyBoolean());

        // 执行测试
        rmsSyncSoDataManage.saveBatchDb(batchRequests);

        // 验证调用
        verify(qtyService).doQtyBatchSave(anyList());
        verify(syncSoToEsProducer).sendSyncEsMsg(eq(DataSyncDataTypeEnum.QTY), anyList(), eq(false));
    }

    @Test
    @DisplayName("批量保存-参数为空异常")
    void saveBatchDb_NullParameters_ThrowsException() {
        // 准备空参数
        List<RmsSyncSoDataReqDto> nullRequests = Arrays.asList(
                createNullRequest(), createNullRequest()
        );

        // 执行测试并验证异常
        assertThrows(IllegalArgumentException.class, () -> rmsSyncSoDataManage.saveBatchDb(nullRequests));
    }

    @Test
    @DisplayName("批量保存-不支持的数据类型")
    void saveBatchDb_UnsupportedDataType_ThrowsException() {
        // 准备不支持的数据类型
        RmsSyncSoDataReqDto invalidRequest = new RmsSyncSoDataReqDto();
        invalidRequest.setType("INVALID_TYPE");
        invalidRequest.setOperateType("CREATE");
        invalidRequest.setData(new JSONObject());

        List<RmsSyncSoDataReqDto> invalidRequests = Arrays.asList(invalidRequest);

        // 执行测试并验证异常
        assertThrows(CommonBusinessException.class, () -> rmsSyncSoDataManage.saveBatchDb(invalidRequests));
    }

    @Test
    @DisplayName("获取锁失败-IMEI同步")
    void saveDb_ImeiLockFailed_ThrowsException() {
        // Mock锁获取失败
        when(distributionLockService.tryLock(anyString(), anyString())).thenReturn(null);

        // 执行测试并验证异常
        assertThrows(BizException.class, () -> rmsSyncSoDataManage.saveDb(imeiRequest));
    }

    @Test
    @DisplayName("获取锁失败-QTY同步")
    void saveDb_QtyLockFailed_ThrowsException() {
        // Mock锁获取失败
        when(distributionLockService.tryLock(anyString(), anyString())).thenReturn(null);

        // 执行测试并验证异常
        assertThrows(BizException.class, () -> rmsSyncSoDataManage.saveDb(qtyRequest));
    }

    @Test
    @DisplayName("IMEI数据验证失败-缺少必要字段")
    void saveDb_ImeiValidationFailed_ThrowsException() {
        // 准备无效的IMEI数据
        RmsSyncImeiData invalidImeiData = new RmsSyncImeiData();
        invalidImeiData.setDataFrom(DataFromEnum.RETAIL.getCode());
        invalidImeiData.setRetailId(null); // 缺少retailId

        RmsSyncSoDataReqDto invalidRequest = new RmsSyncSoDataReqDto();
        invalidRequest.setType(DataSyncDataTypeEnum.IMEI.getMessage());
        invalidRequest.setOperateType(DataSyncOperateTypeTypeEnum.CREATE.getMessage());
        invalidRequest.setData(JSONObject.parseObject(JSONObject.toJSONString(invalidImeiData)));

        // 执行测试并验证异常
        assertThrows(BizException.class, () -> rmsSyncSoDataManage.saveDb(invalidRequest));
    }

    @Test
    @DisplayName("QTY数据验证失败-缺少rmsId")
    void saveDb_QtyValidationFailed_ThrowsException() {
        // 准备无效的QTY数据
        RmsSyncQtyData invalidQtyData = new RmsSyncQtyData();
        invalidQtyData.setRmsId(null); // 缺少rmsId

        RmsSyncSoDataReqDto invalidRequest = new RmsSyncSoDataReqDto();
        invalidRequest.setType(DataSyncDataTypeEnum.QTY.getMessage());
        invalidRequest.setOperateType(DataSyncOperateTypeTypeEnum.CREATE.getMessage());
        invalidRequest.setData(JSONObject.parseObject(JSONObject.toJSONString(invalidQtyData)));

        // 执行测试并验证异常
        assertThrows(BizException.class, () -> rmsSyncSoDataManage.saveDb(invalidRequest));
    }

    // 辅助方法：创建IntlSoImei对象
    private IntlSoImei createIntlSoImei(Long id) {
        IntlSoImei imei = new IntlSoImei();
        imei.setId(id);
        imei.setRmsId("RMS" + id);
        return imei;
    }

    // 辅助方法：创建IntlSoQty对象
    private IntlSoQty createIntlSoQty(Long id) {
        IntlSoQty qty = new IntlSoQty();
        qty.setId(id);
        qty.setRmsId("RMS" + id);
        return qty;
    }

    // 辅助方法：创建空请求
    private RmsSyncSoDataReqDto createNullRequest() {
        RmsSyncSoDataReqDto request = new RmsSyncSoDataReqDto();
        request.setType(null);
        request.setOperateType(null);
        request.setData(null);
        return request;
    }
} 