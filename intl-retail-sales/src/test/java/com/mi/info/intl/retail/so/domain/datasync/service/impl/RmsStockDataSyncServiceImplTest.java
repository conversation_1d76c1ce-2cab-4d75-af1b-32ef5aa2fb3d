package com.mi.info.intl.retail.so.domain.datasync.service.impl;

import com.mi.info.intl.retail.intlretail.service.api.mq.RmsSyncSoDataService;
import com.mi.info.intl.retail.intlretail.service.api.mq.dto.RmsSyncDataRequest;
import com.mi.info.intl.retail.service.DistributionLock;
import com.mi.info.intl.retail.service.DistributionLockService;
import com.mi.info.intl.retail.so.app.mq.dto.RmsStockDataSyncDto;
import com.mi.info.intl.retail.so.app.mq.dto.RmsSyncImeiData;
import com.mi.info.intl.retail.so.app.mq.dto.RmsSyncQtyData;
import com.mi.info.intl.retail.so.domain.datasync.dto.StockDataSyncReqDto;
import com.mi.info.intl.retail.so.infra.database.mapper.datasync.RmsStockDataSyncMapper;
import com.mi.info.intl.retail.utils.redis.RedisClient;
import com.mi.info.intl.retail.utils.redis.RedisKey;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.ArgumentMatchers.eq;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * RmsStockDataSyncServiceImpl 单元测试类
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class RmsStockDataSyncServiceImplTest {

    @Mock
    private RmsStockDataSyncMapper rmsStockDataSyncMapper;

    @Mock
    private RedisClient redisClient;

    @Mock
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Mock
    private RmsSyncSoDataService rmsSyncSoDataService;

    @Mock
    private DistributionLockService distributionLockService;

    @Mock
    private DistributionLock distributionLock;

    @InjectMocks
    private RmsStockDataSyncServiceImpl rmsStockDataSyncService;

    private RmsSyncImeiData mockImeiData;
    private RmsSyncQtyData mockQtyData;

    private static final Integer NO_UPDATE = 0;

    @BeforeEach
    void setUp() {
        // 设置私有字段值
        ReflectionTestUtils.setField(rmsStockDataSyncService, "batchSendMqSize", 500);
        ReflectionTestUtils.setField(rmsStockDataSyncService, "queryMaxSize", 5000);

        // 创建测试数据
        mockImeiData = new RmsSyncImeiData();
        mockImeiData.setIdNew(1L);
        mockImeiData.setRmsId("IMEI001");

        mockQtyData = new RmsSyncQtyData();
        mockQtyData.setIdNew(2L);
        mockQtyData.setRmsId("QTY001");

        // 模拟分布式锁
        when(distributionLockService.tryLockNoArgs(anyString())).thenReturn(distributionLock);

        // 使用any()匹配器来避免RedisKey对象实例不匹配的问题
        when(redisClient.get((RedisKey) any())).thenReturn(null);
    }

    @Test
    void testSyncStockImeiData_Success() {
        // 准备测试数据
        List<RmsSyncImeiData> imeiDataList = Arrays.asList(mockImeiData);

        // 模拟Redis返回空值（首次同步）
        when(redisClient.get((RedisKey) any())).thenReturn(null);

        // 模拟数据库查询
        when(rmsStockDataSyncMapper.getImeiMinId()).thenReturn(1L);
        when(rmsStockDataSyncMapper.getImeiMaxId()).thenReturn(10L);
        when(rmsStockDataSyncMapper.queryStockImeiDataResult(any(RmsStockDataSyncDto.class)))
                .thenReturn(imeiDataList);

        // 模拟状态更新
        when(rmsStockDataSyncMapper.updateImeiSyncStatus(any(StockDataSyncReqDto.class))).thenReturn(1);

        // 执行测试
        assertDoesNotThrow(() -> rmsStockDataSyncService.syncStockImeiData(100, NO_UPDATE));

        // 验证调用
        verify(redisClient).set(any(), eq("1"));
        verify(rmsSyncSoDataService).bathSyncRmsSoData(any(RmsSyncDataRequest.class));
    }

    @Test
    void testSyncStockImeiData_WithExistingLastId() {
        // 准备测试数据
        List<RmsSyncImeiData> imeiDataList = Arrays.asList(mockImeiData);

        // 模拟Redis返回上次同步ID
        when(redisClient.get((RedisKey) any())).thenReturn("5");
        when(rmsStockDataSyncMapper.getImeiMaxId()).thenReturn(10L);
        when(rmsStockDataSyncMapper.queryStockImeiDataResult(any(RmsStockDataSyncDto.class)))
                .thenReturn(imeiDataList);
        when(rmsStockDataSyncMapper.updateImeiSyncStatus(any(StockDataSyncReqDto.class))).thenReturn(1);

        // 执行测试
        assertDoesNotThrow(() -> rmsStockDataSyncService.syncStockImeiData(100, NO_UPDATE));

        // 验证调用
        verify(redisClient).set(any(), eq("1"));
    }

    @Test
    void testSyncStockImeiData_NoDataToSync() {
        // 模拟Redis返回上次同步ID
        when(redisClient.get((RedisKey) any())).thenReturn("10");
        when(rmsStockDataSyncMapper.getImeiMaxId()).thenReturn(10L);

        // 执行测试
        assertDoesNotThrow(() -> rmsStockDataSyncService.syncStockImeiData(100, NO_UPDATE));

        // 验证没有调用数据查询和处理
        verify(rmsStockDataSyncMapper, never()).queryStockImeiDataResult(any());
        verify(rmsSyncSoDataService, never()).bathSyncRmsSoData(any());
    }

    @Test
    void testSyncStockQtyData_Success() {
        // 准备测试数据
        List<RmsSyncQtyData> qtyDataList = Arrays.asList(mockQtyData);

        // 模拟Redis返回空值（首次同步）
        when(redisClient.get((RedisKey) any())).thenReturn(null);
        when(rmsStockDataSyncMapper.getQtyMinId()).thenReturn(1L);
        when(rmsStockDataSyncMapper.getQtyMaxId()).thenReturn(10L);
        when(rmsStockDataSyncMapper.queryStockQtyDataResult(any(RmsStockDataSyncDto.class)))
                .thenReturn(qtyDataList);
        doNothing().when(rmsStockDataSyncMapper).updateQtySyncStatus(any(StockDataSyncReqDto.class));

        // 执行测试
        assertDoesNotThrow(() -> rmsStockDataSyncService.syncStockQtyData(100, NO_UPDATE));

        // 验证调用
        verify(redisClient).set(any(), eq("2"));
        verify(rmsSyncSoDataService).bathSyncRmsSoData(any(RmsSyncDataRequest.class));
    }

    @Test
    void testSyncStockQtyData_WithExistingLastId() {
        // 准备测试数据
        List<RmsSyncQtyData> qtyDataList = Arrays.asList(mockQtyData);

        // 模拟Redis返回上次同步ID
        when(redisClient.get((RedisKey) any())).thenReturn("5");
        when(rmsStockDataSyncMapper.getQtyMaxId()).thenReturn(10L);
        when(rmsStockDataSyncMapper.queryStockQtyDataResult(any(RmsStockDataSyncDto.class)))
                .thenReturn(qtyDataList);
        doNothing().when(rmsStockDataSyncMapper).updateQtySyncStatus(any(StockDataSyncReqDto.class));

        // 执行测试
        assertDoesNotThrow(() -> rmsStockDataSyncService.syncStockQtyData(100, NO_UPDATE));

        // 验证调用
        verify(redisClient).set(any(), eq("2"));
    }

    @Test
    void testSyncStockQtyData_NoDataToSync() {
        // 模拟Redis返回上次同步ID
        when(redisClient.get((RedisKey) any())).thenReturn("10");
        when(rmsStockDataSyncMapper.getQtyMaxId()).thenReturn(10L);

        // 执行测试
        assertDoesNotThrow(() -> rmsStockDataSyncService.syncStockQtyData(100, NO_UPDATE));

        // 验证没有调用数据查询和处理
        verify(rmsStockDataSyncMapper, never()).queryStockQtyDataResult(any());
        verify(rmsSyncSoDataService, never()).bathSyncRmsSoData(any());
    }

    @Test
    void testSyncStockImeiData_EmptyDataList() {
        // 模拟Redis返回空值（首次同步）
        when(redisClient.get((RedisKey) any())).thenReturn(null);
        when(rmsStockDataSyncMapper.getImeiMinId()).thenReturn(1L);
        when(rmsStockDataSyncMapper.getImeiMaxId()).thenReturn(10L);
        when(rmsStockDataSyncMapper.queryStockImeiDataResult(any(RmsStockDataSyncDto.class)))
                .thenReturn(Collections.emptyList());

        // 执行测试
        assertDoesNotThrow(() -> rmsStockDataSyncService.syncStockImeiData(100, NO_UPDATE));

        // 验证没有调用MQ发送
        verify(rmsSyncSoDataService, never()).bathSyncRmsSoData(any());
    }

    @Test
    void testSyncStockQtyData_EmptyDataList() {
        // 模拟Redis返回空值（首次同步）
        when(redisClient.get((RedisKey) any())).thenReturn(null);
        when(rmsStockDataSyncMapper.getQtyMinId()).thenReturn(1L);
        when(rmsStockDataSyncMapper.getQtyMaxId()).thenReturn(10L);
        when(rmsStockDataSyncMapper.queryStockQtyDataResult(any(RmsStockDataSyncDto.class)))
                .thenReturn(Collections.emptyList());

        // 执行测试
        assertDoesNotThrow(() -> rmsStockDataSyncService.syncStockQtyData(100, NO_UPDATE));

        // 验证没有调用MQ发送
        verify(rmsSyncSoDataService, never()).bathSyncRmsSoData(any());
    }

    @Test
    void testSyncStockImeiData_LargeBatchSize() {
        // 准备大量测试数据
        List<RmsSyncImeiData> largeDataList = new ArrayList<>();
        for (int i = 1; i <= 1000; i++) {
            RmsSyncImeiData data = new RmsSyncImeiData();
            data.setIdNew((long) i);
            data.setRmsId("IMEI" + String.format("%03d", i));
            largeDataList.add(data);
        }

        // 模拟Redis返回空值（首次同步）
        when(redisClient.get((RedisKey) any())).thenReturn(null);
        when(rmsStockDataSyncMapper.getImeiMinId()).thenReturn(1L);
        when(rmsStockDataSyncMapper.getImeiMaxId()).thenReturn(1000L);
        when(rmsStockDataSyncMapper.queryStockImeiDataResult(any(RmsStockDataSyncDto.class)))
                .thenReturn(largeDataList);
        when(rmsStockDataSyncMapper.updateImeiSyncStatus(any(StockDataSyncReqDto.class))).thenReturn(1);

        // 执行测试
        assertDoesNotThrow(() -> rmsStockDataSyncService.syncStockImeiData(1000, NO_UPDATE));

        // 验证调用（应该分成2个批次，每批500条）
        verify(rmsSyncSoDataService, times(2)).bathSyncRmsSoData(any(RmsSyncDataRequest.class));
        verify(redisClient).set(any(), eq("1000"));
    }

    @Test
    void testSyncStockQtyData_LargeBatchSize() {
        // 准备大量测试数据
        List<RmsSyncQtyData> largeDataList = new ArrayList<>();
        for (int i = 1; i <= 1000; i++) {
            RmsSyncQtyData data = new RmsSyncQtyData();
            data.setIdNew((long) i);
            data.setRmsId("QTY" + String.format("%03d", i));
            largeDataList.add(data);
        }

        // 模拟Redis返回空值（首次同步）
        when(redisClient.get((RedisKey) any())).thenReturn(null);
        when(rmsStockDataSyncMapper.getQtyMinId()).thenReturn(1L);
        when(rmsStockDataSyncMapper.getQtyMaxId()).thenReturn(1000L);
        when(rmsStockDataSyncMapper.queryStockQtyDataResult(any(RmsStockDataSyncDto.class)))
                .thenReturn(largeDataList);
        doNothing().when(rmsStockDataSyncMapper).updateQtySyncStatus(any(StockDataSyncReqDto.class));

        // 执行测试
        assertDoesNotThrow(() -> rmsStockDataSyncService.syncStockQtyData(1000, NO_UPDATE));

        // 验证调用（应该分成2个批次，每批500条）
        verify(rmsSyncSoDataService, times(2)).bathSyncRmsSoData(any(RmsSyncDataRequest.class));
        verify(redisClient).set(any(), eq("1000"));
    }

    @Test
    void testSyncStockImeiData_InvalidRedisValue() {
        // 模拟Redis返回无效值
        when(redisClient.get((RedisKey) any())).thenReturn("invalid");

        // 执行测试
        assertDoesNotThrow(() -> rmsStockDataSyncService.syncStockImeiData(100, NO_UPDATE));

        // 验证没有调用数据查询和处理
        verify(rmsStockDataSyncMapper, never()).queryStockImeiDataResult(any());
        verify(rmsSyncSoDataService, never()).bathSyncRmsSoData(any());
    }

    @Test
    void testSyncStockQtyData_InvalidRedisValue() {
        // 模拟Redis返回无效值
        when(redisClient.get((RedisKey) any())).thenReturn("invalid");

        // 执行测试
        assertDoesNotThrow(() -> rmsStockDataSyncService.syncStockQtyData(100, NO_UPDATE));

        // 验证没有调用数据查询和处理
        verify(rmsStockDataSyncMapper, never()).queryStockQtyDataResult(any());
        verify(rmsSyncSoDataService, never()).bathSyncRmsSoData(any());
    }

    @Test
    void testSyncStockImeiData_UseDefaultQuerySize() {
        // 准备测试数据
        List<RmsSyncImeiData> imeiDataList = Arrays.asList(mockImeiData);

        // 模拟Redis返回空值（首次同步）
        when(redisClient.get((RedisKey) any())).thenReturn(null);
        when(rmsStockDataSyncMapper.getImeiMinId()).thenReturn(1L);
        when(rmsStockDataSyncMapper.getImeiMaxId()).thenReturn(10L);
        when(rmsStockDataSyncMapper.queryStockImeiDataResult(any(RmsStockDataSyncDto.class)))
                .thenReturn(imeiDataList);
        when(rmsStockDataSyncMapper.updateImeiSyncStatus(any(StockDataSyncReqDto.class))).thenReturn(1);

        // 执行测试，不传入querySize参数
        assertDoesNotThrow(() -> rmsStockDataSyncService.syncStockImeiData(null, NO_UPDATE));

        // 验证调用
        verify(rmsStockDataSyncMapper).queryStockImeiDataResult(argThat(dto ->
                dto.getBatchSize() == 5000)); // 应该使用默认值
    }

    @Test
    void testSyncStockQtyData_UseDefaultQuerySize() {
        // 准备测试数据
        List<RmsSyncQtyData> qtyDataList = Arrays.asList(mockQtyData);

        // 模拟Redis返回空值（首次同步）
        when(redisClient.get((RedisKey) any())).thenReturn(null);
        when(rmsStockDataSyncMapper.getQtyMinId()).thenReturn(1L);
        when(rmsStockDataSyncMapper.getQtyMaxId()).thenReturn(10L);
        when(rmsStockDataSyncMapper.queryStockQtyDataResult(any(RmsStockDataSyncDto.class)))
                .thenReturn(qtyDataList);
        doNothing().when(rmsStockDataSyncMapper).updateQtySyncStatus(any(StockDataSyncReqDto.class));

        // 执行测试，不传入querySize参数
        assertDoesNotThrow(() -> rmsStockDataSyncService.syncStockQtyData(null, NO_UPDATE));

        // 验证调用
        verify(rmsStockDataSyncMapper).queryStockQtyDataResult(argThat(dto ->
                dto.getBatchSize() == 5000)); // 应该使用默认值
    }

    @Test
    void testSyncStockImeiData_ExceptionHandling() {
        // 模拟Redis返回空值（首次同步）
        when(redisClient.get((RedisKey) any())).thenReturn(null);
        when(rmsStockDataSyncMapper.getImeiMinId()).thenReturn(1L);
        when(rmsStockDataSyncMapper.getImeiMaxId()).thenReturn(10L);
        when(rmsStockDataSyncMapper.queryStockImeiDataResult(any(RmsStockDataSyncDto.class)))
                .thenReturn(Arrays.asList(mockImeiData));
        when(rmsStockDataSyncMapper.updateImeiSyncStatus(any(StockDataSyncReqDto.class))).thenReturn(1);

        // 模拟MQ发送异常
        doThrow(new RuntimeException("MQ发送失败"))
                .when(rmsSyncSoDataService).bathSyncRmsSoData(any(RmsSyncDataRequest.class));

        // 执行测试并验证异常
        Exception exception = assertThrows(RuntimeException.class,
                () -> rmsStockDataSyncService.syncStockImeiData(100, NO_UPDATE));

        assertTrue(exception.getMessage().contains("同步数据过程中发生异常"));
    }

    @Test
    void testSyncStockQtyData_ExceptionHandling() {
        // 模拟Redis返回空值（首次同步）
        when(redisClient.get((RedisKey) any())).thenReturn(null);
        when(rmsStockDataSyncMapper.getQtyMinId()).thenReturn(1L);
        when(rmsStockDataSyncMapper.getQtyMaxId()).thenReturn(10L);
        when(rmsStockDataSyncMapper.queryStockQtyDataResult(any(RmsStockDataSyncDto.class)))
                .thenReturn(Arrays.asList(mockQtyData));
        doNothing().when(rmsStockDataSyncMapper).updateQtySyncStatus(any(StockDataSyncReqDto.class));

        // 模拟MQ发送异常
        doThrow(new RuntimeException("MQ发送失败"))
                .when(rmsSyncSoDataService).bathSyncRmsSoData(any(RmsSyncDataRequest.class));

        // 执行测试并验证异常
        Exception exception = assertThrows(RuntimeException.class,
                () -> rmsStockDataSyncService.syncStockQtyData(100, NO_UPDATE));

        assertTrue(exception.getMessage().contains("同步数据过程中发生异常"));
    }
} 