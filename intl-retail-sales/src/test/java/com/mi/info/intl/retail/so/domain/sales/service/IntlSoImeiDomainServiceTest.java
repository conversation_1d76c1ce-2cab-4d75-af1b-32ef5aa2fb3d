package com.mi.info.intl.retail.so.domain.sales.service;

import com.alibaba.cola.dto.PageResponse;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mi.info.intl.retail.exception.BizException;
import com.mi.info.intl.retail.exception.ErrorCodes;
import com.mi.info.intl.retail.intlretail.service.api.fds.FdsService;
import com.mi.info.intl.retail.intlretail.service.api.fds.dto.FdsUploadResult;
import com.mi.info.intl.retail.intlretail.service.api.so.sales.dto.ReportingRoleRespDto;
import com.mi.info.intl.retail.intlretail.service.api.so.sales.dto.SalesImeiRespDto;
import com.mi.info.intl.retail.intlretail.service.api.so.sales.dto.VerificationResultRespDto;
import com.mi.info.intl.retail.intlretail.service.api.so.sales.request.SalesImeiReqDto;
import com.mi.info.intl.retail.model.UserInfo;
import com.mi.info.intl.retail.so.domain.sales.constant.SalesConstant;
import com.mi.info.intl.retail.so.domain.sales.converter.SalesImeiConverter;
import com.mi.info.intl.retail.so.domain.sales.enums.ReportingRoleEnum;
import com.mi.info.intl.retail.so.domain.sales.enums.VerificationResultEnum;
import com.mi.info.intl.retail.so.domain.sales.model.SoImeiIndex;
import com.mi.info.intl.retail.so.domain.upload.config.SalesQueryPageConfig;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoPlaintextImeiUser;
import com.mi.info.intl.retail.so.domain.upload.enums.IntlSoPlaintextImeiUserStatusEnum;
import com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlSoPlaintextImeiUserMapper;
import com.mi.info.intl.retail.utils.UserInfoUtil;
import com.mi.info.intl.retail.utils.redis.RedisClient;
import com.xiaomi.nr.job.admin.service.NrJobService;
import com.xiaomi.youpin.infra.rpc.Result;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class IntlSoImeiDomainServiceTest {

    @InjectMocks
    private IntlSoImeiDomainService service;

    @Mock
    private IntlSoImeiEsService intlSoImeiEsService;
    @Mock
    private SalesImeiConverter salesImeiConverter;
    @Mock
    private IntlSoPlaintextImeiUserMapper intlSoPlaintextImeiUserMapper;
    @Mock
    private SalesQueryPageConfig salesQueryPageConfig;
    @Mock
    private NrJobService nrJobService;
    @Mock
    private RedisClient redisClient;
    @Mock
    private FdsService fdsService;

    // ------------------------- getSalesImeiForPage -------------------------

    @Test
    @DisplayName("getSalesImeiForPage - pageSize 超过上限抛出 BizException")
    void testGetSalesImeiForPage_PageSizeTooLarge() {
        try (MockedStatic<UserInfoUtil> mocked = mockStatic(UserInfoUtil.class)) {
            mocked.when(UserInfoUtil::getUserContext).thenReturn(createUser());
            SalesImeiReqDto req = new SalesImeiReqDto();
            req.setPageSize(SalesConstant.MAX_PAGE_SIZE + 1);
            req.setPageIndex(1);
            BizException ex = assertThrows(BizException.class, () -> service.getSalesImeiForPage(req));
            assertEquals(ErrorCodes.PARAMS_ERROR.getCode(), ex.getCode());
            assertTrue(ex.getMessage().contains("maximum"));
        }
    }

    @Test
    @DisplayName("getSalesImeiForPage - offset+pageSize 超过 ES 查询上限抛出 BizException")
    void testGetSalesImeiForPage_OverEsMaximum() {
        try (MockedStatic<UserInfoUtil> mocked = mockStatic(UserInfoUtil.class)) {
            mocked.when(UserInfoUtil::getUserContext).thenReturn(createUser());
            when(salesQueryPageConfig.getEsQueryMaximum()).thenReturn(100);

            SalesImeiReqDto req = new SalesImeiReqDto();
            req.setPageSize(500);
            req.setPageIndex(21);

            BizException ex = assertThrows(BizException.class, () -> service.getSalesImeiForPage(req));
            assertEquals(ErrorCodes.PARAMS_ERROR.getCode(), ex.getCode());
            assertTrue(ex.getMessage().contains("pieces of data"));
        }
    }

    @Test
    @DisplayName("getSalesImeiForPage - ES 返回 null 抛出 BizException")
    void testGetSalesImeiForPage_NullEsPage() {
        try (MockedStatic<UserInfoUtil> mocked = mockStatic(UserInfoUtil.class)) {
            mocked.when(UserInfoUtil::getUserContext).thenReturn(createUser());
            when(salesQueryPageConfig.getEsQueryMaximum()).thenReturn(1000);
            when(intlSoImeiEsService.queryByPage(any(SalesImeiReqDto.class))).thenReturn(null);

            SalesImeiReqDto req = new SalesImeiReqDto();
            req.setPageSize(10);
            req.setPageIndex(1);

            BizException ex = assertThrows(BizException.class, () -> service.getSalesImeiForPage(req));
            assertEquals(ErrorCodes.BIZ_ERROR.getCode(), ex.getCode());
            assertTrue(ex.getMessage().contains("soImeiIndexPage"));
        }
    }

    @Test
    @DisplayName("getSalesImeiForPage - 正常返回分页数据并完成转换")
    void testGetSalesImeiForPage_Success() {
        try (MockedStatic<UserInfoUtil> mocked = mockStatic(UserInfoUtil.class)) {
            UserInfo user = createUser();
            mocked.when(UserInfoUtil::getUserContext).thenReturn(user);
            when(salesQueryPageConfig.getEsQueryMaximum()).thenReturn(1000);

            // mock ES page
            List<SoImeiIndex> esData = new ArrayList<>();
            SoImeiIndex idx = new SoImeiIndex();
            esData.add(idx);
            PageResponse<SoImeiIndex> esPage = PageResponse.of(esData, 1, 1, 10);
            when(intlSoImeiEsService.count(any())).thenReturn(1L);
            when(intlSoImeiEsService.queryByPage(any(SalesImeiReqDto.class))).thenReturn(esPage);

            // mock converter
            List<SalesImeiRespDto> converted = new ArrayList<>();
            SalesImeiRespDto dto = new SalesImeiRespDto();
            dto.setId(1L);
            converted.add(dto);
            when(salesImeiConverter.toTargetByTypeAndAreaId(anyList(), anyString(), eq(user.getAreaId())))
                    .thenReturn(converted);

            SalesImeiReqDto req = new SalesImeiReqDto();
            req.setPageSize(10);
//            req.setOffset(0);
            req.setPageIndex(1);

            IPage<SalesImeiRespDto> page = service.getSalesImeiForPage(req);
            assertNotNull(page);
            assertEquals(1L, page.getTotal());
            assertEquals(1, page.getRecords().size());
            assertEquals(1L, page.getRecords().get(0).getId());
        }
    }

    // ------------------------- getImeiVerificationResultData -------------------------

    @Test
    @DisplayName("getImeiVerificationResultData - 聚合结果映射并对缺失项置0")
    void testGetImeiVerificationResultData_Mapping() {
        Map<String, Long> map = new HashMap<>();
        map.put(VerificationResultEnum.VEFIRYING.getCode(), 10L);
        map.put(VerificationResultEnum.SUCCESSFULLY.getCode(), 5L);
        // FAILED/TIMEOUT 不放，应该置0
        when(intlSoImeiEsService.groupByAgg(any(SalesImeiReqDto.class))).thenReturn(map);

        VerificationResultRespDto dto = service.getImeiVerificationResultData(new SalesImeiReqDto());
        assertEquals(10L, dto.getVerifyingCount());
        assertEquals(5L, dto.getSuccessfullyCount());
        assertEquals(0L, dto.getFailedCount());
        assertEquals(0L, dto.getTimeoutCount());
    }

    // ------------------------- getImeiReportingRoleData -------------------------

    @Test
    @DisplayName("getImeiReportingRoleData - 聚合映射并计算 othersCount")
    void testGetImeiReportingRoleData_Mapping() {
        Map<String, Long> map = new HashMap<>();
        map.put(ReportingRoleEnum.PROMOTER.getCode(), 3L);
        map.put(ReportingRoleEnum.TEMPORARY_PROMOTER.getCode(), 2L);
        map.put(ReportingRoleEnum.SUPERVISOR.getCode(), 1L);
        map.put(ReportingRoleEnum.SUPERVISOR_WITHOUT_PROMOTERS.getCode(), 4L);
        map.put(ReportingRoleEnum.MERCHANDISER.getCode(), 5L);
        // 额外未知角色
        map.put("OTHER_ROLE", 10L);
        when(intlSoImeiEsService.groupByAgg(any(SalesImeiReqDto.class))).thenReturn(map);

        ReportingRoleRespDto dto = service.getImeiReportingRoleData(new SalesImeiReqDto());
        assertEquals(3L, dto.getPromoterCount());
        assertEquals(2L, dto.getTemporaryPromoterCount());
        assertEquals(1L, dto.getSupervisorCount());
        assertEquals(4L, dto.getSupervisorWithoutPromoterCount());
        assertEquals(5L, dto.getMerchandiserCount());
        // total = 3+2+1+4+5+10 = 25, known sum = 15, others = 10
        assertEquals(10L, dto.getOthersCount());
    }

    // ------------------------- isPlaintextImeiUser -------------------------

    @Test
    @DisplayName("isPlaintextImeiUser - userInfo 为 null 抛出 BizException")
    void testIsPlaintextImeiUser_UserNull() {
        BizException ex = assertThrows(BizException.class, () -> service.isPlaintextImeiUser(null));
        assertEquals(ErrorCodes.SYS_ERROR.getCode(), ex.getCode());
        assertTrue(ex.getMessage().contains("userInfo"));
    }

    @Test
    @DisplayName("isPlaintextImeiUser - miID 为 null 抛出 BizException")
    void testIsPlaintextImeiUser_MidNull() {
        UserInfo u = UserInfo.builder().miID(null).build();
        BizException ex = assertThrows(BizException.class, () -> service.isPlaintextImeiUser(u));
        assertEquals(ErrorCodes.SYS_ERROR.getCode(), ex.getCode());
        assertTrue(ex.getMessage().contains("miID"));
    }

    @Test
    @DisplayName("isPlaintextImeiUser - 存在启用记录返回 true，否则 false")
    void testIsPlaintextImeiUser_ReturnsFlag() {
        UserInfo u = UserInfo.builder().miID(123L).build();
        IntlSoPlaintextImeiUser enabled = new IntlSoPlaintextImeiUser();
        enabled.setUserMid(123L);
        enabled.setStatus(IntlSoPlaintextImeiUserStatusEnum.ENABLED.getCode());
        when(intlSoPlaintextImeiUserMapper.selectCount(any())).thenReturn(1L);
        assertTrue(service.isPlaintextImeiUser(u));

        when(intlSoPlaintextImeiUserMapper.selectCount(any())).thenReturn(null);
        assertFalse(service.isPlaintextImeiUser(u));
    }

    // ------------------------- excelExport4SalesImei 参数校验 -------------------------

    @Test
    @DisplayName("excelExport4SalesImei - 记录数为0抛出 BizException，不进行后续 IO")
    void testExcelExport4SalesImei_NoData() {
        SalesImeiReqDto req = new SalesImeiReqDto();
        when(intlSoImeiEsService.count(any(SalesImeiReqDto.class))).thenReturn(0L);
        BizException ex = assertThrows(BizException.class, () -> service.excelExport4SalesImei(req));
        assertEquals(ErrorCodes.BIZ_ERROR.getCode(), ex.getCode());
        assertTrue(ex.getMessage().contains(SalesConstant.ERROR_NO_EXPORTED_DATA));
    }

    @Test
    @DisplayName("excelExport4SalesImei - 超过导出上限抛出 BizException")
    void testExcelExport4SalesImei_ExceedsLimit() {
        SalesImeiReqDto req = new SalesImeiReqDto();
        when(intlSoImeiEsService.count(any(SalesImeiReqDto.class))).thenReturn(1000L);
        when(salesQueryPageConfig.getEsExportMaximum()).thenReturn(500);
        BizException ex = assertThrows(BizException.class, () -> service.excelExport4SalesImei(req));
        assertEquals(ErrorCodes.BIZ_ERROR.getCode(), ex.getCode());
        assertTrue(ex.getMessage().contains("can be exported"));
    }

    // ------------------------- export -------------------------

    @Test
    @DisplayName("export - 明文权限校验失败抛出 BizException")
    void testExport_PlaintextPermissionDenied() {
        try (MockedStatic<UserInfoUtil> mocked = mockStatic(UserInfoUtil.class)) {
            UserInfo user = UserInfo.builder().userId("u1").miID(1L).areaId("CN").build();
            mocked.when(UserInfoUtil::getUserContext).thenReturn(user);
            // 请求为明文导出
            SalesImeiReqDto req = new SalesImeiReqDto();
            req.setReqType(SalesConstant.REQ_TYPE_2);
//            // 数据量>0，以便走到权限校验前
//            when(intlSoImeiEsService.count(any(SalesImeiReqDto.class))).thenReturn(10L);
//            when(salesQueryPageConfig.getEsExportMaximum()).thenReturn(100);
            // 明文用户查询返回null => 非明文用户
            when(intlSoPlaintextImeiUserMapper.selectCount(any())).thenReturn(null);

            BizException ex = assertThrows(BizException.class, () -> service.export(req));
            assertEquals(ErrorCodes.BIZ_ERROR.getCode(), ex.getCode());
            assertTrue(ex.getMessage().contains("plaintext IMEI user"));
        }
    }

    @Test
    @DisplayName("export - 正常返回任务ID")
    void testExport_Success() {
        try (MockedStatic<UserInfoUtil> mocked = mockStatic(UserInfoUtil.class)) {
            UserInfo user = UserInfo.builder().userId("u1").miID(1L).areaId("CN").build();
            mocked.when(UserInfoUtil::getUserContext).thenReturn(user);

            SalesImeiReqDto req = new SalesImeiReqDto();
            req.setReqType(SalesConstant.REQ_TYPE_1);

            when(intlSoImeiEsService.count(any(SalesImeiReqDto.class))).thenReturn(5L);
            when(salesQueryPageConfig.getEsExportMaximum()).thenReturn(100);
            when(salesQueryPageConfig.getSalesProjectId()).thenReturn(1L);
            when(salesQueryPageConfig.getSalesProjectName()).thenReturn("sales");
            when(nrJobService.triggerJob(any())).thenReturn(Result.success("task-001"));

            String taskId = service.export(req);
            assertEquals("task-001", taskId);
        }
    }

    // ------------------------- excelExport4SalesImei -------------------------

    @Test
    @DisplayName("excelExport4SalesImei - 正常导出并上传返回URL")
    void testExcelExport4SalesImei_Success() throws Exception {
        SalesImeiReqDto req = new SalesImeiReqDto();
        // 统计数与分页
        when(intlSoImeiEsService.count(any(SalesImeiReqDto.class))).thenReturn(15L);
        when(salesQueryPageConfig.getEsExportMaximum()).thenReturn(100);
        when(salesQueryPageConfig.getEsExportPageSize()).thenReturn(10);

        // 构造两页ES数据
        PageResponse<SoImeiIndex> p1 = PageResponse.of(buildEsList(10), 15, 10, 1);
        PageResponse<SoImeiIndex> p2 = PageResponse.of(buildEsList(5), 15, 10, 2);
        when(intlSoImeiEsService.queryByPage(any(SalesImeiReqDto.class)))
                .thenReturn(p1, p2);

        // 转换结果
        when(salesImeiConverter.toTargetByTypeAndAreaId(anyList(), anyString(), any()))
                .thenAnswer(invocation -> {
                    List<SoImeiIndex> list = invocation.getArgument(0);
                    List<SalesImeiRespDto> out = new ArrayList<>();
                    for (int i = 0; i < list.size(); i++) {
                        out.add(new SalesImeiRespDto());
                    }
                    return out;
                });

        FdsUploadResult uploadResult = new FdsUploadResult();
        uploadResult.setUrl("https://fds/sales.xlsx");
        when(fdsService.upload(anyString(), any(), eq(true))).thenReturn(uploadResult);

        String url = service.excelExport4SalesImei(req);
        assertEquals("https://fds/sales.xlsx", url);
    }

    // ------------------------- exportHandler -------------------------

    @Test
    @DisplayName("exportHandler - 成功生成文件并返回Result.success")
    void testExportHandler_Success() throws Exception {
        // 使用spy以便替换导出实现
        IntlSoImeiDomainService spy = org.mockito.Mockito.spy(service);
        try (MockedStatic<com.xiaomi.nr.job.core.context.JobHelper> mockedJob = mockStatic(com.xiaomi.nr.job.core.context.JobHelper.class)) {
            mockedJob.when(com.xiaomi.nr.job.core.context.JobHelper::getJobParam).thenReturn("{}\n");
            // stub 具体导出
            org.mockito.Mockito.doReturn("https://fds/ok.xlsx").when(spy).excelExport4SalesImei(any(SalesImeiReqDto.class));
            Result<com.xiaomi.nr.job.core.biz.model.HandleMsg> res = spy.exportHandler();
            assertNotNull(res);
            assertNotNull(res.getData());
            assertEquals("https://fds/ok.xlsx", res.getData().getFileUrl());
        }
    }

    // ------------------------- helpers -------------------------

    private UserInfo createUser() {
        return UserInfo.builder()
                .userId("u1")
                .miID(1L)
                .areaId("CN")
                .build();
    }

    private List<SoImeiIndex> buildEsList(int n) {
        List<SoImeiIndex> list = new ArrayList<>();
        for (int i = 0; i < n; i++) {
            SoImeiIndex idx = new SoImeiIndex();
            idx.setId((long) i + 1);
            idx.setCreatedOn(System.currentTimeMillis());
            list.add(idx);
        }
        return list;
    }
}


