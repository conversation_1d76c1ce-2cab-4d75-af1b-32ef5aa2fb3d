package com.mi.info.intl.retail.intlretail.service.api.copilot;

import com.mi.info.intl.retail.intlretail.service.app.copilot.CopilotServiceImpl;
import com.xiaomi.nr.copilot.api.request.*;
import com.xiaomi.nr.copilot.api.response.*;
import com.xiaomi.nr.copilot.api.service.BaseInfoService;
import com.xiaomi.nr.copilot.api.service.ChatAiAssistantService;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ErrorCode;
import com.xiaomi.youpin.infra.rpc.errors.Scopes;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;


/**
 * 类描述：
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025-05-26 9:59
 */
@ExtendWith(MockitoExtension.class)
public class CopilotServiceTest {

    @InjectMocks
    private CopilotServiceImpl copilotService;

    @Mock
    private ChatAiAssistantService chatAiAssistantService;

    @Mock
    private BaseInfoService baseInfoService;

    @Test
    void testGetProductParams_Success() throws BizError {
        // 准备测试数据
        AIAssistantProductParamsRequest  request = new AIAssistantProductParamsRequest();
        request.setItemCode("123456");
        request.setChannelId(1);
        request.setAreaId("ID");
        request.setUserId("123456");
        request.setLanguage("en-US");

        AIAssistantProductParamsResponse response = new AIAssistantProductParamsResponse();
        Result<AIAssistantProductParamsResponse> result = Result.success(response);

        // 模拟chatAiAssistantService的返回值
        when(chatAiAssistantService.getProductParams(request)).thenReturn(result);

        // 调用被测试方法
        AIAssistantProductParamsResponse actualResponse = copilotService.getProductParams(request);
        // 验证结果
        assertEquals(response, actualResponse);
    }

    @Test
    void testBreakeAIAnswer_Success() throws BizError {
        // 准备测试数据
        BreakAIAnswerRequest request = new BreakAIAnswerRequest();
        request.setRequestId("1");


        // 模拟chatAiAssistantService的返回值
        when(chatAiAssistantService.breakAiGenerate(request)).thenReturn(Result.success(true));

        // 调用被测试方法
        Boolean aBoolean = copilotService.breakAIAnswer(request);
        // 验证结果
        assertEquals(true, aBoolean);
    }

    @Test
    void testGetProductParams_BizError() throws BizError {
        // 准备测试数据
        AIAssistantProductParamsRequest  request = new AIAssistantProductParamsRequest();
        request.setItemCode("123456");
        request.setChannelId(1);
        request.setAreaId("ID");
        request.setUserId("123456");
        request.setLanguage("en-US");

        // 模拟chatAiAssistantService抛出BizError异常
        when(chatAiAssistantService.getProductParams(request)).thenThrow(new RuntimeException("Biz error"));

        // 验证是否抛出RuntimeException
        assertThrows(RuntimeException.class, () -> {
            copilotService.getProductParams(request);
        });
    }

    @Test
    void testGetProductParams_NullResult() throws BizError {
        // 准备测试数据
        AIAssistantProductParamsRequest  request = new AIAssistantProductParamsRequest();
        request.setItemCode("123456");
        request.setChannelId(1);
        request.setAreaId("ID");
        request.setUserId("123456");
        request.setLanguage("en-US");

        // 模拟chatAiAssistantService返回null
        when(chatAiAssistantService.getProductParams(request)).thenReturn(null);

        // 验证是否抛出RuntimeException
        assertThrows(RuntimeException.class, () -> {
            copilotService.getProductParams(request);
        });
    }

    @Test
    void testBreakAiAnswer_NullResult() throws BizError {
        // 准备测试数据
        BreakAIAnswerRequest request = new BreakAIAnswerRequest();
        request.setRequestId("1");

        // 模拟chatAiAssistantService返回null
        when(chatAiAssistantService.breakAiGenerate(request)).thenReturn(null);

        // 验证是否抛出RuntimeException
        assertThrows(RuntimeException.class, () -> {
            copilotService.breakAIAnswer(request);
        });
    }

    @Test
    void testGetProductParams_NonZeroCode() throws BizError {
        // 准备测试数据
        AIAssistantProductParamsRequest  request = new AIAssistantProductParamsRequest();
        request.setItemCode("123456");
        request.setChannelId(1);
        request.setAreaId("ID");
        request.setUserId("123456");
        request.setLanguage("en-US");

        Result<AIAssistantProductParamsResponse> result = Result.fail(ErrorCode.createOnce(Scopes.SCOPE_TAGGING, 1), "Error message");

        // 模拟chatAiAssistantService返回非零code的结果
        when(chatAiAssistantService.getProductParams(request)).thenReturn(result);

        // 验证是否抛出RuntimeException
        assertThrows(RuntimeException.class, () -> {
            copilotService.getProductParams(request);
        });
    }

    @Test
    public void getComparableProductsTest() throws BizError {
        AIAssistantComparableProductRequest request = new AIAssistantComparableProductRequest();
        request.setProductId("123456");
        request.setIsXiaomiProduct(true);
        request.setChannelId(1);
        request.setAreaId("ID");
        request.setUserId("123456");
        request.setLanguage("en-US");

        AIAssistantComparableProductResponse response = new AIAssistantComparableProductResponse();
        response.setItems(null);

        when(chatAiAssistantService.getComparableProducts(request)).thenReturn(Result.success(response));

        AIAssistantComparableProductResponse resp = copilotService.getComparableProducts(request);
        assertNull(resp.getItems());
    }

    @Test
    public void contrastProductParamsTest() throws BizError {
        AIAssistantContrastProductParamsRequest request = new AIAssistantContrastProductParamsRequest();
        request.setItemCode("123456");
        request.setTargetItemCode("456789");
        request.setOrgId("JIM123");
        request.setChannelId(1);
        request.setAreaId("ID");
        request.setUserId("123456");
        request.setLanguage("en-US");

        Result<AIAssistantContrastProductParamsResponse> result = Result.success(null);
        when(chatAiAssistantService.contrastProductParams(request)).thenReturn(result);

        AIAssistantContrastProductParamsResponse resp = copilotService.contrastProductParams(request);
        assertNull(resp);
    }

    @Test
    public void getUserComparisonHistoryTest() throws BizError {
        CopilotComparisonHistoryRequest request = new CopilotComparisonHistoryRequest();
        request.setChannelId(1);
        request.setAreaId("ID");
        request.setUserId("123456");
        request.setLanguage("en-US");

        Result<AIAssistantProductComparisonHistoryResponse> result = Result.success(null);
        when(chatAiAssistantService.getUserComparisonHistory(request)).thenReturn(result);

        AIAssistantProductComparisonHistoryResponse resp = copilotService.getUserComparisonHistory(request);
        assertNull(resp);
    }

    @Test
    void testGetFeedbackTags_Success() throws BizError {
        AIAssistantFeedbackTagsRequest request = new AIAssistantFeedbackTagsRequest();
        request.setChannelId(1);

        AIAssistantFeedbackTagsResponse response = new AIAssistantFeedbackTagsResponse();

        Result<AIAssistantFeedbackTagsResponse> result = Result.success(response);

        when(chatAiAssistantService.getFeedbackTags(request)).thenReturn(result);

        AIAssistantFeedbackTagsResponse actualResponse = copilotService.getFeedbackTags(request);
        assertEquals(response, actualResponse);
    }

    @Test
    void testGetFeedbackTags_Exception() throws BizError {
        AIAssistantFeedbackTagsRequest request = new AIAssistantFeedbackTagsRequest();
        request.setChannelId(1);

        when(chatAiAssistantService.getFeedbackTags(request)).thenThrow(new RuntimeException("Service error"));

        assertThrows(RuntimeException.class, () -> {
            copilotService.getFeedbackTags(request);
        });
    }

    @Test
    void testGetFeedbackTags_NullResult() throws BizError {
        AIAssistantFeedbackTagsRequest request = new AIAssistantFeedbackTagsRequest();
        request.setChannelId(1);

        when(chatAiAssistantService.getFeedbackTags(request)).thenReturn(null);

        assertThrows(RuntimeException.class, () -> {
            copilotService.getFeedbackTags(request);
        });
    }

    @Test
    void testGetFeedbackTags_NonZeroCode() throws BizError {
        AIAssistantFeedbackTagsRequest request = new AIAssistantFeedbackTagsRequest();
        request.setChannelId(1);

        Result<AIAssistantFeedbackTagsResponse> result = Result.fail(ErrorCode.createOnce(Scopes.SCOPE_TAGGING, 2), "Error message");

        when(chatAiAssistantService.getFeedbackTags(request)).thenReturn(result);

        assertThrows(RuntimeException.class, () -> {
            copilotService.getFeedbackTags(request);
        });
    }

    @Test
    void testGetHotQuestion_Success() throws BizError {
        AIAssistantHotQuestionRequest request = new AIAssistantHotQuestionRequest();
        request.setChannelId(1);

        AIAssistantHotQuestionResponse response = new AIAssistantHotQuestionResponse();

        Result<AIAssistantHotQuestionResponse> result = Result.success(response);

        when(chatAiAssistantService.getHotQuestion(request)).thenReturn(result);

        AIAssistantHotQuestionResponse actualResponse = copilotService.getHotQuestion(request);
        assertEquals(response, actualResponse);
    }

    @Test
    void testGetHotQuestion_Exception() throws BizError {
        AIAssistantHotQuestionRequest request = new AIAssistantHotQuestionRequest();
        request.setChannelId(1);

        when(chatAiAssistantService.getHotQuestion(request)).thenThrow(new RuntimeException("Service error"));

        assertThrows(RuntimeException.class, () -> {
            copilotService.getHotQuestion(request);
        });
    }

    @Test
    void testGetHotQuestion_NullResult() throws BizError {
        AIAssistantHotQuestionRequest request = new AIAssistantHotQuestionRequest();
        request.setChannelId(1);

        when(chatAiAssistantService.getHotQuestion(request)).thenReturn(null);

        assertThrows(RuntimeException.class, () -> {
            copilotService.getHotQuestion(request);
        });
    }

    // @Test
    void testGetHotQuestion_NonZeroCode() throws BizError {
        AIAssistantHotQuestionRequest request = new AIAssistantHotQuestionRequest();
        request.setChannelId(1);

        Result<AIAssistantHotQuestionResponse> result = Result.fail(ErrorCode.createOnce(Scopes.SCOPE_TAGGING, 2), "Error message");

        when(chatAiAssistantService.getHotQuestion(request)).thenReturn(result);

        assertThrows(RuntimeException.class, () -> {
            copilotService.getHotQuestion(request);
        });
    }

    @Test
    void testGetRelationQuestion_Success() throws BizError {
        GetRelationQuestionRequest request = new GetRelationQuestionRequest();
        request.setChannelId(1);

        GetRelationQuestionResponse response = new GetRelationQuestionResponse();

        Result<GetRelationQuestionResponse> result = Result.success(response);

        when(chatAiAssistantService.getRelationQuestion(request)).thenReturn(result);

        GetRelationQuestionResponse actualResponse = copilotService.getRelationQuestion(request);
        assertEquals(response, actualResponse);
    }

    @Test
    void testGetRelationQuestion_Exception() throws BizError {
        GetRelationQuestionRequest request = new GetRelationQuestionRequest();
        request.setChannelId(1);

        when(chatAiAssistantService.getRelationQuestion(request)).thenThrow(new RuntimeException("Service error"));

        assertThrows(RuntimeException.class, () -> {
            copilotService.getRelationQuestion(request);
        });
    }

    @Test
    void testGetRelationQuestion_NullResult() throws BizError {
        GetRelationQuestionRequest request = new GetRelationQuestionRequest();
        request.setChannelId(1);

        when(chatAiAssistantService.getRelationQuestion(request)).thenReturn(null);

        assertThrows(RuntimeException.class, () -> {
            copilotService.getRelationQuestion(request);
        });
    }

    // @Test
    void testGetRelationQuestion_NonZeroCode() throws BizError {
        GetRelationQuestionRequest request = new GetRelationQuestionRequest();
        request.setChannelId(1);

        Result<GetRelationQuestionResponse> result = Result.fail(ErrorCode.createOnce(Scopes.SCOPE_TAGGING, 2), "Error message");

        when(chatAiAssistantService.getRelationQuestion(request)).thenReturn(result);

        assertThrows(RuntimeException.class, () -> {
            copilotService.getRelationQuestion(request);
        });
    }

    @Test
    void testGetSpuListByCategory_Success() {
        // 准备测试数据
        GetItemListRequest request = new GetItemListRequest();
        GoodsItemResponseBase item1 = new GoodsItemResponseBase();
        GoodsItemResponseBase item2 = new GoodsItemResponseBase();
        List<GoodsItemResponseBase> items = Arrays.asList(item1, item2);
        Result<List<GoodsItemResponseBase>> result = Result.success(items);

        when(baseInfoService.getSpuListByCategory(request)).thenReturn(result);

        // 调用被测试的方法
        List<GoodsItemResponseBase> response = copilotService.getSpuListByCategory(request);

        // 验证结果
        assertEquals(items, response);
    }

    @Test
    void testGetSpuListByCategory_Null() {
        // 准备测试数据
        GetItemListRequest request = new GetItemListRequest();

        // 模拟 baseInfoService.getSpuListByCategory 方法的返回值
        when(baseInfoService.getSpuListByCategory(request)).thenReturn(null);

        // 调用被测试的方法并捕获异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            copilotService.getSpuListByCategory(request);
        });

        // 验证异常信息
        assertEquals("getSpuListByCategory error", exception.getMessage());
    }

    // @Test
    void testGetSpuListByCategory_Error() {
        // 准备测试数据
        GetItemListRequest request = new GetItemListRequest();
        Result<List<GoodsItemResponseBase>> result = Result.fail(ErrorCode.createOnce(Scopes.SCOPE_TAGGING, 1), null);

        // 模拟 baseInfoService.getSpuListByCategory 方法的返回值
        when(baseInfoService.getSpuListByCategory(request)).thenReturn(result);

        // 调用被测试的方法并捕获异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            copilotService.getSpuListByCategory(request);
        });

        // 验证异常信息
        assertEquals("getSpuListByCategory error", exception.getMessage());
    }

    @Test
    void testGetSpuListByCategory_Exception() {
        // 准备测试数据
        GetItemListRequest request = new GetItemListRequest();

        // 模拟 baseInfoService.getSpuListByCategory 方法抛出异常
        when(baseInfoService.getSpuListByCategory(request)).thenThrow(new RuntimeException("Service error"));

        // 调用被测试的方法并捕获异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            copilotService.getSpuListByCategory(request);
        });

        // 验证异常信息
        assertEquals("java.lang.RuntimeException: Service error", exception.getMessage());
    }

    @Test
    void testGetAllSpuNames_Success() throws BizError {
        // 准备测试数据
        List<String> spuNames = Arrays.asList("Product1", "Product2");
        Result<List<String>> result = Result.success(spuNames);

        // 模拟 chatAiAssistantService.getProductNameList 方法的返回值
        when(chatAiAssistantService.getProductNameList()).thenReturn(result);

        // 调用被测试的方法
        List<String> response = copilotService.getAllSpuNames();

        // 验证结果
        assertEquals(spuNames, response);
    }

    @Test
    void testGetAllSpuNames_Null() throws BizError {
        // 准备测试数据

        // 模拟 chatAiAssistantService.getProductNameList 方法的返回值
        when(chatAiAssistantService.getProductNameList()).thenReturn(null);

        // 调用被测试的方法并捕获异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            copilotService.getAllSpuNames();
        });

        // 验证异常信息
        assertEquals("getProductNameList error", exception.getMessage());
    }

    // @Test
    void testGetAllSpuNames_Error() throws BizError {
        // 准备测试数据
        Result<List<String>> result = Result.fail(ErrorCode.createOnce(Scopes.SCOPE_TAGGING, 1), null);

        // 模拟 chatAiAssistantService.getProductNameList 方法的返回值
        when(chatAiAssistantService.getProductNameList()).thenReturn(result);

        // 调用被测试的方法并捕获异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            copilotService.getAllSpuNames();
        });

        // 验证异常信息
        assertEquals("getProductNameList error", exception.getMessage());
    }

    @Test
    void testGetAllSpuNames_Exception() throws BizError {
        // 模拟 chatAiAssistantService.getProductNameList 方法抛出异常
        when(chatAiAssistantService.getProductNameList()).thenThrow(new RuntimeException("Service error"));

        // 调用被测试的方法并捕获异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            copilotService.getAllSpuNames();
        });

        // 验证异常信息
        assertEquals("java.lang.RuntimeException: Service error", exception.getMessage());
    }
}
