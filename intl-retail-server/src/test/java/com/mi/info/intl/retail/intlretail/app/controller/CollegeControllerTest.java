package com.mi.info.intl.retail.intlretail.app.controller;

import com.mi.info.intl.retail.intlretail.service.api.market.CollegeService;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.user.app.UserService;
import com.mi.info.intl.retail.user.infra.entity.IntlRmsUser;
import com.xiaomi.nrme.market.api.vo.article.*;
import com.xiaomi.nrme.market.api.vo.article.intl.pojo.IntlArticleVO;
import com.xiaomi.nrme.market.api.vo.article.intl.req.*;
import com.xiaomi.nrme.market.api.vo.article.intl.resp.IntlCollegeArticleResp;
import com.xiaomi.nrme.market.api.vo.article.intl.resp.IntlPageResp;
import com.xiaomi.nrme.market.api.vo.category.i18n.IntlCategoryAppFirstPageRespVO;
import com.xiaomi.nrme.market.api.vo.category.i18n.IntlCategoryAppSecondVO;
import com.xiaomi.nrme.market.api.vo.category.i18n.IntlCategoryPageableReqVO;
import com.xiaomi.nrme.market.api.vo.category.i18n.IntlPosCategorySpuVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;

import javax.servlet.http.HttpServletRequest;

public class CollegeControllerTest {

    @Mock
    private CollegeService collegeService;

    @Mock
    private UserService userService;

    @InjectMocks
    private CollegeController collegeController;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }
    @Test
    void testAppFirstCategoryList() {
        // 构造请求参数
        IntlCategoryPageableReqVO req = new IntlCategoryPageableReqVO();
        req.setCategoryType(1);
        // 构造返回数据
        List<IntlCategoryAppFirstPageRespVO> categoryAppFirstPageRespVOPageResp = new ArrayList<>();
        IntlCategoryAppFirstPageRespVO categoryAppFirstPageRespVO = new IntlCategoryAppFirstPageRespVO();
        categoryAppFirstPageRespVOPageResp.add(categoryAppFirstPageRespVO);

        when(collegeService.appFirstCategoryList(any(IntlCategoryPageableReqVO.class))).thenReturn(categoryAppFirstPageRespVOPageResp);

        CommonApiResponse<List<IntlCategoryAppFirstPageRespVO>> response = collegeController.appFirstCategoryList(req);
        assertNotNull(response);
        assertEquals(0, response.getCode());
        assertEquals("ok", response.getMessage());
        assertNotNull(response.getData());
        assertEquals(1, response.getData().size());
    }
    @Test
    void testIntlAppSecondCategoryList() {
        // 构造请求参数
        IntlCategoryPageableReqVO req = new IntlCategoryPageableReqVO();
        req.setFirstCategoryId(11L);
        req.setPageNum(1);
        req.setPageSize(10);
        req.setAreaId("ID");
        req.setBusinessMode(3);
        // 构造返回数据
        List<IntlCategoryAppSecondVO> categoryAppSecondVOS = new ArrayList<>();
        IntlCategoryAppSecondVO categoryAppSecondVO = new IntlCategoryAppSecondVO();
        categoryAppSecondVOS.add(categoryAppSecondVO);

        when(collegeService.intlAppSecondCategoryList(any(IntlCategoryPageableReqVO.class))).thenReturn(categoryAppSecondVOS);

        CommonApiResponse<List<IntlCategoryAppSecondVO>> response = collegeController.intlAppSecondCategoryList(req);
        assertNotNull(response);
        assertEquals(0, response.getCode());
        assertEquals("ok", response.getMessage());
        assertNotNull(response.getData());
        assertEquals(1, response.getData().size());
    }
    @Test
    void testPageList() {
        // 构造请求参数
        IntlAppArticlePageableVO req = new IntlAppArticlePageableVO();
        req.setOffset(0L);
        req.setPageSize(10);
        req.setSpuIds(Arrays.asList(736000059L));
        req.setAreaId("ID");
        req.setBusinessMode(3);
        // 构造返回数据
        IntlPageResp<IntlArticleVO> pageResp = new IntlPageResp<>();
        pageResp.setData(Collections.emptyList());
        pageResp.setPageNum(1);
        pageResp.setPageSize(10);
        pageResp.setTotalNum(0L);
        Jwt jwt = mock(Jwt.class);
        when(jwt.getSubject()).thenReturn("testUserId");
        JwtAuthenticationToken token = new JwtAuthenticationToken(jwt);
        SecurityContextHolder.getContext().setAuthentication(token);
        IntlRmsUser intlRmsUser =  new IntlRmsUser();
        intlRmsUser.setMiId(3678909909L);
        HttpServletRequest httpServletRequest = mock(HttpServletRequest.class);
        when(httpServletRequest.getHeader("X-Retail-Language")).thenReturn("zh-TW");
        when(userService.getUserInfoDomainName(any())).thenReturn(intlRmsUser);
        when(collegeService.pageList(any(IntlAppArticlePageableVO.class))).thenReturn(pageResp);

        CommonApiResponse<IntlPageResp<IntlArticleVO>> response = collegeController.pageList(req,httpServletRequest);
        assertNotNull(response);
        assertEquals(0, response.getCode());
        assertEquals("ok", response.getMessage());
        assertNotNull(response.getData());
        assertEquals(0, response.getData().getData().size());
    }
    @Test
    void testToggleLike() {
        // 构造请求参数
        IntlRetailCollegeLikeRequest req = new IntlRetailCollegeLikeRequest();
        req.setId(109L);
        req.setMino("122");
        req.setBusinessMode(3);
        // 构造返回数据
        Long result = 1L;
        Jwt jwt = mock(Jwt.class);
        when(jwt.getSubject()).thenReturn("testUserId");
        JwtAuthenticationToken token = new JwtAuthenticationToken(jwt);
        SecurityContextHolder.getContext().setAuthentication(token);
        IntlRmsUser intlRmsUser =  new IntlRmsUser();
        intlRmsUser.setMiId(3678909909L);
        when(userService.getUserInfoDomainName(any())).thenReturn(intlRmsUser);

        when(collegeService.toggleLike(any(IntlRetailCollegeLikeRequest.class))).thenReturn(result);

        CommonApiResponse<Long> response = collegeController.toggleLike(req);
        assertNotNull(response);
        assertEquals(0, response.getCode());
        assertEquals("ok", response.getMessage());
        assertNotNull(response.getData());
        assertEquals(1, response.getData());
    }
    @Test
    void testDetail() {
        // 构造请求参数
        IntlCollegeArticleReq req = new IntlCollegeArticleReq();
        req.setId(109L);
        req.setBusinessMode(3);
        Jwt jwt = mock(Jwt.class);
        when(jwt.getSubject()).thenReturn("testUserId");
        JwtAuthenticationToken token = new JwtAuthenticationToken(jwt);
        SecurityContextHolder.getContext().setAuthentication(token);
        IntlRmsUser intlRmsUser =  new IntlRmsUser();
        intlRmsUser.setMiId(3678909909L);
        when(userService.getUserInfoDomainName(any())).thenReturn(intlRmsUser);
        // 构造返回数据
        IntlCollegeArticleResp result = new IntlCollegeArticleResp();

        when(collegeService.detail(any(IntlCollegeArticleReq.class))).thenReturn(result);

        CommonApiResponse<IntlCollegeArticleResp> response = collegeController.detail(req);
        assertNotNull(response);
        assertEquals(0, response.getCode());
        assertEquals("ok", response.getMessage());
        assertNotNull(response.getData());
    }
    @Test
    void testSyncArticleProgress() {
        Jwt jwt = mock(Jwt.class);
        when(jwt.getSubject()).thenReturn("testUserId");
        JwtAuthenticationToken token = new JwtAuthenticationToken(jwt);
        SecurityContextHolder.getContext().setAuthentication(token);
        IntlRmsUser intlRmsUser =  new IntlRmsUser();
        intlRmsUser.setMiId(3678909909L);
        when(userService.getUserInfoDomainName(any())).thenReturn(intlRmsUser);
        // 构造请求参数
        IntlArticleViewProgressReqVO req = new IntlArticleViewProgressReqVO();
        req.setArticleId(109L);
        req.setProgressRate(100);
        req.setBusinessMode(3);

        CommonApiResponse<Void> response = collegeController.syncArticleProgress(req);
        assertNotNull(response);
        assertEquals(0, response.getCode());
        assertEquals("ok", response.getMessage());
    }
    @Test
    void testFullTextExplore() {
        // 构造请求参数
        ArticleSearchEsPageableReq req = new ArticleSearchEsPageableReq();
        req.setPageNum(1);
        req.setPageSize(10);
        req.setAreaId("ID");
        req.setExploreKey("测试");
        // 构造返回数据
        PageResp<IntlArticleVO> pageResp = new PageResp<>();
        pageResp.setData(Collections.emptyList());
        pageResp.setPageNum(1);
        pageResp.setPageSize(10);
        pageResp.setTotalNum(0L);
        HttpServletRequest httpServletRequest = mock(HttpServletRequest.class);
        when(httpServletRequest.getHeader("X-Retail-Language")).thenReturn("zh-TW");
        when(collegeService.fullTextExplore(any(ArticleSearchEsPageableReq.class))).thenReturn(pageResp);

        CommonApiResponse<PageResp<IntlArticleVO>> response = collegeController.fullTextExplore(req, httpServletRequest);
        assertNotNull(response);
        assertEquals(0, response.getCode());
        assertEquals("ok", response.getMessage());
        assertNotNull(response.getData());
        assertEquals(0, response.getData().getData().size());
    }

    @Test
    void testSearchSpuByName() {
        // 构造请求参数
        IntlAppSpuListVO req = new IntlAppSpuListVO();
        req.setAreaId("ID");
        req.setSpuName("测试");
        // 构造返回数据
        List<IntlPosCategorySpuVO> resp = new ArrayList<>();
        resp.addAll(Collections.emptyList());
        when(collegeService.searchSpuByName(any(IntlAppSpuListVO.class))).thenReturn(resp);

        CommonApiResponse<List<IntlPosCategorySpuVO>> response = collegeController.searchSpuByName(req);
        assertNotNull(response);
        assertEquals(0, response.getCode());
        assertEquals("ok", response.getMessage());
        assertNotNull(response.getData());
        assertEquals(0, response.getData().size());
    }


} 