package com.mi.info.intl.retail.intlretail.app.controller;

import com.mi.info.intl.retail.intlretail.service.api.so.dto.GetImportLogListRequest;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.GetImportLogListResponse;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.ImeiDetailDto;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.PhotoDto;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.SubmitImeiReq;
import com.mi.info.intl.retail.intlretail.service.api.so.upload.ImeiUploadService;
import com.mi.info.intl.retail.intlretail.service.api.so.upload.SoImportService;
import com.mi.info.intl.retail.model.CommonApiResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * SoController 单元测试
 *
 * <AUTHOR>
 * @date 2025/7/28
 */
@ExtendWith(MockitoExtension.class)
class SoUploadControllerTest {

    @Mock
    private ImeiUploadService imeiUploadService;

    @Mock
    private SoImportService soImportService;

    @InjectMocks
    private SoUploadController soUploadController;

    private SubmitImeiReq validRequest;

    @BeforeEach
    void setUp() {
        setupValidRequest();
    }

    private void setupValidRequest() {
        validRequest = new SubmitImeiReq();
        validRequest.setRuleId(1L);
        validRequest.setImeiRuleId(1);
        validRequest.setCountryCode("CN");
        validRequest.setPositionCode("POS001");
        validRequest.setStoreCode("STORE001");
        validRequest.setUserId("user-guid-123");
        validRequest.setMiId(12345L);
        validRequest.setUserTitle(1001L);

        // IMEI明细
        ImeiDetailDto detail = new ImeiDetailDto();
        detail.setDetailId("detail-001");
        detail.setImei("861234567890123");
        detail.setImei2("861234567890124");
        detail.setSn("SN1234567890");
        detail.setProductId(10001L);
        detail.setProductCode("20001");
        detail.setReportingType(1);
        detail.setNote("测试备注");
        detail.setInputImei("861234567890123");
        detail.setSalesTime(1722142200000L); // 2025-07-28 10:30:00 的时间戳
        detail.setSnhash("a1b2c3d4e5f67890");
        validRequest.setDetailList(new ArrayList<>(Arrays.asList(detail)));

        // 图片信息
        PhotoDto photo = new PhotoDto();
        photo.setDetailId("detail-001");
        photo.setUrl("https://example.com/photo1.jpg");
        photo.setUploadTime(1722142260000L); // 2025-07-28 10:31:00 的时间戳
        validRequest.setPhotoList(new ArrayList<>(Arrays.asList(photo)));
    }

    @Test
    void testSubmitImei_Success() {
        // 准备Mock返回值
        CommonApiResponse<Object> successResponse = new CommonApiResponse<>(null);
        when(imeiUploadService.submitImei(any(SubmitImeiReq.class))).thenReturn(successResponse);

        // 执行测试
        CommonApiResponse<Object> actualResponse = soUploadController.submitImei(validRequest);

        // 验证结果
        assertNotNull(actualResponse);
        assertEquals(0, actualResponse.getCode());
        assertEquals("ok", actualResponse.getMessage());
        assertNull(actualResponse.getData());

        // 验证方法调用
        verify(imeiUploadService).submitImei(validRequest);
    }

    @Test
    void testSubmitImei_ValidationError() {
        // 准备Mock返回值 - 参数校验失败
        CommonApiResponse<Object> errorResponse = new CommonApiResponse<>(400, "请求参数不能为空", null);
        when(imeiUploadService.submitImei(any(SubmitImeiReq.class))).thenReturn(errorResponse);

        // 执行测试
        CommonApiResponse<Object> actualResponse = soUploadController.submitImei(validRequest);

        // 验证结果
        assertNotNull(actualResponse);
        assertEquals(400, actualResponse.getCode());
        assertEquals("请求参数不能为空", actualResponse.getMessage());

        // 验证方法调用
        verify(imeiUploadService).submitImei(validRequest);
    }

    @Test
    void testSubmitImei_BusinessError() {
        // 准备Mock返回值 - 业务错误
        CommonApiResponse<Object> errorResponse = new CommonApiResponse<>(400, "SO上报规则不存在", null);
        when(imeiUploadService.submitImei(any(SubmitImeiReq.class))).thenReturn(errorResponse);

        // 执行测试
        CommonApiResponse<Object> actualResponse = soUploadController.submitImei(validRequest);

        // 验证结果
        assertNotNull(actualResponse);
        assertEquals(400, actualResponse.getCode());
        assertEquals("SO上报规则不存在", actualResponse.getMessage());

        // 验证方法调用
        verify(imeiUploadService).submitImei(validRequest);
    }

    @Test
    void testSubmitImei_SystemError() {
        // 准备Mock返回值 - 系统异常
        CommonApiResponse<Object> errorResponse = new CommonApiResponse<>(500, "系统异常：数据库连接失败", null);
        when(imeiUploadService.submitImei(any(SubmitImeiReq.class))).thenReturn(errorResponse);

        // 执行测试
        CommonApiResponse<Object> actualResponse = soUploadController.submitImei(validRequest);

        // 验证结果
        assertNotNull(actualResponse);
        assertEquals(500, actualResponse.getCode());
        assertEquals("系统异常：数据库连接失败", actualResponse.getMessage());

        // 验证方法调用
        verify(imeiUploadService).submitImei(validRequest);
    }

    @Test
    void testSubmitImei_NullRequest() {
        // 准备Mock返回值 - null请求
        CommonApiResponse<Object> errorResponse = new CommonApiResponse<>(400, "请求参数不能为空", null);
        when(imeiUploadService.submitImei(null)).thenReturn(errorResponse);

        // 执行测试
        CommonApiResponse<Object> actualResponse = soUploadController.submitImei(null);

        // 验证结果
        assertNotNull(actualResponse);
        assertEquals(400, actualResponse.getCode());
        assertEquals("请求参数不能为空", actualResponse.getMessage());

        // 验证方法调用
        verify(imeiUploadService).submitImei(null);
    }

    @Test
    void testSubmitImei_ServiceThrowsException() {
        // 准备Mock - 服务抛出异常
        when(imeiUploadService.submitImei(any(SubmitImeiReq.class)))
                .thenThrow(new RuntimeException("Service exception"));

        // 执行测试并验证异常
        assertThrows(RuntimeException.class, () -> {
            soUploadController.submitImei(validRequest);
        });

        // 验证方法调用
        verify(imeiUploadService).submitImei(validRequest);
    }

    @Test
    void testSubmitImei_ComplexRequest() {
        // 准备复杂的请求数据
        ImeiDetailDto detail2 = new ImeiDetailDto();
        detail2.setDetailId("detail-002");
        detail2.setImei("869876543210987");
        detail2.setImei2("869876543210988");
        detail2.setSn("SN9876543210");
        detail2.setProductId(10002L);
        detail2.setProductCode("20002");
        detail2.setReportingType(2);
        detail2.setNote("测试备注2");
        detail2.setInputImei("869876543210987");
        detail2.setSalesTime(1722158130000L); // 2025-07-28 14:15:30 的时间戳
        detail2.setSnhash("f9e8d7c6b5a43210");

        validRequest.getDetailList().add(detail2);

        PhotoDto photo2 = new PhotoDto();
        photo2.setDetailId("detail-002");
        photo2.setUrl("https://example.com/photo2.jpg");
        photo2.setUploadTime(1722158160000L); // 2025-07-28 14:16:00 的时间戳
        validRequest.getPhotoList().add(photo2);

        // 准备Mock返回值
        CommonApiResponse<Object> successResponse = new CommonApiResponse<>(null);
        when(imeiUploadService.submitImei(any(SubmitImeiReq.class))).thenReturn(successResponse);

        // 执行测试
        CommonApiResponse<Object> actualResponse = soUploadController.submitImei(validRequest);

        // 验证结果
        assertNotNull(actualResponse);
        assertEquals(0, actualResponse.getCode());
        assertEquals("ok", actualResponse.getMessage());

        // 验证方法调用
        verify(imeiUploadService).submitImei(validRequest);
    }

    @Test
    void testGetImportLogList_Success() {
        // 准备数据
        GetImportLogListRequest request = new GetImportLogListRequest();
        request.setMiId(123456789L);
        request.setPageNum(1);
        request.setPageSize(20);

        GetImportLogListResponse expectedResponse = new GetImportLogListResponse();
        expectedResponse.setTotal(1);
        expectedResponse.setCurrentPage(1);
        expectedResponse.setPageSize(20);

        ArrayList<GetImportLogListResponse.ImportLogRecord> records = new ArrayList<>();
        GetImportLogListResponse.ImportLogRecord record = new GetImportLogListResponse.ImportLogRecord();
        record.setTaskName("测试任务");
        record.setDataSource("1");
        record.setImportType(1);
        record.setFileName("test.xlsx");
        record.setStatus(1);
        record.setImportProgress(100);
        record.setImportDuration(1);
        record.setOperator("测试用户");
        record.setOperationTime(System.currentTimeMillis());
        record.setAction(1);
        record.setFileUrl("http://test.com/file.xlsx");
        records.add(record);
        expectedResponse.setRecords(records);

        CommonApiResponse<GetImportLogListResponse> mockResponse = new CommonApiResponse<>(expectedResponse);

        // mock
        when(soImportService.getImportLogList(any(GetImportLogListRequest.class))).thenReturn(mockResponse);

        // 执行测试
        CommonApiResponse<GetImportLogListResponse> result = soUploadController.getImportLogList(request);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getCode());
        assertNotNull(result.getData());
        assertEquals(1, result.getData().getTotal());
        assertEquals(1, result.getData().getCurrentPage());
        assertEquals(20, result.getData().getPageSize());
        assertEquals(1, result.getData().getRecords().size());

        GetImportLogListResponse.ImportLogRecord resultRecord = result.getData().getRecords().get(0);
        assertEquals("测试任务", resultRecord.getTaskName());
        assertEquals("1", resultRecord.getDataSource());
        assertEquals(Integer.valueOf(1), resultRecord.getImportType());
        assertEquals("test.xlsx", resultRecord.getFileName());
        assertEquals(Integer.valueOf(1), resultRecord.getStatus());
        assertEquals(Integer.valueOf(100), resultRecord.getImportProgress());

        // 验证mock调用
        verify(soImportService).getImportLogList(any(GetImportLogListRequest.class));
    }

    @Test
    void testGetImportLogList_ServiceException() {
        // 准备数据
        GetImportLogListRequest request = new GetImportLogListRequest();
        request.setMiId(123456789L);

        String errorMessage = "服务异常";
        CommonApiResponse<GetImportLogListResponse> mockResponse = new CommonApiResponse<>(500, errorMessage, null);

        // mock
        when(soImportService.getImportLogList(any(GetImportLogListRequest.class))).thenReturn(mockResponse);

        // 执行测试
        CommonApiResponse<GetImportLogListResponse> result = soUploadController.getImportLogList(request);

        // 验证结果
        assertNotNull(result);
        assertEquals(500, result.getCode());
        assertEquals(errorMessage, result.getMessage());
        assertNull(result.getData());

        // 验证mock调用
        verify(soImportService).getImportLogList(any(GetImportLogListRequest.class));
    }

    @Test
    void testGetImportLogList_WithFilters() {
        // 准备数据
        GetImportLogListRequest request = new GetImportLogListRequest();
        request.setMiId(123456789L);
        request.setTaskName("销售数据");
        request.setDataSource(Arrays.asList("SalesApp", "InventorySys"));
        request.setStatus(Arrays.asList(1, 2));
        request.setImportDuration(Arrays.asList(0, 1, 2));
        request.setImportTypes(Arrays.asList(1, 2));
        request.setOperationStartTime(1704067200000L);
        request.setOperationEndTime(1706745599000L);

        GetImportLogListResponse expectedResponse = new GetImportLogListResponse();
        expectedResponse.setTotal(0);
        expectedResponse.setCurrentPage(1);
        expectedResponse.setPageSize(20);
        expectedResponse.setRecords(new ArrayList<>());

        CommonApiResponse<GetImportLogListResponse> mockResponse = new CommonApiResponse<>(expectedResponse);

        // mock
        when(soImportService.getImportLogList(any(GetImportLogListRequest.class))).thenReturn(mockResponse);

        // 执行测试
        CommonApiResponse<GetImportLogListResponse> result = soUploadController.getImportLogList(request);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getCode());
        assertNotNull(result.getData());
        assertEquals(0, result.getData().getTotal());

        // 验证mock调用
        verify(soImportService).getImportLogList(argThat(req ->
            req.getTaskName().equals("销售数据") &&
            req.getDataSource().contains("SalesApp") &&
            req.getStatus().contains(1) &&
            req.getImportDuration().contains(0) &&
            req.getImportTypes().contains(1) &&
            req.getMiId().equals(123456789L)
        ));
    }
}
