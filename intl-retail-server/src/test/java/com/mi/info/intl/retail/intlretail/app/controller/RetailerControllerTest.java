package com.mi.info.intl.retail.intlretail.app.controller;

import com.mi.info.intl.retail.intlretail.service.api.fds.FdsService;
import com.mi.info.intl.retail.intlretail.service.api.retailer.RetailerService;
import com.mi.info.intl.retail.intlretail.service.api.retailer.RetailerWebService;
import com.mi.info.intl.retail.intlretail.service.api.retailer.dto.CommonResponse;
import com.mi.info.intl.retail.intlretail.service.api.retailer.dto.RetailerAreaResponse;
import com.mi.info.intl.retail.intlretail.service.api.retailer.dto.RetailerExcelValidationResponse;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.BusinessDataInputRequest;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.BusinessDataResponse;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.DtTokenRequest;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.DtTokenResponse;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.ExcelValidationRequest;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.RetailerInfoRequest;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.RetailerInfoResponse;
import com.mi.info.intl.retail.model.CommonApiResponse;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
class RetailerControllerTest {

    @Mock
    private RetailerService retailerService;

    @Mock
    private RetailerWebService retailerWebService;

    @Mock
    private FdsService fdsService;

    @InjectMocks
    private RetailerController retailerController;

    private AutoCloseable closeable;

    @BeforeEach
    void setUp() {
        closeable = MockitoAnnotations.openMocks(this);
        ReflectionTestUtils.setField(retailerController, "tokenSecret", "test-secret");
    }

    @AfterEach
    void tearDown() throws Exception {
        closeable.close();
    }

    @Test
    void uploadFds_ShouldReturnValidationResponse() {
        // 准备测试数据
        ExcelValidationRequest request = new ExcelValidationRequest();
        RetailerExcelValidationResponse expectedResponse = new RetailerExcelValidationResponse();
        CommonResponse<RetailerExcelValidationResponse> expectedCommonResponse = new CommonResponse<>(expectedResponse);
        
        // 配置mock行为
        when(retailerWebService.uploadFds(any(ExcelValidationRequest.class))).thenReturn(expectedCommonResponse);
        
        // 执行测试
        CommonResponse<RetailerExcelValidationResponse> response = retailerController.uploadFds(request);
        
        // 验证结果
        assertNotNull(response);
        assertEquals(0, response.getCode());
        assertEquals("ok", response.getMessage());
        assertEquals(expectedResponse, response.getData());
        
        // 验证交互
        verify(retailerWebService, times(1)).uploadFds(request);
    }

    @Test
    void getArea_ShouldReturnAreaResponse() {
        // 准备测试数据
        RetailerAreaResponse expectedResponse = new RetailerAreaResponse();
        
        // 配置mock行为
        when(retailerService.getArea()).thenReturn(expectedResponse);
        
        // 执行测试
        CommonApiResponse<RetailerAreaResponse> response = retailerController.getArea();
        
        // 验证结果
        assertNotNull(response);
        assertEquals(0, response.getCode());
        assertEquals("ok", response.getMessage());
        assertEquals(expectedResponse, response.getData());
        
        // 验证交互
        verify(retailerService, times(1)).getArea();
    }

    @Test
    void getOrgPerson_ShouldReturnBusinessDataResponseList() {
        // 准备测试数据
        BusinessDataInputRequest request = new BusinessDataInputRequest();
        List<BusinessDataResponse> expectedResponseList = new ArrayList<>();
        BusinessDataResponse businessDataResponse = new BusinessDataResponse();
        expectedResponseList.add(businessDataResponse);
        
        // 配置mock行为
        when(retailerService.getOrgPerson(any(BusinessDataInputRequest.class))).thenReturn(expectedResponseList);
        
        // 执行测试
        CommonApiResponse<List<BusinessDataResponse>> response = retailerController.getOrgPerson(request);
        
        // 验证结果
        assertNotNull(response);
        assertEquals(0, response.getCode());
        assertEquals("ok", response.getMessage());
        assertEquals(expectedResponseList, response.getData());
        assertEquals(1, response.getData().size());
        
        // 验证交互
        verify(retailerService, times(1)).getOrgPerson(request);
    }

    @Test
    void getRetailerInfo_ShouldReturnRetailerInfoResponseList() {
        // 准备测试数据
        RetailerInfoRequest request = new RetailerInfoRequest();
        List<RetailerInfoResponse> expectedResponseList = new ArrayList<>();
        RetailerInfoResponse retailerInfoResponse = new RetailerInfoResponse();
        expectedResponseList.add(retailerInfoResponse);
        
        // 配置mock行为
        when(retailerService.getRetailerInfo(any(RetailerInfoRequest.class))).thenReturn(expectedResponseList);
        
        // 执行测试
        CommonApiResponse<List<RetailerInfoResponse>> response = retailerController.getRetailerInfo(request);
        
        // 验证结果
        assertNotNull(response);
        assertEquals(0, response.getCode());
        assertEquals("ok", response.getMessage());
        assertEquals(expectedResponseList, response.getData());
        assertEquals(1, response.getData().size());
        
        // 验证交互
        verify(retailerService, times(1)).getRetailerInfo(request);
    }

    @Test
    void getToken_ShouldReturnTokenResponse() {
        // 准备测试数据
        DtTokenRequest request = new DtTokenRequest();
        DtTokenResponse expectedResponse = new DtTokenResponse();
        CommonResponse<DtTokenResponse> expectedCommonResponse = new CommonResponse<>(expectedResponse);
        
        // 配置mock行为
        when(retailerWebService.getToken(any(DtTokenRequest.class))).thenReturn(expectedCommonResponse);
        
        // 执行测试
        CommonResponse<DtTokenResponse> response = retailerController.getToken(request);
        
        // 验证结果
        assertNotNull(response);
        assertEquals(0, response.getCode());
        assertEquals("ok", response.getMessage());
        assertEquals(expectedResponse, response.getData());
        
        // 验证交互
        verify(retailerWebService, times(1)).getToken(request);
    }
}