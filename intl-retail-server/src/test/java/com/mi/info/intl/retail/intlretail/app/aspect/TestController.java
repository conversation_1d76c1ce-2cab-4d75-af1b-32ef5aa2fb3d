package com.mi.info.intl.retail.intlretail.app.aspect;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 测试Controller，用于验证AOP切面
 * 基于注解的切点测试
 */
@RestController
@RequestMapping("/test")
public class TestController {

    @PostMapping("/logTest")
    @AccessLog(response = true)
    public String logTest(@RequestBody String input) {
        return "Response: " + input;
    }
    
    @PostMapping("/logTestWithResponse")
    @AccessLog(response = true)
    public String logTestWithResponse(@RequestBody String input) {
        return "Response with logging: " + input;
    }
    
    @PostMapping("/logTestClassLevel")
    @AccessLog(response = true)
    public String logTestClassLevel(@RequestBody String input) {
        return "Response from class level annotation: " + input;
    }
    
    @PostMapping("/logTestDisableResponse")
    @AccessLog(response = false)
    public String logTestDisableResponse(@RequestBody String input) {
        return "Response disabled by method annotation: " + input;
    }
    
    @PostMapping("/logTestNoAnnotation")
    public String logTestNoAnnotation(@RequestBody String input) {
        return "Response without annotation: " + input;
    }
}
