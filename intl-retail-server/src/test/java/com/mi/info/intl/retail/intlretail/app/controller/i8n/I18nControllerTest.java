package com.mi.info.intl.retail.intlretail.app.controller.i8n;

import com.mi.info.intl.retail.intlretail.app.controller.i18n.AreaDetailReq;
import com.mi.info.intl.retail.intlretail.app.controller.i18n.GetAreaDetailResp;
import com.mi.info.intl.retail.intlretail.app.controller.i18n.I18nController;
import com.xiaomi.com.i18n.area.Area;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * <AUTHOR> Dong 董鑫儒
 * @Description
 * @Date 创建于 2025/8/6 19:44
 */
@ExtendWith(MockitoExtension.class)
public class I18nControllerTest {
    private I18nController controller;

    @BeforeEach
    void setUp() {
        controller = new I18nController();
    }

    @Test
    void testAreaDetail_QueryAll_Normal() {
        // queryAll=1，Area.all()有数据，Area.onlineAreaIdList()包含部分areaId
        AreaDetailReq req = new AreaDetailReq();
        req.setQueryAll(1);

        List<Area> areaList = new ArrayList<>();
        Area area1 = Mockito.mock(Area.class);
        Mockito.when(area1.getAreaId()).thenReturn("1001");
        Mockito.when(area1.getExtend()).thenReturn(null);
        areaList.add(area1);

        Area area2 = Mockito.mock(Area.class);
        Mockito.when(area2.getAreaId()).thenReturn("1002");
        Area.Extend extend = Mockito.mock(Area.Extend.class);
        Mockito.when(area2.getExtend()).thenReturn(extend);
        areaList.add(area2);

        List<String> onlineAreaIdList = new ArrayList<>();
        onlineAreaIdList.add("1001");
        onlineAreaIdList.add("1002");

        try (MockedStatic<Area> areaMockedStatic = Mockito.mockStatic(Area.class)) {
            areaMockedStatic.when(Area::all).thenReturn(areaList);
            areaMockedStatic.when(Area::onlineAreaIdList).thenReturn(onlineAreaIdList);

            Result<GetAreaDetailResp> result = controller.areaDetail(req);
            assertEquals(GeneralCodes.OK.getCode(), result.getCode());
            assertNotNull(result.getData());
            assertEquals(2, result.getData().getAreaItemList().size());
        }
    }

    @Test
    void testAreaDetail_QueryAll_EmptyAreaList() {
        // queryAll=1，Area.all()返回空
        AreaDetailReq req = new AreaDetailReq();
        req.setQueryAll(1);

        try (MockedStatic<Area> areaMockedStatic = Mockito.mockStatic(Area.class)) {
            areaMockedStatic.when(Area::all).thenReturn(Collections.emptyList());
            areaMockedStatic.when(Area::onlineAreaIdList).thenReturn(Collections.emptyList());

            Result<GetAreaDetailResp> result = controller.areaDetail(req);
            assertEquals(GeneralCodes.OK.getCode(), result.getCode());
            assertNotNull(result.getData());
            assertEquals(0, result.getData().getAreaItemList().size());
        }
    }

    @Test
    void testAreaDetail_QueryAll_OnlineAreaIdListEmpty() {
        // queryAll=1，Area.all()有数据，但onlineAreaIdList为空
        AreaDetailReq req = new AreaDetailReq();
        req.setQueryAll(1);

        List<Area> areaList = new ArrayList<>();
        Area area1 = Mockito.mock(Area.class);
        Mockito.when(area1.getAreaId()).thenReturn("1001");
        areaList.add(area1);

        try (MockedStatic<Area> areaMockedStatic = Mockito.mockStatic(Area.class)) {
            areaMockedStatic.when(Area::all).thenReturn(areaList);
            areaMockedStatic.when(Area::onlineAreaIdList).thenReturn(Collections.emptyList());

            Result<GetAreaDetailResp> result = controller.areaDetail(req);
            assertEquals(GeneralCodes.OK.getCode(), result.getCode());
            assertNotNull(result.getData());
            assertEquals(0, result.getData().getAreaItemList().size());
        }
    }

    @Test
    void testAreaDetail_QuerySingleArea_AreaIdInReq() {
        // queryAll!=1，req.getAreaId()有值，Area.of返回正常
        AreaDetailReq req = new AreaDetailReq();
        req.setQueryAll(0);
        req.setAreaId("2001");

        Area area = Mockito.mock(Area.class);
        Mockito.when(area.getExtend()).thenReturn(null);

        try (MockedStatic<Area> areaMockedStatic = Mockito.mockStatic(Area.class)) {
            areaMockedStatic.when(() -> Area.of("2001")).thenReturn(area);

            // mock RequestContextInfo.getAreaId() 不会被用到
            try (MockedStatic<com.xiaomi.nr.global.dev.base.RequestContextInfo> ctxMock = Mockito.mockStatic(com.xiaomi.nr.global.dev.base.RequestContextInfo.class)) {
                Result<GetAreaDetailResp> result = controller.areaDetail(req);
                assertEquals(GeneralCodes.OK.getCode(), result.getCode());
                assertNotNull(result.getData());
                assertEquals(1, result.getData().getAreaItemList().size());
            }
        }
    }

    @Test
    void testAreaDetail_QuerySingleArea_AreaIdFromHeader() {
        // queryAll!=1，req.getAreaId()为空，从header取，Area.of返回正常
        AreaDetailReq req = new AreaDetailReq();
        req.setQueryAll(0);
        req.setAreaId("");

        Area area = Mockito.mock(Area.class);

        try (MockedStatic<com.xiaomi.nr.global.dev.base.RequestContextInfo> ctxMock = Mockito.mockStatic(com.xiaomi.nr.global.dev.base.RequestContextInfo.class)) {
            ctxMock.when(com.xiaomi.nr.global.dev.base.RequestContextInfo::getAreaId).thenReturn("3001");

            try (MockedStatic<Area> areaMockedStatic = Mockito.mockStatic(Area.class)) {
                areaMockedStatic.when(() -> Area.of("ID")).thenReturn(area);

                Result<GetAreaDetailResp> result = controller.areaDetail(req);
                assertEquals(GeneralCodes.ParamError.getCode(), result.getCode());
                assertNull(result.getData());
            }
        }
    }

    @Test
    void testAreaDetail_QuerySingleArea_AreaNotFound() {
        // queryAll!=1，Area.of返回null
        AreaDetailReq req = new AreaDetailReq();
        req.setQueryAll(0);
        req.setAreaId("9999");

        try (MockedStatic<com.xiaomi.nr.global.dev.base.RequestContextInfo> ctxMock = Mockito.mockStatic(com.xiaomi.nr.global.dev.base.RequestContextInfo.class)) {
            ctxMock.when(com.xiaomi.nr.global.dev.base.RequestContextInfo::getAreaId).thenReturn("9999");

            try (MockedStatic<Area> areaMockedStatic = Mockito.mockStatic(Area.class)) {
                areaMockedStatic.when(() -> Area.of("9999")).thenReturn(null);

                Result<GetAreaDetailResp> result = controller.areaDetail(req);
                assertEquals(GeneralCodes.ParamError.getCode(), result.getCode());
                assertTrue(result.getMessage().contains("area not found"));
            }
        }
    }

    @Test
    void testAreaDetail_QuerySingleArea_AreaWithExtend() {
        // queryAll!=1，Area.of返回有extend
        AreaDetailReq req = new AreaDetailReq();
        req.setQueryAll(0);
        req.setAreaId("ID");

        Area area = Mockito.mock(Area.class);
        Area.Extend extend = Mockito.mock(Area.Extend.class);
        Mockito.when(area.getExtend()).thenReturn(extend);

        try (MockedStatic<com.xiaomi.nr.global.dev.base.RequestContextInfo> ctxMock = Mockito.mockStatic(com.xiaomi.nr.global.dev.base.RequestContextInfo.class)) {
            ctxMock.when(com.xiaomi.nr.global.dev.base.RequestContextInfo::getAreaId).thenReturn("ID");

            try (MockedStatic<Area> areaMockedStatic = Mockito.mockStatic(Area.class)) {
                areaMockedStatic.when(() -> Area.of("ID")).thenReturn(area);

                Result<GetAreaDetailResp> result = controller.areaDetail(req);
                assertEquals(GeneralCodes.OK.getCode(), result.getCode());
                assertNotNull(result.getData());
                assertEquals(1, result.getData().getAreaItemList().size());
                assertNotNull(result.getData().getAreaItemList().get(0).getExtend());
            }
        }
    }
}

