package com.mi.info.intl.retail.intlretail.app.controller;

import com.mi.info.intl.retail.intlretail.service.api.copilot.ICopilotService;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.xiaomi.nr.copilot.api.request.*;
import com.xiaomi.nr.copilot.api.response.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * 类描述：
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025-05-26 11:29
 */
@ExtendWith(MockitoExtension.class)
public class CopilotControllerTest {

    @Mock
    private ICopilotService iCopilotService;

    @InjectMocks
    private CopilotController copilotController;

    private MockHttpServletRequest mockHttpServletRequest;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        mockHttpServletRequest = new MockHttpServletRequest();
    }

    @Test
    public void testGetConfig_DefaultAreaId() {
        CopilotConfigRequest request = new CopilotConfigRequest();

        CopilotConfigResponse expectedResponse = new CopilotConfigResponse();
        expectedResponse.setChannelId(1);
        expectedResponse.setAreaId("ID");
        expectedResponse.setUserId("testUserId");
        expectedResponse.setShowCopilot(1);

        HttpServletRequest httpServletRequest = mock(HttpServletRequest.class);
        when(httpServletRequest.getHeader("X-Retail-Global-Area")).thenReturn("ID");
        when(iCopilotService.getConfig(request)).thenReturn(expectedResponse);

        Jwt jwt = mock(Jwt.class);
        when(jwt.getSubject()).thenReturn("testUserId");
        JwtAuthenticationToken token = new JwtAuthenticationToken(jwt);
        SecurityContextHolder.getContext().setAuthentication(token);

        // 调用被测试方法
        CommonApiResponse<CopilotConfigResponse> response = copilotController.getConfig(request, httpServletRequest);

        // 验证结果
        assertNotNull(response);
        assertEquals(expectedResponse, response.getData());
    }

    @Test
    public void testGetConfig_NonDefaultAreaId() {
        HttpServletRequest httpServletRequest = mock(HttpServletRequest.class);
        when(httpServletRequest.getHeader("X-Retail-Global-Area")).thenReturn("TEST_AREA");

        Jwt jwt = mock(Jwt.class);
        when(jwt.getSubject()).thenReturn("testUserId");
        JwtAuthenticationToken token = new JwtAuthenticationToken(jwt);
        SecurityContextHolder.getContext().setAuthentication(token);

        // 调用被测试方法
        CommonApiResponse<CopilotConfigResponse> response = copilotController.getConfig(new CopilotConfigRequest(), httpServletRequest);

        // 验证结果
        assertNotNull(response);
        assertEquals(0, response.getData().getShowCopilot());
    }

    @Test
    void testGetSpuInfo_Success() {
        // 准备测试数据
        GetSpuInfoRequest request = new GetSpuInfoRequest();
        request.setChannelId(1);
        request.setItemId("SPU123456");

        SpuInfoReponse response = new SpuInfoReponse();
        response.setImgUrl("https://example.com/image.jpg");
        response.setProductDetailUrl("https://example.com/product-detail.html");

        // 模拟iCopilotService的返回值
        when(iCopilotService.getSpuInfo(request)).thenReturn(response);

        // 调用被测试方法
        CommonApiResponse<SpuInfoReponse> actualResponse = copilotController.getSpuInfo(request, mockHttpServletRequest);

        // 验证结果
        assertEquals(response, actualResponse.getData());
        assertEquals(0, actualResponse.getCode());
        assertEquals("ok", actualResponse.getMessage());
    }

    @Test
    void testGetSpuInfo_DefaultChannelId() {
        // 准备测试数据
        GetSpuInfoRequest request = new GetSpuInfoRequest();
        request.setChannelId(null);
        request.setItemId("SPU123456");

        SpuInfoReponse response = new SpuInfoReponse();
        response.setImgUrl("https://example.com/image.jpg");
        response.setProductDetailUrl("https://example.com/product-detail.html");

        // 模拟iCopilotService的返回值
        when(iCopilotService.getSpuInfo(request)).thenReturn(response);

        // 调用被测试方法
        CommonApiResponse<SpuInfoReponse> actualResponse = copilotController.getSpuInfo(request, mockHttpServletRequest);

        // 验证结果
        assertEquals(CopilotController.DEFAULT_CHANNEL_ID, request.getChannelId());
        assertEquals(response, actualResponse.getData());
        assertEquals(0, actualResponse.getCode());
        assertEquals("ok", actualResponse.getMessage());
    }

    @Test
    void testGetSpuInfo_Exception() {
        // 准备测试数据
        GetSpuInfoRequest request = new GetSpuInfoRequest();
        request.setChannelId(1);
        request.setItemId("SPU123456");

        // 模拟iCopilotService抛出异常
        when(iCopilotService.getSpuInfo(request)).thenThrow(new RuntimeException("Service error"));

        // 调用被测试方法并验证是否抛出异常
        assertThrows(RuntimeException.class, () -> {
            copilotController.getSpuInfo(request, mockHttpServletRequest);
        });
    }

    @Test
    void testGetProductParams_Success() {
        // 准备测试数据
        AIAssistantProductParamsRequest request = new AIAssistantProductParamsRequest();
        request.setItemCode("123456");
        request.setChannelId(1);

        AIAssistantProductParamsResponse response = new AIAssistantProductParamsResponse();
        response.setItemName("test product");
        response.setParamGroups(new ArrayList<>());

        // 模拟iCopilotService的返回值
        when(iCopilotService.getProductParams(request)).thenReturn(response);

        Jwt jwt = mock(Jwt.class);
        when(jwt.getSubject()).thenReturn("123456");
        JwtAuthenticationToken token = new JwtAuthenticationToken(jwt);
        SecurityContextHolder.getContext().setAuthentication(token);

        // 调用被测试方法
        CommonApiResponse<AIAssistantProductParamsResponse> actualResponse = copilotController.getProductParams(request, mockHttpServletRequest);

        // 验证结果
        assertEquals(response, actualResponse.getData());
        assertEquals(0, actualResponse.getCode());
        assertEquals("ok", actualResponse.getMessage());
    }

    @Test
    void testGetProductParams_DefaultChannelId() {
        // 准备测试数据
        AIAssistantProductParamsRequest request = new AIAssistantProductParamsRequest();
        request.setItemCode("123456");
        request.setChannelId(null);

        AIAssistantProductParamsResponse response = new AIAssistantProductParamsResponse();
        response.setItemName("test product");
        response.setParamGroups(new ArrayList<>());

        // 模拟iCopilotService的返回值
        when(iCopilotService.getProductParams(request)).thenReturn(response);

        Jwt jwt = mock(Jwt.class);
        when(jwt.getSubject()).thenReturn("123456");
        JwtAuthenticationToken token = new JwtAuthenticationToken(jwt);
        SecurityContextHolder.getContext().setAuthentication(token);

        // 调用被测试方法
        CommonApiResponse<AIAssistantProductParamsResponse> actualResponse = copilotController.getProductParams(request, mockHttpServletRequest);

        // 验证结果
        assertEquals(response, actualResponse.getData());
        assertEquals(0, actualResponse.getCode());
        assertEquals("ok", actualResponse.getMessage());
    }

    @Test
    void testGetProductParams_Exception() {
        // 准备测试数据
        AIAssistantProductParamsRequest request = new AIAssistantProductParamsRequest();

        // 模拟iCopilotService抛出异常
        when(iCopilotService.getProductParams(request)).thenThrow(new RuntimeException("Service error"));

        // 调用被测试方法并验证是否抛出异常
        assertThrows(RuntimeException.class, () -> {
            copilotController.getProductParams(request, mockHttpServletRequest);
        });
    }

    @Test
    void testGetComparableProducts_Success() {
        // 准备测试数据
        AIAssistantComparableProductRequest request = new AIAssistantComparableProductRequest();
        request.setChannelId(1);
        request.setProductId("123456");
        request.setIsXiaomiProduct(true);

        AIAssistantComparableProductResponse response = new AIAssistantComparableProductResponse();
        response.setItems(new ArrayList<>());

        // 模拟iCopilotService的返回值
        when(iCopilotService.getComparableProducts(request)).thenReturn(response);

        // 模拟JWT鉴权
        Jwt jwt = mock(Jwt.class);
        when(jwt.getSubject()).thenReturn("testUser");
        JwtAuthenticationToken token = new JwtAuthenticationToken(jwt);
        SecurityContextHolder.getContext().setAuthentication(token);

        // 调用被测试方法
        CommonApiResponse<AIAssistantComparableProductResponse> actualResponse = copilotController.getComparableProducts(request, mockHttpServletRequest);

        // 验证结果
        assertEquals(response, actualResponse.getData());
        assertEquals(0, actualResponse.getCode());
        assertEquals("ok", actualResponse.getMessage());
    }

    @Test
    void testGetComparableProducts_DefaultChannelId() {
        // 准备测试数据
        AIAssistantComparableProductRequest request = new AIAssistantComparableProductRequest();
        request.setChannelId(null);
        request.setProductId("123456");
        request.setIsXiaomiProduct(true);

        AIAssistantComparableProductResponse response = new AIAssistantComparableProductResponse();
        response.setItems(new ArrayList<>());

        // 模拟iCopilotService的返回值
        when(iCopilotService.getComparableProducts(request)).thenReturn(response);

        // 模拟JWT鉴权
        Jwt jwt = mock(Jwt.class);
        when(jwt.getSubject()).thenReturn("testUser");
        JwtAuthenticationToken token = new JwtAuthenticationToken(jwt);
        SecurityContextHolder.getContext().setAuthentication(token);

        // 调用被测试方法
        CommonApiResponse<AIAssistantComparableProductResponse> actualResponse = copilotController.getComparableProducts(request, mockHttpServletRequest);

        // 验证结果
        assertEquals(response, actualResponse.getData());
        assertEquals(0, actualResponse.getCode());
        assertEquals("ok", actualResponse.getMessage());
    }

    @Test
    void testGetComparableProducts_Exception() {
        // 准备测试数据
        AIAssistantComparableProductRequest request = new AIAssistantComparableProductRequest();
        request.setChannelId(1);

        // 模拟iCopilotService抛出异常
        when(iCopilotService.getComparableProducts(request)).thenThrow(new RuntimeException("Service error"));

        // 模拟JWT鉴权
        Jwt jwt = mock(Jwt.class);
        when(jwt.getSubject()).thenReturn("testUser");
        JwtAuthenticationToken token = new JwtAuthenticationToken(jwt);
        SecurityContextHolder.getContext().setAuthentication(token);

        // 调用被测试方法并验证是否抛出异常
        assertThrows(RuntimeException.class, () -> {
            copilotController.getComparableProducts(request, mockHttpServletRequest);
        });
    }

    @Test
    void testContrastProductParams_Success() {
        // 准备测试数据
        AIAssistantContrastProductParamsRequest request = new AIAssistantContrastProductParamsRequest();
        request.setChannelId(1);
        request.setItemCode("123456");
        request.setTargetItemCode("654321");
        request.setOrgId("testOrg");


        AIAssistantContrastProductParamsResponse response = new AIAssistantContrastProductParamsResponse();
        response.setContrastProducts(new ArrayList<>());

        // 模拟iCopilotService的返回值
        when(iCopilotService.contrastProductParams(request)).thenReturn(response);

        // 模拟JWT鉴权
        Jwt jwt = mock(Jwt.class);
        when(jwt.getSubject()).thenReturn("testUser");
        JwtAuthenticationToken token = new JwtAuthenticationToken(jwt);
        SecurityContextHolder.getContext().setAuthentication(token);

        // 调用被测试方法
        CommonApiResponse<AIAssistantContrastProductParamsResponse> actualResponse = copilotController.contrastProductParams(request, mockHttpServletRequest);

        // 验证结果
        assertEquals(response, actualResponse.getData());
        assertEquals(0, actualResponse.getCode());
        assertEquals("ok", actualResponse.getMessage());
    }

    @Test
    void testContrastProductParams_DefaultChannelId() {
        // 准备测试数据
        AIAssistantContrastProductParamsRequest request = new AIAssistantContrastProductParamsRequest();
        request.setChannelId(null);
        request.setItemCode("123456");
        request.setTargetItemCode("654321");
        request.setOrgId("testOrg");


        AIAssistantContrastProductParamsResponse response = new AIAssistantContrastProductParamsResponse();
        response.setContrastProducts(new ArrayList<>());

        // 模拟iCopilotService的返回值
        when(iCopilotService.contrastProductParams(request)).thenReturn(response);

        // 模拟JWT鉴权
        Jwt jwt = mock(Jwt.class);
        when(jwt.getSubject()).thenReturn("testUser");
        JwtAuthenticationToken token = new JwtAuthenticationToken(jwt);
        SecurityContextHolder.getContext().setAuthentication(token);

        // 调用被测试方法
        CommonApiResponse<AIAssistantContrastProductParamsResponse> actualResponse = copilotController.contrastProductParams(request, mockHttpServletRequest);

        // 验证结果
        assertEquals(response, actualResponse.getData());
        assertEquals(0, actualResponse.getCode());
        assertEquals("ok", actualResponse.getMessage());
    }

    @Test
    void testContrastProductParams_Exception() {
        // 准备测试数据
        AIAssistantContrastProductParamsRequest request = new AIAssistantContrastProductParamsRequest();
        request.setChannelId(1);

        // 模拟iCopilotService抛出异常
        when(iCopilotService.contrastProductParams(request)).thenThrow(new RuntimeException("Service error"));

        // 模拟JWT鉴权
        Jwt jwt = mock(Jwt.class);
        when(jwt.getSubject()).thenReturn("testUser");
        JwtAuthenticationToken token = new JwtAuthenticationToken(jwt);
        SecurityContextHolder.getContext().setAuthentication(token);

        // 调用被测试方法并验证是否抛出异常
        assertThrows(RuntimeException.class, () -> {
            copilotController.contrastProductParams(request, mockHttpServletRequest);
        });
    }

    @Test
    void testGetUserComparisonHistory_Success() {
        // 准备测试数据
        CopilotComparisonHistoryRequest request = new CopilotComparisonHistoryRequest();
        request.setChannelId(1);

        AIAssistantProductComparisonHistoryResponse response = new AIAssistantProductComparisonHistoryResponse();
        response.setProductComparisonHistory(new ArrayList<>());

        // 模拟iCopilotService的返回值
        when(iCopilotService.getUserComparisonHistory(request)).thenReturn(response);

        // 模拟JWT鉴权
        Jwt jwt = mock(Jwt.class);
        when(jwt.getSubject()).thenReturn("testUser");
        JwtAuthenticationToken token = new JwtAuthenticationToken(jwt);
        SecurityContextHolder.getContext().setAuthentication(token);

        // 调用被测试方法
        CommonApiResponse<AIAssistantProductComparisonHistoryResponse> actualResponse = copilotController.getUserComparisonHistory(request, mockHttpServletRequest);

        // 验证结果
        assertEquals(response, actualResponse.getData());
        assertEquals(0, actualResponse.getCode());
        assertEquals("ok", actualResponse.getMessage());
    }

    @Test
    void testGetUserComparisonHistory_DefaultChannelId() {
        // 准备测试数据
        CopilotComparisonHistoryRequest request = new CopilotComparisonHistoryRequest();
        request.setChannelId(null);

        AIAssistantProductComparisonHistoryResponse response = new AIAssistantProductComparisonHistoryResponse();
        response.setProductComparisonHistory(new ArrayList<>());

        // 模拟iCopilotService的返回值
        when(iCopilotService.getUserComparisonHistory(request)).thenReturn(response);

        // 模拟JWT鉴权
        Jwt jwt = mock(Jwt.class);
        when(jwt.getSubject()).thenReturn("testUser");
        JwtAuthenticationToken token = new JwtAuthenticationToken(jwt);
        SecurityContextHolder.getContext().setAuthentication(token);

        // 调用被测试方法
        CommonApiResponse<AIAssistantProductComparisonHistoryResponse> actualResponse = copilotController.getUserComparisonHistory(request, mockHttpServletRequest);

        // 验证结果
        assertEquals(response, actualResponse.getData());
        assertEquals(0, actualResponse.getCode());
        assertEquals("ok", actualResponse.getMessage());
    }

    @Test
    void testGetUserComparisonHistory_Exception() {
        // 准备测试数据
        CopilotComparisonHistoryRequest request = new CopilotComparisonHistoryRequest();
        request.setChannelId(1);

        // 模拟iCopilotService抛出异常
        when(iCopilotService.getUserComparisonHistory(request)).thenThrow(new RuntimeException("Service error"));

        // 模拟JWT鉴权
        Jwt jwt = mock(Jwt.class);
        when(jwt.getSubject()).thenReturn("testUser");
        JwtAuthenticationToken token = new JwtAuthenticationToken(jwt);
        SecurityContextHolder.getContext().setAuthentication(token);

        // 调用被测试方法并验证是否抛出异常
        assertThrows(RuntimeException.class, () -> {
            copilotController.getUserComparisonHistory(request, mockHttpServletRequest);
        });
    }

    @Test
    void testBreakAIAnswer_Success() {
        // 准备测试数据
        BreakAIAnswerRequest request = new BreakAIAnswerRequest();
        request.setRequestId("1");

        // 模拟iCopilotService的返回值
        when(iCopilotService.breakAIAnswer(request)).thenReturn(true);

        // 调用被测试方法
        CommonApiResponse<Boolean> actualResponse = copilotController.breakAIAnswer(request);

        // 验证结果
        assertEquals(true, actualResponse.getData());
        assertEquals(0, actualResponse.getCode());

    }

    @Test
    void testGetFeedbackTags_Success() {
        AIAssistantFeedbackTagsRequest request = new AIAssistantFeedbackTagsRequest();
        request.setChannelId(1);

        AIAssistantFeedbackTagsResponse response = new AIAssistantFeedbackTagsResponse();

        Jwt jwt = mock(Jwt.class);
        when(jwt.getSubject()).thenReturn("testUser");
        JwtAuthenticationToken token = new JwtAuthenticationToken(jwt);
        SecurityContextHolder.getContext().setAuthentication(token);

        when(iCopilotService.getFeedbackTags(request)).thenReturn(response);

        CommonApiResponse<AIAssistantFeedbackTagsResponse> actualResponse = copilotController.getFeedbackTags(request, mockHttpServletRequest);

        assertEquals(response, actualResponse.getData());
        assertEquals(0, actualResponse.getCode());
        assertEquals("ok", actualResponse.getMessage());
    }

    @Test
    void testGetFeedbackTags_DefaultChannelId() {
        AIAssistantFeedbackTagsRequest request = new AIAssistantFeedbackTagsRequest();
        request.setChannelId(null);

        AIAssistantFeedbackTagsResponse response = new AIAssistantFeedbackTagsResponse();

        Jwt jwt = mock(Jwt.class);
        when(jwt.getSubject()).thenReturn("testUser");
        JwtAuthenticationToken token = new JwtAuthenticationToken(jwt);
        SecurityContextHolder.getContext().setAuthentication(token);

        when(iCopilotService.getFeedbackTags(request)).thenReturn(response);

        CommonApiResponse<AIAssistantFeedbackTagsResponse> actualResponse = copilotController.getFeedbackTags(request, mockHttpServletRequest);

        assertEquals(CopilotController.DEFAULT_CHANNEL_ID, request.getChannelId());
        assertEquals(response, actualResponse.getData());
        assertEquals(0, actualResponse.getCode());
        assertEquals("ok", actualResponse.getMessage());
    }

    @Test
    void testGetFeedbackTags_Exception() {
        AIAssistantFeedbackTagsRequest request = new AIAssistantFeedbackTagsRequest();
        request.setChannelId(1);

        Jwt jwt = mock(Jwt.class);
        when(jwt.getSubject()).thenReturn("testUser");
        JwtAuthenticationToken token = new JwtAuthenticationToken(jwt);
        SecurityContextHolder.getContext().setAuthentication(token);

        when(iCopilotService.getFeedbackTags(request)).thenThrow(new RuntimeException("Service error"));

        assertThrows(RuntimeException.class, () -> {
            copilotController.getFeedbackTags(request, mockHttpServletRequest);
        });
    }

    @Test
    void testGetHotQuestion_Success() {
        AIAssistantHotQuestionRequest request = new AIAssistantHotQuestionRequest();
        request.setChannelId(1);

        AIAssistantHotQuestionResponse response = new AIAssistantHotQuestionResponse();

        Jwt jwt = mock(Jwt.class);
        when(jwt.getSubject()).thenReturn("testUser");
        JwtAuthenticationToken token = new JwtAuthenticationToken(jwt);
        SecurityContextHolder.getContext().setAuthentication(token);

        when(iCopilotService.getHotQuestion(request)).thenReturn(response);

        CommonApiResponse<AIAssistantHotQuestionResponse> actualResponse = copilotController.getHotQuestion(request, mockHttpServletRequest);

        assertEquals(response, actualResponse.getData());
        assertEquals(0, actualResponse.getCode());
        assertEquals("ok", actualResponse.getMessage());
    }

    @Test
    void testGetHotQuestion_DefaultChannelId() {
        AIAssistantHotQuestionRequest request = new AIAssistantHotQuestionRequest();
        request.setChannelId(null);

        AIAssistantHotQuestionResponse response = new AIAssistantHotQuestionResponse();

        Jwt jwt = mock(Jwt.class);
        when(jwt.getSubject()).thenReturn("testUser");
        JwtAuthenticationToken token = new JwtAuthenticationToken(jwt);
        SecurityContextHolder.getContext().setAuthentication(token);

        when(iCopilotService.getHotQuestion(request)).thenReturn(response);

        CommonApiResponse<AIAssistantHotQuestionResponse> actualResponse = copilotController.getHotQuestion(request, mockHttpServletRequest);

        assertEquals(CopilotController.DEFAULT_CHANNEL_ID, request.getChannelId());
        assertEquals(response, actualResponse.getData());
        assertEquals(0, actualResponse.getCode());
        assertEquals("ok", actualResponse.getMessage());
    }

    @Test
    void testGetHotQuestion_Exception() {
        AIAssistantHotQuestionRequest request = new AIAssistantHotQuestionRequest();
        request.setChannelId(1);

        Jwt jwt = mock(Jwt.class);
        when(jwt.getSubject()).thenReturn("testUser");
        JwtAuthenticationToken token = new JwtAuthenticationToken(jwt);
        SecurityContextHolder.getContext().setAuthentication(token);

        when(iCopilotService.getHotQuestion(request)).thenThrow(new RuntimeException("Service error"));

        assertThrows(RuntimeException.class, () -> {
            copilotController.getHotQuestion(request, mockHttpServletRequest);
        });
    }

    @Test
    void testGetRelationQuestion_Success() {
        GetRelationQuestionRequest request = new GetRelationQuestionRequest();
        request.setChannelId(1);

        GetRelationQuestionResponse response = new GetRelationQuestionResponse();

        Jwt jwt = mock(Jwt.class);
        when(jwt.getSubject()).thenReturn("testUser");
        JwtAuthenticationToken token = new JwtAuthenticationToken(jwt);
        SecurityContextHolder.getContext().setAuthentication(token);

        when(iCopilotService.getRelationQuestion(request)).thenReturn(response);

        CommonApiResponse<GetRelationQuestionResponse> actualResponse = copilotController.getRelationQuestion(request, mockHttpServletRequest);

        assertEquals(response, actualResponse.getData());
        assertEquals(0, actualResponse.getCode());
        assertEquals("ok", actualResponse.getMessage());
    }

    @Test
    void testGetRelationQuestion_DefaultChannelId() {
        GetRelationQuestionRequest request = new GetRelationQuestionRequest();
        request.setChannelId(null);

        GetRelationQuestionResponse response = new GetRelationQuestionResponse();

        Jwt jwt = mock(Jwt.class);
        when(jwt.getSubject()).thenReturn("testUser");
        JwtAuthenticationToken token = new JwtAuthenticationToken(jwt);
        SecurityContextHolder.getContext().setAuthentication(token);

        when(iCopilotService.getRelationQuestion(request)).thenReturn(response);

        CommonApiResponse<GetRelationQuestionResponse> actualResponse = copilotController.getRelationQuestion(request, mockHttpServletRequest);

        assertEquals(CopilotController.DEFAULT_CHANNEL_ID, request.getChannelId());
        assertEquals(response, actualResponse.getData());
        assertEquals(0, actualResponse.getCode());
        assertEquals("ok", actualResponse.getMessage());
    }

    @Test
    void testGetRelationQuestion_Exception() {
        GetRelationQuestionRequest request = new GetRelationQuestionRequest();
        request.setChannelId(1);

        Jwt jwt = mock(Jwt.class);
        when(jwt.getSubject()).thenReturn("testUser");
        JwtAuthenticationToken token = new JwtAuthenticationToken(jwt);
        SecurityContextHolder.getContext().setAuthentication(token);

        when(iCopilotService.getRelationQuestion(request)).thenThrow(new RuntimeException("Service error"));

        assertThrows(RuntimeException.class, () -> {
            copilotController.getRelationQuestion(request, mockHttpServletRequest);
        });
    }

    @Test
    void testGetSpuListByCategory_Success() {
        GetItemListRequest request = new GetItemListRequest();
        request.setChannelId(1);

        List<GoodsItemResponseBase> response = new ArrayList<>();

        Jwt jwt = mock(Jwt.class);
        when(jwt.getSubject()).thenReturn("testUser");
        JwtAuthenticationToken token = new JwtAuthenticationToken(jwt);
        SecurityContextHolder.getContext().setAuthentication(token);

        when(iCopilotService.getSpuListByCategory(request)).thenReturn(response);

        CommonApiResponse<List<GoodsItemResponseBase>> actualResponse = copilotController.getSpuListByCategory(request, mockHttpServletRequest);

        assertEquals(response, actualResponse.getData());
        assertEquals(0, actualResponse.getCode());
        assertEquals("ok", actualResponse.getMessage());
    }

    @Test
    void testGetSpuListByCategory_DefaultChannelId() {
        GetItemListRequest request = new GetItemListRequest();
        request.setChannelId(null);

        List<GoodsItemResponseBase> response = new ArrayList<>();

        Jwt jwt = mock(Jwt.class);
        when(jwt.getSubject()).thenReturn("testUser");
        JwtAuthenticationToken token = new JwtAuthenticationToken(jwt);
        SecurityContextHolder.getContext().setAuthentication(token);

        when(iCopilotService.getSpuListByCategory(request)).thenReturn(response);

        CommonApiResponse<List<GoodsItemResponseBase>> actualResponse = copilotController.getSpuListByCategory(request, mockHttpServletRequest);

        assertEquals(CopilotController.DEFAULT_CHANNEL_ID, request.getChannelId());
        assertEquals(response, actualResponse.getData());
        assertEquals(0, actualResponse.getCode());
        assertEquals("ok", actualResponse.getMessage());
    }

    @Test
    void testGetSpuListByCategory_Exception() {
        GetItemListRequest request = new GetItemListRequest();
        request.setChannelId(1);

        Jwt jwt = mock(Jwt.class);
        when(jwt.getSubject()).thenReturn("testUser");
        JwtAuthenticationToken token = new JwtAuthenticationToken(jwt);
        SecurityContextHolder.getContext().setAuthentication(token);

        when(iCopilotService.getSpuListByCategory(request)).thenThrow(new RuntimeException("Service error"));

        assertThrows(RuntimeException.class, () -> {
            copilotController.getSpuListByCategory(request, mockHttpServletRequest);
        });
    }

    @Test
    void testGetAllSpuNames_Success() {
        List<String> response = new ArrayList<>();

        Jwt jwt = mock(Jwt.class);
        when(jwt.getSubject()).thenReturn("testUser");
        JwtAuthenticationToken token = new JwtAuthenticationToken(jwt);
        SecurityContextHolder.getContext().setAuthentication(token);

        when(iCopilotService.getAllSpuNames()).thenReturn(response);

        CommonApiResponse<List<String>> actualResponse = copilotController.getAllSpuNames();

        assertEquals(response, actualResponse.getData());
        assertEquals(0, actualResponse.getCode());
        assertEquals("ok", actualResponse.getMessage());
    }

    @Test
    void testGetAllSpuNames_DefaultChannelId() {

        List<String> response = new ArrayList<>();

        Jwt jwt = mock(Jwt.class);
        when(jwt.getSubject()).thenReturn("testUser");
        JwtAuthenticationToken token = new JwtAuthenticationToken(jwt);
        SecurityContextHolder.getContext().setAuthentication(token);

        when(iCopilotService.getAllSpuNames()).thenReturn(response);

        CommonApiResponse<List<String>> actualResponse = copilotController.getAllSpuNames();

        assertEquals(response, actualResponse.getData());
        assertEquals(0, actualResponse.getCode());
        assertEquals("ok", actualResponse.getMessage());
    }

    @Test
    void testGetAllSpuNames_Exception() {
        GetItemListRequest request = new GetItemListRequest();
        request.setChannelId(1);

        Jwt jwt = mock(Jwt.class);
        when(jwt.getSubject()).thenReturn("testUser");
        JwtAuthenticationToken token = new JwtAuthenticationToken(jwt);
        SecurityContextHolder.getContext().setAuthentication(token);

        when(iCopilotService.getAllSpuNames()).thenThrow(new RuntimeException("Service error"));

        assertThrows(RuntimeException.class, () -> {
            copilotController.getAllSpuNames();
        });
    }
}
