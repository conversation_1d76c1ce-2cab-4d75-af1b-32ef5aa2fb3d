package com.mi.info.intl.retail.intlretail.app.controller.ldu;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;

import java.util.Arrays;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.context.ActiveProfiles;

import com.mi.info.intl.retail.intlretail.service.api.ldu.IntlLduReportLogService;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.CommonResponse;

/**
 * IntlLduReportLogController 测试类
 * 主要测试 getDistinctProjectCodes 方法
 */
//@SpringBootTest
@ActiveProfiles("test")
@DisplayName("IntlLduReportLogController 测试")
class IntlLduReportLogControllerTest {

    @Mock
    private IntlLduReportLogService intlLduReportLogService;

    @InjectMocks
    private IntlLduReportLogController intlLduReportLogController;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    @DisplayName("成功获取不重复的 project_code 列表")
    void testGetDistinctProjectCodes_Success() {
        // 准备测试数据
        List<String> expectedProjectCodes = Arrays.asList("PROJECT_A", "PROJECT_B", "PROJECT_C");
        CommonResponse<List<String>> serviceResponse = new CommonResponse<>(expectedProjectCodes);
        
        // Mock 服务方法
        when(intlLduReportLogService.getDistinctProjectCodes()).thenReturn(serviceResponse);
        
        // 执行测试
        CommonResponse<List<String>> response = intlLduReportLogController.getDistinctProjectCodes();
        
        // 验证结果
        assertNotNull(response, "控制器响应不应为空");
        assertEquals(0, response.getCode(), "成功时 code 应该为 0");
        assertNotNull(response.getData(), "数据不应为空");
        assertEquals(expectedProjectCodes, response.getData(), "返回的数据应该与期望值一致");
        assertEquals(3, response.getData().size(), "应该返回3个 project_code");
        assertEquals("ok", response.getMessage(), "成功时消息应该为 'ok'");
        
        // 验证服务方法被调用
        verify(intlLduReportLogService, times(1)).getDistinctProjectCodes();
        verifyNoMoreInteractions(intlLduReportLogService);
    }

    @Test
    @DisplayName("获取空列表")
    void testGetDistinctProjectCodes_EmptyResult() {
        // 准备测试数据 - 空列表
        List<String> expectedProjectCodes = Arrays.asList();
        CommonResponse<List<String>> serviceResponse = new CommonResponse<>(expectedProjectCodes);
        
        // Mock 服务方法
        when(intlLduReportLogService.getDistinctProjectCodes()).thenReturn(serviceResponse);
        
        // 执行测试
        CommonResponse<List<String>> response = intlLduReportLogController.getDistinctProjectCodes();
        
        // 验证结果
        assertNotNull(response, "控制器响应不应为空");
        assertEquals(0, response.getCode(), "成功时 code 应该为 0");
        assertNotNull(response.getData(), "数据不应为空");
        assertEquals(expectedProjectCodes, response.getData(), "返回的数据应该与期望值一致");
        assertEquals(0, response.getData().size(), "应该返回0个 project_code");
        assertTrue(response.getData().isEmpty(), "列表应该为空");
        
        // 验证服务方法被调用
        verify(intlLduReportLogService, times(1)).getDistinctProjectCodes();
        verifyNoMoreInteractions(intlLduReportLogService);
    }

    @Test
    @DisplayName("服务层异常处理")
    void testGetDistinctProjectCodes_ServiceException() {
        // Mock 服务方法抛出异常
        when(intlLduReportLogService.getDistinctProjectCodes()).thenThrow(new RuntimeException("Service error"));
        
        // 执行测试并验证异常
        assertThrows(RuntimeException.class, () -> {
            intlLduReportLogController.getDistinctProjectCodes();
        }, "应该抛出 RuntimeException");
        
        // 验证服务方法被调用
        verify(intlLduReportLogService, times(1)).getDistinctProjectCodes();
        verifyNoMoreInteractions(intlLduReportLogService);
    }

    @Test
    @DisplayName("获取大量 project_code")
    void testGetDistinctProjectCodes_LargeResult() {
        // 准备测试数据 - 大量 project_code
        List<String> expectedProjectCodes = Arrays.asList(
            "PROJECT_001", "PROJECT_002", "PROJECT_003", "PROJECT_004", "PROJECT_005",
            "PROJECT_006", "PROJECT_007", "PROJECT_008", "PROJECT_009", "PROJECT_010"
        );
        CommonResponse<List<String>> serviceResponse = new CommonResponse<>(expectedProjectCodes);
        
        // Mock 服务方法
        when(intlLduReportLogService.getDistinctProjectCodes()).thenReturn(serviceResponse);
        
        // 执行测试
        CommonResponse<List<String>> response = intlLduReportLogController.getDistinctProjectCodes();
        
        // 验证结果
        assertNotNull(response, "控制器响应不应为空");
        assertEquals(0, response.getCode(), "成功时 code 应该为 0");
        assertNotNull(response.getData(), "数据不应为空");
        assertEquals(expectedProjectCodes, response.getData(), "返回的数据应该与期望值一致");
        assertEquals(10, response.getData().size(), "应该返回10个 project_code");
        
        // 验证数据排序（按字母顺序）
        List<String> sortedData = response.getData();
        for (int i = 0; i < sortedData.size() - 1; i++) {
            assertTrue(sortedData.get(i).compareTo(sortedData.get(i + 1)) <= 0, 
                "数据应该按字母顺序排序");
        }
        
        // 验证服务方法被调用
        verify(intlLduReportLogService, times(1)).getDistinctProjectCodes();
        verifyNoMoreInteractions(intlLduReportLogService);
    }

    @Test
    @DisplayName("验证控制器响应的完整性")
    void testGetDistinctProjectCodes_ResponseIntegrity() {
        // 准备测试数据
        List<String> expectedProjectCodes = Arrays.asList("TEST_PROJECT");
        CommonResponse<List<String>> serviceResponse = new CommonResponse<>(expectedProjectCodes);
        
        // Mock 服务方法
        when(intlLduReportLogService.getDistinctProjectCodes()).thenReturn(serviceResponse);
        
        // 执行测试
        CommonResponse<List<String>> response = intlLduReportLogController.getDistinctProjectCodes();
        
        // 验证响应完整性
        assertNotNull(response, "控制器响应不应为空");
        assertEquals(0, response.getCode(), "成功时 code 应该为 0");
        assertEquals("ok", response.getMessage(), "成功时消息应该为 'ok'");
        assertNotNull(response.getData(), "数据不应为空");
        assertEquals(1, response.getData().size(), "应该返回1个 project_code");
        assertEquals("TEST_PROJECT", response.getData().get(0), "第一个元素应该正确");
        
        // 验证服务方法被调用
        verify(intlLduReportLogService, times(1)).getDistinctProjectCodes();
        verifyNoMoreInteractions(intlLduReportLogService);
    }

    @Test
    @DisplayName("测试控制器方法的幂等性")
    void testGetDistinctProjectCodes_Idempotency() {
        // 准备测试数据
        List<String> expectedProjectCodes = Arrays.asList("PROJECT_A", "PROJECT_B");
        CommonResponse<List<String>> serviceResponse = new CommonResponse<>(expectedProjectCodes);
        
        // Mock 服务方法
        when(intlLduReportLogService.getDistinctProjectCodes()).thenReturn(serviceResponse);
        
        // 执行多次调用
        CommonResponse<List<String>> firstResponse = intlLduReportLogController.getDistinctProjectCodes();
        CommonResponse<List<String>> secondResponse = intlLduReportLogController.getDistinctProjectCodes();
        CommonResponse<List<String>> thirdResponse = intlLduReportLogController.getDistinctProjectCodes();
        
        // 验证结果一致性
        assertNotNull(firstResponse, "第一次响应不应为空");
        assertNotNull(secondResponse, "第二次响应不应为空");
        assertNotNull(thirdResponse, "第三次响应不应为空");
        
        assertEquals(firstResponse.getCode(), secondResponse.getCode(), "响应 code 应该一致");
        assertEquals(secondResponse.getCode(), thirdResponse.getCode(), "响应 code 应该一致");
        
        assertEquals(firstResponse.getData(), secondResponse.getData(), "响应数据应该一致");
        assertEquals(secondResponse.getData(), thirdResponse.getData(), "响应数据应该一致");
        
        // 验证服务方法被调用次数
        verify(intlLduReportLogService, times(3)).getDistinctProjectCodes();
        verifyNoMoreInteractions(intlLduReportLogService);
    }
} 