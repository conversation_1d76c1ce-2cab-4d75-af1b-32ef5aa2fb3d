package com.mi.info.intl.retail.intlretail.app.exception.handler;

import com.mi.info.intl.retail.model.CommonApiResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * 参数校验异常处理器测试
 */
@ExtendWith(MockitoExtension.class)
class ValidationExceptionTest {

    private CustomGlobalExceptionHandler handler;

    @BeforeEach
    void setUp() {
        handler = new CustomGlobalExceptionHandler();
    }

    @Test
    void testHandleValidationException() {
        // 创建模拟的BindingResult
        BindingResult bindingResult = mock(BindingResult.class);
        FieldError fieldError1 = new FieldError("testObject", "country", "国家短码不能为空");
        FieldError fieldError2 = new FieldError("testObject", "positionCode", "阵地编码不能为空");
        
        when(bindingResult.getFieldErrors()).thenReturn(Arrays.asList(fieldError1, fieldError2));
        
        // 创建MethodArgumentNotValidException
        MethodArgumentNotValidException ex = mock(MethodArgumentNotValidException.class);
        when(ex.getBindingResult()).thenReturn(bindingResult);
        when(ex.getMessage()).thenReturn("Validation failed for argument");
        
        // 执行测试
        CommonApiResponse<String> response = handler.handleValidationException(ex);
        
        // 验证结果
        assertNotNull(response);
        assertEquals(410, response.getCode());
        assertTrue(response.getMessage().contains("参数校验失败"));
        assertTrue(response.getMessage().contains("country: 国家短码不能为空"));
        assertTrue(response.getMessage().contains("positionCode: 阵地编码不能为空"));
    }

    @Test
    void testHandleConstraintViolationException() {
        // 创建模拟的ConstraintViolation
        ConstraintViolation<?> violation = mock(ConstraintViolation.class);
        when(violation.getMessage()).thenReturn("用户ID不能为空");
        
        Set<ConstraintViolation<?>> violations = new HashSet<>();
        violations.add(violation);
        
        // 创建ConstraintViolationException
        ConstraintViolationException ex = new ConstraintViolationException("约束校验失败", violations);
        
        // 执行测试
        CommonApiResponse<String> response = handler.handleConstraintViolationException(ex);
        
        // 验证结果
        assertNotNull(response);
        assertEquals(410, response.getCode());
        assertTrue(response.getMessage().contains("约束校验失败"));
        assertTrue(response.getMessage().contains("用户ID不能为空"));
    }
}
