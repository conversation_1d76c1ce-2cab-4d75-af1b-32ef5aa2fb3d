package com.mi.info.intl.retail.intlretail.app.controller.ldu;

import com.mi.info.intl.retail.intlretail.service.api.ldu.IntlRmsProductApiService;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.ProjectInfoDto;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.SearchProductReq;
import com.mi.info.intl.retail.model.CommonApiResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * IntlRmsProductController 单元测试
 *
 * <AUTHOR>
 * @date 2025/1/27
 */
@ExtendWith(MockitoExtension.class)
class IntlRmsProductControllerTest {

    @Mock
    private IntlRmsProductApiService intlRmsProductApiService;

    @InjectMocks
    private IntlRmsProductController intlRmsProductController;

    private ProjectInfoDto mockProduct1;
    private ProjectInfoDto mockProduct2;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        mockProduct1 = new ProjectInfoDto();
        mockProduct1.setGoodsId("MI001");
        mockProduct1.setName("小米手机13");
        mockProduct1.setProductLine("智能手机");
        mockProduct1.setProductLineCode(1L);
        mockProduct1.setProductLineEn("Smartphone");

        mockProduct2 = new ProjectInfoDto();
        mockProduct2.setGoodsId("MI002");
        mockProduct2.setName("小米手机13 Pro");
        mockProduct2.setProductLine("智能手机");
        mockProduct2.setProductLineCode(1L);
        mockProduct2.setProductLineEn("Smartphone");
    }

    @Test
    void searchProductsByProjectCode_FuzzySearch_Success() {
        // Given
        String keyword = "MI";
        String searchType = "FUZZY";
        List<ProjectInfoDto> mockProducts = Arrays.asList(mockProduct1, mockProduct2);
        CommonApiResponse<List<ProjectInfoDto>> mockResponse = new CommonApiResponse<>(mockProducts);
        when(intlRmsProductApiService.searchProductsByProjectCode(any(SearchProductReq.class))).thenReturn(mockResponse);

        // When
        CommonApiResponse<List<ProjectInfoDto>> response = intlRmsProductController.searchProductsByProjectCode(keyword, searchType);

        // Then
        assertNotNull(response);
        assertEquals(200, response.getCode());
        assertEquals("success", response.getMessage());

        List<ProjectInfoDto> data = response.getData();
        assertNotNull(data);
        assertEquals(2, data.size());

        // 验证第一个产品
        assertEquals("MI001", data.get(0).getGoodsId());
        assertEquals("小米手机13", data.get(0).getName());
        assertEquals("智能手机", data.get(0).getProductLine());
        assertEquals(1L, data.get(0).getProductLineCode());
        assertEquals("Smartphone", data.get(0).getProductLineEn());

        // 验证第二个产品
        assertEquals("MI002", data.get(1).getGoodsId());
        assertEquals("小米手机13 Pro", data.get(1).getName());
        assertEquals("智能手机", data.get(1).getProductLine());
        assertEquals(1L, data.get(1).getProductLineCode());
        assertEquals("Smartphone", data.get(1).getProductLineEn());

        verify(intlRmsProductApiService, times(1)).searchProductsByProjectCode(any(SearchProductReq.class));
    }

    @Test
    void searchProductsByProjectCode_ExactSearch_Success() {
        // Given
        String keyword = "MI13";
        String searchType = "EXACT";
        List<ProjectInfoDto> mockProducts = Arrays.asList(mockProduct1);
        CommonApiResponse<List<ProjectInfoDto>> mockResponse = new CommonApiResponse<>(mockProducts);
        when(intlRmsProductApiService.searchProductsByProjectCode(any(SearchProductReq.class))).thenReturn(mockResponse);

        // When
        CommonApiResponse<List<ProjectInfoDto>> response = intlRmsProductController.searchProductsByProjectCode(keyword, searchType);

        // Then
        assertNotNull(response);
        assertEquals(200, response.getCode());
        assertEquals("success", response.getMessage());

        List<ProjectInfoDto> data = response.getData();
        assertNotNull(data);
        assertEquals(1, data.size());

        // 验证产品信息
        assertEquals("MI001", data.get(0).getGoodsId());
        assertEquals("小米手机13", data.get(0).getName());
        assertEquals("智能手机", data.get(0).getProductLine());
        assertEquals(1L, data.get(0).getProductLineCode());
        assertEquals("Smartphone", data.get(0).getProductLineEn());

        verify(intlRmsProductApiService, times(1)).searchProductsByProjectCode(any(SearchProductReq.class));
    }

    @Test
    void searchProductsByProjectCode_DefaultFuzzySearch() {
        // Given
        String keyword = "MI";
        // 不传递searchType参数，应该默认为FUZZY
        List<ProjectInfoDto> mockProducts = Arrays.asList(mockProduct1, mockProduct2);
        CommonApiResponse<List<ProjectInfoDto>> mockResponse = new CommonApiResponse<>(mockProducts);
        when(intlRmsProductApiService.searchProductsByProjectCode(any(SearchProductReq.class))).thenReturn(mockResponse);

        // When
        CommonApiResponse<List<ProjectInfoDto>> response = intlRmsProductController.searchProductsByProjectCode(keyword, null);

        // Then
        assertNotNull(response);
        assertEquals(200, response.getCode());
        assertEquals("success", response.getMessage());

        List<ProjectInfoDto> data = response.getData();
        assertNotNull(data);
        assertEquals(2, data.size());

        verify(intlRmsProductApiService, times(1)).searchProductsByProjectCode(any(SearchProductReq.class));
    }

    @Test
    void searchProductsByProjectCode_EmptyResult() {
        // Given
        String keyword = "NONEXISTENT";
        String searchType = "FUZZY";
        CommonApiResponse<List<ProjectInfoDto>> mockResponse = new CommonApiResponse<>(Collections.emptyList());
        when(intlRmsProductApiService.searchProductsByProjectCode(any(SearchProductReq.class))).thenReturn(mockResponse);

        // When
        CommonApiResponse<List<ProjectInfoDto>> response = intlRmsProductController.searchProductsByProjectCode(keyword, searchType);

        // Then
        assertNotNull(response);
        assertEquals(200, response.getCode());
        assertEquals("success", response.getMessage());

        List<ProjectInfoDto> data = response.getData();
        assertNotNull(data);
        assertTrue(data.isEmpty());

        verify(intlRmsProductApiService, times(1)).searchProductsByProjectCode(any(SearchProductReq.class));
    }

    @Test
    void searchProductsByProjectCode_ServiceThrowsException() {
        // Given
        String keyword = "MI";
        String searchType = "FUZZY";
        when(intlRmsProductApiService.searchProductsByProjectCode(any(SearchProductReq.class)))
                .thenThrow(new RuntimeException("Service error"));

        // When
        CommonApiResponse<List<ProjectInfoDto>> response = intlRmsProductController.searchProductsByProjectCode(keyword, searchType);

        // Then
        assertNotNull(response);
        assertEquals(500, response.getCode());
        assertTrue(response.getMessage().contains("查询失败"));
        assertNull(response.getData());

        verify(intlRmsProductApiService, times(1)).searchProductsByProjectCode(any(SearchProductReq.class));
    }

    @Test
    void searchProductsByProjectCode_SingleProduct() {
        // Given
        String keyword = "MI13";
        String searchType = "EXACT";
        List<ProjectInfoDto> mockProducts = Arrays.asList(mockProduct1);
        CommonApiResponse<List<ProjectInfoDto>> mockResponse = new CommonApiResponse<>(mockProducts);
        when(intlRmsProductApiService.searchProductsByProjectCode(any(SearchProductReq.class))).thenReturn(mockResponse);

        // When
        CommonApiResponse<List<ProjectInfoDto>> response = intlRmsProductController.searchProductsByProjectCode(keyword, searchType);

        // Then
        assertNotNull(response);
        assertEquals(200, response.getCode());
        assertEquals("success", response.getMessage());

        List<ProjectInfoDto> data = response.getData();
        assertNotNull(data);
        assertEquals(1, data.size());

        // 验证产品信息
        assertEquals("MI001", data.get(0).getGoodsId());
        assertEquals("小米手机13", data.get(0).getName());
        assertEquals("智能手机", data.get(0).getProductLine());
        assertEquals(1L, data.get(0).getProductLineCode());
        assertEquals("Smartphone", data.get(0).getProductLineEn());

        verify(intlRmsProductApiService, times(1)).searchProductsByProjectCode(any(SearchProductReq.class));
    }

    @Test
    void searchProductsByProjectCode_MaxResults() {
        // Given
        String keyword = "MI";
        String searchType = "FUZZY";
        List<ProjectInfoDto> mockProducts = Arrays.asList(mockProduct1, mockProduct2);
        CommonApiResponse<List<ProjectInfoDto>> mockResponse = new CommonApiResponse<>(mockProducts);
        when(intlRmsProductApiService.searchProductsByProjectCode(any(SearchProductReq.class))).thenReturn(mockResponse);

        // When
        CommonApiResponse<List<ProjectInfoDto>> response = intlRmsProductController.searchProductsByProjectCode(keyword, searchType);

        // Then
        assertNotNull(response);
        assertEquals(200, response.getCode());
        assertEquals("success", response.getMessage());

        List<ProjectInfoDto> data = response.getData();
        assertNotNull(data);
        assertEquals(2, data.size());

        verify(intlRmsProductApiService, times(1)).searchProductsByProjectCode(any(SearchProductReq.class));
    }

    @Test
    void searchProductsByProjectCode_SpecialCharacters() {
        // Given
        String keyword = "MI-13";
        String searchType = "FUZZY";
        List<ProjectInfoDto> mockProducts = Arrays.asList(mockProduct1);
        CommonApiResponse<List<ProjectInfoDto>> mockResponse = new CommonApiResponse<>(mockProducts);
        when(intlRmsProductApiService.searchProductsByProjectCode(any(SearchProductReq.class))).thenReturn(mockResponse);

        // When
        CommonApiResponse<List<ProjectInfoDto>> response = intlRmsProductController.searchProductsByProjectCode(keyword, searchType);

        // Then
        assertNotNull(response);
        assertEquals(200, response.getCode());
        assertEquals("success", response.getMessage());

        List<ProjectInfoDto> data = response.getData();
        assertNotNull(data);
        assertEquals(1, data.size());

        verify(intlRmsProductApiService, times(1)).searchProductsByProjectCode(any(SearchProductReq.class));
    }

    @Test
    void searchProductsByProjectCode_LongProjectCode() {
        // Given
        String keyword = "MI13PROMAXULTRA";
        String searchType = "EXACT";
        List<ProjectInfoDto> mockProducts = Arrays.asList(mockProduct2);
        CommonApiResponse<List<ProjectInfoDto>> mockResponse = new CommonApiResponse<>(mockProducts);
        when(intlRmsProductApiService.searchProductsByProjectCode(any(SearchProductReq.class))).thenReturn(mockResponse);

        // When
        CommonApiResponse<List<ProjectInfoDto>> response = intlRmsProductController.searchProductsByProjectCode(keyword, searchType);

        // Then
        assertNotNull(response);
        assertEquals(200, response.getCode());
        assertEquals("success", response.getMessage());

        List<ProjectInfoDto> data = response.getData();
        assertNotNull(data);
        assertEquals(1, data.size());

        verify(intlRmsProductApiService, times(1)).searchProductsByProjectCode(any(SearchProductReq.class));
    }

    @Test
    void searchProductsByProjectCode_ChineseProjectCode() {
        // Given
        String keyword = "小米";
        String searchType = "FUZZY";
        List<ProjectInfoDto> mockProducts = Arrays.asList(mockProduct1);
        CommonApiResponse<List<ProjectInfoDto>> mockResponse = new CommonApiResponse<>(mockProducts);
        when(intlRmsProductApiService.searchProductsByProjectCode(any(SearchProductReq.class))).thenReturn(mockResponse);

        // When
        CommonApiResponse<List<ProjectInfoDto>> response = intlRmsProductController.searchProductsByProjectCode(keyword, searchType);

        // Then
        assertNotNull(response);
        assertEquals(200, response.getCode());
        assertEquals("success", response.getMessage());

        List<ProjectInfoDto> data = response.getData();
        assertNotNull(data);
        assertEquals(1, data.size());

        verify(intlRmsProductApiService, times(1)).searchProductsByProjectCode(any(SearchProductReq.class));
    }

    @Test
    void searchProductsByProjectCode_NullProjectCode() {
        // Given
        String keyword = null;
        String searchType = "FUZZY";
        CommonApiResponse<List<ProjectInfoDto>> mockResponse = new CommonApiResponse<>(Collections.emptyList());
        when(intlRmsProductApiService.searchProductsByProjectCode(any(SearchProductReq.class))).thenReturn(mockResponse);

        // When
        CommonApiResponse<List<ProjectInfoDto>> response = intlRmsProductController.searchProductsByProjectCode(keyword, searchType);

        // Then
        assertNotNull(response);
        assertEquals(200, response.getCode());
        assertEquals("success", response.getMessage());

        List<ProjectInfoDto> data = response.getData();
        assertNotNull(data);
        assertTrue(data.isEmpty());

        verify(intlRmsProductApiService, times(1)).searchProductsByProjectCode(any(SearchProductReq.class));
    }
}
