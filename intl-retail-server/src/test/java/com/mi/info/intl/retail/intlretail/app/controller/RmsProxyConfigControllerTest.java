package com.mi.info.intl.retail.intlretail.app.controller;

import com.mi.info.intl.retail.intlretail.service.api.proxy.RetailerAppConfigService;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.RmsUserBaseDataResponse;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.org.domain.position.service.PositionInspectionDomainService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class RmsProxyConfigControllerTest {

    @InjectMocks
    private RmsProxyConfigController rmsProxyConfigController;

    @Mock
    private RetailerAppConfigService retailerAppConfigService;

    @Mock
    private PositionInspectionDomainService positionInspectionDomainService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void homeFunctions_hasUnCompletedTask_addsPositionInspection() {
        // Arrange
        String account = "testAccount";
        String tokenValue = "testToken";

        // mock SecurityContextHolder 和 getAccount()
        Jwt jwt = mock(Jwt.class);
        when(jwt.getTokenValue()).thenReturn(tokenValue);
        JwtAuthenticationToken token = mock(JwtAuthenticationToken.class);
        when(token.getToken()).thenReturn(jwt);
        when(token.getTokenAttributes()).thenReturn(Collections.singletonMap("upn", account));
        SecurityContextHolder.getContext().setAuthentication(token);

        // mock userBaseInfo.getMenu()
        RmsUserBaseDataResponse.MenuInfo menuInfo = new RmsUserBaseDataResponse.MenuInfo();
        menuInfo.setScreenName("MenuA");
        List<RmsUserBaseDataResponse.MenuInfo> menuList = new ArrayList<>();
        menuList.add(menuInfo);
        RmsUserBaseDataResponse userBaseInfo = mock(RmsUserBaseDataResponse.class);
        when(userBaseInfo.getMenu()).thenReturn(menuList);
        when(retailerAppConfigService.requestRmsGetUserMenuInfo(eq(account), eq(tokenValue))).thenReturn(userBaseInfo);

        // mock positionInspectionDomainService.hasUnCompletedTask 返回true
        when(positionInspectionDomainService.hasUnCompletedTask(account)).thenReturn(true);

        // Act
        CommonApiResponse<List<String>> response = rmsProxyConfigController.homeFunctions();

        // Assert
        List<String> result = response.getData();
        assertTrue(result.contains("MenuA"));
        assertTrue(result.contains("PositionInspection"));
        assertEquals(2, result.size());
    }
} 