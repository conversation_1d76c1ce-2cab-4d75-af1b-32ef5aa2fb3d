package com.mi.info.intl.retail.intlretail.app.aspect;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 示例Controller，演示@AccessLog注解的使用
 * 只有标记了注解的方法才会打印响应内容
 */
@RestController
@RequestMapping("/example")
public class ExampleController {

    /**
     * 标记了@AccessLog注解，会打印响应内容
     */
    @PostMapping("/method1")
    @AccessLog(response = true)
    public String method1(@RequestBody String input) {
        return "Method1 response: " + input;
    }

    /**
     * 标记了@AccessLog注解，会打印响应内容
     */
    @PostMapping("/method2")
    @AccessLog(response = true)
    public String method2(@RequestBody String input) {
        return "Method2 response: " + input;
    }

    /**
     * 标记了@AccessLog(response = false)注解，禁用响应日志
     */
    @PostMapping("/method3")
    @AccessLog(response = false)
    public String method3(@RequestBody String input) {
        return "Method3 response (no logging): " + input;
    }

    /**
     * 标记了@AccessLog(response = true)注解，明确启用响应日志
     */
    @PostMapping("/method4")
    @AccessLog(response = true)
    public String method4(@RequestBody String input) {
        return "Method4 response (explicit logging): " + input;
    }
    
    /**
     * 没有注解，不会被切面拦截
     */
    @PostMapping("/method5")
    public String method5(@RequestBody String input) {
        return "Method5 response (no annotation): " + input;
    }
}
