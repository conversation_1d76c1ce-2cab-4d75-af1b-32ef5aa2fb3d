package com.mi.info.intl.retail.management.service.impl;

import com.mi.info.intl.retail.intlretail.service.api.management.dto.RuleQueryReq;
import com.mi.info.intl.retail.management.entity.StoreGradeRule;
import com.mi.info.intl.retail.management.mapper.StoreGradeMapper;
import com.mi.info.intl.retail.management.mapper.StoreGradeRuleMapper;
import com.mi.info.intl.retail.management.mapper.StoreGradeRelationMapper;
import com.mi.info.intl.retail.model.ChannelTypeStatistics;
import com.mi.info.intl.retail.model.StoreGradeCompleteStatistics;
import com.mi.info.intl.retail.intlretail.service.api.retailer.dto.CommonResponse;
import com.mi.info.intl.retail.utils.RpcContextUtil;
import com.mi.info.intl.retail.intlretail.app.StartApplication;
import org.apache.dubbo.rpc.RpcContext;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * StoreGradeService Spring Boot 集成测试
 * 注意：此类不会被 Maven test 扫描到，需要手动运行
 */
//@SpringBootTest(classes = StartApplication.class)
@ActiveProfiles("test")
@TestPropertySource(properties = {
    "spring.main.allow-bean-definition-overriding=true",
    "logging.level.com.mi.info.intl.retail=DEBUG"
})
@Transactional
public class StoreGradeServiceIntegrationTest {

    @Resource
    private StoreGradeServiceImpl storeGradeService;

    @Resource
    private StoreGradeMapper storeGradeMapper;

    @Resource
    private StoreGradeRuleMapper storeGradeRuleMapper;

    @Resource
    private StoreGradeRelationMapper storeGradeRelationMapper;

    @BeforeEach
    void setUp() {
        // 设置 RPC 上下文的国家代码
        setupRpcContext();
        
        // 清理测试数据
        storeGradeRelationMapper.delete(null);
        storeGradeRuleMapper.delete(null);
        
        // 插入测试数据
        insertTestData();
    }

    /**
     * 设置 RPC 上下文，模拟真实的 RPC 环境
     */
    private void setupRpcContext() {
        try {
            // 模拟 heracontext，包含国家代码和语言信息
            String heracontext = "mone-retail-area-for-global:SG;mone-retail-language-for-global:zh-CN";
            
            // 设置到 Dubbo RPC 上下文中
            RpcContext.getContext().setAttachment("heracontext", heracontext);
            
            // 验证设置是否成功
            String countryCode = RpcContextUtil.getCurrentAreaId();
            assertNotNull(countryCode, "RPC 上下文设置失败，无法获取国家代码");
            assertEquals("SG", countryCode, "国家代码设置不正确");
            
        } catch (Exception e) {
            // 如果 RPC 上下文设置失败，记录警告但继续测试
            System.err.println("警告：无法设置 RPC 上下文，测试可能无法正常运行: " + e.getMessage());
        }
    }

    @Test
    void testGetChannelTypeStatistics_Integration() {
        // 执行测试
        CommonResponse<List<ChannelTypeStatistics>> response = storeGradeService.getChannelTypeStatistics();
        List<ChannelTypeStatistics> result = response.getData();

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isEmpty());
        
        // 验证数据结构
        for (ChannelTypeStatistics stats : result) {
            assertNotNull(stats.getChannelType());
            assertNotNull(stats.getCompleteCount());
            assertNotNull(stats.getNotCompleteCount());
            assertNotNull(stats.getTotalCount());
            assertNotNull(stats.getCompletePercentage());
            assertNotNull(stats.getNotCompletePercentage());
            
            // 验证数据一致性
            assertEquals(stats.getCompleteCount() + stats.getNotCompleteCount(), stats.getTotalCount());
        }
    }

    @Test
    void testGetStoreGradeCompleteStatistics_CurrentMethod_Integration() {
        // 准备测试数据
        Long ruleId = 1L;
        RuleQueryReq ruleQueryReq = new RuleQueryReq();
        ruleQueryReq.setRuleId(ruleId);
        // 执行测试
        CommonResponse<List<StoreGradeCompleteStatistics>> response = storeGradeService.getStoreGradeCompleteStatistics(ruleQueryReq);
        List<StoreGradeCompleteStatistics> result = response.getData();

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isEmpty());
        
        // 验证总计
        StoreGradeCompleteStatistics totalStats = result.get(0);
        assertEquals("ALL", totalStats.getGrade());
        assertTrue(totalStats.getCount() > 0);
        assertEquals(1.0, totalStats.getPercentage(), 0.001);
        assertEquals(0L, totalStats.getTotalKapa());

        // 验证各等级统计
        for (StoreGradeCompleteStatistics stats : result) {
            if (!"ALL".equals(stats.getGrade())) {
                assertNotNull(stats.getGrade());
                assertTrue(stats.getCount() >= 0);
                assertTrue(stats.getPercentage() >= 0.0 && stats.getPercentage() <= 1.0);
                assertEquals(0L, stats.getTotalKapa());
            }
        }
    }

    @Test
    void testGetStoreGradeCompleteStatistics_RelationMethod_Integration() {
        // 准备测试数据
        Long ruleId = 2L;
        RuleQueryReq ruleQueryReq = new RuleQueryReq();
        ruleQueryReq.setRuleId(ruleId);
        // 执行测试
        CommonResponse<List<StoreGradeCompleteStatistics>> response = storeGradeService.getStoreGradeCompleteStatistics(ruleQueryReq);
        List<StoreGradeCompleteStatistics> result = response.getData();

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isEmpty());
        
        // 验证总计
        StoreGradeCompleteStatistics totalStats = result.get(0);
        assertEquals("ALL", totalStats.getGrade());
        assertTrue(totalStats.getCount() > 0);
        assertEquals(1.0, totalStats.getPercentage(), 0.001);
        assertEquals(0L, totalStats.getTotalKapa());

        // 验证各等级统计
        for (StoreGradeCompleteStatistics stats : result) {
            if (!"ALL".equals(stats.getGrade())) {
                assertNotNull(stats.getGrade());
                assertTrue(stats.getCount() >= 0);
                assertTrue(stats.getPercentage() >= 0.0 && stats.getPercentage() <= 1.0);
                assertEquals(0L, stats.getTotalKapa());
            }
        }
    }

    @Test
    void testGetStoreGradeCompleteStatistics_InvalidRuleId_Integration() {
        // 准备测试数据
        Long ruleId = 999L;
        RuleQueryReq ruleQueryReq = new RuleQueryReq();
        ruleQueryReq.setRuleId(ruleId);
        // 执行测试
        CommonResponse<List<StoreGradeCompleteStatistics>> response = storeGradeService.getStoreGradeCompleteStatistics(ruleQueryReq);
        List<StoreGradeCompleteStatistics> result = response.getData();

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testGetStoreGradeCompleteStatistics_UnsupportedMethod_Integration() {
        // 准备测试数据
        Long ruleId = 3L;
        RuleQueryReq ruleQueryReq = new RuleQueryReq();
        ruleQueryReq.setRuleId(ruleId);
        // 执行测试
        CommonResponse<List<StoreGradeCompleteStatistics>> response = storeGradeService.getStoreGradeCompleteStatistics(ruleQueryReq);
        List<StoreGradeCompleteStatistics> result = response.getData();

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * 插入测试数据
     */
    private void insertTestData() {
        // 插入门店等级规则
        StoreGradeRule rule1 = StoreGradeRule.builder()
                .channelType(1111)
                .retailerCode("MI")
                .method(1)
                .build();
        rule1.setCountryCode("CN");
        storeGradeRuleMapper.insert(rule1);

        StoreGradeRule rule2 = StoreGradeRule.builder()
                .channelType(111)
                .retailerCode("MI")
                .method(2)
                .build();
        rule2.setCountryCode("CN");
        storeGradeRuleMapper.insert(rule2);

        StoreGradeRule rule3 = StoreGradeRule.builder()
                .channelType(11)
                .retailerCode("MI")
                .method(2)
                .build();
        rule3.setCountryCode("CN");
        storeGradeRuleMapper.insert(rule3);

        // 插入门店等级关系数据
        insertStoreGradeRelation(rule1.getId(), "CN_STORE001", "S");
        insertStoreGradeRelation(rule1.getId(), "CN_STORE002", "A");
        insertStoreGradeRelation(rule1.getId(), "CN_STORE003", "B");
        insertStoreGradeRelation(rule1.getId(), "CN_STORE004", "S");
        insertStoreGradeRelation(rule1.getId(), "CN_STORE005", "A");

        insertStoreGradeRelation(rule2.getId(), "CN_STORE006", "S");
        insertStoreGradeRelation(rule2.getId(), "CN_STORE007", "A");
        insertStoreGradeRelation(rule2.getId(), "CN_STORE008", "B");
        insertStoreGradeRelation(rule2.getId(), "CN_STORE009", "C");
        insertStoreGradeRelation(rule2.getId(), "CN_STORE010", "S");
        insertStoreGradeRelation(rule2.getId(), "CN_STORE011", "A");
    }

    /**
     * 插入门店等级关系数据
     */
    private void insertStoreGradeRelation(Long ruleId, String storeCode, String storeGrade) {
        com.mi.info.intl.retail.management.entity.StoreGradeRelation relation = 
            com.mi.info.intl.retail.management.entity.StoreGradeRelation.builder()
                .storeGradeRuleId(ruleId)
                .storeCode(storeCode)
                .storeGrade(storeGrade)
                .build();
        relation.setCountryCode("CN");
        storeGradeRelationMapper.insert(relation);
    }
} 