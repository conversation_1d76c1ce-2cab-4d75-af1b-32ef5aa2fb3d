package com.mi.info.intl.retail.intlretail.app.aspect;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.Arrays;

/**
 * 访问日志切面
 * 用于记录Controller方法的输入和输出
 * 基于@AccessLog注解实现切点，只有标记了注解的Controller类或方法才打印日志
 * 设置最高优先级，确保最先执行
 */
@Aspect
@Component
@Slf4j
@Order(Integer.MIN_VALUE) // 设置为最高优先级，确保在参数校验之前执行
public class AccessLogAspect {

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 环绕通知：记录Controller方法的输入和输出
     * 基于@AccessLog注解实现切点，只有标记了注解的Controller类或方法才打印日志
     */
    @Around("@annotation(com.mi.info.intl.retail.intlretail.app.aspect.AccessLog) || " +
            "@within(com.mi.info.intl.retail.intlretail.app.aspect.AccessLog)")
    public Object logController(ProceedingJoinPoint joinPoint) throws Throwable {
        // 获取请求信息
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes != null ? attributes.getRequest() : null;
        
        String methodName = joinPoint.getSignature().getName();
        String className = joinPoint.getTarget().getClass().getSimpleName();
        String requestUrl = request != null ? request.getRequestURL().toString() : "unknown";
        String httpMethod = request != null ? request.getMethod() : "unknown";
        
        // 记录输入参数
        Object[] args = joinPoint.getArgs();
        String inputParams = "";
        if (args.length > 0) {
            try {
                inputParams = objectMapper.writeValueAsString(args);
            } catch (Exception e) {
                inputParams = Arrays.toString(args);
            }
        }
        
        // 检查是否需要打印响应内容
        boolean shouldLogResponse = shouldLogResponse(joinPoint);
        
        // 合并输入信息到一行
        log.info("=== Controller调用开始 === 类名:{} 方法名:{} 请求URL:{} {} 输入参数:{}", 
                className, methodName, httpMethod, requestUrl, inputParams);
        
        long startTime = System.currentTimeMillis();
        Object result = null;
        Exception exception = null;
        
        try {
            // 执行目标方法
            result = joinPoint.proceed();
            return result;
        } catch (Exception e) {
            exception = e;
            throw e;
        } finally {
            long endTime = System.currentTimeMillis();
            long executionTime = endTime - startTime;
            
            if (exception != null) {
                // 异常情况合并到一行
                log.error("=== Controller调用异常 === 类名:{} 方法名:{} 异常信息:{} 执行时间:{}ms", 
                        className, methodName, exception.getMessage(), executionTime);
            } else {
                // 成功情况合并到一行
                String outputResult = "";
                if (shouldLogResponse && result != null) {
                    try {
                        outputResult = objectMapper.writeValueAsString(result);
                    } catch (Exception e) {
                        outputResult = result.toString();
                    }
                } else {
                    outputResult = "[已省略响应内容]";
                }
                
                log.info("=== Controller调用成功 === 类名:{} 方法名:{} 输出结果:{} 执行时间:{}ms", 
                        className, methodName, outputResult, executionTime);
            }
        }
    }
    
    /**
     * 检查是否需要打印响应内容
     * 由于切点已经基于@AccessLog注解，这里只需要检查注解的response属性
     * @param joinPoint 连接点
     * @return true表示需要打印响应内容，false表示不需要
     */
    private boolean shouldLogResponse(ProceedingJoinPoint joinPoint) {
        try {
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            
            // 检查方法是否有@AccessLog注解（优先级更高）
            AccessLog accessLog = method.getAnnotation(AccessLog.class);
            if (accessLog != null) {
                return accessLog.response();
            }
            
            // 检查类是否有@AccessLog注解
            Class<?> targetClass = joinPoint.getTarget().getClass();
            accessLog = targetClass.getAnnotation(AccessLog.class);
            if (accessLog != null) {
                return accessLog.response();
            }
            
            // 由于切点已经基于注解，理论上不会到达这里
            // 但为了安全起见，正常情况下默认不打印响应内容
            return false;
        } catch (Exception e) {
            log.warn("检查@AccessLog注解时发生异常: {}", e.getMessage());
            return true; // 异常情况下默认打印响应内容
        }
    }
}
