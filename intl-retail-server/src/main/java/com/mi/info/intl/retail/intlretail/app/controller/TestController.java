package com.mi.info.intl.retail.intlretail.app.controller;

import com.mi.info.intl.retail.dto.LabelValueDTO;
import com.mi.info.intl.retail.intlretail.service.api.PageInfoDto;
import com.mi.info.intl.retail.intlretail.service.api.bpm.BpmApiCallBack;
import com.mi.info.intl.retail.intlretail.service.api.bpm.dto.BpmCallBackParamDto;
import com.mi.info.intl.retail.intlretail.service.api.fds.FdsService;
import com.mi.info.intl.retail.intlretail.service.api.masterdata.dto.BatchDisableRequest;
import com.mi.info.intl.retail.intlretail.service.api.mq.RmsSyncDbService;
import com.mi.info.intl.retail.intlretail.service.api.mq.RmsSyncSoDataService;
import com.mi.info.intl.retail.intlretail.service.api.mq.dto.RmsDbRequest;
import com.mi.info.intl.retail.intlretail.service.api.mq.dto.RmsSyncDataRequest;
import com.mi.info.intl.retail.intlretail.service.api.retailer.dto.CommonResponse;
import com.mi.info.intl.retail.intlretail.service.api.so.sales.dto.SearchReferenceReqDto;
import com.mi.info.intl.retail.intlretail.service.api.so.sales.dto.SearchReferenceRespDto;
import com.mi.info.intl.retail.intlretail.service.api.so.sys.dto.SkuAvailableSearch;
import com.mi.info.intl.retail.intlretail.service.api.so.sys.provider.AvailableSkuApiService;
import com.mi.info.intl.retail.intlretail.service.api.so.sys.provider.DictSysDubboProvider;
import com.mi.info.intl.retail.intlretail.service.api.so.sys.request.DictSysRequest;
import com.mi.info.intl.retail.intlretail.service.api.so.template.FileTemplateDubboProvider;
import com.mi.info.intl.retail.intlretail.service.api.so.template.dto.SaveFileTemplateDTO;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.org.domain.RmsSyncDbManager;
import com.mi.info.intl.retail.so.app.mq.dto.RetailSyncToRmsInfo;
import com.mi.info.intl.retail.so.app.mq.dto.RmsStockDataSyncDto;
import com.mi.info.intl.retail.so.app.mq.dto.SyncMiIdInfo;
import com.mi.info.intl.retail.so.domain.datasync.RetailSyncToRmsManage;
import com.mi.info.intl.retail.so.domain.datasync.enums.DataSyncDataTypeEnum;
import com.mi.info.intl.retail.so.domain.datasync.service.RmsStockDataSyncService;
import com.mi.info.intl.retail.so.domain.sales.service.IntlSoCommonDomainService;
import com.mi.info.intl.retail.so.domain.upload.service.IntlSkuAvailableService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping("/api/test")
public class TestController extends BaseController {

    @Resource
    private RmsSyncDbService rmsSyncDbService;

    @Resource
    private BpmApiCallBack bpmCallBack;

    @Resource
    private AvailableSkuApiService aviableSkuApiService;

    @Resource
    private FileTemplateDubboProvider fileTemplateService;

    @Resource
    private FdsService fdsService;

    @Resource
    private DictSysDubboProvider dictSysDubboProvider;

    @Resource
    private IntlSkuAvailableService intlSkuAvailableService;

    @Resource
    private RmsStockDataSyncService rmsStockDataSyncService;

    @Resource
    private RmsSyncSoDataService rmsSyncSoDataService;

    @Resource
    private RmsSyncDbManager rmsSyncDbManager;

    @Resource
    private IntlSoCommonDomainService intlSoCommonDomainService;

    @PostMapping("/syncRmsDb")
    @ResponseBody
    public String syncRmsDbMsg(@RequestBody RmsDbRequest request) {

        CommonResponse commonResponse = rmsSyncDbService.syncRmsDbMsg(request);
        return commonResponse.getMessage();

    }

    @PostMapping("/consumer")
    @ResponseBody
    public String consumer(@RequestBody RmsDbRequest request) {
        try {
            rmsSyncDbManager.editDb(request);
        } catch (Exception e) {
            return e.getMessage();
        }

        return "success";

    }

    @PostMapping("/bpmCallBack")
    @ResponseBody
    public CommonApiResponse<String> bpmCallBack(@RequestBody BpmCallBackParamDto request) {

        return bpmCallBack.callback(request);

    }

    @PostMapping("/availableSku")
    @ResponseBody
    public CommonApiResponse<PageInfoDto> availableSku(@RequestBody SkuAvailableSearch request) {

        return aviableSkuApiService.getDataBySearch(request);

    }

    @PostMapping("/skuSearch")
    @ResponseBody
    public CommonApiResponse<List<SearchReferenceRespDto>> skuSearch(@RequestBody SearchReferenceReqDto request) {

        List<SearchReferenceRespDto> searchReferencesData = intlSoCommonDomainService.getSearchReferencesData(request);
        return CommonApiResponse.success(searchReferencesData);

    }

    @PostMapping("/exportAvailableSku")
    @ResponseBody
    public CommonApiResponse<String> exportAvailableSku(@RequestBody SkuAvailableSearch request) {

        return aviableSkuApiService.exportDataBySearch(request);

    }

    @GetMapping("/initZeroSalesTime")
    @ResponseBody
    public CommonApiResponse<String> initZeroSalesTime() {
        intlSkuAvailableService.initZeroSalesTimeData();
        return CommonApiResponse.success("success");
    }

    @PostMapping("/saveFileTemplate")
    public CommonApiResponse<String> saveFileTemplate(@RequestBody SaveFileTemplateDTO request) {

        return fileTemplateService.saveTemplate(request);

    }

    @PostMapping("/uploadTemplate")
    public CommonApiResponse<String> uploadTemplate(MultipartFile file) throws IOException {
        File tempFile = File.createTempFile("temp", file.getOriginalFilename());
        FileUtils.copyInputStreamToFile(file.getInputStream(), tempFile);
        BatchDisableRequest request = new BatchDisableRequest();
        String url = fdsService.upload(file.getOriginalFilename(), tempFile, true).getUrl();
        return CommonApiResponse.success(url);
    }

    @PostMapping("/dict")
    @ResponseBody
    public CommonApiResponse<Map<String, List<LabelValueDTO>>> getDictLabelListByType(
            @RequestBody DictSysRequest request) {

        return dictSysDubboProvider.getDictLabelListByType(request);

    }

    @PostMapping("/syncStockImeiData")
    @ResponseBody
    public CommonResponse syncStockImeiData(@RequestBody RmsStockDataSyncDto reqDto) {
        rmsStockDataSyncService.syncStockImeiData(reqDto.getQuerySize(), reqDto.getIsUpdate());
        return new CommonResponse<>("success");
    }

    @PostMapping("/syncStockQtyData")
    @ResponseBody
    public CommonResponse syncStockQtyData(@RequestBody RmsStockDataSyncDto reqDto) {
        rmsStockDataSyncService.syncStockQtyData(reqDto.getQuerySize(), reqDto.getIsUpdate());
        return new CommonResponse<>("success");
    }

    @PostMapping("/testSyncSoData")
    @ResponseBody
    public String test(@RequestBody RmsSyncDataRequest request) {
        CommonResponse commonResponse = rmsSyncSoDataService.syncRmsSoData(request);
        return commonResponse.getMessage();
    }

    /**
     * 推送RMS调用接口
     */
    @Resource
    private RetailSyncToRmsManage retailSyncToRmsManage;
    @PostMapping("/testToRms")
    @ResponseBody
    public CommonResponse testToRms(@RequestBody RetailSyncToRmsInfo retailSyncInfo) {
        List<Long> dataIds =
                Arrays.stream(retailSyncInfo.getDataId().split(",")).map(Long::parseLong).collect(Collectors.toList());
        retailSyncToRmsManage.handleRetailData(DataSyncDataTypeEnum.getEnumByMessage(retailSyncInfo.getDataType()),
                retailSyncInfo.getOperateType(), dataIds);
        return new CommonResponse<>("success");
    }
}
