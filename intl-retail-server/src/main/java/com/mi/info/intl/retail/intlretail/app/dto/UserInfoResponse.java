package com.mi.info.intl.retail.intlretail.app.dto;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class UserInfoResponse implements Serializable {

    private static final long serialVersionUID = -8454147953454983508L;
    @JsonProperty("user_info")
    private UserInfo userInfo;
    @JsonProperty("phone_property_info")
    private PhonePropertyInfo phonePropertyInfo;


    @Data
    static class PhonePropertyInfo implements Serializable {
        private static final long serialVersionUID = -943992176809526992L;
        @JsonProperty("role")
        private Integer role;
        @JsonProperty("channel_dot_keys")
        private List<String> channelDotKeys;
        @JsonProperty("channels")
        private List<Integer> channels;
        @JsonProperty("role_key")
        private String roleKey;
        @JsonProperty("role_name")
        private String roleName;
        @JsonProperty("channels_sign")
        private List<String> channelsSign;
    }

    @Data
    public static class UserInfo implements Serializable {
        @JsonProperty("role")
        private Integer role;
        @JsonProperty("channel_dot_keys")
        private List<String> channelDotKeys;
        @JsonProperty("own")
        private Integer own;
        @JsonProperty("default_app_retail_mode")
        private Integer defaultAppRetailMode;
        @JsonProperty("user_id")
        private Long userId;
        @JsonProperty("realname")
        private String realname;
        @JsonProperty("switchable_app_retail_mode")
        private List<Integer> switchableAppRetailMode;
        @JsonProperty("mockable")
        private String mockable;
        @JsonProperty("area_id")
        private String areaId;
        @JsonProperty("channels")
        private List<Integer> channels;
        @JsonProperty("switchable_shadow_list")
        private List<Integer> switchableShadowList;
        @JsonProperty("avata")
        private String avata;
        @JsonProperty("nickname")
        private String nickname;
        @JsonProperty("role_key")
        private String roleKey;
        @JsonProperty("role_name")
        private String roleName;
        @JsonProperty("global_area_id")
        private String globalAreaId;
        @JsonProperty("channels_sign")
        private List<String> channelsSign;
        @JsonProperty("suspension")
        private Boolean suspension;

        @JsonProperty("rms_property_info")
        private RmsPropertyInfo rmsPropertyInfo;
    }
}



