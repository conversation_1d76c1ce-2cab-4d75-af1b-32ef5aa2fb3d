package com.mi.info.intl.retail.intlretail.app.controller.fieldforce;

import java.util.Optional;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.apache.log4j.spi.ErrorCode;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.mi.info.intl.retail.core.aspect.log.AccessLog;
import com.mi.info.intl.retail.fieldforce.domain.user.service.IntlRmsUserService;
import com.mi.info.intl.retail.fieldforce.domain.user.service.UserAccountService;
import com.mi.info.intl.retail.intlretail.app.controller.BaseController;
import com.mi.info.intl.retail.intlretail.service.api.request.UserAttributeModifyRequest;
import com.mi.info.intl.retail.intlretail.util.AppProviderUtil;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.model.UserInfo;
import com.mi.info.intl.retail.utils.UserInfoUtil;
import com.xiaomi.mone.docs.annotations.dubbo.ApiDoc;
import com.xiaomi.mone.docs.annotations.dubbo.ApiModule;
import com.xiaomi.mone.docs.annotations.http.MiApiRequestMethod;

import lombok.extern.slf4j.Slf4j;

/**
 * app绑定mid接口
 *
 * <AUTHOR>
 * @date 2025/9/1 16:57
 */
@ApiModule(value = "国际渠道零售服务", apiInterface = UserAccountController.class)
@Slf4j
@Validated
@RestController
@RequestMapping("/api/fieldforce/account/v1")
public class UserAccountController extends BaseController {

    @Resource
    private UserAccountService userAccountService;
    @Resource
    private IntlRmsUserService intlRmsUserService;

    @ApiDoc(name = "米ID绑定", value = "/api/fieldforce/account/v1/bindMiId", method = MiApiRequestMethod.POST)
    @PostMapping("/bindMiId")
    @AccessLog(request = true)
    public CommonApiResponse<Boolean> bindMiId() {
        return AppProviderUtil.wrap(log, "BindMiId", userAccountService::bindMiId, false);
    }

    @ApiDoc(name = "修改用户信息", value = "/api/fieldforce/account/v1/modifyUserAttribute", method = MiApiRequestMethod.POST)
    @PostMapping("/modifyUserAttribute")
    @ResponseBody
    @AccessLog(request = true)
    public CommonApiResponse<Boolean> modifyUserAttribute(@RequestBody @Valid UserAttributeModifyRequest request) {
        Optional<UserInfo> optional = UserInfoUtil.getUserInfo();
        if (!optional.isPresent() || optional.get().getMiID() == null) {
            //目前仅支持mid存在的用户基本信息
            return CommonApiResponse.failure(ErrorCode.GENERIC_FAILURE, "当前用户信息获取失败");
        }
        UserInfo userInfo = optional.get();
        //确保用户mid存在
        boolean result = intlRmsUserService.modifyUserAttribute(userInfo.getMiID(), request.getType(),
                request.getModifyData());
        return CommonApiResponse.success(result);
    }

    @GetMapping("/exportMigrateAccount")
    @AccessLog(request = true)
    public CommonApiResponse<String> getUserMigrateData() {
        return AppProviderUtil.wrap(log, "getMigrateData", intlRmsUserService::getUserMigrateData, false);
    }
}
