package com.mi.info.intl.retail.intlretail.app.controller.common;

import com.mi.info.intl.retail.intlretail.app.controller.BaseController;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.service.DistributionLock;
import com.mi.info.intl.retail.service.DistributionLockService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/7/29 11:05
 */
@Slf4j
@RestController
@RequestMapping("/common/distributionLockTest")
public class DistributionLockTestController extends BaseController {

    @Resource
    private DistributionLockService distributionLockService;


    /**
     * 测试分布式锁
     *
     * @param id 锁的唯一标识
     * @return response
     */
    @GetMapping("/getLock")
    public CommonApiResponse<String> getLock(@RequestParam("id") String id) {
        try (DistributionLock ignore = distributionLockService.tryLock("getLock-{0}", id)) {
            // 模拟多请求下获取锁
            log.info("获取锁成功，id:{}", id);
            Thread.sleep(40000);
        } catch (InterruptedException e) {
            log.error("线程休眠中断，id:{}", id, e);
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            log.error("获取锁失败，id:{}", id, e);
            return CommonApiResponse.failure(500, "获取锁失败");
        }
        return new CommonApiResponse<>("ok");
    }

    /**
     * 测试分布式锁，指定等待时间
     *
     * @param id 锁的唯一标识
     * @return response
     */
    @GetMapping("/getLockWithWaitTime")
    public CommonApiResponse<String> getLockWithWaitTime(@RequestParam("id") String id, @RequestParam Long waitTime) {
        try (DistributionLock ignore = distributionLockService.tryLock("getLockWithWaitTime-{0}", waitTime, id)) {
            // 模拟多请求下获取锁
            log.info("获取锁成功，id:{}", id);
            Thread.sleep(5000);
        } catch (InterruptedException e) {
            log.error("线程休眠中断，id:{}", id, e);
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            log.error("获取锁失败，id:{}", id, e);
            return CommonApiResponse.failure(500, "获取锁失败");
        }
        return new CommonApiResponse<>("ok");
    }

    /**
     * 测试分布式锁，指定等待时间，指定租期时间
     *
     * @param id 锁的唯一标识
     * @return response
     */
    @GetMapping("/getLockWithWaitTimeAndLeaseTime")
    public CommonApiResponse<String> getLockWithWaitTimeAndLeaseTime(@RequestParam("id") String id, @RequestParam("leaseTime") Long leaseTime,
                                                                     @RequestParam("waitTime") Long waitTime) {
        try (DistributionLock ignore = distributionLockService.tryLock("getLockWithWaitTimeAndLeaseTime-{0}", waitTime, leaseTime, 10, id)) {
            // 模拟多请求下获取锁
            log.info("获取锁成功，id:{}", id);
            Thread.sleep(5000);
        } catch (InterruptedException e) {
            log.error("线程休眠中断，id:{}", id, e);
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            log.error("获取锁失败，id:{}", id, e);
            return CommonApiResponse.failure(500, "获取锁失败");
        }
        return new CommonApiResponse<>("ok");
    }
}
