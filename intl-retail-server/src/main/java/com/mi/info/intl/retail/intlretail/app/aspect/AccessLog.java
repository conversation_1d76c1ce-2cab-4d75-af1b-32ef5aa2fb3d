package com.mi.info.intl.retail.intlretail.app.aspect;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 访问日志注解
 * 用于标记需要打印访问日志的Controller方法或类
 * 支持方法级别和类级别注解
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface AccessLog {
    
    /**
     * 是否打印响应内容
     * @return true表示打印响应内容，false表示不打印
     * 
     * 使用示例：
     * 1. 方法级别：@AccessLog 或 @AccessLog(response = true)
     * 2. 类级别：@AccessLog 或 @AccessLog(response = true) - 该类下所有方法都打印响应
     * 3. 禁用响应：@AccessLog(response = false) - 禁用响应日志打印
     */
    boolean response() default false;
}
