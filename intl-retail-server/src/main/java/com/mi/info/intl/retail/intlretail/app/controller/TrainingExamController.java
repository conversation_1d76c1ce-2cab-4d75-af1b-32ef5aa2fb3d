package com.mi.info.intl.retail.intlretail.app.controller;

import com.alibaba.fastjson.JSON;
import com.mi.info.intl.retail.exception.RetailRunTimeException;
import com.mi.info.intl.retail.intlretail.service.api.proxy.IntlTrainingExamProxyService;
import com.mi.info.intl.retail.intlretail.service.api.proxy.IntlTrainingStudyTaskProxyService;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.xiaomi.proretail.training.api.vo.trainingcheck.intl.IntlExamDetailResponse;
import com.xiaomi.proretail.training.api.vo.trainingcheck.intl.IntlExamRequest;
import com.xiaomi.proretail.training.api.vo.trainingcheck.intl.IntlExamResponse;
import com.xiaomi.proretail.training.api.vo.trainingcheck.intl.IntlExamSubmitRequest;
import com.xiaomi.proretail.training.api.vo.trainingcheck.intl.IntlExamSubmitResponse;
import com.xiaomi.proretail.training.api.vo.trainingcheck.intl.IntlInstruction;
import com.xiaomi.proretail.training.api.vo.trainingcheck.intl.IntlProblem;
import com.xiaomi.proretail.training.api.vo.trainingstudy.intl.IntlAddOperateParam;
import com.xiaomi.proretail.training.api.vo.trainingstudy.intl.IntlBaseRequest;
import com.xiaomi.proretail.training.api.vo.trainingstudy.intl.IntlExamineeListParam;
import com.xiaomi.proretail.training.api.vo.trainingstudy.intl.IntlExamineeListResp;
import com.xiaomi.proretail.training.api.vo.trainingstudy.intl.IntlExamineeLogListParam;
import com.xiaomi.proretail.training.api.vo.trainingstudy.intl.IntlExamineeLogListResp;
import com.xiaomi.proretail.training.api.vo.trainingstudy.intl.IntlSearchHistoryListParam;
import com.xiaomi.proretail.training.api.vo.trainingstudy.intl.IntlSearchHistoryListResp;
import com.xiaomi.proretail.training.api.vo.trainingstudy.intl.IntlSearchHistoryParam;
import com.xiaomi.proretail.training.api.vo.trainingstudy.intl.IntlStudyProgressParam;
import com.xiaomi.proretail.training.api.vo.trainingstudy.intl.IntlStudyProgressUploadParam;
import com.xiaomi.proretail.training.api.vo.trainingstudy.intl.IntlStudyTaskDownLineParam;
import com.xiaomi.proretail.training.api.vo.trainingstudy.intl.IntlUserCheckParam;
import com.xiaomi.proretail.training.api.vo.trainingstudy.intl.IntlUserCheckResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * - <AUTHOR>
 * - @date 2025/7/17
 * - @description:
 * -
 **/
@Slf4j
@RestController
@RequestMapping({"/intl", "/*/intl"})
public class TrainingExamController extends BaseController {
    @Resource
    private IntlTrainingExamProxyService intlTrainingExamProxyService;

    @Resource
    private IntlTrainingStudyTaskProxyService intlTrainingStudyTaskProxyService;

    @PostMapping("/teach/getExamDetail")
    @ResponseBody
    public CommonApiResponse<IntlExamDetailResponse> getExamDetail(@RequestBody
                                                                           IntlExamRequest request) {
        log.info("getExamDetail:{}", JSON.toJSON(request));
        request.setAreaId(getCountryCode());
        IntlExamDetailResponse response = intlTrainingExamProxyService.getExamDetail(request);
        return new CommonApiResponse<>(response);

    }

    @PostMapping("/training/exam/submit")
    @ResponseBody
    public CommonApiResponse<IntlExamSubmitResponse> submit(@RequestBody
                                                                    IntlExamSubmitRequest request) {
        log.info("submit:{}", JSON.toJSON(request));
        request.setAreaId(getCountryCode());
        IntlExamSubmitResponse response = null;
        try {
            response = intlTrainingExamProxyService.submit(request);
        } catch (Exception e) {
            log.error("TrainingExamController#submit error", e);
            return new CommonApiResponse<>(500, e.getMessage(), null);
        }
        return new CommonApiResponse<>(response);

    }

    @PostMapping("/teach/getExamProblems")
    @ResponseBody
    public CommonApiResponse<IntlExamResponse<IntlProblem>> getExamProblems(@RequestBody
                                                                                    IntlExamRequest request) {
        log.info("getExamProblems:{}", JSON.toJSON(request));
        request.setAreaId(getCountryCode());
        IntlExamResponse<IntlProblem> response = intlTrainingExamProxyService.getExamProblems(request);
        return new CommonApiResponse<>(response);

    }

    @PostMapping("/teach/getExamInstruction")
    @ResponseBody
    public CommonApiResponse<IntlInstruction> getExamInstruction(@RequestBody
                                                                         IntlExamRequest request) {
        log.info("getExamInstruction:{}", JSON.toJSON(request));
        request.setAreaId(getCountryCode());
        return intlTrainingExamProxyService.getExamInstruction(request);

    }

    @PostMapping("/study/task/app/projectList")
    @ResponseBody
    public CommonApiResponse<Object> projectList(@RequestBody IntlBaseRequest request) {
        log.info("projectList:{}", JSON.toJSON(request));
        request.setAreaId(getCountryCode());
        Object response = intlTrainingStudyTaskProxyService.projectList(request);
        return new CommonApiResponse<>(response);
    }

    @PostMapping("/study/task/app/courseList")
    @ResponseBody
    public CommonApiResponse<Object> courseList(@RequestBody IntlBaseRequest request) {
        log.info("courseList:{}", JSON.toJSON(request));
        request.setAreaId(getCountryCode());
        Object response = intlTrainingStudyTaskProxyService.courseList(request);
        return new CommonApiResponse<>(response);
    }

    @PostMapping("/study/task/app/upload/process")
    @ResponseBody
    public CommonApiResponse<Object> uploadStudyProgress(@RequestBody IntlStudyProgressUploadParam request) {
        log.info("uploadStudyProgress:{}", JSON.toJSON(request));
        request.setAreaId(getCountryCode());
        Object response = intlTrainingStudyTaskProxyService.uploadStudyProgress(request);
        return new CommonApiResponse<>(response);
    }

    @PostMapping("/study/task/app/detail")
    @ResponseBody
    public CommonApiResponse<Object> getStudyTaskDetail(@RequestBody IntlStudyTaskDownLineParam request) {
        log.info("getStudyTaskDetail:{}", JSON.toJSON(request));
        request.setAreaId(getCountryCode());
        Object response = intlTrainingStudyTaskProxyService.getStudyTaskDetail(request);
        return new CommonApiResponse<>(response);
    }

    @PostMapping("/study/task/app/detail/progress")
    @ResponseBody
    public CommonApiResponse<Object> getDetailProgress(@RequestBody IntlStudyProgressParam request) {
        log.info("getDetailProgress:{}", JSON.toJSON(request));
        request.setAreaId(getCountryCode());
        Object response = intlTrainingStudyTaskProxyService.getDetailProgress(request);
        return new CommonApiResponse<>(response);
    }

    @PostMapping("/exam/app/list")
    @ResponseBody
    public CommonApiResponse<IntlExamineeListResp> getExamList(@RequestBody IntlExamineeListParam request) {
        log.debug("getExamList:{}", JSON.toJSON(request));
        if (request == null) {
            throw new IllegalArgumentException("param cannot be null");
        }
        request.setAreaId(getCountryCode());
        IntlExamineeListResp response = intlTrainingExamProxyService.getExamList(request);
        return new CommonApiResponse<>(response);
    }

    @PostMapping("/exam/app/history")
    @ResponseBody
    public CommonApiResponse<IntlExamineeLogListResp> getHistoryList(@RequestBody IntlExamineeLogListParam request) {
        log.debug("getHistoryList:{}", JSON.toJSON(request));
        if (request == null) {
            throw new IllegalArgumentException("param cannot be null");
        }
        request.setAreaId(getCountryCode());
        IntlExamineeLogListResp response = intlTrainingExamProxyService.getHistoryList(request);
        return new CommonApiResponse<>(response);
    }

    @PostMapping("/exam/app/searchHistory/add")
    @ResponseBody
    public CommonApiResponse<String> addSearchHistory(@RequestBody IntlSearchHistoryParam request) {
        log.debug("addSearchHistory:{}", JSON.toJSON(request));
        if (request == null) {
            throw new IllegalArgumentException("param cannot be null");
        }
        request.setAreaId(getCountryCode());
        String response = intlTrainingExamProxyService.addSearchHistory(request);
        return new CommonApiResponse<>(response);
    }

    @PostMapping("/exam/app/searchHistory/delete")
    @ResponseBody
    public CommonApiResponse<String> deleteSearchHistory(@RequestBody IntlSearchHistoryParam request) {
        log.debug("deleteSearchHistory:{}", JSON.toJSON(request));
        if (request == null) {
            throw new IllegalArgumentException("param cannot be null");
        }
        request.setAreaId(getCountryCode());
        String response = intlTrainingExamProxyService.deleteSearchHistory(request);
        return new CommonApiResponse<>(response);
    }

    @PostMapping("/exam/app/searchHistory/deleteAll")
    @ResponseBody
    public CommonApiResponse<String> deleteAllSearchHistory(@RequestBody IntlBaseRequest request) {
        log.debug("deleteAllSearchHistory:{}", JSON.toJSON(request));
        if (request == null) {
            throw new IllegalArgumentException("param cannot be null");
        }
        request.setAreaId(getCountryCode());
        String response = intlTrainingExamProxyService.deleteAllSearchHistory(request);
        return new CommonApiResponse<>(response);
    }

    @PostMapping("/exam/app/searchHistory/list")
    @ResponseBody
    public CommonApiResponse<List<IntlSearchHistoryListResp>> searchHistoryList(@RequestBody IntlSearchHistoryListParam request) {
        log.debug("searchHistoryList:{}", JSON.toJSON(request));
        if (request == null) {
            throw new IllegalArgumentException("param cannot be null");
        }
        request.setAreaId(getCountryCode());
        List<IntlSearchHistoryListResp> response = intlTrainingExamProxyService.searchHistoryList(request);
        return new CommonApiResponse<>(response);
    }

    @PostMapping("/exam/app/user/operate")
    @ResponseBody
    public CommonApiResponse<String> addOperate(@RequestBody IntlAddOperateParam request) {
        log.debug("addOperate:{}", JSON.toJSON(request));
        if (request == null) {
            throw new RetailRunTimeException("param cannot be null");
        }
        request.setAreaId(getCountryCode());
        String response = intlTrainingExamProxyService.addOperate(request);
        return new CommonApiResponse<>(response);
    }

    @PostMapping("/exam/app/user/check")
    @ResponseBody
    public CommonApiResponse<List<IntlUserCheckResp>> userCheck(@RequestBody IntlUserCheckParam request) {
        log.debug("userCheck:{}", JSON.toJSON(request));
        if (request == null) {
            throw new RetailRunTimeException("param cannot be null");
        }
        request.setAreaId(getCountryCode());
        List<IntlUserCheckResp> response = intlTrainingExamProxyService.userCheck(request);
        return new CommonApiResponse<>(response);
    }
}
