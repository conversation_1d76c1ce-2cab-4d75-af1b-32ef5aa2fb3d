package com.mi.info.intl.retail.intlretail.app.controller;

import com.mi.info.intl.retail.intlretail.service.api.retailer.dto.CommonResponse;
import com.mi.info.intl.retail.intlretail.service.api.so.ImeiReportVerifyService;
import com.mi.info.intl.retail.intlretail.service.api.so.QtyService;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.*;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.TriggerDataSyncRequest;
import com.mi.info.intl.retail.intlretail.service.api.so.upload.ImeiImportService;
import com.mi.info.intl.retail.intlretail.service.api.so.upload.ImeiUploadService;
import com.mi.info.intl.retail.intlretail.service.api.so.upload.QtyImportService;
import com.mi.info.intl.retail.intlretail.service.api.so.upload.SoImportService;
import com.mi.info.intl.retail.intlretail.service.api.so.upload.dto.ImeiImportRequest;
import com.mi.info.intl.retail.intlretail.service.api.so.upload.dto.QtyImportRequest;
import com.mi.info.intl.retail.intlretail.service.api.so.upload.dto.QtyImportResponse;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.so.app.mq.SyncSoToEsProducer;
import com.mi.info.intl.retail.so.domain.datasync.enums.DataSyncDataTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/so")
public class SoUploadController {
    @Resource
    private ImeiUploadService imeiUploadService;
    @Resource
    private QtyService qtyService;
    @Resource
    private ImeiReportVerifyService imeiReportVerifyService;
    @Resource
    private ImeiImportService imeiImportService;
    @Resource
    private SoImportService soImportService;
    @Resource
    private QtyImportService qtyImportService;
    @Resource
    private SyncSoToEsProducer syncSoToEsProducer;

    @Value("${intl-retail.rocketmq.so-to-es.max-batch-size:500}")
    private Integer maxBatchSize;


    @PostMapping("/submitImei")
    @ResponseBody
    public CommonApiResponse<Object> submitImei(@RequestBody SubmitImeiReq request) {
        log.info("submitImei request: {}", request);
        return imeiUploadService.submitImei(request);
    }

    @PostMapping("/imeiReportVerify")
    @ResponseBody
    public CommonApiResponse<Object> imeiReportVerify(@RequestBody ImeiReportVerifyRequest request) {
        return imeiReportVerifyService.imeiReportVerify(request);
    }

    @PostMapping("/getSkuList")
    @ResponseBody
    public CommonResponse<GetSkuListResponse> getSkuList(@RequestBody GetSkuListRequest request) {
        log.info("getSkuList request: {}", request);
        return qtyService.getSkuList(request);
    }

    @PostMapping("/submitQty")
    @ResponseBody
    public CommonResponse<Object> submitQty(@RequestBody SubmitQtyReq request) {
        log.info("submitQty request: {}", request);
        return qtyService.submitQty(request);
    }

    @PostMapping("/getFilterList")
    @ResponseBody
    public CommonResponse<GetFilterListResponse> getFilterList() {
        log.info("getFilterList request");
        return qtyService.getFilterList();
    }

    @PostMapping("/queryQtyStatistics")
    @ResponseBody
    public CommonResponse<QueryQtyStatisticsResponse> queryQtyStatistics(@RequestBody QueryQtyStatisticsRequest request) {
        log.info("queryQtyStatistics request: {}", request);
        return qtyService.queryQtyStatistics(request);
    }

    @PostMapping("/queryQtyList")
    @ResponseBody
    public CommonResponse<QueryQtyListResponse> queryQtyList(@RequestBody QueryQtyListRequest request) {
        log.info("queryQtyList request: {}", request);
        return qtyService.queryQtyList(request);
    }

    @PostMapping("/getQtyBoardData")
    @ResponseBody
    public CommonResponse<GetQtyBoardDataResponse> getQtyBoardData(@RequestBody GetQtyBoardDataRequest request) {
        log.info("getQtyBoardData request: {}", request);
        return qtyService.getQtyBoardData(request);
    }

    @PostMapping("/queryQtyDetail")
    @ResponseBody
    public CommonResponse<QueryQtyDetailResponse> queryQtyDetail(@RequestBody QueryQtyDetailRequest request) {
        log.info("queryQtyDetail request: {}", request);
        return qtyService.queryQtyDetail(request);
    }

    @PostMapping("/getStoreList")
    @ResponseBody
    public CommonResponse<GetStoreListResponse> getStoreList(@RequestBody GetStoreListRequest request) {
        log.info("getStoreList request: {}", request);
        return qtyService.getStoreList(request);
    }

    @PostMapping("/queryImeiListByPage")
    @ResponseBody
    public CommonApiResponse<ImeiListQueryResp> queryImeiListByPage(@RequestBody ImeiListQueryReq request) {
        log.info("queryImeiListByPage request: {}", request);
        return imeiUploadService.queryImeiListByPage(request);
    }

    @PostMapping("/queryImeiDetail")
    @ResponseBody
    public CommonApiResponse<ImeiDetailQueryResp> queryImeiDetail(@RequestBody ImeiDetailQueryReq request) {
        log.info("queryImeiDetail request: {}", request);
        return imeiUploadService.queryImeiDetail(request);
    }

    @PostMapping("/queryImeiSummary")
    @ResponseBody
    public CommonApiResponse<ImeiSummaryQueryResp> queryImeiSummary(@RequestBody ImeiSummaryQueryReq request) {
        log.info("queryImeiSummary request: {}", request);
        return imeiUploadService.queryImeiSummary(request);
    }

    @PostMapping("/getImportTemplate")
    @ResponseBody
    public CommonApiResponse<GetImportTemplateResponse> getImportTemplate(@RequestBody GetImportTemplateRequest request) {
        log.info("getImportTemplate request: {}", request);
        return soImportService.getImportTemplate(request);
    }

    @PostMapping("/getImportLogList")
    @ResponseBody
    public CommonApiResponse<GetImportLogListResponse> getImportLogList(@RequestBody GetImportLogListRequest request) {
        log.info("getImportLogList request: {}", request);
        return soImportService.getImportLogList(request);
    }

    @PostMapping("/importImeiData")
    @ResponseBody
    public CommonApiResponse<ImportDataResponse> queryImeiSummary(@RequestBody ImeiImportRequest request) {
        log.info("importImeiData request: {}", request);
        return imeiImportService.importImeiData(request);
    }

    @PostMapping("/importData")
    @ResponseBody
    public CommonApiResponse<ImportDataResponse> importData(@RequestBody ImportDataRequest request) {
        log.info("importData request: {}", request);
        return soImportService.importData(request);
    }
    @PostMapping("/imeiBarcodeRead")
    @ResponseBody
    public CommonApiResponse<Object> imeiBarcodeRead(@RequestBody ImeiBarcodeReadReq request) {
        log.info("imeiBarcodeRead request: {}", request);
        return imeiUploadService.imeiBarcodeRead(request);
    }
    @PostMapping("/qtyImportData")
    @ResponseBody
    public CommonApiResponse<QtyImportResponse> qtyImportData(@RequestBody QtyImportRequest request) {
        log.info("QtyImportData request: {}", request);
        return qtyImportService.importData(request);
    }

    /**
     * 触发数据同步到ES
     * 调用SyncSoToEsProducer.sendSyncEsMsg方法触发数据更新
     * 支持大批量数据自动拆分
     */
    @PostMapping("/triggerEsDataSync")
    @ResponseBody
    public CommonApiResponse<String> triggerEsDataSync(@RequestBody TriggerDataSyncRequest request) {
        log.info("triggerEsDataSync request: {}", request);
        try {
            // 参数校验
            if (request == null || request.getDataType() == null || request.getIds() == null || request.getIds().isEmpty()) {
                return CommonApiResponse.failure(400, "参数不能为空");
            }

            // 根据数据类型获取对应的枚举
            DataSyncDataTypeEnum dataTypeEnum = DataSyncDataTypeEnum.getEnumByMessage(request.getDataType());
            if (dataTypeEnum == null) {
                return CommonApiResponse.failure(400, "不支持的数据类型: " + request.getDataType());
            }

            List<Long> allIds = request.getIds();
            int totalSize = allIds.size();

            log.info("开始处理数据同步，总数据量: {}, 批次大小: {}", totalSize, maxBatchSize);

            // 如果数据量小于等于批次大小，直接发送
            if (totalSize <= maxBatchSize) {
                syncSoToEsProducer.sendSyncEsMsg(dataTypeEnum, allIds, request.isUseLatestInfo());
                log.info("单批次数据同步完成，数据量: {}", totalSize);
            } else {
                // 数据量大于批次大小，需要拆分
                int batchCount = (int) Math.ceil((double) totalSize / maxBatchSize);
                log.info("数据量超过批次限制，将拆分为 {} 个批次处理", batchCount);

                int successCount = 0;
                int errorCount = 0;

                for (int i = 0; i < batchCount; i++) {
                    int startIndex = i * maxBatchSize;
                    int endIndex = Math.min(startIndex + maxBatchSize, totalSize);
                    List<Long> batchIds = allIds.subList(startIndex, endIndex);

                    try {
                        log.info("处理第 {}/{} 批次，数据范围: {}-{}, 数据量: {}",
                                i + 1, batchCount, startIndex, endIndex - 1, batchIds.size());

                        syncSoToEsProducer.sendSyncEsMsg(dataTypeEnum, batchIds, request.isUseLatestInfo());
                        successCount++;

                        log.info("第 {}/{} 批次处理成功", i + 1, batchCount);
                    } catch (Exception e) {
                        errorCount++;
                        log.error("第 {}/{} 批次处理失败: {}", i + 1, batchCount, e.getMessage(), e);
                        // 继续处理下一批次，不中断整个流程
                    }
                }

                log.info("批量数据同步完成 - 总批次: {}, 成功: {}, 失败: {}", batchCount, successCount, errorCount);

                if (errorCount > 0) {
                    return CommonApiResponse.success(String.format("数据同步部分成功，总批次: %d, 成功: %d, 失败: %d",
                            batchCount, successCount, errorCount));
                }
            }

            log.info("triggerEsDataSync success, dataType: {}, totalIds: {}", request.getDataType(), totalSize);
            return CommonApiResponse.success("数据同步触发成功");

        } catch (Exception e) {
            log.error("triggerEsDataSync error", e);
            return CommonApiResponse.failure(500, "数据同步触发失败: " + e.getMessage());
        }
    }

}
