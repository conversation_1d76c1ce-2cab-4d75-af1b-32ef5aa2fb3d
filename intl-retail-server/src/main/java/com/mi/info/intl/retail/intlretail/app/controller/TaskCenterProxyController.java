package com.mi.info.intl.retail.intlretail.app.controller;

import com.mi.info.intl.retail.api.task.enums.TaskActionEnum;
import cn.hutool.json.JSONUtil;
import com.mi.info.intl.retail.cooperation.task.config.InspectionConfig;
import com.mi.info.intl.retail.cooperation.task.inspection.IntlInspectionTaskConfService;
import com.mi.info.intl.retail.cooperation.task.inspection.NewProductPromotionService;
import com.mi.info.intl.retail.cooperation.task.inspection.UserEventPopWindowService;
import com.mi.info.intl.retail.intlretail.app.aspect.AccessLog;
import com.mi.info.intl.retail.intlretail.app.event.RmsApiRequestEvent;
import com.mi.info.intl.retail.intlretail.service.api.inspection.dto.CheckPopupRequest;
import com.mi.info.intl.retail.intlretail.service.api.inspection.dto.DisablePopupRequest;
import com.mi.info.intl.retail.intlretail.service.api.inspection.dto.NeedCheckInEventTypeRequest;
import com.mi.info.intl.retail.intlretail.service.api.inspection.dto.NeedCheckInEventTypeResponse;
import com.mi.info.intl.retail.intlretail.service.api.material.NewProductInspectionService;
import com.mi.info.intl.retail.intlretail.service.api.mq.dto.RmsAipRequestInfo;
import com.mi.info.intl.retail.intlretail.service.api.position.PositionInspectionService;
import com.mi.info.intl.retail.intlretail.service.api.proxy.TaskCenterProxyService;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.taskcenter.ConfirmPopWindowsReq;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.taskcenter.HeadCalendarReq;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.taskcenter.TaskCenterCalendarReq;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.taskcenter.TaskCenterCommonReq;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.taskcenter.TaskCenterDetailReq;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.taskcenter.TaskCenterFinishTaskReq;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.taskcenter.TaskCenterInspectionConfReq;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.taskcenter.TaskCenterTaskNumReq;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.xiaomi.cnzone.brain.platform.api.model.resp.app.TaskNumResp;
import com.xiaomi.cnzone.nr.common.utils.GsonUtil;
import com.xiaomi.cnzone.proretail.newcommon.util.RetailJsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/taskcenter")
public class TaskCenterProxyController extends BaseController {
    
    @Autowired
    private TaskCenterProxyService taskCenterProxyService;
    
    //督导Job：500900002或者100000051或者100000024
    @Value("${promotion.notify:500900002,100000051,100000024}")
    private List<String> notifyJobTitles;
    
    @Autowired
    private ApplicationEventPublisher eventPublisher;
    
    @Autowired
    private PositionInspectionService positionInspectionService;
    @Autowired
    private NewProductInspectionService newProductInspectionService;
    
    @Resource
    private IntlInspectionTaskConfService inspectionTaskConfService;
    
    @Resource
    private UserEventPopWindowService userEventPopWindowService;
    
    @Autowired
    private InspectionConfig inspectionConfig;
    
    @Resource
    private NewProductPromotionService newProductPromotionService;
    
    /**
     * 列表视图(日/周/月)
     */
    @PostMapping("/getCalendar")
    @ResponseBody
    public CommonApiResponse<Object> getCalendarByType(@RequestBody TaskCenterCalendarReq req) {
        if (StringUtils.isNotBlank(req.getJobValue()) && notifyJobTitles.contains(req.getJobValue())) {
            long startTime = System.currentTimeMillis();
            Object calendarEventDetail = taskCenterProxyService.getEventCalendarByType(req);
            long endTime = System.currentTimeMillis();
            log.info("getEventCalendarByType req:{}, cost:{}", GsonUtil.toJson(req), endTime - startTime);
            return new CommonApiResponse<>(calendarEventDetail);
        }
        Object calendarDetail = taskCenterProxyService.getCalendarByType(req);
        return new CommonApiResponse<>(calendarDetail);
    }
    
    @PostMapping("/noNeedCompleteTask")
    @ResponseBody
    public CommonApiResponse<Void> noNeedCompleteTask(@RequestBody TaskCenterFinishTaskReq req) {
        // 先调用原有的服务
        taskCenterProxyService.noNeedCompleteTask(req);
        
        // 调用阵地巡检服务处理
        try {
            log.info("调用阵地巡检服务处理无需完成任务: {}", req);
            positionInspectionService.noNeedCompleteTask(req);
        } catch (Exception e) {
            log.error("阵地巡检服务处理无需完成任务异常", e);
            // 不影响原有流程，异常不向上抛出
        }
        
        return new CommonApiResponse<>(null);
    }
    
    @PostMapping("/finishOuterTask")
    @ResponseBody
    public CommonApiResponse<Void> finishOuterTask(@RequestBody TaskCenterFinishTaskReq req) {
        try {
            if (notifyJobTitles.contains(req.getJobValue())) {
                log.info("督导员 销量上报(无销量), req: {}", JSONUtil.toJsonStr(req));
                String actionName = TaskActionEnum.SALES_UPLOAD_NO_SALES.getActionName();
                eventPublisher.publishEvent(new RmsApiRequestEvent(this,
                        new RmsAipRequestInfo(actionName, actionName, getAccount(), req.getStoreId())));
            } else {
                taskCenterProxyService.finishOuterTask(req);
            }
            return new CommonApiResponse<>(null);
        } catch (Exception e) {
            throw new RuntimeException("Error finish outer task: " + e.getMessage(), e);
        }
    }
    
    @PostMapping("/queryTaskNum")
    @ResponseBody
    public CommonApiResponse<Object> queryTaskNum(@RequestBody TaskCenterTaskNumReq req) {
        log.info("queryTaskNum req:{}", RetailJsonUtil.toJson(req));
        if (req.getOrgId() == null || StringUtils.isBlank(req.getOrgId())) {
            // 没有有效的阵地，返回空的数组，防止报错
            TaskNumResp resp = new TaskNumResp();
            resp.setCompletedNum(0);
            resp.setUnCompletedNum(0);
            resp.setAllTaskNum(0);
            resp.setNoNeedCompletedNum(0);
            return new CommonApiResponse<>(resp);
        }
        long startTime = System.currentTimeMillis();
        Object result = null;
        try {
            boolean isNewProductInspection = newProductInspectionService.checkNewProductInspection(req);
            log.info("检查是否为新品巡检: {}, req: {}", isNewProductInspection, req);
            // 检查是否为特定职位
            boolean isNotifyJob =
                    StringUtils.isNotBlank(req.getJobValue()) && notifyJobTitles.contains(req.getJobValue());
            if (isNotifyJob) {
                // 特定职位查询
                result = isNewProductInspection ? taskCenterProxyService.queryNewProductEventTaskNum(req)
                        : taskCenterProxyService.queryEventTaskNum(req);
            } else {
                // 普通查询
                result = isNewProductInspection ? taskCenterProxyService.queryNewProductTaskNum(req)
                        : taskCenterProxyService.queryTaskNum(req);
            }
            long endTime = System.currentTimeMillis();
            log.info("queryTaskNum req:{}, cost:{}", GsonUtil.toJson(req), endTime - startTime);
            return new CommonApiResponse<>(result);
        } catch (Exception e) {
            log.error("Error querying task num for request: {}", RetailJsonUtil.toJson(req), e);
            // 根据业务需求返回错误响应或默认值
        }
        return new CommonApiResponse<>(result);
    }
    
    @PostMapping("/getDetailTaskInfo")
    @ResponseBody
    public CommonApiResponse<Object> getDetailTaskInfo(@RequestBody TaskCenterDetailReq req) {
        Integer businessTypeId = req.getBusinessTypeId();
        // 如果是201或者202，调用大脑的getDetailTaskEventInfo接口，否则继续调原接口
        if (businessTypeId != null && (businessTypeId == 201 || businessTypeId == 202)) {
            Object detailTaskInfo = taskCenterProxyService.getDetailTaskEventInfo(req);
            return new CommonApiResponse<>(detailTaskInfo);
        } else {
            Object detailTaskInfo = taskCenterProxyService.getDetailTaskInfo(req);
            return new CommonApiResponse<>(detailTaskInfo);
        }
    }
    
    @PostMapping("/getPopWindowContent")
    @ResponseBody
    public CommonApiResponse<Object> getPopWindowContent(@RequestBody TaskCenterCommonReq req) {
        Object popWindowContent = taskCenterProxyService.getPopWindowContent(req);
        return new CommonApiResponse<>(popWindowContent);
    }
    
    @PostMapping("/confirmPopWindow")
    @ResponseBody
    public CommonApiResponse<Void> confirmPopWindow(@RequestBody ConfirmPopWindowsReq req) {
        taskCenterProxyService.confirmPopWindow(req);
        return new CommonApiResponse<>(null);
    }
    
    @PostMapping("/getHeadCalendar")
    @ResponseBody
    public CommonApiResponse<Object> getCalendarForHead(@RequestBody HeadCalendarReq req) {
        Object calendarForHead = taskCenterProxyService.getCalendarForHead(req);
        return new CommonApiResponse<>(calendarForHead);
    }
    
    
    @PostMapping("/getInspectionConfTask")
    @ResponseBody
    public CommonApiResponse<Object> getInspectionConfTask(@RequestBody TaskCenterInspectionConfReq req) {
        log.info("getInspectionConfTask req:{}", RetailJsonUtil.toJson(req));
        if (StringUtils.isBlank(req.getOrgId())) {
            return new CommonApiResponse<>(500, "orgId is blank", null);
        }
        if (StringUtils.isBlank(req.getJobValue())) {
            return new CommonApiResponse<>(500, "jobValue is blank", null);
        }
        Object object = taskCenterProxyService.getInspectionConfTask(req);
        return new CommonApiResponse<>(object);
    }
    
    /****
     * 获取需要CheckIn事件类型列表
     * @param request 需要CheckIn事件类型列表请求
     * @return 需要CheckIn事件类型列表
     */
    @PostMapping("/needCheckInEventTypeList")
    @AccessLog(response = true)
    public CommonApiResponse<NeedCheckInEventTypeResponse> needCheckInEventTypeList(
            @Valid @RequestBody NeedCheckInEventTypeRequest request) {
        NeedCheckInEventTypeResponse response = inspectionTaskConfService.getNeedCheckInEventTypeListWithEventTypeList(
                request.getCountry(), request.getPositionCode(), request.getType());
        return new CommonApiResponse<>(response);
    }
    
    /**
     * 检查是否需要弹框提醒
     *
     * @param request 检查弹框请求
     * @return 是否需要弹窗
     */
    @PostMapping("/checkPopup")
    @AccessLog(response = true)
    public CommonApiResponse<Boolean> checkPopup(@Valid @RequestBody CheckPopupRequest request) {
        try {
            // 1. 检查是否为督导职位
            if (!notifyJobTitles.contains(request.getJobValue())) {
                log.info("用户职位{}不是督导，返回false（不弹窗）", request.getJobValue());
                return new CommonApiResponse<>(false);
            }
            
            // 2. 检查是否在新品大促期间
            if (newProductPromotionService.isInNewProductPromotion(request.getCountry())) {
                log.info("国家{}在新品大促期间，返回false（不弹窗）", request.getCountry());
                return new CommonApiResponse<>(false);
            }
            
            // 3. 调用queryNewProductEventTaskNum接口
            TaskCenterTaskNumReq taskNumReq = new TaskCenterTaskNumReq();
            taskNumReq.setMiId(request.getMiId());
            taskNumReq.setArea(request.getCountry());
            taskNumReq.setOrgId(request.getPositionCode());
            taskNumReq.setJobValue(request.getJobValue());
            
            // 获取工作时间
            InspectionConfig.WorkTimeInfo workTimeInfo = inspectionConfig.getWorkTime(request.getCountry());
            taskNumReq.setStartTimeStamp(workTimeInfo.getStartTimeStamp(request.getCountry()));
            taskNumReq.setEndTimeStamp(workTimeInfo.getEndTimeStamp(request.getCountry()));
            
            // 调用queryNewProductEventTaskNum接口
            Object taskNumResponse = taskCenterProxyService.queryEventTaskNum(taskNumReq);
            log.info("queryNewProductEventTaskNum req:{}, response: {} ", RetailJsonUtil.toJson(taskNumReq),
                    RetailJsonUtil.toJson(taskNumResponse));
            
            if (taskNumResponse == null) {
                log.warn("queryNewProductEventTaskNum调用失败，返回false（异常默认不弹窗）");
                return new CommonApiResponse<>(false);
            }
            
            // 解析返回数据
            if (taskNumResponse instanceof TaskNumResp) {
                TaskNumResp taskNumResp = (TaskNumResp) taskNumResponse;
                Integer inspectionTime = taskNumResp.getInspectionTime();
                
                // 如果inspectionTime == 0，返回false（不弹窗）
                if (inspectionTime == null || inspectionTime == 0) {
                    log.info("inspectionTime={}，返回false（不弹窗）", inspectionTime);
                    return new CommonApiResponse<>(false);
                }
                
                // 否则调用UserEventPopWindowService.needPopup
                boolean needPopup = userEventPopWindowService.needPopup(request.getUserId(), request.getEventType(),
                        request.getPositionCode());
                log.info("inspectionTime={}，UserEventPopWindowService.needPopup={}", inspectionTime, needPopup);
                return new CommonApiResponse<>(needPopup);
            } else {
                log.warn("queryNewProductEventTaskNum返回数据类型不正确: {}，返回false（异常默认不弹窗）",
                        taskNumResponse.getClass().getName());
                return new CommonApiResponse<>(false);
            }
        } catch (Exception e) {
            log.error("checkPopup处理异常，返回false（异常默认不弹窗）", e);
            return new CommonApiResponse<>(false);
        }
    }
    
    /**
     * 设置无需弹窗提醒
     *
     * @param request 禁用弹框请求
     * @return 保存结果
     */
    @PostMapping("/disablePopup")
    @AccessLog(response = true)
    public CommonApiResponse<Boolean> disablePopup(@Valid @RequestBody DisablePopupRequest request) {
        boolean success = userEventPopWindowService.disablePopup(request.getUserId(), request.getEventType());
        return new CommonApiResponse<>(success);
    }
}
