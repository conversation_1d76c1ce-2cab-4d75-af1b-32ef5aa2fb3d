package com.mi.info.intl.retail.intlretail.app.convert;
import com.mi.info.intl.retail.intlretail.app.dto.RmsPropertyInfo;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.RmsUserBaseDataResponse;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper
public interface RmsPropertyInfoConvert {

    RmsPropertyInfoConvert INSTANCE = Mappers.getMapper(RmsPropertyInfoConvert.class);

    @Mapping(source = "userId", target = "userId")
    @Mapping(source = "jobValue", target = "jobValue")
    @Mapping(source = "employeeCode", target = "employeeCode")
    @Mapping(source = "englishName", target = "englishName")
    @Mapping(source = "userAccount", target = "userAccount")
    @Mapping(source = "userCountryId", target = "userCountryId")
    @Mapping(source = "userCountryCode", target = "userCountryCode")
    @Mapping(source = "userLanguageId", target = "userLanguageId")
    @Mapping(source = "storeId", target = "storeId")
    @Mapping(source = "storeName", target = "storeName")
    @Mapping(source = "storeCode", target = "storeCode")
    @Mapping(source = "storeGrade", target = "storeGrade")
    @Mapping(source = "storeType", target = "storeType")
    @Mapping(source = "storeAccountId", target = "storeAccountId")
    @Mapping(source = "storeCountryId", target = "storeCountryId")
    @Mapping(source = "jobTitle", target = "jobTitle")
    @Mapping(source = "userCountryShortCode", target = "userCountryShortCode")
    @Mapping(source = "currencyCode", target = "currencyCode")
    @Mapping(source = "isOffline", target = "isOffline")
    @Mapping(source = "userLanguageCode", target = "userLanguageCode")
    @Mapping(source = "dailyHour", target = "dailyHour")
    @Mapping(source = "longitude", target = "longitude")
    @Mapping(source = "latitude", target = "latitude")
    @Mapping(source = "storeCount", target = "storeCount")
    @Mapping(source = "storeAddress", target = "storeAddress")
    @Mapping(source = "isPromotionStore", target = "isPromotionStore")
    @Mapping(source = "isBrand", target = "isBrand")
    @Mapping(source = "signRuleId", target = "signRuleId")
    @Mapping(source = "isNormal", target = "isNormal")
    @Mapping(source = "normalPhotoRequired", target = "normalPhotoRequired")
    @Mapping(source = "normalNoteRequired", target = "normalNoteRequired")
    @Mapping(source = "effectiveDuration", target = "effectiveDuration")
    @Mapping(source = "effectiveRange", target = "effectiveRange")
    @Mapping(source = "isAbnormalPhoto", target = "isAbnormalPhoto")
    @Mapping(source = "abNormalPhotoRequired", target = "abNormalPhotoRequired")
    @Mapping(source = "isAbnormalNote", target = "isAbnormalNote")
    @Mapping(source = "abNormalNoteRequired", target = "abNormalNoteRequired")
    @Mapping(source = "signRecordId", target = "signRecordId")
    @Mapping(source = "signinTime", target = "signinTime")
    @Mapping(source = "signoutTime", target = "signoutTime")
    @Mapping(source = "miIdVirtual", target = "miIdVirtual")
    RmsPropertyInfo toRmsPropertyInfo(RmsUserBaseDataResponse response);
}

