package com.mi.info.intl.retail.intlretail.app.event;

import com.mi.info.intl.retail.intlretail.service.api.mq.dto.RmsAipRequestInfo;
import org.springframework.context.ApplicationEvent;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;


public class RmsApiRequestEvent extends ApplicationEvent {
    private RmsAipRequestInfo rmsAipRequestInfo;
    private JwtAuthenticationToken token;

    public RmsApiRequestEvent(Object source, RmsAipRequestInfo rmsAipRequestInfo) {
        super(source);
        this.rmsAipRequestInfo = rmsAipRequestInfo;
    }
    
    public RmsApiRequestEvent(Object source, RmsAipRequestInfo rmsAipRequestInfo, JwtAuthenticationToken token) {
        this(source, rmsAipRequestInfo);
        this.token = token;
    }

    public RmsAipRequestInfo getRmsAipRequestInfo() {
        return rmsAipRequestInfo;
    }

    public void setRmsAipRequestInfo(RmsAipRequestInfo rmsAipRequestInfo) {
        this.rmsAipRequestInfo = rmsAipRequestInfo;
    }
    
    public JwtAuthenticationToken getToken() {
        return token;
    }
    
    public void setToken(JwtAuthenticationToken token) {
        this.token = token;
    }
}
