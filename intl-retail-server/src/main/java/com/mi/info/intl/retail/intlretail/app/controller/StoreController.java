package com.mi.info.intl.retail.intlretail.app.controller;

import com.mi.info.intl.retail.intlretail.app.dto.query.HaveListPermissionQuery;
import com.mi.info.intl.retail.intlretail.service.api.proxy.RetailerAppConfigService;
import com.mi.info.intl.retail.intlretail.service.api.proxy.StoreProxyService;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.RmsUserBaseDataResponse;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.UpdateDefaultStoreDto;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.xiaomi.cnzone.storeapi.model.channelbuild.common.req.AppPageRequest;
import com.xiaomi.cnzone.storeapi.model.channelbuild.common.req.RmsCallBackReq;
import com.xiaomi.cnzone.storeapi.model.v2.storenode.req.NodeQueryReq;
import com.xiaomi.cnzone.storeapi.model.v2.storenode.req.NodeSubmitReq;
import com.xiaomi.cnzone.storems.api.model.req.store.GetPositionInfoRequest;
import com.xiaomi.cnzone.storems.api.model.req.store.GetStoreLogListRequest;
import com.xiaomi.cnzone.storems.api.model.req.store.GetStorePositionLogListRequest;
import com.xiaomi.cnzone.storems.api.model.req.store.PositionListReq;
import com.xiaomi.cnzone.storems.api.model.req.store.international.GetInternationalChannelMergeListRequest;
import com.xiaomi.cnzone.storems.api.model.req.store.international.GetInternationalChannelStoreDetailRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/*
 * RequestMapping多路径兼容，不要删除
 */
@Slf4j
@RestController
@RequestMapping({"/api/proxy/store", "/*/api/proxy/store"})
public class StoreController extends BaseController {

    @Resource
    private StoreProxyService storeProxyService;

    @Resource
    RetailerAppConfigService retailerAppConfigService;

    @PostMapping("/updateDefaultStore")
    @ResponseBody
    public CommonApiResponse updateDefaultStore(@RequestBody UpdateDefaultStoreDto updateDefaultStoreDto) {
        JwtAuthenticationToken token = (JwtAuthenticationToken) SecurityContextHolder.getContext().getAuthentication();
        String name = String.valueOf(token.getTokenAttributes().get("unique_name"));
        updateDefaultStoreDto.setUserAccount(name);
        storeProxyService.updateDefaultStore(this.getCurrentDay(), this.getAccount(),
                updateDefaultStoreDto, token.getToken().getTokenValue());
        return new CommonApiResponse("");
    }

    @PostMapping("/haveListPermission")
    public CommonApiResponse haveListPermission(@RequestBody HaveListPermissionQuery query) {
        String tokenValue = this.getTokenValue();
        String account = this.getAccount();
        RmsUserBaseDataResponse userBaseInfo = retailerAppConfigService.requestRmsGetUserInfo(account, tokenValue);
        return new CommonApiResponse(CollectionUtils.isNotEmpty(storeProxyService.listRMSIds(tokenValue, userBaseInfo.getUserId(), query.getType())));
    }

    @PostMapping("/listStore")
    public CommonApiResponse listStore(@RequestBody GetInternationalChannelMergeListRequest request) {
        String tokenValue = this.getTokenValue();
        String account = this.getAccount();
        RmsUserBaseDataResponse userBaseInfo = retailerAppConfigService.requestRmsGetUserInfo(account, tokenValue);
        return new CommonApiResponse(storeProxyService.listStore(request, userBaseInfo.getUserId(), account, tokenValue));
    }

    @PostMapping("/listAllStore")
    public CommonApiResponse listAllStore(@RequestBody GetInternationalChannelMergeListRequest request) {
        String tokenValue = this.getTokenValue();
        String account = this.getAccount();
        RmsUserBaseDataResponse userBaseInfo = retailerAppConfigService.requestRmsGetUserInfo(account, tokenValue);
        return new CommonApiResponse(storeProxyService.listAllStore(request, userBaseInfo.getUserId(), account, tokenValue));
    }

    @PostMapping("/getStoreDetail")
    public CommonApiResponse getStoreDetail(@RequestBody GetInternationalChannelStoreDetailRequest query) {
        return new CommonApiResponse(storeProxyService.getStore(query));
    }

    @PostMapping("/getPositionDetail")
    public CommonApiResponse getPositionDetail(@RequestBody GetPositionInfoRequest request) {
        return new CommonApiResponse(storeProxyService.getPosition(request));
    }

    @PostMapping("/listStoreLog")
    public CommonApiResponse listStoreLog(@RequestBody GetStoreLogListRequest var1) {
        return new CommonApiResponse(storeProxyService.listStoreLogs(var1));
    }

    @PostMapping("/listPositionLog")
    public CommonApiResponse listPositionLog(@RequestBody GetStorePositionLogListRequest var1) {
        return new CommonApiResponse(storeProxyService.listPositionLogs(var1));
    }

    @PostMapping("/listPosition")
    @ResponseBody
    public CommonApiResponse listPosition(@RequestBody PositionListReq request) {
        String tokenValue = this.getTokenValue();
        String account = this.getAccount();
        RmsUserBaseDataResponse userBaseInfo = retailerAppConfigService.requestRmsGetUserInfo(account, tokenValue);
        return new CommonApiResponse(storeProxyService.listPosition(request, userBaseInfo.getUserId(), account, tokenValue));
    }


    @PostMapping("/listSubmit")
    @ResponseBody
    public CommonApiResponse listSubmit(@RequestBody AppPageRequest var1) {
        JwtAuthenticationToken token = (JwtAuthenticationToken) SecurityContextHolder.getContext().getAuthentication();
        String account = this.getAccount();
        RmsUserBaseDataResponse userBaseInfo = retailerAppConfigService.requestRmsGetUserMenuInfo(account, token.getToken().getTokenValue());
        var1.setRmsUser(userBaseInfo.getUserId());
        return new CommonApiResponse(storeProxyService.listSubmitStore(var1));
    }

    /**
     * 查询数据
     *
     * @param request
     * @return
     */
    @PostMapping("/query")
    public CommonApiResponse query(@RequestBody NodeQueryReq request) {
        Object result = storeProxyService.query(request);
        return new CommonApiResponse<>(result);
    }

    @PostMapping("/audit")
    public CommonApiResponse audit(@RequestBody RmsCallBackReq var1) {
        return new CommonApiResponse(storeProxyService.audit(var1));
    }

    @PostMapping("/getSelectorAll")
    public CommonApiResponse getSelectorAll() {
        return new CommonApiResponse(storeProxyService.getSelectorAll());
    }

    /**
     * 提交数据
     *
     * @return
     */
    @PostMapping("/submit")
    public CommonApiResponse submit(@RequestBody NodeSubmitReq request) {
        JwtAuthenticationToken token = (JwtAuthenticationToken) SecurityContextHolder.getContext().getAuthentication();
        String account = this.getAccount();
        RmsUserBaseDataResponse userBaseInfo = retailerAppConfigService.requestRmsGetUserInfo(account, token.getToken().getTokenValue());
        Object result = storeProxyService.submit(request, token.getToken().getTokenValue(), userBaseInfo);
        return new CommonApiResponse<>(result);
    }

    /**
     * 撤销审核
     *
     * @return
     */
    @PostMapping("/auditCancel")
    public CommonApiResponse auditCancel(@RequestBody NodeSubmitReq request) {
        JwtAuthenticationToken token = (JwtAuthenticationToken) SecurityContextHolder.getContext().getAuthentication();
        request.setToken(token.getToken().getTokenValue());
        Object result = storeProxyService.recall(request, null, null);
        return new CommonApiResponse<>(result);
    }

}
