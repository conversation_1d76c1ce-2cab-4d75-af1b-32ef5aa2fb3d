package com.mi.info.intl.retail.intlretail.app.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class RmsPropertyInfo implements Serializable {

    @JsonProperty("user_id")
    private String userId;

    @JsonProperty("job_value")
    private Long jobValue;

    @JsonProperty("employee_code")
    private String employeeCode;

    @JsonProperty("english_name")
    private String englishName;

    @JsonProperty("user_account")
    private String userAccount;

    @JsonProperty("user_country_id")
    private String userCountryId;

    @JsonProperty("user_country_code")
    private String userCountryCode;

    @JsonProperty("user_language_id")
    private String userLanguageId;

    @JsonProperty("store_id")
    private String storeId;

    @JsonProperty("store_name")
    private String storeName;

    @JsonProperty("store_code")
    private String storeCode;

    @JsonProperty("store_grade")
    private Long storeGrade;

    @JsonProperty("store_type")
    private Integer storeType;

    @JsonProperty("store_account_id")
    private String storeAccountId;

    @JsonProperty("store_country_id")
    private String storeCountryId;

    @JsonProperty("job_title")
    private String jobTitle;

    @JsonProperty("user_country_short_code")
    private String userCountryShortCode;

    @JsonProperty("currency_code")
    private String currencyCode;

    @JsonProperty("is_offline")
    private Boolean isOffline;

    @JsonProperty("user_language_code")
    private String userLanguageCode;

    @JsonProperty("daily_hour")
    private Integer dailyHour;

    @JsonProperty("longitude")
    private String longitude;

    @JsonProperty("latitude")
    private String latitude;

    @JsonProperty("store_count")
    private Integer storeCount;

    @JsonProperty("store_address")
    private String storeAddress;


    @JsonProperty("is_promotion_store")
    private Boolean isPromotionStore;

    @JsonProperty("is_brand")
    private Boolean isBrand;

    @JsonProperty("sign_rule_id")
    private String signRuleId;

    @JsonProperty("is_normal")
    private Boolean isNormal;

    @JsonProperty("normal_photo_required")
    private Boolean normalPhotoRequired;

    @JsonProperty("normal_note_required")
    private Boolean normalNoteRequired;

    @JsonProperty("effective_duration")
    private Double effectiveDuration;

    @JsonProperty("effective_range")
    private Double effectiveRange;

    @JsonProperty("is_abnormal_photo")
    private Boolean isAbnormalPhoto;

    @JsonProperty("abnormal_photo_required")
    private Boolean abNormalPhotoRequired;

    @JsonProperty("is_abnormal_note")
    private Boolean isAbnormalNote;

    @JsonProperty("abnormal_note_required")
    private Boolean abNormalNoteRequired;

    @JsonProperty("sign_record_id")
    private String signRecordId;

    @JsonProperty("signin_time")
    private String signinTime;

    @JsonProperty("signout_time")
    private String signoutTime;

    @JsonProperty("mi_id")
    private String miIdVirtual;

    @JsonProperty("mi_talk")
    private String miTalk;

    @JsonProperty("parent_store_code")
    private String parentStoreCode;

    @JsonProperty("parent_store_crss_code")
    private String parentStoreCrssCode;
}
