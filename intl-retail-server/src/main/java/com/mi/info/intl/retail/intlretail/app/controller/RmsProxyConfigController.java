package com.mi.info.intl.retail.intlretail.app.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.stream.JsonReader;
import com.mi.info.intl.retail.intlretail.app.dto.FunctionalRequest;
import com.mi.info.intl.retail.intlretail.service.api.proxy.RetailerAppConfigService;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.RmsUserBaseDataResponse;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.org.domain.material.service.NewProductInspectionDomainService;
import com.mi.info.intl.retail.org.domain.position.service.PositionInspectionDomainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/*
 * RequestMapping多路径兼容，不要删除
 */
@RestController
@RequestMapping({"/api/proxy/config", "/*/api/proxy/config"})
@Slf4j
public class RmsProxyConfigController extends BaseController {

    public static final String FILE_NAME = "functional.json";
    @Autowired
    private RetailerAppConfigService retailerAppConfigService;

    @Autowired
    private PositionInspectionDomainService positionInspectionDomainService;
    @Autowired
    private NewProductInspectionDomainService newProductInspectionDomainService;

    @PostMapping("/h5Url")
    @ResponseBody
    public CommonApiResponse<Map<String, String>> getFunctional(@RequestBody FunctionalRequest request) {
        Map<String, String> hashMap = new HashMap<>();
        try (InputStream inputStream = JsonReader.class.getClassLoader().getResourceAsStream(FILE_NAME)) {
            if (inputStream != null) {
                ObjectMapper objectMapper = new ObjectMapper();
                hashMap = objectMapper.readValue(inputStream, Map.class);
            }
        } catch (Exception e) {
            throw new RuntimeException("Error reading JSON file: " + e.getMessage(), e);
        }
        return new CommonApiResponse<>(hashMap);
    }

    @PostMapping("/homeFunctions")
    @ResponseBody
    public CommonApiResponse<List<String>> homeFunctions() {
        JwtAuthenticationToken token = (JwtAuthenticationToken) SecurityContextHolder.getContext().getAuthentication();
        String account = this.getAccount();
        RmsUserBaseDataResponse userBaseInfo = retailerAppConfigService.requestRmsGetUserMenuInfo(account, token.getToken().getTokenValue());
        List<String> menuList = new ArrayList<>();
        if (userBaseInfo.getMenu() != null && !userBaseInfo.getMenu().isEmpty()) {
            menuList = userBaseInfo.getMenu().stream().map(RmsUserBaseDataResponse.MenuInfo::getScreenName)
                    .collect(Collectors.toList());
        }
        //阵地巡检模块
        if (positionInspectionDomainService.hasUnCompletedTask(account)) {
            menuList.add("PositionInspection");
        }
        //物料巡检模块
        if (newProductInspectionDomainService.hasUnCompletedTask(account)) {
            menuList.add("MaterialInspection");
        }
        return new CommonApiResponse<>(menuList);

    }
}