package com.mi.info.intl.retail.intlretail.app.controller;

import com.mi.info.intl.retail.intlretail.app.dto.AuthResourceResponse;
import com.mi.info.intl.retail.intlretail.app.dto.RmsPropertyInfo;
import com.mi.info.intl.retail.intlretail.app.dto.UserInfoResponse;
import com.mi.info.intl.retail.intlretail.domain.common.utils.JsonUtil;
import com.mi.info.intl.retail.intlretail.service.api.proxy.RetailerAppConfigService;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.RmsUserBaseDataResponse;
import com.mi.info.intl.retail.intlretail.app.convert.RmsPropertyInfoConvert;
import com.mi.info.intl.retail.model.CommonApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

/**
 * 适配国际版零售通框架
 * RequestMapping多路径兼容，不要删除
 */
@RestController
@RequestMapping({"/api/proretail", "/*/api/proretail"})
public class RetailerAppController extends BaseController {

    @Autowired
    private RetailerAppConfigService retailerAppConfigService;


    @PostMapping("/account/users/center/getUserInfoGlobal")
    @ResponseBody
    public CommonApiResponse<UserInfoResponse> getUserInfoGlobal() {
        String account = this.getAccount();
        String token = this.getTokenValue();
        String json = "{\"user_info\":{\"role\":1,\"channel_dot_keys\":[\"全渠道\"],\"own\":0,\"default_app_retail_mode\":2,\"user_id\":0," +
                "\"realname\":\"zhulin\",\"switchable_app_retail_mode\":[2],"
                + "\"mockable\":\"N\",\"area_id\":\"ID\",\"channels\":[0],\"switchable_shadow_list\":[2],\"avata\":\"\",\"nickname\":\"zhulin\"," +
                "\"role_key\":\"\",\"role_name\":\"\","
                + "\"global_area_id\":\"ID\",\"channels_sign\":[\"total\"],\"suspension\":false," +
                "\"rms_property_info\":{\"store_id\":\"faeb92f2-d2a9-ec11-9840-000d3a0850b9\"}},"
                + "\"phone_property_info\":{\"role\":1,\"channel_dot_keys\":[\"全渠道\"],\"channels\":[0],\"role_key\":\"\"," +
                "\"role_name\":\"\",\"channels_sign\":[\"total\"]}}";
        UserInfoResponse userInfoResponse = JsonUtil.json2bean(json, UserInfoResponse.class);
        RmsUserBaseDataResponse userBaseInfo = retailerAppConfigService.requestRmsGetUserInfo(account, token);
        userInfoResponse.getUserInfo().setAreaId(userBaseInfo.getUserCountryShortCode());
        userInfoResponse.getUserInfo().setGlobalAreaId(userBaseInfo.getUserCountryShortCode());
        if (Objects.isNull(userInfoResponse.getUserInfo())) {
            userInfoResponse.setUserInfo(new UserInfoResponse.UserInfo());
        }
        convertRmsPropertyInfo(userInfoResponse, userBaseInfo);
        return new CommonApiResponse(userInfoResponse);
    }

    private void convertRmsPropertyInfo(UserInfoResponse userInfoResponse, RmsUserBaseDataResponse userBaseInfo) {
        UserInfoResponse.UserInfo userInfo = userInfoResponse.getUserInfo();
        userInfo.setRealname(userBaseInfo.getEnglishName());
        userInfo.setNickname(userBaseInfo.getEnglishName());
        // 组装数据
        RmsPropertyInfo propertyInfo = RmsPropertyInfoConvert.INSTANCE.toRmsPropertyInfo(userBaseInfo);
        userInfoResponse.getUserInfo().setRmsPropertyInfo(propertyInfo);
    }

    @PostMapping("/auth/config/queryAuthResource")
    @ResponseBody
    public CommonApiResponse<AuthResourceResponse> queryAuthResource() {
        String json = "{\"code\":0,\"data\":{\"tabResources\":{\"resourceKeyList\":[\"tab.manager.workbench\",\"tab.myCenter\"]," +
                "\"defaultDisplayKey\":\"tab.manager.workbench\"},\"lastModify\":1730184605173,\"pageResources\":{\"p1\":[\"f1\"," +
                "\"ff1\",\"feat1.xia.name\"],\"manager.headquartersManagement\":[\"manager.headquartersManagement.storeSales\"," +
                "\"manager.headquartersManagement.storePhoneSaleDetail\",\"manager.headquartersManagement.monthlySaleReportSubFloor\"," +
                "\"manager.headquartersManagement.saleRatio\",\"manager.headquartersManagement.monthlySaleReport\"," +
                "\"manager.headquartersManagement.showMessageIcon\",\"manager.headquartersManagement.saleCategoryRatio\"," +
                "\"manager.headquartersManagement.todaySale\",\"manager.headquartersManagement.saleCategory\"," +
                "\"manager.headquartersManagement.dataBriefing\"]," +
                "\"my.feedback\":[\"my.feedback.submit\"]}},\"message\":\"ok\"}";
        return new CommonApiResponse(JsonUtil.json2bean(json, AuthResourceResponse.class));
    }
}
