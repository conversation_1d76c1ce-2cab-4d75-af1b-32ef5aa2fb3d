package com.mi.info.intl.retail.intlretail.app.controller;

import com.mi.info.intl.retail.intlretail.infra.utils.CommonUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.rpc.RpcContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.web.context.request.RequestContextHolder;

import javax.servlet.http.HttpServletRequest;

public abstract class BaseController {
    static final String CURRENT_DAY = "currentDay";
    static final String RETAIL_GLOBAL_AREA = "X-Retail-Global-Area";
    //X-Retail-Language
    static final String RETAIL_LANGUAGE = "X-Retail-Language";

    public String getLoginName() {
        JwtAuthenticationToken token = (JwtAuthenticationToken) SecurityContextHolder.getContext().getAuthentication();
        return String.valueOf(token.getTokenAttributes().get("name"));
    }

    //获取upn作为账号
    public String getAccount() {
        JwtAuthenticationToken token = (JwtAuthenticationToken) SecurityContextHolder.getContext().getAuthentication();
        return String.valueOf(token.getTokenAttributes().get("upn"));
    }

    public String getTokenValue() {
        JwtAuthenticationToken token = (JwtAuthenticationToken) SecurityContextHolder.getContext().getAuthentication();
        return token != null && token.getToken() != null ? token.getToken().getTokenValue() : "";
    }


    void setContext() {
        RpcContext.getContext().setAttachment("$upc_account", getAccount());
        RpcContext.getContext().setAttachment("$mid", "**********");
//        HeraContextKeyValueHolder.setAreaForDev("ID");
    }

    public String getCurrentDay() {
        return (String) RequestContextHolder.currentRequestAttributes().getAttribute(CURRENT_DAY, 0);
    }

    public String getCountryCode() {
        HttpServletRequest request = CommonUtils.getCurrentRequest();
        if (request == null) {
            return "";
        }
        String countryCode = request.getHeader(RETAIL_GLOBAL_AREA);
        if (countryCode == null || countryCode.trim().isEmpty()) {
            return "";
        }
        return countryCode;
    }

    public String getLocale() {
        HttpServletRequest request = CommonUtils.getCurrentRequest();

        if (request == null) {
            return null;
        }

        String locale = request.getHeader("X-Retail-Locale");

        if (StringUtils.isNotBlank(locale)) {
            return locale;
        }

        return null;
    }

}

