package com.mi.info.intl.retail.intlretail.app.controller;

import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.intlretail.service.api.market.CollegeService;
import com.mi.info.intl.retail.user.app.UserService;
import com.mi.info.intl.retail.user.infra.entity.IntlRmsUser;
import com.xiaomi.nrme.market.api.vo.article.*;
import com.xiaomi.nrme.market.api.vo.article.intl.pojo.IntlArticleVO;
import com.xiaomi.nrme.market.api.vo.article.intl.req.*;
import com.xiaomi.nrme.market.api.vo.article.intl.resp.IntlCollegeArticleResp;
import com.xiaomi.nrme.market.api.vo.article.intl.resp.IntlPageResp;
import com.xiaomi.nrme.market.api.vo.category.i18n.IntlCategoryAppFirstPageRespVO;
import com.xiaomi.nrme.market.api.vo.category.i18n.IntlCategoryAppSecondVO;
import com.xiaomi.nrme.market.api.vo.category.i18n.IntlCategoryPageableReqVO;
import com.xiaomi.nrme.market.api.vo.category.i18n.IntlPosCategorySpuVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * - <AUTHOR>
 * - @date 2025/6/24
 * - @description:
 * -
 **/
@Slf4j
@RestController
@RequestMapping({"/api/proxy/app/college", "/*/api/proxy/app/college"})
public class CollegeController extends BaseController {
    @Resource
    private CollegeService collegeService;

    @Autowired
    private UserService userService;

    @PostMapping("/firstList")
    @ResponseBody
    public CommonApiResponse<List<IntlCategoryAppFirstPageRespVO>> appFirstCategoryList(@RequestBody IntlCategoryPageableReqVO pageableReqVO) {
        List<IntlCategoryAppFirstPageRespVO> categoryAppFirstPageRespVOS = collegeService.appFirstCategoryList(pageableReqVO);
        return new CommonApiResponse<>(categoryAppFirstPageRespVOS);

    }
    @PostMapping("/secondList")
    @ResponseBody
    public CommonApiResponse<List<IntlCategoryAppSecondVO>> intlAppSecondCategoryList(@RequestBody IntlCategoryPageableReqVO pageableReqVO) {
        List<IntlCategoryAppSecondVO> categoryAppSecondVOS = collegeService.intlAppSecondCategoryList(pageableReqVO);

        return new CommonApiResponse<>(categoryAppSecondVOS);
    }

    @PostMapping("/pageList")
    @ResponseBody
    public CommonApiResponse<IntlPageResp<IntlArticleVO>> pageList(@RequestBody IntlAppArticlePageableVO articlePageableVO,
                                                                    HttpServletRequest httpServletRequest) {
        IntlRmsUser rmsUser = userService.getUserInfoDomainName(getAccount());
        articlePageableVO.setMino(String.valueOf(rmsUser.getMiId()));
        articlePageableVO.setLanguage(this.getLanguage(httpServletRequest));
        IntlPageResp<IntlArticleVO> articleVOPageResp = collegeService.pageList(articlePageableVO);
        return new CommonApiResponse<>(articleVOPageResp);
    }

    @PostMapping("/detail")
    @ResponseBody
    public CommonApiResponse<IntlCollegeArticleResp> detail(@RequestBody IntlCollegeArticleReq intlCollegeArticleReq) {
        IntlRmsUser rmsUser = userService.getUserInfoDomainName(getAccount());
        intlCollegeArticleReq.setMid(rmsUser.getMiId());
        IntlCollegeArticleResp detail = collegeService.detail(intlCollegeArticleReq);
        return new CommonApiResponse<>(detail);
    }
    @PostMapping("/toggleLike")
    @ResponseBody
    public CommonApiResponse<Long> toggleLike(@RequestBody IntlRetailCollegeLikeRequest retailCollegeLikeRequest) {
        IntlRmsUser rmsUser = userService.getUserInfoDomainName(getAccount());
        retailCollegeLikeRequest.setMino(String.valueOf(rmsUser.getMiId()));
        Long toggleLike = collegeService.toggleLike(retailCollegeLikeRequest);
        return new CommonApiResponse<>(toggleLike);
    }
    @PostMapping("/progressRate")
    @ResponseBody
    public CommonApiResponse<Void> syncArticleProgress(@RequestBody IntlArticleViewProgressReqVO progressReqVO) {
        IntlRmsUser rmsUser = userService.getUserInfoDomainName(getAccount());
        progressReqVO.setMino(String.valueOf(rmsUser.getMiId()));

        Void result = collegeService.syncArticleProgress(progressReqVO);
        return new CommonApiResponse<>(result);
    }

    @PostMapping("/fullTextExplore")
    @ResponseBody
    public CommonApiResponse<PageResp<IntlArticleVO>> fullTextExplore(@RequestBody ArticleSearchEsPageableReq articleSearchEsPageableReq,
                                                                      HttpServletRequest httpServletRequest) {

        articleSearchEsPageableReq.setLanguage(this.getLanguage(httpServletRequest));
        PageResp<IntlArticleVO> articleVOPageResp = collegeService.fullTextExplore(articleSearchEsPageableReq);
        return new CommonApiResponse<>(articleVOPageResp);
    }

    @PostMapping("/searchSpuByName")
    @ResponseBody
    public CommonApiResponse<List<IntlPosCategorySpuVO>> searchSpuByName(@RequestBody IntlAppSpuListVO articleSearchEsPageableReq) {

        List<IntlPosCategorySpuVO> intlPosCategorySpuVOS = collegeService.searchSpuByName(articleSearchEsPageableReq);
        return new CommonApiResponse<>(intlPosCategorySpuVOS);
    }

    private String getLanguage(HttpServletRequest request) {
        String language = request.getHeader("X-Retail-Language");
        if (StringUtils.isNotBlank(language)) {
            return language;
        }
        return null;
    }

}
