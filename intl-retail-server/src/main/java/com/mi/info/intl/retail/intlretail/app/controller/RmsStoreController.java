package com.mi.info.intl.retail.intlretail.app.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.mi.info.intl.retail.intlretail.service.api.proxy.RmsProxyService;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.utils.intl.IntlTimeUtil;
import com.xiaomi.cnzone.brain.platform.api.model.req.app.TaskFrequencyReq;
import com.xiaomi.cnzone.brain.platform.api.model.resp.app.TaskFrequencyResp;
import com.xiaomi.cnzone.brain.platform.api.model.resp.app.TaskFrequencyResp.TaskFrequency;
import com.xiaomi.cnzone.brain.platform.api.provider.BrainPlatformOuterProvider;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.util.Assert;

import com.mi.info.intl.retail.exception.RetailRunTimeException;
import com.mi.info.intl.retail.intlretail.app.enums.UserJobSupervisorEnum;
import org.springframework.web.bind.annotation.*;

import java.util.Optional;
import java.util.stream.Collectors;
import java.util.List;
import java.util.ArrayList;

@RestController
@RequestMapping({"/api/proxy", "/*/api/proxy"})
@Slf4j
public class RmsStoreController extends BaseController {

    private static final String TOKEN_NULL_MESSAGE = "token is null";
    private static final String QUERY_STORE_PATH = "new_QueryUserStoreDataReturnStr";

    @Autowired
    private RmsProxyService rmsProxyService;

    @Autowired
    private ObjectMapper objectMapper;

    @DubboReference(group = "${center.dubbo.group:}", check = false, timeout = 20000, retries = 0)
    private BrainPlatformOuterProvider brainPlatformOuterProvider;


    @PostMapping("/rmsUserToken2/new_QueryUserStoreDataReturnStr")
    @ResponseBody
    public CommonApiResponse<Object> queryUserStore(@RequestBody(required = false) Object requestBody) {
        // 获取认证token
        JwtAuthenticationToken token = (JwtAuthenticationToken) SecurityContextHolder.getContext().getAuthentication();
        Assert.notNull(token, TOKEN_NULL_MESSAGE);

        // 调用RMS接口
        String result = rmsProxyService.requestByUserToken(
                "/api/data/v9.2/" + QUERY_STORE_PATH,
                objectToString(requestBody),
                token.getToken().getTokenValue(),
                "POST"
        );

        // 处理返回数据
        return processQueryResult(result, requestBody);
    }

    /**
     * 处理查询结果
     */
    private CommonApiResponse<Object> processQueryResult(String result, Object requestBody) {
        if (StringUtils.isEmpty(result)) {
            return new CommonApiResponse<>(Optional.ofNullable(result).orElse(""));
        }

        try {
            // 解析请求体和返回结果
            JsonNode resultNode = objectMapper.readTree(result);
            JsonNode requestNode = (requestBody != null) ?
                    objectMapper.valueToTree(requestBody) : objectMapper.createObjectNode();
            // 处理返回数据
            JsonNode processedNode = processResultData(resultNode, requestNode);
            return new CommonApiResponse<>(processedNode);
        } catch (JsonProcessingException e) {
            log.error("Failed to process result", e);
            return new CommonApiResponse<>(result);
        }
    }

    /**
     * 处理返回数据
     * 在这里实现特定的业务逻辑
     */
    private JsonNode processResultData(JsonNode resultNode, JsonNode requestNode) {
        try {
            String userJob = requestNode.has("userJob") ? requestNode.get("userJob").asText() : "";
            Long mid = requestNode.has("mid") ? requestNode.get("mid").asLong() : null;


            // 只有特定角色才需要处理数据
            if (UserJobSupervisorEnum.isValidJob(userJob) && mid != null) {
                // 获取店铺编码列表
                List<String> storeCodes = extractStoreCodes(resultNode);

                // 调用任务列表查询方法
                TaskFrequencyReq taskFrequencyReq = new TaskFrequencyReq();
                taskFrequencyReq.setMid(mid);
                taskFrequencyReq.setOrgIds(storeCodes);
                TaskFrequencyResp taskFrequencyResp = brainPlatformOuterProvider.queryTaskFrequency(taskFrequencyReq);

                // 先设置默认值
                processStoresWithTasksDefault(resultNode);

                // 根据店铺编码列表和任务列表处理数据
                processStoresWithTasks(requestNode, resultNode, taskFrequencyResp.getList());
            }

            return resultNode;
        } catch (Exception e) {
            log.error("Error processing result data", e);
            return resultNode;
        }
    }

    /**
     * 从结果中提取店铺编码列表
     */
    private List<String> extractStoreCodes(JsonNode resultNode) {
        List<String> storeCodes = new ArrayList<>();
        try {
            // 处理店铺列表
            JsonNode storeList = resultNode.path("userStoreList");
            if (storeList.isArray()) {
                for (JsonNode store : storeList) {
                    String storeCode = store.path("new_code").asText();
                    if (StringUtils.isNotEmpty(storeCode) && !storeCodes.contains(storeCode)) {
                        storeCodes.add(storeCode);
                    }
                }
            }
        } catch (Exception e) {
            log.error("Error extracting store codes", e);
        }
        return storeCodes;
    }

    /**
     * 处理店铺和任务数据
     */
    void processStoresWithTasks(JsonNode requestNode, JsonNode resultNode, List<TaskFrequency> tasks) {
        if (resultNode == null || !resultNode.has("userStoreList") || tasks == null || tasks.isEmpty()) {
            return;
        }
        String area = requestNode.has("area") ? requestNode.get("area").asText() : "";
        // 过滤tasks中businessTypeId=201的任务
        List<TaskFrequency> filteredTasks = tasks.stream()
                .filter(task -> task.getBusinessTypeId() == 201)
                .collect(Collectors.toList());
        // 遍历resultNode.userStoreList,根据new_code和tasks中的orgId匹配
        JsonNode userStoreList = resultNode.path("userStoreList");
        if (userStoreList.isArray()) {
            for (JsonNode store : userStoreList) {
                String storeCode = store.path("new_code").asText();
                // 找到匹配的第一条任务
                TaskFrequency matchingTask = filteredTasks.stream()
                        .filter(task -> task.getOrgId().equals(storeCode))
                        .findFirst()
                        .orElse(null);
                if (matchingTask != null) {
                    // 将匹配的任务数据添加到店铺信息中
                    ((ObjectNode) store).put("frequency", matchingTask.getFrequency()); // 任务频率
                    ((ObjectNode) store).put("completionTimes", matchingTask.getCompletionTimes()); // 完成次数
                    // 任务开始/结束时间(时间戳转换为字符串)
                    ((ObjectNode) store).put("taskStartDate", IntlTimeUtil.parseTimestampToAreaTime(area, matchingTask.getStartTimeStamp()));
                    ((ObjectNode) store).put("taskEndDate", IntlTimeUtil.parseTimestampToAreaTime(area, matchingTask.getEndTimeStamp()));
                }
            }
        }
    }

    void processStoresWithTasksDefault(JsonNode resultNode) {

        // 遍历resultNode.userStoreList,根据new_code和tasks中的orgId匹配
        JsonNode userStoreList = resultNode.path("userStoreList");
        if (userStoreList.isArray()) {
            for (JsonNode store : userStoreList) {

                // 将匹配的任务数据添加到店铺信息中
                ((ObjectNode) store).putNull("frequency"); // 任务频率
                ((ObjectNode) store).putNull("completionTimes"); // 完成次数
                // 任务开始/结束时间(时间戳转换为字符串)
                ((ObjectNode) store).put("taskStartDate", "");
                ((ObjectNode) store).put("taskEndDate", "");

            }
        }
    }


    /**
     * 请求参数序列化
     */
    private String objectToString(Object jsonObject) {
        if (jsonObject == null || "".equals(jsonObject)) {
            return "{}";
        }
        try {
            String result = objectMapper.writeValueAsString(jsonObject);
            log.info("Store query request body: {}", result);
            return result;
        } catch (JsonProcessingException e) {
            throw new RetailRunTimeException("json serialize error:" + e);
        }
    }
}
