package com.mi.info.intl.retail.intlretail.app.event.listener;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mi.info.intl.retail.api.task.enums.TaskActionEnum;
import com.mi.info.intl.retail.cooperation.task.config.InspectionConfig;
import com.mi.info.intl.retail.intlretail.app.event.RmsApiRequestEvent;
import com.mi.info.intl.retail.intlretail.infra.mq.RocketMQProducer;
import com.mi.info.intl.retail.intlretail.service.api.mq.dto.RmsAipRequestInfo;
import com.mi.info.intl.retail.intlretail.service.api.proxy.RetailerAppConfigService;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.RmsUserBaseDataResponse;
import com.mi.info.intl.retail.org.infra.entity.IntlRmsCountryTimezone;
import com.mi.info.intl.retail.org.infra.entity.IntlRmsPosition;
import com.mi.info.intl.retail.org.infra.mapper.IntlRmsCountryTimezoneMapper;
import com.mi.info.intl.retail.org.infra.mapper.read.IntlRmsPositionReadMapper;
import com.mi.info.intl.retail.user.app.UserService;
import com.mi.info.intl.retail.user.infra.entity.IntlRmsUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.event.EventListener;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Slf4j
@Component
public class RmsApiRequestEventListener {

    private static final Set<String> SUPERVISOR_JOB_IDS = new HashSet<String>() {{
        add("500900002");
        add("100000051");
        add("100000024");
    }};

    @Value("${intl-retail.rocketmq.inspection.topic}")
    private String topic;

    @Autowired
    private InspectionConfig inspectionConfig;

    @Autowired
    private RocketMQProducer rocketMQProducer;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private UserService userService;

    @Autowired
    private IntlRmsPositionReadMapper intlRmsPositionReadMapper;

    @Autowired
    private IntlRmsCountryTimezoneMapper intlRmsCountryTimezoneMapper;

    @Autowired
    private RetailerAppConfigService retailerAppConfigService;

    private List<String> actionPathList = Arrays.asList("new_WorkPlanAction", "new_SubmitData", "new_CreationIMEIInfo",
            TaskActionEnum.STORE_CHECK_SR.getActionName(), TaskActionEnum.SALES_UPLOAD_NO_SALES.getActionName());

    @EventListener
    public void handleRmsTypeApiRequestEvent(RmsApiRequestEvent event) {
        RmsAipRequestInfo rmsAipRequestInfo = event.getRmsAipRequestInfo();
        JwtAuthenticationToken token = event.getToken();
        String positionId = rmsAipRequestInfo.getPositionId();
        if (filter(rmsAipRequestInfo.getPath())) {
            log.warn("path is invalid, rmsAipRequestInfo:{}", rmsAipRequestInfo);
            return;
        }
        adapterType(rmsAipRequestInfo);
        if (rmsAipRequestInfo.getType() == null) {
            log.warn("type is null, skip, rmsAipRequestInfo:{}", rmsAipRequestInfo);
            return;
        }
        TaskActionEnum srTaskActionEnum = TaskActionEnum.fromActionName(rmsAipRequestInfo.getType());
        if (srTaskActionEnum == null) {
            log.warn("type action is invalid, skip, rmsAipRequestInfo: {}", rmsAipRequestInfo);
            return;
        }
        if (token != null && srTaskActionEnum == TaskActionEnum.SALES_UPLOAD_QTY && hasIMEIMenu(
                rmsAipRequestInfo.getAccount(), token)) {
            log.warn("type action is SalesUpload_Qty, but user has IMEI menu, skip, rmsAipRequestInfo: {}",
                    rmsAipRequestInfo);
            return;
        }
        try {
            IntlRmsPosition intlRmsPosition = getRmsPositionByPositionId(positionId);

            if (intlRmsPosition == null) {
                log.error("未找到门店信息, 无法定位门店对应国家，positionId={}, rmsAipRequestInfo:{}", positionId,
                        rmsAipRequestInfo);
                return;
            }

            IntlRmsCountryTimezone intlRmsCountryTimezone = getCountryTimezoneByCountryId(
                    intlRmsPosition.getCountryId());
            if (intlRmsCountryTimezone == null) {
                log.error("未找到门店国家信息, 获取门店国家失败，positionId={}, 忽略当前动作， rmsAipRequestInfo:{}",
                        positionId, rmsAipRequestInfo);
                return;
            }
            if (token != null && srTaskActionEnum == TaskActionEnum.SALES_UPLOAD_QTY && hasIMEIMenu(
                    rmsAipRequestInfo.getAccount(), token) &&
                    !inspectionConfig.getCountryWhiteSalesUploadQtySet()
                            .contains(intlRmsCountryTimezone.getCountryCode())) {
                log.warn("type action is SalesUpload_Qty, but user has IMEI menu, skip, rmsAipRequestInfo: {}",
                        rmsAipRequestInfo);
                return;
            }

            IntlRmsUser rmsUser = userService.getUserInfoDomainName(rmsAipRequestInfo.getAccount());
            rmsAipRequestInfo.setPositionCode(intlRmsPosition.getCode());
            rmsAipRequestInfo.setCountryCode(intlRmsCountryTimezone.getCountryCode());
            rmsAipRequestInfo.setMid(rmsUser.getMiId());
            if (rmsUser.getId() != null && SUPERVISOR_JOB_IDS.contains(String.valueOf(rmsUser.getJobId()))) {
                String msg = objectMapper.writeValueAsString(event.getRmsAipRequestInfo());
                String msgId = rocketMQProducer.sendMessageWithResult(topic, msg);
                log.info("发送MQ成功，account={}, job_id={}, country_name={}, 消息id={}, msg={}",
                        event.getRmsAipRequestInfo().getAccount(), rmsUser.getJobId(), rmsUser.getCountryName(), msgId,
                        msg);
            } else {
                log.debug("用户不满足发送MQ条件，account={}, job_id={}, country_name={}",
                        event.getRmsAipRequestInfo().getAccount(), rmsUser.getJobId(), rmsUser.getCountryName());
            }
        } catch (Exception e) {
            log.error("发送MQ失败: {}", e.getMessage(), e);
        }
    }

    private boolean hasIMEIMenu(String account, JwtAuthenticationToken token) {
        RmsUserBaseDataResponse userBaseInfo = retailerAppConfigService.requestRmsGetUserMenuInfo(account,
                token.getToken().getTokenValue());
        if (userBaseInfo.getMenu() != null && !userBaseInfo.getMenu().isEmpty()) {
            return userBaseInfo.getMenu().stream()
                    .anyMatch(menuInfo -> menuInfo.getScreenName().equals("SalesUploadIMEI"));
        }
        return false;
    }

    private IntlRmsPosition getRmsPositionByPositionId(String storeId) {
        LambdaQueryWrapper<IntlRmsPosition> query = new LambdaQueryWrapper<>();
        query.eq(IntlRmsPosition::getPositionId, storeId);
        return intlRmsPositionReadMapper.selectOne(query);
    }

    private IntlRmsCountryTimezone getCountryTimezoneByCountryId(String countryId) {
        LambdaQueryWrapper<IntlRmsCountryTimezone> query = new LambdaQueryWrapper<>();
        query.eq(IntlRmsCountryTimezone::getCountryId, countryId);
        return intlRmsCountryTimezoneMapper.selectOne(query);
    }
    
    private void adapterType(RmsAipRequestInfo rmsAipRequestInfo) {
        String type = rmsAipRequestInfo.getType();
        String path = rmsAipRequestInfo.getPath();
        if (StringUtils.isBlank(type)) {
            if (path.equals("new_CreationIMEIInfo")) {
                type = "new_CreationIMEIInfo";
            }
        }
        rmsAipRequestInfo.setType(type);
    }

    private boolean filter(String path) {
        return !actionPathList.contains(path);
    }
}
