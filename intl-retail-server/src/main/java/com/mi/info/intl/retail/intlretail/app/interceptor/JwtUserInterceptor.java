package com.mi.info.intl.retail.intlretail.app.interceptor;

import java.util.Objects;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.jetbrains.annotations.NotNull;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import com.mi.info.intl.retail.api.fieldforce.user.IntlRmsUserApiService;
import com.mi.info.intl.retail.constant.CommonConstant;
import com.mi.info.intl.retail.dto.IntlRmsUserDTO;
import com.mi.info.intl.retail.model.UserInfo;

import lombok.extern.slf4j.Slf4j;

/**
 * 从请求中解析用户信息，并绑定到请求上下文中。
 *
 * <AUTHOR>
 * @date 2025/7/31 11:35
 */
@Slf4j
@Component
public class JwtUserInterceptor implements HandlerInterceptor {

    @Resource
    private IntlRmsUserApiService intlRmsUserApiService;

    @Override
    public boolean preHandle(@NotNull HttpServletRequest request, @NotNull HttpServletResponse response,
                             @NotNull Object handler) {
        JwtAuthenticationToken token = (JwtAuthenticationToken) SecurityContextHolder.getContext().getAuthentication();
        if (Objects.nonNull(token)) {
            // 目前看这个存的是邮箱，可查域账号intl_rms_user.domain_name
            String uniqueName = String.valueOf(token.getTokenAttributes().get("unique_name"));
            log.info("Get jwt token user for: {}", uniqueName);
            IntlRmsUserDTO rmsUser = intlRmsUserApiService.getRmsUserByUniqueName(uniqueName);
            if (Objects.nonNull(rmsUser)) {
                UserInfo userInfo =
                    UserInfo.builder()
                        .userId(String.valueOf(rmsUser.getMiId()))
                        .userName(rmsUser.getEnglishName())
                        .email(rmsUser.getEmail())
                        .miID(rmsUser.getMiId())
                        .language(rmsUser.getLanguageCode())
                        .build();
                request.setAttribute(CommonConstant.JwtAuth.JWT_TOKEN_USER, userInfo);
            }
        }
        return true;
    }
}
