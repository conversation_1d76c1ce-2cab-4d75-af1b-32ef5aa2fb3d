env: prod
miwork:
  alarm:
    groupId:
      p0: oc_e3e4f0fb3db733a63a8a94037918b099
      p1: oc_39bbf1fd83c670511b59f7b87f0e7049
      p2: oc_57043ca4e6738aa9204bd99e016ee72a
# 加密配置
itsm:
  key@kc-sid: india-sales.g
  key: GCCZeisjOcc_nvkwwRnHuu2qLPzN5ynrFbAKAVgne1uV0xgSpEe5DzZTSc-1zQ7uOBO32ygBGBD26c_1kpZIN4rQyf2sy55VGBQJ6QlClK5BmkL7314psZ5Tid4HTgA
  aesGcmKey@kc-sid: india-sales.g
  aesGcmKey: GDCIWsWWgFDk+xckCntXwAcmjyefuwliEuFZd8Qc4IZU1ZSdvxv2CxX7ZmIRUqLFHp4YElFV+XRrhkZ2tbpaAc0DN8mGARgQNIIjAcznRkKL3Nq63IfrsRgUzq9y799ECIuKazki5Iw6EDdl+EcA
feishu:
  appId: cli_a60d6cea8478d063
  appSecret@kc-sid: india-sales.g
  appSecret: GDAQ8DY/zwslD/1o2SgUup2tzmTxBAeeg4Rvun9vf+1V973FKskgdGIXb/+/Rkx8BQAYEhahcRTj40BUpNnt02bh+WJ2ARgQ63QGTEE4TDqWFJrmdH+vhxgU1CsZYrjxaurzAxIBWFKL5MvZceMA
  getGroupListUrl: https://open.feishu.cn/open-apis/im/v1/chats?page_token=
  getUnionidsUrl: https://open.feishu.cn/open-apis/contact/v3/users/batch_get_id
  sendMessageGroupUrl: https://open.feishu.cn/open-apis/message/v4/send/
  batchSendMessageUrl: https://open.feishu.cn/open-apis/message/v4/batch_send/
  tokenUrl: https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal
  messageUrl: https://open.feishu.cn/open-apis/im/v1/messages

intelTemple-url:
  planUploadUrl: https://qhdx-intl-retail.alsgp0.mi-fds.com/qhdx-intl-retail/新LDU计划维护批量上传模版.xlsx?GalaxyAccessKeyId=5151729087601&Expires=9223372036854775807&Signature=Vp8TqXTJ%2Btt0FKOoDKnCgf2KnxU%3D
  planStopUrl: https://qhdx-intl-retail.alsgp0.mi-fds.com/qhdx-intl-retail/新LDU计划批量停用模版.xlsx?GalaxyAccessKeyId=5151729087601&Expires=9223372036854775807&Signature=GdmHwryYdZTSo1a1AwLbvYXUyz0%3D
  targetUploadUrl: https://qhdx-intl-retail.alsgp0.mi-fds.com/qhdx-intl-retail/新LDU目标维护批量上传模版.xlsx?GalaxyAccessKeyId=5151729087601&Expires=9223372036854775807&Signature=ZW9gWwm%2Bt8KgtYzcx4uLVlfQ%2FA4%3D
  newProductTarget: https://intl-retail.alsgp0.mi-fds.com/intl-retail/pc_new_product_inspection/newProductTargetTemplate.xlsx?GalaxyAccessKeyId=5151729087601&Expires=9223372036854775807&Signature=fSsN9LN4bJU34r5qwEv76wqK0bM%3D
  newAssignStore: https://intl-retail.alsgp0.mi-fds.com/intl-retail/pc_new_product_inspection/storeCodeTemplate1.xlsx?GalaxyAccessKeyId=5151729087601&Expires=9223372036854775807&Signature=mEpzNs7Dz9f7LSZUHkE%2F0UaUPOU%3D

oapi:
  app-id@kc-sid: india-sales.g
  app-id: GCB2mig9Rcx/B1/++xRXwJp1zMsUaCRJ+FvjWDAhFLcknhgSC2ysECIgRc+uSBYxz0zsyroBGBDOPse+NwNGkKaXqu9VtzH2GBRVI2D+WwG6RtJHcmABHGYoAgZYkgA=
  app-secret@kc-sid: india-sales.g
  app-secret: GDCNgYNjNkqtAMVNTx3cwXYT/KxSE1uoofWnAyzyBthWycb5R+VSRhhstrF1pQ5/lJgYEgtsrBAiIEXPrkgWMc9M7Mq6ARgQ7MGWtAvSSP2qOREbkksIBxgUwfQWXcITI3pKf1DMmn85bJyNo/MA
  missing-rule-alert-template: '{"elements":[{"tag":"column_set","horizontal_spacing":"8px","horizontal_align":"left","columns":[{"tag":"column","width":"weighted","elements":[{"tag":"markdown","content":"{content}","text_align":"left","text_size":"normal_v2"}]}]}],"header":{"title":{"tag":"plain_text","content":"{title}"},"template":"red"}}'
  url: https://open.feishu.cn

proretail:
  project:
    id: 11
    name: intl-retail

intl-retail:
  global-data-access:
    http: true


job:
  admin:
    addresses: http://eu-job.be.mi.com/
  accessToken@kc-sid: india-sales.g
  accessToken: GCA9W9dpakcmYejj9GZnVBre8D+CQD/zlRm3TA9m25am8BgSd+5KtsClQp+FFfGZfpTbJHkBGBDVZy86g61K46+UlNgdwtvTGBSOtg4EmOVLhkeplENfFlu//L1H8QA=
  executor:
    appname: intl-retail
    ip: ""
    port: 9991
    logpath: /home/<USER>/log/jobhandler
    logretentiondays: 30

oaucf:
  auth:
    appId: W58UGHBpHxMU
    appSecret@kc-sid: india-sales.g
    appSecret: GDADRhJbszefta7/BFLjFQ43I3QAxXYwSS+EXAIR1ppbwSvBHBEndS/6LeOOs+bSiyAYEk/KJFnFXkVEtdgYaIwtUfMZARgQ/3Ar5zAZQn+uCe0sScbvTBgUy7kPSSUSjB+F4PKxwzy2x/xx/ckA
  bpm:
    enabled: true
    url: https://bpm.infra.mioffice.cn/runtime   # api的url
    connectTimeout: 10          # 连接超时时间 单位秒
    readTimeout: 60