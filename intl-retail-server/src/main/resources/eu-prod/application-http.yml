maindata:
  x5:
    appId: "xmstore-maindata"
    appSecret: "58ab11faf02a68add74ac8f90d2ac20f"
    url: "http://sgp-xmmionegw.test.mi.com/mtop/maindata/position/editPositionInfo"
    method: "editPositionInfo_x5"

# 外部http服务接口参数配置
service-config:
  imei:
    url: http://cn-gw.zg.mi.com/imei/sales_middle/api/x5
    appId: x5_rms
    appKey@kc-sid: india-sales.g
    appKey: GDBWJxZelzxkT/QJMi8xJDLE77oWaavs0xHf1YNi2JCQgZnJuWrktSv0Kp6nrqERUnwYEk5d6cZJn0Z0qKfUEBV0zzKhARgQBpaSCjWuQEi2EIuStPnGcRgUKVDpLkippiSWPeAFYWfMo8cPQIEA
    pageSize: 200
  upc:
    url: https://cn-gw.zg.mi.com/wms/upc/api/x5
    appId: xm_market
    appKey@kc-sid: india-sales.g
    appKey: GDALmUG62YFDIyL/v4dOhTrUaN1gECgQ6V4RVPcBvNWSctymRqFglMveRTCR+7q4u6sYEk5d6cZJn0Z0qKfUEBV0zzKhARgQsPvzWD1jTpqwcU7YTvLuLBgUa0HfNrt3qKA/widL1RemQ2AGOrQA
  imei-active:
    url: https://ams-api.buy.mi.com/imei/http/active/api/x5
    appId: x5_rms_miretail_manager_system
    appKey@kc-sid: india-sales.g
    appKey: GDAm3CxvYCQCepk+csSsK57VZkYXl3DlZk7EixkWNEEetJN8nOYewtV2fH7hR9bhVIIYEpopBHWT4EFytFFjpRW+TWEYARgQfXGajhyKR6msp9qB2nlVxRgUJYoXRZiw076tTQJoTdT6rYiJ2IQA

http:
  maindata:
    baseUrl: https://sgp-xmmionegw-inner.be.mi.com/mtop/maindata/
    StoreProvider:
      selectStoreByOrgIds: maindata/selectStoreByOrgIds
      editStoreBeta: maindata/store/editStoreBeta
    StorePositionProvider:
      getStorePositionDetail: storems/international/channel/getStorePositionDetail
    InternationalChannelProvider:
      getInternationalChannelStoreDetail: storems/international/channel/getInternationalChannelStoreDetail