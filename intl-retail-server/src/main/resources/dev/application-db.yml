spring:
  datasource:
    dynamic:
      primary: xmstorebe #设置默认的数据源或者数据源组,默认值即为xmstorebe
      strict: true #严格匹配数据源,默认false. true未匹配到指定数据源时抛异常,false使用默认数据源
      druid:
        initial-size: 1
        max-active: 200
        max-wait: 600000
        min-idle: 1
        filters: mergeStat
        use-global-data-source-stat: true
        pool-prepared-statements: true
        max-open-prepared-statements: 20
        validation-query: select 'x'
        min-evictable-idle-time-millis: 300000
        time-between-eviction-runs-millis: 60000
        test-while-idle: true
        test-on-borrow: true
        test-on-return: false
        stat:
          slow-sql-millis: 300
          log-slow-sql: true
          merge-sql: true
      datasource:
        xmstorebe:
          url: *********************************************************************************************************************************
          username: store_be_w
          password@kc-sid: india-sales.g
          password: GDBjzLkbQ2nEyO76JuZhGLq3EJFRDd9HFkWOBV6Mc5KvRAfPfobH7bwcpbzQloWdDFgYEr4Ne9XjqEivlyry3yS2XJ_V_xgQZTAt7ASOStaRTWHLZWpt1RgUzjorOsJqrTQ5pcIq2K9fONef75UA
          # 使用druid数据源
          type: com.alibaba.druid.pool.DruidDataSource
          driverClassName: com.mysql.cj.jdbc.Driver
        xmstorebes:
          url: *********************************************************************************************************************************
          username: store_be_w
          password@kc-sid: india-sales.g
          password: GDBjzLkbQ2nEyO76JuZhGLq3EJFRDd9HFkWOBV6Mc5KvRAfPfobH7bwcpbzQloWdDFgYEr4Ne9XjqEivlyry3yS2XJ_V_xgQZTAt7ASOStaRTWHLZWpt1RgUzjorOsJqrTQ5pcIq2K9fONef75UA
          # 使用druid数据源
          type: com.alibaba.druid.pool.DruidDataSource
          driverClassName: com.mysql.cj.jdbc.Driver
  cache:
    enabled: true
  redis:
    database: 0
    host: ares.tj-info-intl-retail-common-cache.cache.srv
    port: 22127
    password@kc-sid: india-sales.g
    password: "GDDOv08pmElZJV5fnVSNTG+dXi/Ywc6dFOYrpwQ40ZdvywtuQ0F4JeBcFGelMWghJLEYEqq4XqeCrELdunl8K9/wMg7R/xgQy8WD/H93SzKw87Kq9enW+BgUuiMZ78R3wJqHfSp0YpAWKOfSG48A"
    timeout: 5000ms
  mybatis:
    mapper-locations: classpath:**.*mapper/*.xml
