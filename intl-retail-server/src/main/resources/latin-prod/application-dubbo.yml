nacos:
  namespace: public
  address: nacos://nacos.systech.b2c.srv:80
  config:
    address: nacos.systech.b2c.srv:80
init:
  # 这个不能动，否则不能上传，app的group已经预设好了
  group: release
  app:
    name: intl-retail-latin
#dubbo provider  #dubbo consumer
center:
  dubbo:
    group: online
dubbo-group:
  provider:
    intl-retail: sg_online
  consumer:
    center: online
    channelBuild: sg_online
    maindata: sg_online

#dubbo provider
cache:
  dubbo:
    group: sg_online_cache
push:
  dubbo:
    group: sg_online_push
nr:
  dubbo:
    group: sg_online
#dubbo consumer
iib:
  dubbo:
    group: sgp_online_iib
proretailbi:
  dubbo:
    group: sg_online
xmstore:
  dubbo:
    group: sg-online
college:
  dubbo:
    group: sg_online
eiam:
  dubbo:
    group: sg_online
#调用建店系统
channelBuild:
  dubbo:
    group: sg_online
material:
  dubbo:
    group: sg_online
#被调用
store:
  dubbo:
    group: sg_online

i18n:
  area:
    app:
      env: sg_online
oapi:
  dubbo:
    group: sg_online

organization:
  scene: new_retail
  # manageChannelList参数：1-新零售直营，2-新零售授权，27-渠道零售
  organMap:
    256:
      positionId: 256
      organCode: GLOBAL
      positionName: Headquarters strategic operation
      manageChannelList:
        - 27
    242:
      positionId: 242
      organCode: null
      positionName: National Retail Manager
      manageChannelList:
        - 27
    255:
      positionId: 255
      organCode: GLOBAL
      positionName: Headquarters Policy Management
      manageChannelList:
        - 27
  dubbo-group: sg_online
file:
  dubbo:
    group: sg_online

training:
  dubbo:
    group: sg_online

dubbo:
  nr-upload-center:
    projectId: 892
    appId: xm-yp-upc-0252
    appKey: 22092fb7edc433319d459f21c16815ad

maindata:
  dubbo:
    group: sg_online
