nacos:
  namespace: public
  address: nacos://sgp.nacos.test.b2c.srv:80
  config:
    address: sgp.nacos.test.b2c.srv:80
init:
  # 这个不能动，否则不能上传，app的group已经预设好了
  group: ${namespace:test}
  app:
    name: intl-retail

dubbo-group:
  provider:
    intl-retail: ${namespace:sg_staging}
  consumer:
    center: staging
    channelBuild: sg_staging
    maindata: sg_staging

xmstore:
  dubbo:
    group: sg_staging

iib:
  dubbo:
    group: sg_staging
maindata:
  dubbo:
    group: sg_staging
push:
  dubbo:
    group: ${namespace:sg_staging}

copilot:
  dubbo:
    group: staging
center:
  dubbo:
    group: staging
proretailbi:
  dubbo:
    group: sg_staging
file:
  dubbo:
    group: sg_staging

i18n:
  area:
    app:
      env: sg_staging

cache:
  dubbo:
    group: ${namespace:sg_staging_cache}
eiam:
  dubbo:
    group: sg_staging

college:
  dubbo:
    group: sg_staging

inspection:
  dubbo:
    group: ${namespace:sg_staging_inspection}

#被调用
store:
  dubbo:
    group: ${namespace:sg_staging}

training:
  dubbo:
    group: ${namespace:sg_staging}

#调用建店系统
channelBuild:
  dubbo:
    group: sg_staging
material:
  dubbo:
    group: ${namespace:sg_staging}

organization:
  scene: new_retail
  # manageChannelList参数：1-新零售直营，2-新零售授权，27-渠道零售
  organMap:
    256:
      positionId: 256
      organCode: GLOBAL
      positionName: Headquarters strategic operation
      manageChannelList:
        - 27
    242:
      positionId: 242
      organCode: null
      positionName: National Retail Manager
      manageChannelList:
        - 27
    255:
      positionId: 255
      organCode: GLOBAL
      positionName: Headquarters Policy Management
      manageChannelList:
        - 27
  dubbo-group: sg_staging
oapi:
  dubbo:
    group: ${namespace:sg_staging}
nr:
  dubbo:
    group: sg_staging
retail:
  dubbo:
    group: ${namespace:sg_staging}
dubbo:
  nr-upload-center:
    projectId: 892
    appId: xm-yp-upc-0892
    appKey: 585e247677365126ec06d7591be4e9e6