env: prod
miwork:
  alarm:
    groupId:
      p0: oc_e3e4f0fb3db733a63a8a94037918b099
      p1: oc_39bbf1fd83c670511b59f7b87f0e7049
      p2: oc_57043ca4e6738aa9204bd99e016ee72a
# 加密配置
itsm:
  key@kc-sid: india-sales.g
  key: GCDXn2luoETagCTfAZPMTrMlOhEQCPeKDugTc-Zrx60SKRgSyaaFHeV0QkOBMmp9EOlpJ70BGBBKc0w93WtDjZKhNy23JP-tGBTlw3NIgfu_svIfs0czxr_hud_N5QA
  aesGcmKey@kc-sid: india-sales.g
  aesGcmKey: GDAx0WsBNP+GSUGt870RCvtYf9fTRRBv4TT6LL9uBJpVopHrKSJSZIqNdwYO+huezbEYEpdfiK36xksgguD/UB+S3R/nARgQhQDJmce1SLGBba9eNWww3hgU6k3GJi1E3Qxoo0QChpedOf7xJokA
feishu:
  appId: cli_a60d6cea8478d063
  appSecret@kc-sid: india-sales.g
  appSecret: GDBmkWjcFTkCwft3pt4R0BIQn779zrY0zSM2j6+MRViF5wkotv93t7BhS2+64he2OMoYEpopBHWT4EFytFFjpRW+TWEYARgQaRrKSAQNRBSOuBbLyih7dRgU7mOqh6TWezMocE25FBjf2i5mcqMA
  getGroupListUrl: https://open.feishu.cn/open-apis/im/v1/chats?page_token=
  getUnionidsUrl: https://open.feishu.cn/open-apis/contact/v3/users/batch_get_id
  sendMessageGroupUrl: https://open.feishu.cn/open-apis/message/v4/send/
  batchSendMessageUrl: https://open.feishu.cn/open-apis/message/v4/batch_send/
  tokenUrl: https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal
  messageUrl: https://open.feishu.cn/open-apis/im/v1/messages

intelTemple-url:
  planUploadUrl: https://qhdx-intl-retail.alsgp0.mi-fds.com/qhdx-intl-retail/新LDU计划维护批量上传模版.xlsx?GalaxyAccessKeyId=5151729087601&Expires=9223372036854775807&Signature=Vp8TqXTJ%2Btt0FKOoDKnCgf2KnxU%3D
  planStopUrl: https://qhdx-intl-retail.alsgp0.mi-fds.com/qhdx-intl-retail/新LDU计划批量停用模版.xlsx?GalaxyAccessKeyId=5151729087601&Expires=9223372036854775807&Signature=GdmHwryYdZTSo1a1AwLbvYXUyz0%3D
  targetUploadUrl: https://qhdx-intl-retail.alsgp0.mi-fds.com/qhdx-intl-retail/新LDU目标维护批量上传模版.xlsx?GalaxyAccessKeyId=5151729087601&Expires=9223372036854775807&Signature=ZW9gWwm%2Bt8KgtYzcx4uLVlfQ%2FA4%3D
  newProductTarget: https://intl-retail.alsgp0.mi-fds.com/intl-retail/pc_new_product_inspection/newProductTargetTemplate.xlsx?GalaxyAccessKeyId=5151729087601&Expires=9223372036854775807&Signature=fSsN9LN4bJU34r5qwEv76wqK0bM%3D
  newAssignStore: https://intl-retail.alsgp0.mi-fds.com/intl-retail/pc_new_product_inspection/storeCodeTemplate1.xlsx?GalaxyAccessKeyId=5151729087601&Expires=9223372036854775807&Signature=mEpzNs7Dz9f7LSZUHkE%2F0UaUPOU%3D

proretail:
  project:
    id: 11
    name: intl-retail

job:
  admin:
    addresses: https://sg-job.be.mi.com/
  accessToken@kc-sid: india-sales.g
  accessToken: GCCv85rf7Xr0FFpETLFyHpp3mvRkOCp2BncKm3+dV9QMABgS20wtd2HeQsyVeyhXGFa4EUQBGBAUadQ2O5tEm7GqIi1d0xseGBTh3QjqCaL20n49FkxIsNMfZzgjWAA=
  executor:
    appname: intl-retail
    ip: ""
    port: 9991
    logpath: /home/<USER>/log/jobhandler
    logretentiondays: 30
oapi:
  app-id@kc-sid: india-sales.g
  # cli_a60d6cea8478d063
  app-id: GCAQRmDN/ehe5xY03SLB4L5GB9jLDDYtNyyPJAjlqVTbIRgSTHBZflT3QfCKCCCQ/Z58HdsBGBB7Xp4k4JxDh7sDySgGzU3RGBQdqjywaxQjHrQd3EF5vWOmtqIayQA=
  app-secret@kc-sid: india-sales.g
  # BUIyDUcTVC9Qj69jnoM5ah7XvYzdFgel
  app-secret: GDBxNSTVpo12UHAKH52zhYE4VmjiBg9EPVbOVIRY0jJS7Q6zMGGwee6/ubQFr43hAzwYEqWX5BGHx0x6mK9jE0S1cm8GARgQG1w5fB+9S7ikfz4ebNQAYhgU/zvjhktTtyYL7E+E2srY0gjjXtgA
  missing-rule-alert-template: '{"elements":[{"tag":"column_set","horizontal_spacing":"8px","horizontal_align":"left","columns":[{"tag":"column","width":"weighted","elements":[{"tag":"markdown","content":"{content}","text_align":"left","text_size":"normal_v2"}]}]}],"header":{"title":{"tag":"plain_text","content":"{title}"},"template":"red"}}'
  url: https://open.feishu.cn

# 是否启用扩展全球数据访问配置
intl-retail:
  global-data-access:
    enabled: true  # 新加坡生产环境启用全球数据访问

oaucf:
  auth:
    appId: W58UGHBpHxMU
    appSecret@kc-sid: india-sales.g
    appSecret: GDADRhJbszefta7/BFLjFQ43I3QAxXYwSS+EXAIR1ppbwSvBHBEndS/6LeOOs+bSiyAYEk/KJFnFXkVEtdgYaIwtUfMZARgQ/3Ar5zAZQn+uCe0sScbvTBgUy7kPSSUSjB+F4PKxwzy2x/xx/ckA
  bpm:
    enabled: true
    url: https://bpm.infra.mioffice.cn/runtime   # api的url
    connectTimeout: 10          # 连接超时时间 单位秒
    readTimeout: 60