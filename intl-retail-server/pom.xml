<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>intl-retail</artifactId>
        <groupId>com.mi.info.intl.retail</groupId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>intl-retail-server</artifactId>

    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>

    <dependencies>
        <!-- intl-retail sub module start -->
        <dependency>
            <groupId>com.mi.info.intl.retail</groupId>
            <artifactId>intl-retail-api</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>neptune-client-starter</artifactId>
                    <groupId>com.mi.xms</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.mi.info.intl.retail</groupId>
            <artifactId>intl-retail-core</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-reload4j</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.mi.info.intl.retail</groupId>
            <artifactId>intl-retail-domain</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mi.info.intl.retail</groupId>
            <artifactId>intl-retail-infra</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>hessian-lite</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <artifactId>market-api</artifactId>
            <groupId>market</groupId>
        </dependency>

        <dependency>
            <groupId>com.mi.info.intl.retail</groupId>
            <artifactId>intl-retail-app</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>dubbo</artifactId>
                    <groupId>org.apache.dubbo</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>nacos-client</artifactId>
                    <groupId>com.alibaba.nacos</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.mi.info.intl.retail</groupId>
            <artifactId>intl-retail-fieldforce</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mi.info.intl.retail</groupId>
            <artifactId>intl-retail-sales</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mi.info.intl.retail</groupId>
            <artifactId>intl-retail-front</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mi.info.intl.retail</groupId>
            <artifactId>intl-retail-cooperation</artifactId>
        </dependency>
        <!-- intl-retail sub module end -->

        <dependency>
            <groupId>com.alibaba.nacos</groupId>
            <artifactId>nacos-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>log4j-api</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j-to-slf4j</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis-spring</artifactId>
        </dependency>


        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mi.info.comb</groupId>
            <artifactId>x5requestmapping-comb-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.azure.spring</groupId>
            <artifactId>spring-cloud-azure-starter-active-directory</artifactId>
        </dependency>
        <!-- spring boot starter dependencies. -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-oauth2-resource-server</artifactId>
        </dependency>
        <!--cursor 没有依赖 启动失败-->
        <dependency>
            <groupId>org.apache.geronimo.specs</groupId>
            <artifactId>geronimo-jms_2.0_spec</artifactId>
            <!-- 使用明确的版本号而非LATEST -->
            <version>1.0-alpha-2</version>
        </dependency>
        <dependency>
            <groupId>com.xiaomi.cnzone</groupId>
            <artifactId>brain-platform-api</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>nr-eiam-api</artifactId>
                    <groupId>com.xiaomi.nr</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.mi.oa.infra.mibpm</groupId>
            <artifactId>mibpm-bpm-spring-boot-starter</artifactId>
        </dependency>

        <!-- miapi -->
        <dependency>
            <groupId>com.xiaomi.mone</groupId>
            <artifactId>http-docs-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xiaomi.mone</groupId>
            <artifactId>miapi-doc-annos</artifactId>
        </dependency>
    </dependencies>

    <build>
        <finalName>intl-retail</finalName>
        <!--        <resources>-->
        <!--            <resource>-->
        <!--                <directory>src/main/resources</directory>-->
        <!--                <filtering>true</filtering>-->
        <!--                <includes>-->
        <!--                    <include>application-db.yml</include>-->
        <!--                    <include>${package.env}/*.yml</include>-->
        <!--                    <include>logback*.xml</include>-->
        <!--                    <include>mappers/*/*.xml</include>-->
        <!--                    <include>errorcode/*/*.properties</include>-->
        <!--                </includes>-->
        <!--            </resource>-->
        <!--        </resources>-->
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.6.7</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <classifier>exec</classifier>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.mi.xms</groupId>
                <artifactId>neptune-maven-plugin</artifactId>
                <version>1.0-SNAPSHOT</version>
                <executions>
                    <execution>
                        <phase>compile</phase>
                        <goals>
                            <goal>translate</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <appId>605cfb858b254dd89f4d5d49b4ddcb33</appId>
                    <env>${neptune_env}</env>
                </configuration>
            </plugin>
            <!-- Maven Surefire 插件配置 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.22.2</version>
                <configuration>
                    <!-- 排除集成测试类 -->
                    <excludes>
                        <exclude>**/StoreGradeServiceIntegrationTest.java</exclude>
                    </excludes>
                    <!-- 包含单元测试 -->
                    <includes>
                        <include>**/*Test.java</include>
                    </includes>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <!-- 集成测试 Profile -->
        <profile>
            <id>integration-test</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <version>2.22.2</version>
                        <configuration>
                            <!-- 只运行集成测试 -->
                            <includes>
                                <include>**/StoreGradeServiceIntegrationTest.java</include>
                            </includes>
                            <excludes>
                                <exclude>**/*Test.java</exclude>
                            </excludes>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <!-- 所有测试 Profile -->
        <profile>
            <id>all-tests</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <version>2.22.2</version>
                        <configuration>
                            <!-- 运行所有测试 -->
                            <includes>
                                <include>**/*Test.java</include>
                            </includes>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

</project>

