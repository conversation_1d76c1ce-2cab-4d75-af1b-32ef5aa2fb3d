package com.mi.info.intl.retail.management.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mi.info.intl.retail.exception.BizException;
import com.mi.info.intl.retail.exception.ErrorCodes;
import com.mi.info.intl.retail.intlretail.service.api.bpm.BpmService;
import com.mi.info.intl.retail.intlretail.service.api.bpm.dto.ApprovalTaskListResp;
import com.mi.info.intl.retail.intlretail.service.api.bpm.dto.BpmUser;
import com.mi.info.intl.retail.intlretail.service.api.fds.FdsService;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.CommonApproveHistoryReq;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.CommonApproveHistoryResp;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.FileTemplateReq;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.RetailerQueryReq;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.RetailerQueryResp;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.StoreGradeExportReq;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.StoreGradeReq;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.StoreGradeResp;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.StoreGradeRuleFormDataReq;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.StoreGradeRuleReq;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.SubmitResp;
import com.mi.info.intl.retail.management.config.StoreGradeRuleConfig;
import com.mi.info.intl.retail.management.entity.CommonApproveLog;
import com.mi.info.intl.retail.management.entity.StoreGradeRule;
import com.mi.info.intl.retail.management.mapper.CommonApproveLogMapper;
import com.mi.info.intl.retail.management.mapper.StoreGradeRelationMapper;
import com.mi.info.intl.retail.management.mapper.StoreGradeRuleMapper;
import com.mi.info.intl.retail.management.rpc.MainDataRpc;
import com.mi.info.intl.retail.management.service.enums.ChannelTypeEnum;
import com.mi.info.intl.retail.management.service.enums.FileTemplateEnum;
import com.mi.info.intl.retail.management.service.enums.RuleStatusEnum;
import com.mi.info.intl.retail.management.service.enums.StoreGradeEnum;
import com.mi.info.intl.retail.management.service.enums.TieringModificationMethodEnum;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.org.infra.entity.IntlRmsCountryTimezone;
import com.mi.info.intl.retail.org.infra.entity.IntlRmsStore;
import com.mi.info.intl.retail.org.infra.mapper.IntlRmsCountryTimezoneMapper;
import com.mi.info.intl.retail.org.infra.mapper.IntlRmsStoreMapper;
import com.mi.info.intl.retail.retailer.entity.IntlRmsRetailer;
import com.mi.info.intl.retail.retailer.mapper.IntlRmsRetailerMapper;
import com.mi.info.intl.retail.utils.RpcContextUtil;
import com.xiaomi.cnzone.storeapi.api.channelbuild.common.ChannelCommonProvider;
import com.xiaomi.cnzone.storeapi.model.channelbuild.common.req.CountryRoleAdminReq;
import com.xiaomi.cnzone.storeapi.model.channelbuild.common.resp.CountryRoleAdminResp;
import com.xiaomi.nr.eiam.admin.dto.provider.user.SearchUserSensitiveInfoRequest;
import com.xiaomi.nr.eiam.admin.provider.UserAdminProvider;
import com.xiaomi.nr.eiam.admin.vo.provider.user.UserSensitiveInfoResp;
import com.xiaomi.nr.eiam.api.dto.userinfo.GetParentOrganPositionUserReq;
import com.xiaomi.nr.eiam.api.dto.userinfo.UserPosition;
import com.xiaomi.nr.eiam.api.provider.UserProvider;
import com.xiaomi.nr.job.admin.dto.TriggerJobRequestDTO;
import com.xiaomi.nr.job.admin.service.NrJobService;
import com.xiaomi.youpin.infra.rpc.Result;
import static org.junit.Assert.assertNull;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyMap;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.transaction.TransactionStatus;

import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * StoreGradeServiceRuleImpl 单元测试
 *
 * <AUTHOR>
 * @date 2024/12/19
 */
@ExtendWith(MockitoExtension.class)
class StoreGradeServiceImplRuleTest {
    @InjectMocks
    private StoreGradeRuleServiceImpl storeGradeRuleService;

    @Mock
    private BpmService bpmService;

    @Mock
    private UserProvider userProvider;

    @Mock
    private UserAdminProvider userAdminProvider;

    @Mock
    private ChannelCommonProvider channelCommonProvider;

    @Mock
    private StoreGradeRuleConfig storeGradeRuleConfig;

    @Mock
    private CommonApproveLogMapper commonApproveLogMapper;

    @Mock
    private IntlRmsCountryTimezoneMapper intlRmsCountryTimezoneMapper;

    @Mock
    private IntlRmsRetailerMapper intlRmsRetailerMapper;

    @Mock
    private IntlRmsStoreMapper intlRmsStoreMapper;

    @Mock
    private NrJobService nrJobService;

    private RetailerQueryReq request;
    private IntlRmsCountryTimezone countryTimezone;
    private List<IntlRmsRetailer> retailers;
    private IntlRmsRetailer retailer1;
    private IntlRmsRetailer retailer2;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);

        // 初始化测试数据
        request = new RetailerQueryReq();

        countryTimezone = new IntlRmsCountryTimezone();
        countryTimezone.setCountryId("CN");

        retailer1 = new IntlRmsRetailer();
        retailer1.setName("retailer1");
        retailer1.setCrmCode("crm1");
        retailer1.setRetailerName("Retailer One");
        retailer1.setCountryId("CN");
        retailer1.setCountryName("China");
        retailer1.setCountryCode("CN");
        retailer1.setRetailerChannelType(1);

        retailer2 = new IntlRmsRetailer();
        retailer2.setName("retailer2");
        retailer2.setCrmCode("crm2");
        retailer2.setRetailerName("Retailer Two");
        retailer2.setCountryId("CN");
        retailer2.setCountryName("China");
        retailer2.setCountryCode("CN");
        retailer2.setRetailerChannelType(2);

        retailers = new ArrayList<>();
        retailers.add(retailer1);
        retailers.add(retailer2);

        // 设置 projectId
        ReflectionTestUtils.setField(storeGradeRuleService, "projectId", 11L);
    }

    /**
     * 测试用例：req 为 null，应抛出 BizException
     */
    @Test
    void testSubmit_WhenReqIsNull_ShouldThrowException() {
        try (MockedStatic<RpcContextUtil> rpcContextUtilMock = mockStatic(RpcContextUtil.class)) {
            rpcContextUtilMock.when(RpcContextUtil::getCurrentEmail).thenReturn("<EMAIL>");

            assertThrows(RuntimeException.class, () -> storeGradeRuleService.submit(null));
        }
    }

    /**
     * 测试用例：req.id 为 null，应抛出 BizException
     */
    @Test
    void testSubmit_WhenIdIsNull_ShouldThrowException() {
        try (MockedStatic<RpcContextUtil> rpcContextUtilMock = mockStatic(RpcContextUtil.class)) {
            rpcContextUtilMock.when(RpcContextUtil::getCurrentEmail).thenReturn("<EMAIL>");

            StoreGradeRuleReq req = new StoreGradeRuleReq(); req.setCountryCode("CN");

            assertThrows(RuntimeException.class, () -> storeGradeRuleService.submit(req));
        }
    }

    /**
     * 测试用例：req.countryCode 为空，应抛出 BizException
     */
    @Test
    void testSubmit_WhenCountryCodeIsEmpty_ShouldThrowException() {
        try (MockedStatic<RpcContextUtil> rpcContextUtilMock = mockStatic(RpcContextUtil.class)) {
            rpcContextUtilMock.when(RpcContextUtil::getCurrentEmail).thenReturn("<EMAIL>");

            StoreGradeRuleReq req = new StoreGradeRuleReq(); req.setId(1L);

            assertThrows(RuntimeException.class, () -> storeGradeRuleService.submit(req));
        }
    }

    /**
     * 测试用例：已有流程提交（flowInstId 不为空）
     */
    @Test
    void testSubmitss_WhenFlowInstIdIsNotNull_ShouldSubmitProcess() {
        try (MockedStatic<RpcContextUtil> rpcContextUtilMock = mockStatic(RpcContextUtil.class)) {
            rpcContextUtilMock.when(RpcContextUtil::getCurrentEmail).thenReturn("<EMAIL>");

            // Mock 用户岗位信息
            List<UserPosition> userPositions = new ArrayList<>(); UserPosition userPosition = new UserPosition();
            userPosition.setMiId(1L); userPositions.add(userPosition);
            Result<List<UserPosition>> userPositionResult = Result.success(userPositions);
            when(userProvider.getParentOrganPositionUser(any(GetParentOrganPositionUserReq.class))).thenReturn(
                    userPositionResult);

            // Mock 用户敏感信息
            List<UserSensitiveInfoResp> userSensitiveInfos = new ArrayList<>();
            UserSensitiveInfoResp userInfo = new UserSensitiveInfoResp(); userInfo.setMiId(1L);
            userInfo.setEmail("<EMAIL>"); userSensitiveInfos.add(userInfo);
            Result<List<UserSensitiveInfoResp>> userInfoResult = Result.success(userSensitiveInfos);
            when(userAdminProvider.searchUserSensitiveInfo(any(SearchUserSensitiveInfoRequest.class))).thenReturn(
                    userInfoResult);

            // Mock 总部审批人
            List<CountryRoleAdminResp> countryRoleAdmins = new ArrayList<>();
            CountryRoleAdminResp adminResp = new CountryRoleAdminResp(); adminResp.setAdmin("<EMAIL>");
            countryRoleAdmins.add(adminResp);
            Result<List<CountryRoleAdminResp>> countryRoleResult = Result.success(countryRoleAdmins);
            when(channelCommonProvider.getCountryRoleAdmins(any(CountryRoleAdminReq.class))).thenReturn(
                    countryRoleResult);

            // Mock BPM 提交流程 - 使用doReturn().when()语法
            doReturn("submitResult")
                    .when(bpmService)
                    .submit(
                            anyString(),
                            anyString(),
                            any(StoreGradeRuleFormDataReq.class),
                            anyMap()
                    );

            // 构造请求
            StoreGradeRuleReq req = new StoreGradeRuleReq(); req.setId(1L); req.setCountryCode("CN");
            req.setCountryName("China"); req.setChannelType(ChannelTypeEnum.IR.getKey());
            req.setModificationMethod(TieringModificationMethodEnum.SYSTEM_CALCULATION.getKey()); req.setSMinCount(100);
            req.setAMinCount(50); req.setBMinCount(20); req.setCMinCount(5); req.setDMinCount(0);
            req.setFileLink("http://file.com"); req.setFlowInstId("existingFlowInstId");

            SubmitResp resp = storeGradeRuleService.submit(req);

            assertNotNull(resp); assertEquals("existingFlowInstId", resp.getFlowInstId());
            assertEquals("submitResult", resp.getRequestApprovalBody());
            verify(bpmService, times(1)).submit(anyString(), anyString(), any(), anyMap());
        }
    }

    /**
     * TC001: 正常情况 - 数据库中有记录且BPM返回非空列表
     */
    @Test
    void testListProcessLog_NormalCase() {
        // 准备输入参数
        CommonApproveHistoryReq req = new CommonApproveHistoryReq();
        req.setRuleLogId(1L);

        // 模拟数据库查询结果
        CommonApproveLog log = new CommonApproveLog();
        log.setFlowInstId("5e0ac9110c0e4cef938e079281a51d00");
        when(commonApproveLogMapper.selectById(1L)).thenReturn(log);

        // 模拟BPM服务返回的审批任务列表
        List<ApprovalTaskListResp> bpmTasks = new ArrayList<>();
        ApprovalTaskListResp task = new ApprovalTaskListResp();
        task.setTaskName("审批阶段1");
        BpmUser assignee = new BpmUser();
        assignee.setUserName("张三");
        task.setAssignee(assignee);
        task.setOperation(com.mi.info.intl.retail.intlretail.service.api.bpm.enums.UserTaskOperation.RECALL);
        task.setComment("同意");
        task.setEndTime(ZonedDateTime.now());
        task.setCreateTime(ZonedDateTime.now().minusDays(1));
        bpmTasks.add(task);
        when(bpmService.listProcessLog("5e0ac9110c0e4cef938e079281a51d00", null)).thenReturn(bpmTasks);

        // 调用被测方法
        CommonApiResponse<List<CommonApproveHistoryResp>> response = storeGradeRuleService.listProcessLog(req);

        // 验证结果
        assertNotNull(response);
        assertEquals(0, response.getCode());
        assertNotNull(response.getData());
        assertEquals(1, response.getData().size());
        CommonApproveHistoryResp historyResp = response.getData().get(0);
        assertEquals("审批阶段1", historyResp.getPhase());
        assertEquals("张三", historyResp.getApprover());
        assertEquals("recall", historyResp.getApproveStatus()); // 修改这里
        assertEquals("同意", historyResp.getComment());
    }

    /**
     * TC002: 异常情况 - 数据库中无记录
     */
    @Test
    void testListProcessLog_RecordNotFound() {
        // 准备输入参数
        CommonApproveHistoryReq req = new CommonApproveHistoryReq();
        req.setRuleLogId(999L);

        // 模拟数据库查询结果为空
        when(commonApproveLogMapper.selectById(999L)).thenReturn(null);

        // 验证抛出异常
        BizException exception = assertThrows(BizException.class, () -> {
            storeGradeRuleService.listProcessLog(req);
        });

        // 验证异常信息
        assertEquals(ErrorCodes.BIZ_ERROR, exception.getErrorCode());
        assertEquals("business exception No corresponding approval record found", exception.getMessage());
    }

    /**
     * TC003: 边界情况 - BPM返回空列表
     */
    @Test
    void testListProcessLog_EmptyBpmResponse() {
        // 准备输入参数
        CommonApproveHistoryReq req = new CommonApproveHistoryReq();
        req.setRuleLogId(2L);

        // 模拟数据库查询结果
        CommonApproveLog log = new CommonApproveLog();
        log.setFlowInstId("flowInstId456");
        when(commonApproveLogMapper.selectById(2L)).thenReturn(log);

        // 模拟BPM服务返回空列表
        when(bpmService.listProcessLog("flowInstId456", null)).thenReturn(new ArrayList<>());

        // 调用被测方法
        CommonApiResponse<List<CommonApproveHistoryResp>> response = storeGradeRuleService.listProcessLog(req);

        // 验证结果
        assertNotNull(response);
        assertEquals(0, response.getCode());
        assertNotNull(response.getData());
        assertTrue(response.getData().isEmpty());
    }

    /**
     * TC01: req == null
     */
    @Test
    void testQueryStoreGrade_ReqIsNull_ReturnsEmptyResp() {
        CommonApiResponse<StoreGradeResp> result = storeGradeRuleService.queryStoreGrade(null);
        assertNotNull(result);
        assertNotNull(result.getData());
        assertNull(result.getData().getStoreGrade());
    }

    /**
     * TC02: req.retailerCode == null
     */
    @Test
    void testQueryStoreGrade_RetailerCodeIsNull_ReturnsEmptyResp() {
        StoreGradeReq req = new StoreGradeReq();
        req.setKapa(100);
        CommonApiResponse<StoreGradeResp> result = storeGradeRuleService.queryStoreGrade(req);
        assertNotNull(result);
        assertNotNull(result.getData());
        assertNull(result.getData().getStoreGrade());
    }

    /**
     * TC03: req.kapa == null
     */
    @Test
    void testQueryStoreGrade_KapaIsNull_ReturnsEmptyResp() {
        StoreGradeReq req = new StoreGradeReq();
        req.setRetailerCode("R001");
        CommonApiResponse<StoreGradeResp> result = storeGradeRuleService.queryStoreGrade(req);
        assertNotNull(result);
        assertNotNull(result.getData());
        assertNull(result.getData().getStoreGrade());
    }

    @Test
    void testQueryStoreGrade_NoRuleFound_ReturnsEmptyResp() {
        StoreGradeReq req = new StoreGradeReq();
        req.setRetailerCode("R001");
        req.setKapa(100);

        StoreGradeRuleServiceImpl spy = spy(storeGradeRuleService);

        LambdaQueryChainWrapper<StoreGradeRule> mockQueryWrapper = mock(LambdaQueryChainWrapper.class);

        doReturn(mockQueryWrapper).when(spy).lambdaQuery();

        when(mockQueryWrapper.eq(any(), any())).thenReturn(mockQueryWrapper);

        when(mockQueryWrapper.one()).thenReturn(null);

        CommonApiResponse<StoreGradeResp> result = spy.queryStoreGrade(req);
        assertNotNull(result);
        assertNotNull(result.getData());
        assertNull(result.getData().getStoreGrade());
    }

    /**
     * TC05: 成功匹配规则，KAPA值满足S等级
     */
    @Test
    void testQueryStoreGrade_Success_SGrade() {
        StoreGradeReq req = new StoreGradeReq();
        req.setRetailerCode("R001");
        req.setKapa(1000);

        StoreGradeRule rule = new StoreGradeRule();
        rule.setSMinCount(900);
        rule.setAMinCount(800);
        rule.setBMinCount(700);
        rule.setCMinCount(600);
        rule.setDMinCount(500);
        rule.setRulesStatus(RuleStatusEnum.IN_EFFECT.getCode());

        // 创建 mock 的 LambdaQueryChainWrapper
        StoreGradeRuleServiceImpl spy = spy(storeGradeRuleService);

        LambdaQueryChainWrapper<StoreGradeRule> mockQueryWrapper = mock(LambdaQueryChainWrapper.class);

        // 使用 doReturn().when() 语法 mock lambdaQuery 方法
        doReturn(mockQueryWrapper).when(spy).lambdaQuery();

        // 使用 doReturn 语法 stub 链式调用的 eq 方法
        when(mockQueryWrapper.eq(any(), any())).thenReturn(mockQueryWrapper);

        // Mock 最终的 one 方法返回规则对象
        when(mockQueryWrapper.one()).thenReturn(rule);

        CommonApiResponse<StoreGradeResp> result = spy.queryStoreGrade(req);
        assertNotNull(result);
        assertNotNull(result.getData());
        assertEquals("S", result.getData().getStoreGrade());
        assertEquals(StoreGradeEnum.S.getKey(), result.getData().getStoreGradeKey());
    }

    /**
     * 测试用例：已有流程提交（flowInstId 不为空）
     */
    @Test
    void testSubmits_WhenFlowInstIdIsNotNull_ShouldSubmitProcess() {
        try (MockedStatic<RpcContextUtil> rpcContextUtilMock = mockStatic(RpcContextUtil.class)) {
            rpcContextUtilMock.when(RpcContextUtil::getCurrentEmail).thenReturn("<EMAIL>");

            // Mock 用户岗位信息
            List<UserPosition> userPositions = new ArrayList<>();
            UserPosition userPosition = new UserPosition();
            userPosition.setMiId(1L);
            userPositions.add(userPosition);
            Result<List<UserPosition>> userPositionResult = Result.success(userPositions);
            when(userProvider.getParentOrganPositionUser(any(GetParentOrganPositionUserReq.class)))
                    .thenReturn(userPositionResult);

            // Mock 用户敏感信息
            List<UserSensitiveInfoResp> userSensitiveInfos = new ArrayList<>();
            UserSensitiveInfoResp userInfo = new UserSensitiveInfoResp();
            userInfo.setMiId(1L);
            userInfo.setEmail("<EMAIL>");
            userSensitiveInfos.add(userInfo);
            Result<List<UserSensitiveInfoResp>> userInfoResult = Result.success(userSensitiveInfos);
            when(userAdminProvider.searchUserSensitiveInfo(any(SearchUserSensitiveInfoRequest.class)))
                    .thenReturn(userInfoResult);

            // Mock 总部审批人
            List<CountryRoleAdminResp> countryRoleAdmins = new ArrayList<>();
            CountryRoleAdminResp adminResp = new CountryRoleAdminResp();
            adminResp.setAdmin("<EMAIL>");
            countryRoleAdmins.add(adminResp);
            Result<List<CountryRoleAdminResp>> countryRoleResult = Result.success(countryRoleAdmins);
            when(channelCommonProvider.getCountryRoleAdmins(any(CountryRoleAdminReq.class)))
                    .thenReturn(countryRoleResult);

            // Mock BPM 提交流程
            when(bpmService.submit(anyString(), anyString(), any(StoreGradeRuleFormDataReq.class), anyMap()))
                    .thenReturn("submitResult");

            // 构造请求
            StoreGradeRuleReq req = new StoreGradeRuleReq();
            req.setId(1L);
            req.setCountryCode("CN");
            req.setCountryName("China");
            req.setChannelType(ChannelTypeEnum.IR.getKey());
            req.setModificationMethod(TieringModificationMethodEnum.SYSTEM_CALCULATION.getKey());
            req.setSMinCount(100);
            req.setAMinCount(50);
            req.setBMinCount(20);
            req.setCMinCount(5);
            req.setDMinCount(0);
            req.setFileLink("http://file.com");
            req.setFlowInstId("existingFlowInstId");

            SubmitResp resp = storeGradeRuleService.submit(req);

            assertNotNull(resp);
            assertEquals("existingFlowInstId", resp.getFlowInstId());
            assertEquals("submitResult", resp.getRequestApprovalBody());
            verify(bpmService, times(1)).submit(anyString(), anyString(), any(), anyMap());
        }
    }

    /**
     * 测试用例：已有流程提交（flowInstId 不为空）
     */
    @Test
    void testSubmit_WhenFlowInstIdIsNotNull_ShouldSubmitProcess() {
        try (MockedStatic<RpcContextUtil> rpcContextUtilMock = mockStatic(RpcContextUtil.class)) {
            rpcContextUtilMock.when(RpcContextUtil::getCurrentEmail).thenReturn("<EMAIL>");

            // Mock 用户岗位信息
            List<UserPosition> userPositions = new ArrayList<>();
            UserPosition userPosition = new UserPosition();
            userPosition.setMiId(1L);
            userPositions.add(userPosition);
            Result<List<UserPosition>> userPositionResult = Result.success(userPositions);
            when(userProvider.getParentOrganPositionUser(any(GetParentOrganPositionUserReq.class)))
                    .thenReturn(userPositionResult);

            // Mock 用户敏感信息
            List<UserSensitiveInfoResp> userSensitiveInfos = new ArrayList<>();
            UserSensitiveInfoResp userInfo = new UserSensitiveInfoResp();
            userInfo.setMiId(1L);
            userInfo.setEmail("<EMAIL>");
            userSensitiveInfos.add(userInfo);
            Result<List<UserSensitiveInfoResp>> userInfoResult = Result.success(userSensitiveInfos);
            when(userAdminProvider.searchUserSensitiveInfo(any(SearchUserSensitiveInfoRequest.class)))
                    .thenReturn(userInfoResult);

            // Mock 总部审批人
            List<CountryRoleAdminResp> countryRoleAdmins = new ArrayList<>();
            CountryRoleAdminResp adminResp = new CountryRoleAdminResp();
            adminResp.setAdmin("<EMAIL>");
            countryRoleAdmins.add(adminResp);
            Result<List<CountryRoleAdminResp>> countryRoleResult = Result.success(countryRoleAdmins);
            when(channelCommonProvider.getCountryRoleAdmins(any(CountryRoleAdminReq.class)))
                    .thenReturn(countryRoleResult);

            // 使用 doReturn().when() 语法 Mock BPM 提交流程
            doReturn("submitResult")
                    .when(bpmService)
                    .submit(
                            anyString(),
                            anyString(),
                            any(StoreGradeRuleFormDataReq.class),
                            anyMap()
                    );

            // 构造请求
            StoreGradeRuleReq req = new StoreGradeRuleReq();
            req.setId(1L);
            req.setCountryCode("CN");
            req.setCountryName("China");
            req.setChannelType(ChannelTypeEnum.IR.getKey());
            req.setModificationMethod(TieringModificationMethodEnum.SYSTEM_CALCULATION.getKey());
            req.setSMinCount(100);
            req.setAMinCount(50);
            req.setBMinCount(20);
            req.setCMinCount(5);
            req.setDMinCount(0);
            req.setFileLink("http://file.com");
            req.setFlowInstId("existingFlowInstId");

            SubmitResp resp = storeGradeRuleService.submit(req);

            assertNotNull(resp);
            assertEquals("existingFlowInstId", resp.getFlowInstId());
            assertEquals("submitResult", resp.getRequestApprovalBody());
            verify(bpmService, times(1)).submit(anyString(), anyString(), any(), anyMap());
        }

    }

    /**
     * 测试用例 TC001：type 匹配 STORE_GRADE_RULE_TEMPLATE
     * 预期：返回 storeGradeRuleConfig 中配置的 URL
     */
    @Test
    public void testGetFileTemplate_TypeMatch_ReturnsConfigUrl() {
        // Given
        FileTemplateReq req = new FileTemplateReq();
        req.setType(FileTemplateEnum.STORE_GRADE_RULE_TEMPLATE.getCode());

        String expectedUrl = "http://example.com/template.xlsx";
        when(storeGradeRuleConfig.getStoreGradeRule()).thenReturn(expectedUrl);

        // When
        CommonApiResponse<String> response = storeGradeRuleService.getFileTemplate(req);

        // Then
        assertEquals(expectedUrl, response.getData());
    }

    /**
     * 测试用例 TC001: 国家代码为空，渠道类型为空，搜索关键词为空
     * 预期：返回所有零售商列表（最多50条）
     */
    @Test
    void testQueryRetailerList_CountryCodeIsNull_ChannelTypeIsNull_SearchIsNull() {
        request.setCountryCode(null);
        request.setChannelType(null);
        request.setSearch(null);

        when(intlRmsRetailerMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(retailers);

        CommonApiResponse<List<RetailerQueryResp>> response = storeGradeRuleService.queryRetailerList(request);

        assertNotNull(response);
        assertEquals(0, response.getCode());
        assertNotNull(response.getData());
        assertEquals(2, response.getData().size());

        verify(intlRmsCountryTimezoneMapper, never()).selectOne(any(LambdaQueryWrapper.class));
        verify(intlRmsRetailerMapper, times(1)).selectList(any(LambdaQueryWrapper.class));
    }

    /**
     * 测试用例 TC002: 国家代码为空，渠道类型为空，搜索关键词不为空
     * 预期：返回匹配 [name](file://D:\intl-retail\intl-retail-fieldforce\src\main\java\com\mi\info\intl\retail\retailer\entity\IntlRmsRetailer.java#L32-L33) 或 [retailerName](file://D:\intl-retail\intl-retail-fieldforce\src\main\java\com\mi\info\intl\retail\retailer\entity\IntlRmsRetailer.java#L44-L45) 的零售商列表（最多50条）
     */
    @Test
    void testQueryRetailerList_CountryCodeIsNull_ChannelTypeIsNull_SearchIsNotNull() {
        request.setCountryCode(null);
        request.setChannelType(null);
        request.setSearch("test");

        when(intlRmsRetailerMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(new ArrayList<>());

        CommonApiResponse<List<RetailerQueryResp>> response = storeGradeRuleService.queryRetailerList(request);

        assertNotNull(response);
        assertEquals(0, response.getCode());
        assertNotNull(response.getData());
        assertTrue(response.getData().isEmpty());

        verify(intlRmsCountryTimezoneMapper, never()).selectOne(any(LambdaQueryWrapper.class));
        verify(intlRmsRetailerMapper, times(1)).selectList(any(LambdaQueryWrapper.class));
    }

    /**
     * 测试用例 TC005: 国家代码不为空，查询国家信息成功，渠道类型为空，搜索关键词为空
     * 预期：返回指定国家的零售商列表（最多50条）
     */
    @Test
    void testQueryRetailerList_CountryCodeIsNotNull_CountryFound_ChannelTypeIsNull_SearchIsNull() {
        request.setCountryCode("CN");
        request.setChannelType(null);
        request.setSearch(null);

        when(intlRmsCountryTimezoneMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(countryTimezone);
        when(intlRmsRetailerMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(retailers);

        CommonApiResponse<List<RetailerQueryResp>> response = storeGradeRuleService.queryRetailerList(request);

        assertNotNull(response);
        assertEquals(0, response.getCode());
        assertNotNull(response.getData());
        assertEquals(2, response.getData().size());

        verify(intlRmsCountryTimezoneMapper, times(1)).selectOne(any(LambdaQueryWrapper.class));
        verify(intlRmsRetailerMapper, times(1)).selectList(any(LambdaQueryWrapper.class));
    }

    /**
     * 测试用例 TC009: 国家代码不为空，查询国家信息失败（返回 `null`）
     * 预期：抛出 [BizException](file://D:\intl-retail\intl-retail-common\src\main\java\com\mi\info\intl\retail\exception\BizException.java#L13-L39)，错误码为 [ErrorCodes.BIZ_ERROR](file://D:\intl-retail\intl-retail-common\src\main\java\com\mi\info\intl\retail\exception\ErrorCodes.java#L21-L21)
     */
    @Test
    void testQueryRetailerList_CountryCodeIsNotNull_CountryNotFound() {
        request.setCountryCode("XX");
        request.setChannelType(null);
        request.setSearch(null);

        when(intlRmsCountryTimezoneMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        BizException exception = assertThrows(BizException.class, () -> {
            storeGradeRuleService.queryRetailerList(request);
        });

        assertEquals(ErrorCodes.BIZ_ERROR, exception.getErrorCode());

        verify(intlRmsCountryTimezoneMapper, times(1)).selectOne(any(LambdaQueryWrapper.class));
        verify(intlRmsRetailerMapper, never()).selectList(any(LambdaQueryWrapper.class));
    }

    /**
     * TC01: 正常流程测试
     */
    @Test
    public void testExportStoreGradeRule_Success() {
        // 1. 准备测试数据（请求参数、返回结果的实体类）
        StoreGradeExportReq request = new StoreGradeExportReq();
        request.setRuleId(1L); // 核心参数：规则ID
        request.setCountryCode("CN"); // 核心参数：国家码

        // 模拟服务层返回的规则数据
        StoreGradeRule rule = new StoreGradeRule();
        rule.setId(1L);
        rule.setRetailerCode("retailer123");

        // 2. 模拟依赖组件的返回结果（仅保留被实际调用的依赖）
        // 2.1 模拟国家时区查询（若exportStoreGradeRule中调用了此mapper，则保留）
        IntlRmsCountryTimezone country = new IntlRmsCountryTimezone();
        country.setCountryId("country123");
        when(intlRmsCountryTimezoneMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(country);

        // 2.2 模拟店铺分页查询（若exportStoreGradeRule中调用了此mapper，则保留）
        Page<IntlRmsStore> storePage = new Page<>();
        storePage.setRecords(Collections.singletonList(new IntlRmsStore()));
        when(intlRmsStoreMapper.selectPage(any(Page.class), any(LambdaQueryWrapper.class))).thenReturn(storePage);

        // 2.3 模拟静态工具类（获取当前账号）
        try (MockedStatic<RpcContextUtil> mockedRpcContextUtil = mockStatic(RpcContextUtil.class)) {
            mockedRpcContextUtil.when(RpcContextUtil::getCurrentAccount).thenReturn("testUser");

            // 2.4 模拟作业服务（触发导出任务）
            Result<String> jobResult = Result.success("taskId123");
            Map<String, String> attachments = new HashMap<>();
            jobResult.setAttachments(attachments);
            when(nrJobService.triggerJob(any(TriggerJobRequestDTO.class))).thenReturn(jobResult);

            // 3. 模拟服务层对象（spy保留真实逻辑，仅覆盖getById方法）
            StoreGradeRuleServiceImpl spyService = spy(storeGradeRuleService);
            // 仅模拟getById(1L)（与请求参数的ruleId一致，避免冗余）
            doReturn(rule).when(spyService).getById(1L);

            // 4. 执行被测试方法
            CommonApiResponse<String> response = spyService.exportStoreGradeRule(request);

            // 验证结果
            assertNotNull(response);
            assertEquals("taskId123", response.getData());
            assertEquals(0, response.getCode());

            // 6. 验证依赖调用（仅验证被实际执行的方法）
            verify(spyService, times(1)).getById(1L); // 验证服务层getById调用
            verify(intlRmsCountryTimezoneMapper, times(1)).selectOne(any(LambdaQueryWrapper.class)); // 验证国家时区查询
            verify(intlRmsStoreMapper, times(1)).selectPage(any(Page.class), any(LambdaQueryWrapper.class)); // 验证店铺查询
            verify(nrJobService, times(1)).triggerJob(any(TriggerJobRequestDTO.class)); // 验证作业服务调用
        }
    }

}
