package com.mi.info.intl.retail.ldu.service.impl;

import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.ProjectInfoDto;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.SearchProductReq;
import com.mi.info.intl.retail.ldu.infra.entity.IntlRmsProduct;
import com.mi.info.intl.retail.ldu.service.IntlRmsProductService;
import com.mi.info.intl.retail.model.CommonApiResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * IntlRmsProductApiServiceImpl 单元测试
 * 
 * <AUTHOR>
 * @date 2025/1/27
 */
@ExtendWith(MockitoExtension.class)
class IntlRmsProductApiServiceImplTest {

    @Mock
    private IntlRmsProductService intlRmsProductService;

    @InjectMocks
    private IntlRmsProductApiServiceImpl intlRmsProductApiService;

    private IntlRmsProduct mockProduct1;
    private IntlRmsProduct mockProduct2;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        mockProduct1 = new IntlRmsProduct();
        mockProduct1.setId(1L);
        mockProduct1.setGoodsId("MI001");
        mockProduct1.setName("小米手机13");
        mockProduct1.setProjectCode("MI13");
        mockProduct1.setProductLine("智能手机");
        mockProduct1.setProductLineCode(1L);
        mockProduct1.setProductLineEn("Smartphone");

        mockProduct2 = new IntlRmsProduct();
        mockProduct2.setId(2L);
        mockProduct2.setGoodsId("MI002");
        mockProduct2.setName("小米手机13 Pro");
        mockProduct2.setProjectCode("MI13PRO");
        mockProduct2.setProductLine("智能手机");
        mockProduct2.setProductLineCode(1L);
        mockProduct2.setProductLineEn("Smartphone");
    }

    @Test
    void searchProductsByProjectCode_FuzzySearch_Success() {
        // Given
        String projectCode = "MI";
        SearchProductReq req = new SearchProductReq();
        req.setKeyword(projectCode);
        req.setSearchType("FUZZY");
        List<IntlRmsProduct> mockProducts = Arrays.asList(mockProduct1, mockProduct2);
        when(intlRmsProductService.searchByProjectCodeFuzzy(projectCode)).thenReturn(mockProducts);

        // When
        CommonApiResponse<List<ProjectInfoDto>> response = intlRmsProductApiService.searchProductsByProjectCode(req);

        // Then
        assertNotNull(response);
        assertEquals(200, response.getCode());
        assertEquals("success", response.getMessage());
        
        List<ProjectInfoDto> result = response.getData();
        assertNotNull(result);
        assertEquals(2, result.size());
        
        // 验证第一个产品
        ProjectInfoDto firstProduct = result.get(0);
        assertEquals("MI001", firstProduct.getGoodsId());
        assertEquals("小米手机13", firstProduct.getName());
        assertEquals("智能手机", firstProduct.getProductLine());
        assertEquals(1L, firstProduct.getProductLineCode());
        assertEquals("Smartphone", firstProduct.getProductLineEn());
        
        // 验证第二个产品
        ProjectInfoDto secondProduct = result.get(1);
        assertEquals("MI002", secondProduct.getGoodsId());
        assertEquals("小米手机13 Pro", secondProduct.getName());
        assertEquals("智能手机", secondProduct.getProductLine());
        assertEquals(1L, secondProduct.getProductLineCode());
        assertEquals("Smartphone", secondProduct.getProductLineEn());
        
        // 验证Service方法被正确调用
        verify(intlRmsProductService, times(1)).searchByProjectCodeFuzzy(projectCode);
        verify(intlRmsProductService, never()).searchByProjectCodeExact(anyString());
    }

    @Test
    void searchProductsByProjectCode_ExactSearch_Success() {
        // Given
        String projectCode = "MI13";
        SearchProductReq req = new SearchProductReq();
        req.setKeyword(projectCode);
        req.setSearchType("EXACT");
        List<IntlRmsProduct> mockProducts = Arrays.asList(mockProduct1);
        when(intlRmsProductService.searchByProjectCodeExact(projectCode)).thenReturn(mockProducts);

        // When
        CommonApiResponse<List<ProjectInfoDto>> response = intlRmsProductApiService.searchProductsByProjectCode(req);

        // Then
        assertNotNull(response);
        assertEquals(200, response.getCode());
        assertEquals("success", response.getMessage());
        
        List<ProjectInfoDto> result = response.getData();
        assertNotNull(result);
        assertEquals(1, result.size());
        
        // 验证产品信息
        ProjectInfoDto product = result.get(0);
        assertEquals("MI001", product.getGoodsId());
        assertEquals("小米手机13", product.getName());
        assertEquals("MI13", product.getProjectCode());
        assertEquals("智能手机", product.getProductLine());
        assertEquals(1L, product.getProductLineCode());
        assertEquals("Smartphone", product.getProductLineEn());
        
        // 验证Service方法被正确调用
        verify(intlRmsProductService, times(1)).searchByProjectCodeExact(projectCode);
        verify(intlRmsProductService, never()).searchByProjectCodeFuzzy(anyString());
    }

    @Test
    void searchProductsByProjectCode_DefaultFuzzySearch() {
        // Given
        String projectCode = "MI";
        SearchProductReq req = new SearchProductReq();
        req.setKeyword(projectCode);
        // 不设置searchType，应该默认为FUZZY
        List<IntlRmsProduct> mockProducts = Arrays.asList(mockProduct1, mockProduct2);
        when(intlRmsProductService.searchByProjectCodeFuzzy(projectCode)).thenReturn(mockProducts);

        // When
        CommonApiResponse<List<ProjectInfoDto>> response = intlRmsProductApiService.searchProductsByProjectCode(req);

        // Then
        assertNotNull(response);
        assertEquals(200, response.getCode());
        assertEquals("success", response.getMessage());
        
        List<ProjectInfoDto> result = response.getData();
        assertNotNull(result);
        assertEquals(2, result.size());
        
        // 验证Service方法被正确调用（默认模糊查询）
        verify(intlRmsProductService, times(1)).searchByProjectCodeFuzzy(projectCode);
        verify(intlRmsProductService, never()).searchByProjectCodeExact(anyString());
    }

    @Test
    void searchProductsByProjectCode_EmptyResult() {
        // Given
        String projectCode = "NONEXISTENT";
        SearchProductReq req = new SearchProductReq();
        req.setKeyword(projectCode);
        req.setSearchType("FUZZY");
        when(intlRmsProductService.searchByProjectCodeFuzzy(projectCode)).thenReturn(Collections.emptyList());

        // When
        CommonApiResponse<List<ProjectInfoDto>> response = intlRmsProductApiService.searchProductsByProjectCode(req);

        // Then
        assertNotNull(response);
        assertEquals(200, response.getCode());
        assertEquals("success", response.getMessage());
        
        List<ProjectInfoDto> result = response.getData();
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(intlRmsProductService, times(1)).searchByProjectCodeFuzzy(projectCode);
    }

    @Test
    void searchProductsByProjectCode_NullInput() {
        // When
        CommonApiResponse<List<ProjectInfoDto>> response = intlRmsProductApiService.searchProductsByProjectCode(null);

        // Then
        assertNotNull(response);
        assertEquals(200, response.getCode());
        assertEquals("success", response.getMessage());
        
        List<ProjectInfoDto> result = response.getData();
        assertNotNull(result);
        assertTrue(result.isEmpty());
        
        // 验证Service方法没有被调用
        verify(intlRmsProductService, never()).searchByProjectCodeFuzzy(anyString());
        verify(intlRmsProductService, never()).searchByProjectCodeExact(anyString());
    }

    @Test
    void searchProductsByProjectCode_NullKeyword() {
        // Given
        SearchProductReq req = new SearchProductReq();
        req.setKeyword(null);

        // When
        CommonApiResponse<List<ProjectInfoDto>> response = intlRmsProductApiService.searchProductsByProjectCode(req);

        // Then
        assertNotNull(response);
        assertEquals(200, response.getCode());
        assertEquals("success", response.getMessage());
        
        List<ProjectInfoDto> result = response.getData();
        assertNotNull(result);
        assertTrue(result.isEmpty());
        
        // 验证Service方法没有被调用
        verify(intlRmsProductService, never()).searchByProjectCodeFuzzy(anyString());
        verify(intlRmsProductService, never()).searchByProjectCodeExact(anyString());
    }

    @Test
    void searchProductsByProjectCode_EmptyString() {
        // Given
        SearchProductReq req = new SearchProductReq();
        req.setKeyword("");

        // When
        CommonApiResponse<List<ProjectInfoDto>> response = intlRmsProductApiService.searchProductsByProjectCode(req);

        // Then
        assertNotNull(response);
        assertEquals(200, response.getCode());
        assertEquals("success", response.getMessage());
        
        List<ProjectInfoDto> result = response.getData();
        assertNotNull(result);
        assertTrue(result.isEmpty());
        
        // 验证Service方法没有被调用
        verify(intlRmsProductService, never()).searchByProjectCodeFuzzy(anyString());
        verify(intlRmsProductService, never()).searchByProjectCodeExact(anyString());
    }

    @Test
    void searchProductsByProjectCode_WhitespaceString() {
        // Given
        SearchProductReq req = new SearchProductReq();
        req.setKeyword("   ");

        // When
        CommonApiResponse<List<ProjectInfoDto>> response = intlRmsProductApiService.searchProductsByProjectCode(req);

        // Then
        assertNotNull(response);
        assertEquals(200, response.getCode());
        assertEquals("success", response.getMessage());
        
        List<ProjectInfoDto> result = response.getData();
        assertNotNull(result);
        assertTrue(result.isEmpty());
        
        // 验证Service方法没有被调用
        verify(intlRmsProductService, never()).searchByProjectCodeFuzzy(anyString());
        verify(intlRmsProductService, never()).searchByProjectCodeExact(anyString());
    }

    @Test
    void searchProductsByProjectCode_TrimmedInput() {
        // Given
        String projectCode = "  MI  ";
        String trimmedProjectCode = "MI";
        SearchProductReq req = new SearchProductReq();
        req.setKeyword(projectCode);
        req.setSearchType("FUZZY");
        List<IntlRmsProduct> mockProducts = Arrays.asList(mockProduct1, mockProduct2);
        when(intlRmsProductService.searchByProjectCodeFuzzy(trimmedProjectCode)).thenReturn(mockProducts);

        // When
        CommonApiResponse<List<ProjectInfoDto>> response = intlRmsProductApiService.searchProductsByProjectCode(req);

        // Then
        assertNotNull(response);
        assertEquals(200, response.getCode());
        assertEquals("success", response.getMessage());
        
        List<ProjectInfoDto> result = response.getData();
        assertNotNull(result);
        assertEquals(2, result.size());
        
        // 验证Service方法被正确调用（使用trimmed的projectCode）
        verify(intlRmsProductService, times(1)).searchByProjectCodeFuzzy(trimmedProjectCode);
    }

    @Test
    void searchProductsByProjectCode_ServiceThrowsException() {
        // Given
        String projectCode = "MI";
        SearchProductReq req = new SearchProductReq();
        req.setKeyword(projectCode);
        req.setSearchType("FUZZY");
        when(intlRmsProductService.searchByProjectCodeFuzzy(projectCode)).thenThrow(new RuntimeException("Database error"));

        // When
        CommonApiResponse<List<ProjectInfoDto>> response = intlRmsProductApiService.searchProductsByProjectCode(req);

        // Then
        assertNotNull(response);
        assertEquals(500, response.getCode());
        assertTrue(response.getMessage().contains("查询失败"));
        assertNull(response.getData());
        
        verify(intlRmsProductService, times(1)).searchByProjectCodeFuzzy(projectCode);
    }

    @Test
    void searchProductsByProjectCode_ProductWithNullFields() {
        // Given
        String projectCode = "MI";
        SearchProductReq req = new SearchProductReq();
        req.setKeyword(projectCode);
        req.setSearchType("FUZZY");
        IntlRmsProduct productWithNullFields = new IntlRmsProduct();
        productWithNullFields.setId(3L);
        productWithNullFields.setGoodsId("MI003");
        // 其他字段保持null
        
        List<IntlRmsProduct> mockProducts = Arrays.asList(productWithNullFields);
        when(intlRmsProductService.searchByProjectCodeFuzzy(projectCode)).thenReturn(mockProducts);

        // When
        CommonApiResponse<List<ProjectInfoDto>> response = intlRmsProductApiService.searchProductsByProjectCode(req);

        // Then
        assertNotNull(response);
        assertEquals(200, response.getCode());
        assertEquals("success", response.getMessage());
        
        List<ProjectInfoDto> result = response.getData();
        assertNotNull(result);
        assertEquals(1, result.size());
        
        ProjectInfoDto product = result.get(0);
        assertEquals("MI003", product.getGoodsId());
        assertNull(product.getName());
        assertNull(product.getProjectCode());
        assertNull(product.getProductLine());
        assertNull(product.getProductLineCode());
        assertNull(product.getProductLineEn());
        
        verify(intlRmsProductService, times(1)).searchByProjectCodeFuzzy(projectCode);
    }

    @Test
    void searchProductsByProjectCode_CaseInsensitiveSearchType() {
        // Given
        String projectCode = "MI13";
        SearchProductReq req = new SearchProductReq();
        req.setKeyword(projectCode);
        req.setSearchType("exact"); // 小写，应该被转换为大写
        List<IntlRmsProduct> mockProducts = Arrays.asList(mockProduct1);
        when(intlRmsProductService.searchByProjectCodeExact(projectCode)).thenReturn(mockProducts);

        // When
        CommonApiResponse<List<ProjectInfoDto>> response = intlRmsProductApiService.searchProductsByProjectCode(req);

        // Then
        assertNotNull(response);
        assertEquals(200, response.getCode());
        assertEquals("success", response.getMessage());
        
        List<ProjectInfoDto> result = response.getData();
        assertNotNull(result);
        assertEquals(1, result.size());
        
        // 验证Service方法被正确调用（精确查询）
        verify(intlRmsProductService, times(1)).searchByProjectCodeExact(projectCode);
        verify(intlRmsProductService, never()).searchByProjectCodeFuzzy(anyString());
    }
}
