package com.mi.info.intl.retail.org.domain;

import com.alibaba.cola.dto.PageResponse;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mi.info.intl.retail.api.so.rule.blacklist.SoBlacklistApiService;
import com.mi.info.intl.retail.cooperation.task.infra.entity.IntlRmsSignRule;
import com.mi.info.intl.retail.cooperation.task.inspection.impl.IntlRmsSignRuleServiceImpl;
import com.mi.info.intl.retail.core.feishu.service.SendMessageService;
import com.mi.info.intl.retail.domain.retailer.service.impl.IntlRetailerServiceImpl;
import com.mi.info.intl.retail.exception.BizException;
import com.mi.info.intl.retail.intlretail.service.api.mq.dto.RmsDbContentRequest;
import com.mi.info.intl.retail.intlretail.service.api.mq.dto.RmsDbRequest;
import com.mi.info.intl.retail.intlretail.service.api.so.sales.request.SalesImeiReqDto;
import com.mi.info.intl.retail.intlretail.service.api.so.sales.request.SalesQtyReqDto;
import com.mi.info.intl.retail.ldu.infra.entity.IntlRmsProduct;
import com.mi.info.intl.retail.ldu.service.impl.IntlRmsProductServiceImpl;
import com.mi.info.intl.retail.ldu.service.impl.RmsStoreServiceImpl;
import com.mi.info.intl.retail.org.domain.country.service.impl.IntlCountryTimeZoneServiceImpl;
import com.mi.info.intl.retail.org.domain.country.service.impl.IntlPositionServiceImpl;
import com.mi.info.intl.retail.org.infra.entity.IntlRmsCountryTimezone;
import com.mi.info.intl.retail.org.infra.entity.IntlRmsPosition;
import com.mi.info.intl.retail.org.infra.entity.IntlRmsStore;
import com.mi.info.intl.retail.retailer.entity.IntlRmsRetailer;
import com.mi.info.intl.retail.so.app.mq.SyncSoToEsProducer;
import com.mi.info.intl.retail.so.domain.datasync.enums.DataSyncDataTypeEnum;
import com.mi.info.intl.retail.so.domain.sales.model.SoImeiIndex;
import com.mi.info.intl.retail.so.domain.sales.model.SoQtyIndex;
import com.mi.info.intl.retail.so.domain.sales.service.IntlSoImeiEsService;
import com.mi.info.intl.retail.so.domain.sales.service.IntlSoQtyEsService;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlRmsCity;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlRmsProvince;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlRmsRrp;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlRmsSecondarychannel;
import com.mi.info.intl.retail.so.domain.upload.service.impl.IntlRmsCityServiceImpl;
import com.mi.info.intl.retail.so.domain.upload.service.impl.IntlRmsProvinceServiceImpl;
import com.mi.info.intl.retail.so.domain.upload.service.impl.IntlRmsRrpServiceImpl;
import com.mi.info.intl.retail.so.domain.upload.service.impl.IntlRmsSecondarychannelServiceImpl;
import com.mi.info.intl.retail.user.app.impl.IntlRmsPersonnelPositionServiceImpl;
import com.mi.info.intl.retail.user.app.impl.UserServiceImpl;
import com.mi.info.intl.retail.user.infra.entity.IntlRmsPersonnelPosition;
import com.mi.info.intl.retail.user.infra.entity.IntlRmsUser;
import com.mi.info.intl.retail.config.AlarmEmailConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * RmsSyncDbManager 单元测试类
 * 
 * <AUTHOR> Test
 * @version 1.0
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("RmsSyncDbManager 单元测试")
public class RmsSyncDbManagerTest {

    @InjectMocks
    private RmsSyncDbManager rmsSyncDbManager;

    @Mock
    private RmsStoreServiceImpl rmsStoreServiceImpl;
    @Mock
    private IntlPositionServiceImpl intlPositionServiceImpl;
    @Mock
    private IntlCountryTimeZoneServiceImpl intlCountryTimeZoneServiceImpl;
    @Mock
    private UserServiceImpl userServiceImpl;
    @Mock
    private IntlRetailerServiceImpl intlRetailerServiceImpl;
    @Mock
    private SoBlacklistApiService soBlacklistApiService;
    @Mock
    private IntlRmsProductServiceImpl intlRmsProductServiceImpl;
    @Mock
    private SyncSoToEsProducer syncSoToEsProducer;
    @Mock
    private SendMessageService sendMessageService;
    @Mock
    private IntlSoImeiEsService intlSoImeiEsService;
    @Mock
    private IntlSoQtyEsService intlSoQtyEsService;
    @Mock
    private IntlRmsPersonnelPositionServiceImpl intlRmsPersonnelPositionServiceImpl;
    @Mock
    private IntlRmsSignRuleServiceImpl intlRmsSignRuleServiceImpl;
    @Mock
    private IntlRmsRrpServiceImpl intlRmsRrpServiceImpl;
    @Mock
    private IntlRmsProvinceServiceImpl intlRmsProvinceServiceImpl;
    @Mock
    private IntlRmsCityServiceImpl intlRmsCityServiceImpl;
    @Mock
    private IntlRmsSecondarychannelServiceImpl intlRmsSecondarychannelServiceImpl;
    @Mock
    private AlarmEmailConfig alarmEmailConfig;

    private RmsDbRequest rmsDbRequest;
    private List<RmsDbContentRequest> contentRequests;
    private IntlRmsStore testStore;
    private IntlRmsUser testUser;
    private IntlRmsPosition testPosition;
    private IntlRmsCountryTimezone testCountryTimezone;

    @BeforeEach
    public void setUp() {
        // 设置测试属性值
        ReflectionTestUtils.setField(rmsSyncDbManager, "p1", "test_group_id");
        
        // 初始化测试数据
        initTestData();
    }

    private void initTestData() {
        // 初始化门店测试数据
        testStore = new IntlRmsStore();
        testStore.setStoreId("STORE001");
        testStore.setName("Test Store");
        testStore.setCode("TS001");
        testStore.setCityCode("CITY001");
        testStore.setCityIdName("Test City");
        testStore.setProvinceCode("PROV001");
        testStore.setProvinceLabel("Test Province");

        // 初始化用户测试数据
        testUser = new IntlRmsUser();
        testUser.setRmsUserid("USER001");
        testUser.setCode("U001");
        testUser.setDomainName("test.user");
        testUser.setEnglishName("Test User");

        // 初始化阵地测试数据
        testPosition = new IntlRmsPosition();
        testPosition.setPositionId("POS001");
        testPosition.setCode("P001");
        testPosition.setName("Test Position");
        testPosition.setStoreId("STORE001");
        testPosition.setStoreName("Test Store");

        // 初始化国家时区测试数据
        testCountryTimezone = new IntlRmsCountryTimezone();
        testCountryTimezone.setCountryCode("CN");
        testCountryTimezone.setCountryName("China");
        testCountryTimezone.setTimezoneCode("Asia/Shanghai");

        // 初始化请求数据
        RmsDbContentRequest storeRequest = new RmsDbContentRequest();
        storeRequest.setTable("intl_rms_store");
        storeRequest.setContent(testStore);
        storeRequest.setUuid("uuid-001");

        contentRequests = Arrays.asList(storeRequest);
        rmsDbRequest = new RmsDbRequest();
        rmsDbRequest.setRmsDBContentList(contentRequests);
    }

    @Test
    @DisplayName("editDb - 空请求列表 - 应该直接返回")
    public void editDb_EmptyRequestList_ShouldReturnDirectly() {
        // 准备数据
        RmsDbRequest emptyRequest = new RmsDbRequest();
        emptyRequest.setRmsDBContentList(Collections.emptyList());

        // 执行测试
        rmsSyncDbManager.editDb(emptyRequest);

        // 验证结果 - 不应该调用任何服务方法
        verifyNoInteractions(rmsStoreServiceImpl);
        verifyNoInteractions(userServiceImpl);
        verifyNoInteractions(intlPositionServiceImpl);
    }

    @Test
    @DisplayName("editDb - 门店数据处理 - 成功")
    public void editDb_StoreDataProcessing_Success() {
        // 准备数据
        when(rmsStoreServiceImpl.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());
        when(rmsStoreServiceImpl.saveBatch(anyList())).thenReturn(true);

        // 执行测试
        rmsSyncDbManager.editDb(rmsDbRequest);

        // 验证结果
        verify(rmsStoreServiceImpl).list(any(LambdaQueryWrapper.class));
        verify(rmsStoreServiceImpl).saveBatch(anyList());
        verify(rmsStoreServiceImpl, never()).updateBatchById(anyList());
    }

    @Test
    @DisplayName("editDb - 不支持的表名 - 应该抛出异常")
    public void editDb_UnsupportedTableName_ShouldThrowException() {
        // 准备数据
        RmsDbContentRequest unsupportedRequest = new RmsDbContentRequest();
        unsupportedRequest.setTable("unsupported_table");
        unsupportedRequest.setContent(new Object());
        
        RmsDbRequest request = new RmsDbRequest();
        request.setRmsDBContentList(Arrays.asList(unsupportedRequest));
        // 设置 alarmEmailConfig mock
        when(alarmEmailConfig.getRmsSyncDbEmail()).thenReturn("<EMAIL>");
        // 执行测试并验证异常
        BizException exception = assertThrows(BizException.class, 
            () -> rmsSyncDbManager.editDb(request));

        // 验证异常信息
        assertFalse(exception.getMessage().contains("table not found"));

    }

    @Test
    @DisplayName("editDb - 处理异常 - 应该发送告警消息")
    public void editDb_ProcessingException_ShouldSendAlarmMessage() {
        // 准备数据
        when(rmsStoreServiceImpl.list(any(LambdaQueryWrapper.class)))
            .thenThrow(new RuntimeException("Database error"));
        // 设置 alarmEmailConfig mock
        when(alarmEmailConfig.getRmsSyncDbEmail()).thenReturn("<EMAIL>");
        // 执行测试并验证异常
        BizException exception = assertThrows(BizException.class, 
            () -> rmsSyncDbManager.editDb(rmsDbRequest));

        // 验证结果
        assertTrue(exception.getMessage().contains("SyncRmsDbError"));
    }

    @Test
    @DisplayName("imeiQuerySync - 无数据 - 应该直接返回")
    public void imeiQuerySync_NoData_ShouldReturnDirectly() {
        // 准备数据
        SalesImeiReqDto query = new SalesImeiReqDto();
        when(intlSoImeiEsService.count(query)).thenReturn(0L);

        // 执行测试
        rmsSyncDbManager.imeiQuerySync(query);

        // 验证结果
        verify(intlSoImeiEsService).count(query);
        verify(intlSoImeiEsService, never()).queryByPage(any());
        verify(syncSoToEsProducer, never()).sendSyncEsMsg(any(), any(), anyBoolean());
    }

    @Test
    @DisplayName("imeiQuerySync - 有数据 - 应该正常处理")
    public void imeiQuerySync_WithData_ShouldProcessNormally() {
        // 准备数据
        SalesImeiReqDto query = new SalesImeiReqDto();
        query.setPageSize(500);
        
        SoImeiIndex imeiIndex = new SoImeiIndex();
        imeiIndex.setId(1L);
        
        PageResponse<SoImeiIndex> pageResponse = new PageResponse<>();
        pageResponse.setData(Arrays.asList(imeiIndex));
        
        when(intlSoImeiEsService.count(query)).thenReturn(1L);
        when(intlSoImeiEsService.queryByPage(query)).thenReturn(pageResponse);

        // 执行测试
        rmsSyncDbManager.imeiQuerySync(query);

        // 验证结果
        verify(intlSoImeiEsService).count(query);
        verify(intlSoImeiEsService).queryByPage(query);
        verify(syncSoToEsProducer).sendSyncEsMsg(eq(DataSyncDataTypeEnum.QTY), 
            eq(Arrays.asList(1L)), eq(true));
    }

    @Test
    @DisplayName("qtyQuerySync - 无数据 - 应该直接返回")
    public void qtyQuerySync_NoData_ShouldReturnDirectly() {
        // 准备数据
        SalesQtyReqDto query = new SalesQtyReqDto();
        when(intlSoQtyEsService.count(query)).thenReturn(0L);

        // 执行测试
        rmsSyncDbManager.qtyQuerySync(query);

        // 验证结果
        verify(intlSoQtyEsService).count(query);
        verify(intlSoQtyEsService, never()).search(any());
        verify(syncSoToEsProducer, never()).sendSyncEsMsg(any(), any(), anyBoolean());
    }

    @Test
    @DisplayName("qtyQuerySync - 有数据 - 应该正常处理")
    public void qtyQuerySync_WithData_ShouldProcessNormally() {
        // 准备数据
        SalesQtyReqDto query = new SalesQtyReqDto();
        query.setPageSize(500);
        
        SoQtyIndex qtyIndex = new SoQtyIndex();
        qtyIndex.setId(1L);
        
        PageResponse<SoQtyIndex> pageResponse = new PageResponse<>();
        pageResponse.setData(Arrays.asList(qtyIndex));
        
        when(intlSoQtyEsService.count(query)).thenReturn(1L);
        when(intlSoQtyEsService.search(query)).thenReturn(pageResponse);

        // 执行测试
        rmsSyncDbManager.qtyQuerySync(query);

        // 验证结果
        verify(intlSoQtyEsService).count(query);
        verify(intlSoQtyEsService).search(query);
        verify(syncSoToEsProducer).sendSyncEsMsg(eq(DataSyncDataTypeEnum.QTY), 
            eq(Arrays.asList(1L)), eq(true));
    }

    @Test
    @DisplayName("getCountryTimezoneByCountryCode - 找到数据 - 应该返回时区信息")
    public void getCountryTimezoneByCountryCode_FoundData_ShouldReturnTimezone() {
        // 准备数据
        String countryCode = "CN";
        when(intlCountryTimeZoneServiceImpl.getOne(any(LambdaQueryWrapper.class)))
            .thenReturn(testCountryTimezone);

        // 执行测试
        IntlRmsCountryTimezone result = rmsSyncDbManager.getCountryTimezoneByCountryCode(countryCode);

        // 验证结果
        assertNotNull(result);
        assertEquals("CN", result.getCountryCode());
        assertEquals("China", result.getCountryName());
        verify(intlCountryTimeZoneServiceImpl).getOne(any(LambdaQueryWrapper.class));
    }

    @Test
    @DisplayName("getCountryTimezoneByCountryCode - 未找到数据 - 应该返回null")
    public void getCountryTimezoneByCountryCode_NotFound_ShouldReturnNull() {
        // 准备数据
        String countryCode = "US";
        when(intlCountryTimeZoneServiceImpl.getOne(any(LambdaQueryWrapper.class)))
            .thenReturn(null);

        // 执行测试
        IntlRmsCountryTimezone result = rmsSyncDbManager.getCountryTimezoneByCountryCode(countryCode);

        // 验证结果
        assertNull(result);
        verify(intlCountryTimeZoneServiceImpl).getOne(any(LambdaQueryWrapper.class));
    }

    @Test
    @DisplayName("getCountryTimezoneByCountryName - 找到数据 - 应该返回时区信息")
    public void getCountryTimezoneByCountryName_FoundData_ShouldReturnTimezone() {
        // 准备数据
        String countryName = "China";
        when(intlCountryTimeZoneServiceImpl.getOne(any(LambdaQueryWrapper.class)))
            .thenReturn(testCountryTimezone);

        // 执行测试
        IntlRmsCountryTimezone result = rmsSyncDbManager.getCountryTimezoneByCountryName(countryName);

        // 验证结果
        assertNotNull(result);
        assertEquals("CN", result.getCountryCode());
        assertEquals("China", result.getCountryName());
        verify(intlCountryTimeZoneServiceImpl).getOne(any(LambdaQueryWrapper.class));
    }

    @Test
    @DisplayName("getCountryTimezoneByCountryName - 未找到数据 - 应该返回null")
    public void getCountryTimezoneByCountryName_NotFound_ShouldReturnNull() {
        // 准备数据
        String countryName = "United States";
        when(intlCountryTimeZoneServiceImpl.getOne(any(LambdaQueryWrapper.class)))
            .thenReturn(null);

        // 执行测试
        IntlRmsCountryTimezone result = rmsSyncDbManager.getCountryTimezoneByCountryName(countryName);

        // 验证结果
        assertNull(result);
        verify(intlCountryTimeZoneServiceImpl).getOne(any(LambdaQueryWrapper.class));
    }

    @Test
    @DisplayName("editDb - 用户数据处理 - 成功")
    public void editDb_UserDataProcessing_Success() {
        // 准备数据
        RmsDbContentRequest userRequest = new RmsDbContentRequest();
        userRequest.setTable("intl_rms_user");
        userRequest.setContent(testUser);
        userRequest.setUuid("uuid-002");

        RmsDbRequest userDbRequest = new RmsDbRequest();
        userDbRequest.setRmsDBContentList(Arrays.asList(userRequest));

        when(userServiceImpl.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());
        when(userServiceImpl.saveBatch(anyList())).thenReturn(true);

        // 执行测试
        rmsSyncDbManager.editDb(userDbRequest);

        // 验证结果
        verify(userServiceImpl).list(any(LambdaQueryWrapper.class));
        verify(userServiceImpl).saveBatch(anyList());
        verify(userServiceImpl, never()).updateBatchById(anyList());
    }

    @Test
    @DisplayName("editDb - 阵地数据处理 - 成功")
    public void editDb_PositionDataProcessing_Success() {
        // 准备数据
        RmsDbContentRequest positionRequest = new RmsDbContentRequest();
        positionRequest.setTable("intl_rms_position");
        positionRequest.setContent(testPosition);
        positionRequest.setUuid("uuid-003");

        RmsDbRequest positionDbRequest = new RmsDbRequest();
        positionDbRequest.setRmsDBContentList(Arrays.asList(positionRequest));

        when(intlPositionServiceImpl.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());
        when(intlPositionServiceImpl.saveBatch(anyList())).thenReturn(true);

        // 执行测试
        rmsSyncDbManager.editDb(positionDbRequest);

        // 验证结果
        verify(intlPositionServiceImpl).list(any(LambdaQueryWrapper.class));
        verify(intlPositionServiceImpl).saveBatch(anyList());
        verify(intlPositionServiceImpl, never()).updateBatchById(anyList());
    }

    @Test
    @DisplayName("editDb - 国家时区数据处理 - 成功")
    public void editDb_CountryTimezoneDataProcessing_Success() {
        // 准备数据
        RmsDbContentRequest timezoneRequest = new RmsDbContentRequest();
        timezoneRequest.setTable("intl_rms_country_timezone");
        timezoneRequest.setContent(testCountryTimezone);
        timezoneRequest.setUuid("uuid-004");

        RmsDbRequest timezoneDbRequest = new RmsDbRequest();
        timezoneDbRequest.setRmsDBContentList(Arrays.asList(timezoneRequest));

        when(intlCountryTimeZoneServiceImpl.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());
        when(intlCountryTimeZoneServiceImpl.saveBatch(anyList())).thenReturn(true);

        // 执行测试
        rmsSyncDbManager.editDb(timezoneDbRequest);

        // 验证结果
        verify(intlCountryTimeZoneServiceImpl).list(any(LambdaQueryWrapper.class));
        verify(intlCountryTimeZoneServiceImpl).saveBatch(anyList());
        verify(intlCountryTimeZoneServiceImpl, never()).updateBatchById(anyList());
    }

    @Test
    @DisplayName("editDb - 零售商数据处理 - 成功")
    public void editDb_RetailerDataProcessing_Success() {
        // 准备数据
        IntlRmsRetailer testRetailer = new IntlRmsRetailer();
        testRetailer.setRetailerId("RETAILER001");
        testRetailer.setName("Test Retailer");
        testRetailer.setRetailerName("Test Retailer Name");

        RmsDbContentRequest retailerRequest = new RmsDbContentRequest();
        retailerRequest.setTable("intl_rms_retailer");
        retailerRequest.setContent(testRetailer);
        retailerRequest.setUuid("uuid-005");

        RmsDbRequest retailerDbRequest = new RmsDbRequest();
        retailerDbRequest.setRmsDBContentList(Arrays.asList(retailerRequest));

        when(intlRetailerServiceImpl.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());
        when(intlRetailerServiceImpl.saveBatch(anyList())).thenReturn(true);

        // 执行测试
        rmsSyncDbManager.editDb(retailerDbRequest);

        // 验证结果
        verify(intlRetailerServiceImpl).list(any(LambdaQueryWrapper.class));
        verify(intlRetailerServiceImpl).saveBatch(anyList());
        verify(intlRetailerServiceImpl, never()).updateBatchById(anyList());
    }

    @Test
    @DisplayName("editDb - SN黑名单数据处理 - 成功")
    public void editDb_SoSnBlacklistDataProcessing_Success() {
        // 准备数据
        RmsDbContentRequest blacklistRequest = new RmsDbContentRequest();
        blacklistRequest.setTable("intl_so_sn_blacklist");
        blacklistRequest.setContent("test_blacklist_data");
        blacklistRequest.setUuid("uuid-006");

        RmsDbRequest blacklistDbRequest = new RmsDbRequest();
        blacklistDbRequest.setRmsDBContentList(Arrays.asList(blacklistRequest));

        doNothing().when(soBlacklistApiService).syncSoSnBlacklist(any());

        // 执行测试
        rmsSyncDbManager.editDb(blacklistDbRequest);

        // 验证结果
        verify(soBlacklistApiService).syncSoSnBlacklist("test_blacklist_data");
    }

    @Test
    @DisplayName("editDb - 产品数据处理 - 成功")
    public void editDb_ProductDataProcessing_Success() {
        // 准备数据
        IntlRmsProduct testProduct = new IntlRmsProduct();
        testProduct.setGoodsId("PRODUCT001");
        testProduct.setModelLevel(100100);
        testProduct.setModelLevelName("Level 1");

        RmsDbContentRequest productRequest = new RmsDbContentRequest();
        productRequest.setTable("intl_rms_product");
        productRequest.setContent(testProduct);
        productRequest.setUuid("uuid-007");

        RmsDbRequest productDbRequest = new RmsDbRequest();
        productDbRequest.setRmsDBContentList(Arrays.asList(productRequest));

        when(intlRmsProductServiceImpl.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // 执行测试
        rmsSyncDbManager.editDb(productDbRequest);

        // 验证结果
        verify(intlRmsProductServiceImpl).list(any(LambdaQueryWrapper.class));
        verify(intlRmsProductServiceImpl, never()).updateBatchById(anyList());
    }

    @Test
    @DisplayName("editDb - 批量处理 - 超过100条数据应该分批处理")
    public void editDb_BatchProcessing_ShouldProcessInBatches() {
        // 准备数据 - 创建150条门店数据
        List<RmsDbContentRequest> largeBatch = new ArrayList<>();
        for (int i = 0; i < 150; i++) {
            IntlRmsStore store = new IntlRmsStore();
            store.setStoreId("STORE" + String.format("%03d", i));
            store.setName("Test Store " + i);
            store.setCode("TS" + String.format("%03d", i));

            RmsDbContentRequest request = new RmsDbContentRequest();
            request.setTable("intl_rms_store");
            request.setContent(store);
            request.setUuid("uuid-" + i);
            largeBatch.add(request);
        }

        RmsDbRequest largeRequest = new RmsDbRequest();
        largeRequest.setRmsDBContentList(largeBatch);

        when(rmsStoreServiceImpl.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());
        when(rmsStoreServiceImpl.saveBatch(anyList())).thenReturn(true);

        // 执行测试
        rmsSyncDbManager.editDb(largeRequest);

        // 验证结果 - 应该被分成两批处理（100 + 50）
        verify(rmsStoreServiceImpl, times(2)).saveBatch(anyList());
        verify(rmsStoreServiceImpl, times(2)).list(any(LambdaQueryWrapper.class));
    }



    @Test
    @DisplayName("editDb - 门店数据更新场景 - 成功")
    public void editDb_StoreDataUpdate_Success() {
        // 准备数据 - 模拟已存在的门店数据
        IntlRmsStore existingStore = new IntlRmsStore();
        existingStore.setId(1);
        existingStore.setStoreId("STORE001");
        existingStore.setName("Old Store Name");
        existingStore.setCode("TS001");
        existingStore.setCityCode("OLD_CITY");
        existingStore.setCityIdName("Old City");
        existingStore.setProvinceCode("OLD_PROV");
        existingStore.setProvinceLabel("Old Province");

        IntlRmsStore newStore = new IntlRmsStore();
        newStore.setStoreId("STORE001");
        newStore.setName("New Store Name");
        newStore.setCode("TS001");
        newStore.setCityCode("NEW_CITY");
        newStore.setCityIdName("New City");
        newStore.setProvinceCode("NEW_PROV");
        newStore.setProvinceLabel("New Province");

        RmsDbContentRequest storeRequest = new RmsDbContentRequest();
        storeRequest.setTable("intl_rms_store");
        storeRequest.setContent(newStore);
        storeRequest.setUuid("uuid-update");

        RmsDbRequest updateRequest = new RmsDbRequest();
        updateRequest.setRmsDBContentList(Arrays.asList(storeRequest));

        when(rmsStoreServiceImpl.list(any(LambdaQueryWrapper.class))).thenReturn(Arrays.asList(existingStore));
        when(rmsStoreServiceImpl.updateBatchById(anyList())).thenReturn(true);

        // 执行测试
        rmsSyncDbManager.editDb(updateRequest);

        // 验证结果
        verify(rmsStoreServiceImpl).list(any(LambdaQueryWrapper.class));
        verify(rmsStoreServiceImpl, never()).saveBatch(anyList());
        verify(rmsStoreServiceImpl).updateBatchById(anyList());
    }

    @Test
    @DisplayName("editDb - 门店数据相同 - 不应该更新")
    public void editDb_StoreDataSame_ShouldNotUpdate() {
        // 准备数据 - 相同的数据
        IntlRmsStore existingStore = new IntlRmsStore();
        existingStore.setId(1);
        existingStore.setStoreId("STORE001");
        existingStore.setName("Same Store Name");
        existingStore.setCode("TS001");
        existingStore.setCityCode("SAME_CITY");
        existingStore.setCityIdName("Same City");
        existingStore.setProvinceCode("SAME_PROV");
        existingStore.setProvinceLabel("Same Province");

        IntlRmsStore sameStore = new IntlRmsStore();
        sameStore.setStoreId("STORE001");
        sameStore.setName("Same Store Name");
        sameStore.setCode("TS001");
        sameStore.setCityCode("SAME_CITY");
        sameStore.setCityIdName("Same City");
        sameStore.setProvinceCode("SAME_PROV");
        sameStore.setProvinceLabel("Same Province");

        RmsDbContentRequest storeRequest = new RmsDbContentRequest();
        storeRequest.setTable("intl_rms_store");
        storeRequest.setContent(sameStore);
        storeRequest.setUuid("uuid-same");

        RmsDbRequest sameRequest = new RmsDbRequest();
        sameRequest.setRmsDBContentList(Arrays.asList(storeRequest));

        when(rmsStoreServiceImpl.list(any(LambdaQueryWrapper.class))).thenReturn(Arrays.asList(existingStore));

        // 执行测试
        rmsSyncDbManager.editDb(sameRequest);

        // 验证结果 - 不应该调用更新方法
        verify(rmsStoreServiceImpl).list(any(LambdaQueryWrapper.class));
        verify(rmsStoreServiceImpl, never()).saveBatch(anyList());
        verify(rmsStoreServiceImpl, never()).updateBatchById(anyList());
    }

    @Test
    @DisplayName("editDb - 用户数据更新场景 - 成功")
    public void editDb_UserDataUpdate_Success() {
        // 准备数据 - 模拟已存在的用户数据
        IntlRmsUser existingUser = new IntlRmsUser();
        existingUser.setId(1);
        existingUser.setRmsUserid("USER001");
        existingUser.setCode("U001");
        existingUser.setDomainName("old.user");
        existingUser.setEnglishName("Old User");

        IntlRmsUser newUser = new IntlRmsUser();
        newUser.setRmsUserid("USER001");
        newUser.setCode("U001");
        newUser.setDomainName("new.user");
        newUser.setEnglishName("New User");

        RmsDbContentRequest userRequest = new RmsDbContentRequest();
        userRequest.setTable("intl_rms_user");
        userRequest.setContent(newUser);
        userRequest.setUuid("uuid-user-update");

        RmsDbRequest updateRequest = new RmsDbRequest();
        updateRequest.setRmsDBContentList(Arrays.asList(userRequest));

        when(userServiceImpl.list(any(LambdaQueryWrapper.class))).thenReturn(Arrays.asList(existingUser));
        when(userServiceImpl.updateBatchById(anyList())).thenReturn(true);

        // 执行测试
        rmsSyncDbManager.editDb(updateRequest);

        // 验证结果
        verify(userServiceImpl).list(any(LambdaQueryWrapper.class));
        verify(userServiceImpl, never()).saveBatch(anyList());
        verify(userServiceImpl).updateBatchById(anyList());
    }

    @Test
    @DisplayName("editDb - 阵地数据更新场景 - 成功")
    public void editDb_PositionDataUpdate_Success() {
        // 准备数据 - 模拟已存在的阵地数据
        IntlRmsPosition existingPosition = new IntlRmsPosition();
        existingPosition.setId(1L);
        existingPosition.setPositionId("POS001");
        existingPosition.setCode("P001");
        existingPosition.setName("Old Position Name");
        existingPosition.setStoreId("STORE001");
        existingPosition.setStoreName("Old Store");

        IntlRmsPosition newPosition = new IntlRmsPosition();
        newPosition.setPositionId("POS001");
        newPosition.setCode("P001");
        newPosition.setName("New Position Name");
        newPosition.setStoreId("STORE001");
        newPosition.setStoreName("New Store");

        RmsDbContentRequest positionRequest = new RmsDbContentRequest();
        positionRequest.setTable("intl_rms_position");
        positionRequest.setContent(newPosition);
        positionRequest.setUuid("uuid-position-update");

        RmsDbRequest updateRequest = new RmsDbRequest();
        updateRequest.setRmsDBContentList(Arrays.asList(positionRequest));

        when(intlPositionServiceImpl.list(any(LambdaQueryWrapper.class))).thenReturn(Arrays.asList(existingPosition));
        when(intlPositionServiceImpl.updateBatchById(anyList())).thenReturn(true);

        // 执行测试
        rmsSyncDbManager.editDb(updateRequest);

        // 验证结果
        verify(intlPositionServiceImpl).list(any(LambdaQueryWrapper.class));
        verify(intlPositionServiceImpl, never()).saveBatch(anyList());
        verify(intlPositionServiceImpl).updateBatchById(anyList());
    }

    @Test
    @DisplayName("editDb - 员工阵地关联数据处理 - 成功")
    public void editDb_PersonnelPositionDataProcessing_Success() {
        // 准备数据
        IntlRmsPersonnelPosition testPersonnelPosition = new IntlRmsPersonnelPosition();
        testPersonnelPosition.setAssociationId("49f72a52-269b-ec11-b400-000d3aa25a3a");
        testPersonnelPosition.setUserId("13d346a5-7fe8-eb11-bacb-0022485809b5");
        testPersonnelPosition.setPositionId("7708f265-fb03-ec11-b6e6-000d3aa2e638");
        testPersonnelPosition.setUserName("Bill#@test.leibao.com");
        testPersonnelPosition.setStoreName("ĐMS_HYE_KDO - Nghĩa Dân");

        RmsDbContentRequest personnelPositionRequest = new RmsDbContentRequest();
        personnelPositionRequest.setTable("intl_rms_personnel_position");
        personnelPositionRequest.setContent(testPersonnelPosition);
        personnelPositionRequest.setUuid("uuid-008");

        RmsDbRequest personnelPositionDbRequest = new RmsDbRequest();
        personnelPositionDbRequest.setRmsDBContentList(Arrays.asList(personnelPositionRequest));

        when(intlRmsPersonnelPositionServiceImpl.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());
        when(intlRmsPersonnelPositionServiceImpl.saveBatch(anyList())).thenReturn(true);

        // 执行测试
        rmsSyncDbManager.editDb(personnelPositionDbRequest);

        // 验证结果
        verify(intlRmsPersonnelPositionServiceImpl).list(any(LambdaQueryWrapper.class));
        verify(intlRmsPersonnelPositionServiceImpl).saveBatch(anyList());
        verify(intlRmsPersonnelPositionServiceImpl, never()).updateBatchById(anyList());
    }

    @Test
    @DisplayName("editDb - 签到规则数据处理 - 成功")
    public void editDb_SignRuleDataProcessing_Success() {
        // 准备数据
        IntlRmsSignRule testSignRule = new IntlRmsSignRule();
        testSignRule.setSignRuleId("RULE001");
        testSignRule.setCode("R001");
        testSignRule.setName("Test Rule");
        testSignRule.setCountryId("CN");
        testSignRule.setCountryName("China");

        RmsDbContentRequest signRuleRequest = new RmsDbContentRequest();
        signRuleRequest.setTable("intl_rms_sign_rule");
        signRuleRequest.setContent(testSignRule);
        signRuleRequest.setUuid("uuid-009");

        RmsDbRequest signRuleDbRequest = new RmsDbRequest();
        signRuleDbRequest.setRmsDBContentList(Arrays.asList(signRuleRequest));

        when(intlRmsSignRuleServiceImpl.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());
        when(intlRmsSignRuleServiceImpl.saveBatch(anyList())).thenReturn(true);

        // 执行测试
        rmsSyncDbManager.editDb(signRuleDbRequest);

        // 验证结果
        verify(intlRmsSignRuleServiceImpl).list(any(LambdaQueryWrapper.class));
        verify(intlRmsSignRuleServiceImpl).saveBatch(anyList());
        verify(intlRmsSignRuleServiceImpl, never()).updateBatchById(anyList());
    }

    @Test
    @DisplayName("editDb - RRP数据处理 - 成功")
    public void editDb_RrpDataProcessing_Success() {
        // 准备数据
        IntlRmsRrp testRrp = new IntlRmsRrp();
        testRrp.setRrpId("RRP001");
        testRrp.setRrpName("Test RRP");
        testRrp.setRrpCode("R001");
        testRrp.setCountryId("CN");
        testRrp.setCountryName("China");

        RmsDbContentRequest rrpRequest = new RmsDbContentRequest();
        rrpRequest.setTable("intl_rms_rrp");
        rrpRequest.setContent(testRrp);
        rrpRequest.setUuid("uuid-010");

        RmsDbRequest rrpDbRequest = new RmsDbRequest();
        rrpDbRequest.setRmsDBContentList(Arrays.asList(rrpRequest));

        when(intlRmsRrpServiceImpl.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());
        when(intlRmsRrpServiceImpl.saveBatch(anyList())).thenReturn(true);

        // 执行测试
        rmsSyncDbManager.editDb(rrpDbRequest);

        // 验证结果
        verify(intlRmsRrpServiceImpl).list(any(LambdaQueryWrapper.class));
        verify(intlRmsRrpServiceImpl).saveBatch(anyList());
        verify(intlRmsRrpServiceImpl, never()).updateBatchById(anyList());
    }

    @Test
    @DisplayName("editDb - 省份数据处理 - 成功")
    public void editDb_ProvinceDataProcessing_Success() {
        // 准备数据
        IntlRmsProvince testProvince = new IntlRmsProvince();
        testProvince.setProvinceCode("PROV001");
        testProvince.setProvinceName("Test Province");
        testProvince.setCountryId("CN");
        testProvince.setCountryName("China");

        RmsDbContentRequest provinceRequest = new RmsDbContentRequest();
        provinceRequest.setTable("intl_rms_province");
        provinceRequest.setContent(testProvince);
        provinceRequest.setUuid("uuid-011");

        RmsDbRequest provinceDbRequest = new RmsDbRequest();
        provinceDbRequest.setRmsDBContentList(Arrays.asList(provinceRequest));

        when(intlRmsProvinceServiceImpl.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());
        when(intlRmsProvinceServiceImpl.saveBatch(anyList())).thenReturn(true);

        // 执行测试
        rmsSyncDbManager.editDb(provinceDbRequest);

        // 验证结果
        verify(intlRmsProvinceServiceImpl).list(any(LambdaQueryWrapper.class));
        verify(intlRmsProvinceServiceImpl).saveBatch(anyList());
        verify(intlRmsProvinceServiceImpl, never()).updateBatchById(anyList());
    }

    @Test
    @DisplayName("editDb - 城市数据处理 - 成功")
    public void editDb_CityDataProcessing_Success() {
        // 准备数据
        IntlRmsCity testCity = new IntlRmsCity();
        testCity.setCityCode("CITY001");
        testCity.setCityName("Test City");
        testCity.setCountryId("CN");
        testCity.setCountryName("China");
        testCity.setProvinceId("PROV001");
        testCity.setProvinceName("Test Province");

        RmsDbContentRequest cityRequest = new RmsDbContentRequest();
        cityRequest.setTable("intl_rms_city");
        cityRequest.setContent(testCity);
        cityRequest.setUuid("uuid-012");

        RmsDbRequest cityDbRequest = new RmsDbRequest();
        cityDbRequest.setRmsDBContentList(Arrays.asList(cityRequest));

        when(intlRmsCityServiceImpl.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());
        when(intlRmsCityServiceImpl.saveBatch(anyList())).thenReturn(true);

        // 执行测试
        rmsSyncDbManager.editDb(cityDbRequest);

        // 验证结果
        verify(intlRmsCityServiceImpl).list(any(LambdaQueryWrapper.class));
        verify(intlRmsCityServiceImpl).saveBatch(anyList());
        verify(intlRmsCityServiceImpl, never()).updateBatchById(anyList());
    }

    @Test
    @DisplayName("editDb - 二级渠道数据处理 - 成功")
    public void editDb_SecondaryChannelDataProcessing_Success() {
        // 准备数据
        IntlRmsSecondarychannel testChannel = new IntlRmsSecondarychannel();
        testChannel.setChannelId("CHANNEL001");
        testChannel.setChannelName("Test Channel");
        testChannel.setChannelBuyChannelId(1L);
        testChannel.setChannelEnglishName("Test Channel EN");

        RmsDbContentRequest channelRequest = new RmsDbContentRequest();
        channelRequest.setTable("intl_rms_secondarychannel");
        channelRequest.setContent(testChannel);
        channelRequest.setUuid("uuid-013");

        RmsDbRequest channelDbRequest = new RmsDbRequest();
        channelDbRequest.setRmsDBContentList(Arrays.asList(channelRequest));

        when(intlRmsSecondarychannelServiceImpl.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());
        when(intlRmsSecondarychannelServiceImpl.saveBatch(anyList())).thenReturn(true);

        // 执行测试
        rmsSyncDbManager.editDb(channelDbRequest);

        // 验证结果
        verify(intlRmsSecondarychannelServiceImpl).list(any(LambdaQueryWrapper.class));
        verify(intlRmsSecondarychannelServiceImpl).saveBatch(anyList());
        verify(intlRmsSecondarychannelServiceImpl, never()).updateBatchById(anyList());
    }

    @Test
    @DisplayName("imeiQuerySync - 分页查询 - 多页数据")
    public void imeiQuerySync_MultiplePages_ShouldProcessAllPages() {
        // 准备数据 - 模拟多页数据
        SalesImeiReqDto query = new SalesImeiReqDto();
        query.setPageSize(500);
        
        SoImeiIndex imeiIndex1 = new SoImeiIndex();
        imeiIndex1.setId(1L);
        
        SoImeiIndex imeiIndex2 = new SoImeiIndex();
        imeiIndex2.setId(2L);
        
        PageResponse<SoImeiIndex> pageResponse1 = new PageResponse<>();
        pageResponse1.setData(Arrays.asList(imeiIndex1));
        
        PageResponse<SoImeiIndex> pageResponse2 = new PageResponse<>();
        pageResponse2.setData(Arrays.asList(imeiIndex2));
        
        when(intlSoImeiEsService.count(query)).thenReturn(1000L);
        when(intlSoImeiEsService.queryByPage(query))
            .thenReturn(pageResponse1)
            .thenReturn(pageResponse2);

        // 执行测试
        rmsSyncDbManager.imeiQuerySync(query);

        // 验证结果 - 应该查询多页
        verify(intlSoImeiEsService).count(query);
        verify(intlSoImeiEsService, atLeast(2)).queryByPage(query);
        verify(syncSoToEsProducer, atLeast(2)).sendSyncEsMsg(eq(DataSyncDataTypeEnum.QTY), anyList(), eq(true));
    }

    @Test
    @DisplayName("qtyQuerySync - 分页查询 - 多页数据")
    public void qtyQuerySync_MultiplePages_ShouldProcessAllPages() {
        // 准备数据 - 模拟多页数据
        SalesQtyReqDto query = new SalesQtyReqDto();
        query.setPageSize(500);
        
        SoQtyIndex qtyIndex1 = new SoQtyIndex();
        qtyIndex1.setId(1L);
        
        SoQtyIndex qtyIndex2 = new SoQtyIndex();
        qtyIndex2.setId(2L);
        
        PageResponse<SoQtyIndex> pageResponse1 = new PageResponse<>();
        pageResponse1.setData(Arrays.asList(qtyIndex1));
        
        PageResponse<SoQtyIndex> pageResponse2 = new PageResponse<>();
        pageResponse2.setData(Arrays.asList(qtyIndex2));
        
        when(intlSoQtyEsService.count(query)).thenReturn(1000L);
        when(intlSoQtyEsService.search(query))
            .thenReturn(pageResponse1)
            .thenReturn(pageResponse2);

        // 执行测试
        rmsSyncDbManager.qtyQuerySync(query);

        // 验证结果 - 应该查询多页
        verify(intlSoQtyEsService).count(query);
        verify(intlSoQtyEsService, atLeast(2)).search(query);
        verify(syncSoToEsProducer, atLeast(2)).sendSyncEsMsg(eq(DataSyncDataTypeEnum.QTY), anyList(), eq(true));
    }

    @Test
    @DisplayName("imeiQuerySync - 空数据列表 - 应该记录错误日志")
    public void imeiQuerySync_EmptyDataList_ShouldLogError() {
        // 准备数据
        SalesImeiReqDto query = new SalesImeiReqDto();
        query.setPageSize(500);
        
        PageResponse<SoImeiIndex> emptyPageResponse = new PageResponse<>();
        emptyPageResponse.setData(Collections.emptyList());
        
        when(intlSoImeiEsService.count(query)).thenReturn(1L);
        when(intlSoImeiEsService.queryByPage(query)).thenReturn(emptyPageResponse);

        // 执行测试
        rmsSyncDbManager.imeiQuerySync(query);

        // 验证结果
        verify(intlSoImeiEsService).count(query);
        verify(intlSoImeiEsService).queryByPage(query);
        verify(syncSoToEsProducer, never()).sendSyncEsMsg(any(), any(), anyBoolean());
    }

    @Test
    @DisplayName("qtyQuerySync - 空数据列表 - 应该记录错误日志")
    public void qtyQuerySync_EmptyDataList_ShouldLogError() {
        // 准备数据
        SalesQtyReqDto query = new SalesQtyReqDto();
        query.setPageSize(500);
        
        PageResponse<SoQtyIndex> emptyPageResponse = new PageResponse<>();
        emptyPageResponse.setData(Collections.emptyList());
        
        when(intlSoQtyEsService.count(query)).thenReturn(1L);
        when(intlSoQtyEsService.search(query)).thenReturn(emptyPageResponse);

        // 执行测试
        rmsSyncDbManager.qtyQuerySync(query);

        // 验证结果
        verify(intlSoQtyEsService).count(query);
        verify(intlSoQtyEsService).search(query);
        verify(syncSoToEsProducer, never()).sendSyncEsMsg(any(), any(), anyBoolean());
    }

    @Test
    @DisplayName("editDb - 产品数据更新场景 - 找到现有产品")
    public void editDb_ProductDataUpdate_FoundExistingProduct() {
        // 准备数据 - 模拟已存在的产品
        IntlRmsProduct existingProduct = new IntlRmsProduct();
        existingProduct.setId(1L);
        existingProduct.setGoodsId("PRODUCT001");
        existingProduct.setModelLevel(100100);
        existingProduct.setModelLevelName("Old Level");

        IntlRmsProduct newProduct = new IntlRmsProduct();
        newProduct.setGoodsId("PRODUCT001");
        newProduct.setModelLevel(100200);
        newProduct.setModelLevelName("New Level");

        RmsDbContentRequest productRequest = new RmsDbContentRequest();
        productRequest.setTable("intl_rms_product");
        productRequest.setContent(newProduct);
        productRequest.setUuid("uuid-product-update");

        RmsDbRequest productDbRequest = new RmsDbRequest();
        productDbRequest.setRmsDBContentList(Arrays.asList(productRequest));

        when(intlRmsProductServiceImpl.list(any(LambdaQueryWrapper.class))).thenReturn(Arrays.asList(existingProduct));
        when(intlRmsProductServiceImpl.updateBatchById(anyList())).thenReturn(true);

        // 执行测试
        rmsSyncDbManager.editDb(productDbRequest);

        // 验证结果
        verify(intlRmsProductServiceImpl).list(any(LambdaQueryWrapper.class));
        verify(intlRmsProductServiceImpl).updateBatchById(anyList());
    }

    @Test
    @DisplayName("editDb - 零售商数据更新场景 - 成功")
    public void editDb_RetailerDataUpdate_Success() {
        // 准备数据 - 模拟已存在的零售商数据
        IntlRmsRetailer existingRetailer = new IntlRmsRetailer();
        existingRetailer.setId(1L);
        existingRetailer.setRetailerId("RETAILER001");
        existingRetailer.setName("Old Retailer");
        existingRetailer.setRetailerName("Old Retailer Name");
        existingRetailer.setIsNew(0);

        IntlRmsRetailer newRetailer = new IntlRmsRetailer();
        newRetailer.setRetailerId("RETAILER001");
        newRetailer.setName("New Retailer");
        newRetailer.setRetailerName("New Retailer Name");

        RmsDbContentRequest retailerRequest = new RmsDbContentRequest();
        retailerRequest.setTable("intl_rms_retailer");
        retailerRequest.setContent(newRetailer);
        retailerRequest.setUuid("uuid-retailer-update");

        RmsDbRequest retailerDbRequest = new RmsDbRequest();
        retailerDbRequest.setRmsDBContentList(Arrays.asList(retailerRequest));

        when(intlRetailerServiceImpl.list(any(LambdaQueryWrapper.class))).thenReturn(Arrays.asList(existingRetailer));
        when(intlRetailerServiceImpl.updateBatchById(anyList())).thenReturn(true);

        // 执行测试
        rmsSyncDbManager.editDb(retailerDbRequest);

        // 验证结果
        verify(intlRetailerServiceImpl).list(any(LambdaQueryWrapper.class));
        verify(intlRetailerServiceImpl, never()).saveBatch(anyList());
        verify(intlRetailerServiceImpl).updateBatchById(anyList());
    }

    @Test
    @DisplayName("editDb - 零售商数据相同 - 不应该更新")
    public void editDb_RetailerDataSame_ShouldNotUpdate() {
        // 准备数据 - 相同的数据
        IntlRmsRetailer existingRetailer = new IntlRmsRetailer();
        existingRetailer.setId(1L);
        existingRetailer.setRetailerId("RETAILER001");
        existingRetailer.setName("Same Retailer");
        existingRetailer.setRetailerName("Same Retailer Name");
        existingRetailer.setIsNew(0);

        IntlRmsRetailer sameRetailer = new IntlRmsRetailer();
        sameRetailer.setRetailerId("RETAILER001");
        sameRetailer.setName("Same Retailer");
        sameRetailer.setRetailerName("Same Retailer Name");

        RmsDbContentRequest retailerRequest = new RmsDbContentRequest();
        retailerRequest.setTable("intl_rms_retailer");
        retailerRequest.setContent(sameRetailer);
        retailerRequest.setUuid("uuid-retailer-same");

        RmsDbRequest retailerDbRequest = new RmsDbRequest();
        retailerDbRequest.setRmsDBContentList(Arrays.asList(retailerRequest));

        when(intlRetailerServiceImpl.list(any(LambdaQueryWrapper.class))).thenReturn(Arrays.asList(existingRetailer));

        // 执行测试
        rmsSyncDbManager.editDb(retailerDbRequest);

        // 验证结果 - 不应该调用更新方法
        verify(intlRetailerServiceImpl).list(any(LambdaQueryWrapper.class));
        verify(intlRetailerServiceImpl, never()).saveBatch(anyList());
        verify(intlRetailerServiceImpl, never()).updateBatchById(anyList());
    }

    @Test
    @DisplayName("editDb - 员工阵地关联数据更新场景 - 成功")
    public void editDb_PersonnelPositionDataUpdate_Success() {
        // 准备数据 - 模拟已存在的员工阵地关联数据
        IntlRmsPersonnelPosition existingPersonnelPosition = new IntlRmsPersonnelPosition();
        existingPersonnelPosition.setId(1);
        existingPersonnelPosition.setAssociationId("ASSOC001");
        existingPersonnelPosition.setUserId("USER001");
        existingPersonnelPosition.setPositionId("POS001");
        existingPersonnelPosition.setUserName("Old User");
        existingPersonnelPosition.setStoreName("Old Store");

        IntlRmsPersonnelPosition newPersonnelPosition = new IntlRmsPersonnelPosition();
        newPersonnelPosition.setAssociationId("ASSOC001");
        newPersonnelPosition.setUserId("USER001");
        newPersonnelPosition.setPositionId("POS001");
        newPersonnelPosition.setUserName("New User");
        newPersonnelPosition.setStoreName("New Store");

        RmsDbContentRequest personnelPositionRequest = new RmsDbContentRequest();
        personnelPositionRequest.setTable("intl_rms_personnel_position");
        personnelPositionRequest.setContent(newPersonnelPosition);
        personnelPositionRequest.setUuid("uuid-personnel-position-update");

        RmsDbRequest personnelPositionDbRequest = new RmsDbRequest();
        personnelPositionDbRequest.setRmsDBContentList(Arrays.asList(personnelPositionRequest));

        when(intlRmsPersonnelPositionServiceImpl.list(any(LambdaQueryWrapper.class))).thenReturn(Arrays.asList(existingPersonnelPosition));
        when(intlRmsPersonnelPositionServiceImpl.updateBatchById(anyList())).thenReturn(true);

        // 执行测试
        rmsSyncDbManager.editDb(personnelPositionDbRequest);

        // 验证结果
        verify(intlRmsPersonnelPositionServiceImpl).list(any(LambdaQueryWrapper.class));
        verify(intlRmsPersonnelPositionServiceImpl, never()).saveBatch(anyList());
        verify(intlRmsPersonnelPositionServiceImpl).updateBatchById(anyList());
    }

    @Test
    @DisplayName("editDb - 签到规则数据更新场景 - 成功")
    public void editDb_SignRuleDataUpdate_Success() {
        // 准备数据 - 模拟已存在的签到规则数据
        IntlRmsSignRule existingSignRule = new IntlRmsSignRule();
        existingSignRule.setId(1);
        existingSignRule.setSignRuleId("RULE001");
        existingSignRule.setCode("R001");
        existingSignRule.setName("Old Rule");
        existingSignRule.setCountryId("CN");
        existingSignRule.setCountryName("China");

        IntlRmsSignRule newSignRule = new IntlRmsSignRule();
        newSignRule.setSignRuleId("RULE001");
        newSignRule.setCode("R001");
        newSignRule.setName("New Rule");
        newSignRule.setCountryId("CN");
        newSignRule.setCountryName("China");

        RmsDbContentRequest signRuleRequest = new RmsDbContentRequest();
        signRuleRequest.setTable("intl_rms_sign_rule");
        signRuleRequest.setContent(newSignRule);
        signRuleRequest.setUuid("uuid-sign-rule-update");

        RmsDbRequest signRuleDbRequest = new RmsDbRequest();
        signRuleDbRequest.setRmsDBContentList(Arrays.asList(signRuleRequest));

        when(intlRmsSignRuleServiceImpl.list(any(LambdaQueryWrapper.class))).thenReturn(Arrays.asList(existingSignRule));
        when(intlRmsSignRuleServiceImpl.updateBatchById(anyList())).thenReturn(true);

        // 执行测试
        rmsSyncDbManager.editDb(signRuleDbRequest);

        // 验证结果
        verify(intlRmsSignRuleServiceImpl).list(any(LambdaQueryWrapper.class));
        verify(intlRmsSignRuleServiceImpl, never()).saveBatch(anyList());
        verify(intlRmsSignRuleServiceImpl).updateBatchById(anyList());
    }

    @Test
    @DisplayName("editDb - 国家时区数据更新场景 - 成功")
    public void editDb_CountryTimezoneDataUpdate_Success() {
        // 准备数据 - 模拟已存在的国家时区数据
        IntlRmsCountryTimezone existingTimezone = new IntlRmsCountryTimezone();
        existingTimezone.setId(1);
        existingTimezone.setCountryCode("CN");
        existingTimezone.setCountryName("China");
        existingTimezone.setTimezoneCode("Asia/Shanghai");

        IntlRmsCountryTimezone newTimezone = new IntlRmsCountryTimezone();
        newTimezone.setCountryCode("CN");
        newTimezone.setCountryName("China");
        newTimezone.setTimezoneCode("Asia/Beijing");

        RmsDbContentRequest timezoneRequest = new RmsDbContentRequest();
        timezoneRequest.setTable("intl_rms_country_timezone");
        timezoneRequest.setContent(newTimezone);
        timezoneRequest.setUuid("uuid-timezone-update");

        RmsDbRequest timezoneDbRequest = new RmsDbRequest();
        timezoneDbRequest.setRmsDBContentList(Arrays.asList(timezoneRequest));

        when(intlCountryTimeZoneServiceImpl.list(any(LambdaQueryWrapper.class))).thenReturn(Arrays.asList(existingTimezone));
        when(intlCountryTimeZoneServiceImpl.updateBatchById(anyList())).thenReturn(true);

        // 执行测试
        rmsSyncDbManager.editDb(timezoneDbRequest);

        // 验证结果
        verify(intlCountryTimeZoneServiceImpl).list(any(LambdaQueryWrapper.class));
        verify(intlCountryTimeZoneServiceImpl, never()).saveBatch(anyList());
        verify(intlCountryTimeZoneServiceImpl).updateBatchById(anyList());
    }

    @Test
    @DisplayName("editDb - 混合表类型处理 - 成功")
    public void editDb_MixedTableTypes_Success() {
        // 准备数据 - 混合多种表类型
        List<RmsDbContentRequest> mixedRequests = new ArrayList<>();
        
        // 门店数据
        RmsDbContentRequest storeRequest = new RmsDbContentRequest();
        storeRequest.setTable("intl_rms_store");
        storeRequest.setContent(testStore);
        storeRequest.setUuid("uuid-mixed-1");
        mixedRequests.add(storeRequest);
        
        // 用户数据
        RmsDbContentRequest userRequest = new RmsDbContentRequest();
        userRequest.setTable("intl_rms_user");
        userRequest.setContent(testUser);
        userRequest.setUuid("uuid-mixed-2");
        mixedRequests.add(userRequest);
        
        // 阵地数据
        RmsDbContentRequest positionRequest = new RmsDbContentRequest();
        positionRequest.setTable("intl_rms_position");
        positionRequest.setContent(testPosition);
        positionRequest.setUuid("uuid-mixed-3");
        mixedRequests.add(positionRequest);

        RmsDbRequest mixedRequest = new RmsDbRequest();
        mixedRequest.setRmsDBContentList(mixedRequests);

        // Mock 所有服务
        when(rmsStoreServiceImpl.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());
        when(rmsStoreServiceImpl.saveBatch(anyList())).thenReturn(true);
        when(userServiceImpl.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());
        when(userServiceImpl.saveBatch(anyList())).thenReturn(true);
        when(intlPositionServiceImpl.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());
        when(intlPositionServiceImpl.saveBatch(anyList())).thenReturn(true);

        // 执行测试
        rmsSyncDbManager.editDb(mixedRequest);

        // 验证结果 - 所有服务都应该被调用
        verify(rmsStoreServiceImpl).list(any(LambdaQueryWrapper.class));
        verify(rmsStoreServiceImpl).saveBatch(anyList());
        verify(userServiceImpl).list(any(LambdaQueryWrapper.class));
        verify(userServiceImpl).saveBatch(anyList());
        verify(intlPositionServiceImpl).list(any(LambdaQueryWrapper.class));
        verify(intlPositionServiceImpl).saveBatch(anyList());
    }
}
