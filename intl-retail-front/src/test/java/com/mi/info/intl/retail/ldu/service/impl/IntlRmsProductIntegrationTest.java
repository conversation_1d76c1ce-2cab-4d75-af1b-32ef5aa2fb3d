package com.mi.info.intl.retail.ldu.service.impl;

import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.ProjectInfoDto;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.SearchProductReq;
import com.mi.info.intl.retail.ldu.infra.entity.IntlRmsProduct;
import com.mi.info.intl.retail.ldu.infra.mapper.IntlRmsProductMapper;
import com.mi.info.intl.retail.model.CommonApiResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * IntlRmsProduct 集成测试
 * 测试从API Service到Mapper的完整调用链
 * 
 * <AUTHOR>
 * @date 2025/1/27
 */
@ExtendWith(MockitoExtension.class)
class IntlRmsProductIntegrationTest {

    @Mock
    private IntlRmsProductMapper intlRmsProductMapper;

    @InjectMocks
    private IntlRmsProductServiceImpl intlRmsProductService;

    @InjectMocks
    private IntlRmsProductApiServiceImpl intlRmsProductApiService;

    private IntlRmsProduct mockProduct1;
    private IntlRmsProduct mockProduct2;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        mockProduct1 = new IntlRmsProduct();
        mockProduct1.setId(1L);
        mockProduct1.setGoodsId("MI001");
        mockProduct1.setName("小米手机13");
        mockProduct1.setProjectCode("MI13");
        mockProduct1.setProductLine("智能手机");
        mockProduct1.setProductLineCode(1L);
        mockProduct1.setProductLineEn("Smartphone");

        mockProduct2 = new IntlRmsProduct();
        mockProduct2.setId(2L);
        mockProduct2.setGoodsId("MI002");
        mockProduct2.setName("小米手机13 Pro");
        mockProduct2.setProjectCode("MI13PRO");
        mockProduct2.setProductLine("智能手机");
        mockProduct2.setProductLineCode(1L);
        mockProduct2.setProductLineEn("Smartphone");

        // 设置API Service的依赖
        intlRmsProductApiService = new IntlRmsProductApiServiceImpl();
        // 使用反射设置私有字段
        try {
            java.lang.reflect.Field serviceField = IntlRmsProductApiServiceImpl.class.getDeclaredField("intlRmsProductService");
            serviceField.setAccessible(true);
            serviceField.set(intlRmsProductApiService, intlRmsProductService);
        } catch (Exception e) {
            throw new RuntimeException("Failed to set up test", e);
        }
    }

    @Test
    void integrationTest_SearchProductsByProjectCode_FuzzySearch_Success() {
        // Given - 测试模糊查询，现在只按project_code字段进行模糊匹配
        String projectCode = "MI";
        SearchProductReq req = new SearchProductReq();
        req.setKeyword(projectCode);
        req.setSearchType("FUZZY");
        List<IntlRmsProduct> mockProducts = Arrays.asList(mockProduct1, mockProduct2);
        when(intlRmsProductMapper.selectByProjectCodeFuzzy(projectCode)).thenReturn(mockProducts);

        // When - 测试Service层
        List<IntlRmsProduct> serviceResult = intlRmsProductService.searchByProjectCodeFuzzy(projectCode);

        // Then - 验证Service层结果
        assertNotNull(serviceResult);
        assertEquals(2, serviceResult.size());
        assertEquals("MI001", serviceResult.get(0).getGoodsId());
        assertEquals("小米手机13", serviceResult.get(0).getName());
        assertEquals("MI002", serviceResult.get(1).getGoodsId());
        assertEquals("小米手机13 Pro", serviceResult.get(1).getName());

        // When - 测试API Service层
        CommonApiResponse<List<ProjectInfoDto>> apiResponse = intlRmsProductApiService.searchProductsByProjectCode(req);

        // Then - 验证API Service层结果
        assertNotNull(apiResponse);
        assertEquals(200, apiResponse.getCode());
        assertEquals("success", apiResponse.getMessage());
        
        List<ProjectInfoDto> apiResult = apiResponse.getData();
        assertNotNull(apiResult);
        assertEquals(2, apiResult.size());
        assertEquals("MI001", apiResult.get(0).getGoodsId());
        assertEquals("小米手机13", apiResult.get(0).getName());
        assertEquals("智能手机", apiResult.get(0).getProductLine());
        assertEquals(1L, apiResult.get(0).getProductLineCode());
        assertEquals("Smartphone", apiResult.get(0).getProductLineEn());
        assertEquals("MI002", apiResult.get(1).getGoodsId());
        assertEquals("小米手机13 Pro", apiResult.get(1).getName());
        assertEquals("智能手机", apiResult.get(1).getProductLine());
        assertEquals(1L, apiResult.get(1).getProductLineCode());
        assertEquals("Smartphone", apiResult.get(1).getProductLineEn());

        // 验证Mapper被调用了两次（Service层一次，API Service层一次）
        verify(intlRmsProductMapper, times(2)).selectByProjectCodeFuzzy(projectCode);
    }

    @Test
    void integrationTest_SearchProductsByProjectCode_ExactSearch_Success() {
        // Given
        String projectCode = "MI13";
        SearchProductReq req = new SearchProductReq();
        req.setKeyword(projectCode);
        req.setSearchType("EXACT");
        List<IntlRmsProduct> mockProducts = Arrays.asList(mockProduct1);
        when(intlRmsProductMapper.selectByProjectCodeExact(projectCode)).thenReturn(mockProducts);

        // When - 测试Service层
        List<IntlRmsProduct> serviceResult = intlRmsProductService.searchByProjectCodeExact(projectCode);

        // Then - 验证Service层结果
        assertNotNull(serviceResult);
        assertEquals(1, serviceResult.size());
        assertEquals("MI001", serviceResult.get(0).getGoodsId());
        assertEquals("小米手机13", serviceResult.get(0).getName());

        // When - 测试API Service层
        CommonApiResponse<List<ProjectInfoDto>> apiResponse = intlRmsProductApiService.searchProductsByProjectCode(req);

        // Then - 验证API Service层结果
        assertNotNull(apiResponse);
        assertEquals(200, apiResponse.getCode());
        assertEquals("success", apiResponse.getMessage());
        
        List<ProjectInfoDto> apiResult = apiResponse.getData();
        assertNotNull(apiResult);
        assertEquals(1, apiResult.size());
        assertEquals("MI001", apiResult.get(0).getGoodsId());
        assertEquals("小米手机13", apiResult.get(0).getName());
        assertEquals("MI13", apiResult.get(0).getProjectCode());
        assertEquals("智能手机", apiResult.get(0).getProductLine());
        assertEquals(1L, apiResult.get(0).getProductLineCode());
        assertEquals("Smartphone", apiResult.get(0).getProductLineEn());

        // 验证Mapper被调用了两次（Service层一次，API Service层一次）
        verify(intlRmsProductMapper, times(2)).selectByProjectCodeExact(projectCode);
    }

    @Test
    void integrationTest_SearchProductsByProjectCode_EmptyResult() {
        // Given
        String projectCode = "NONEXISTENT";
        SearchProductReq req = new SearchProductReq();
        req.setKeyword(projectCode);
        req.setSearchType("FUZZY");
        when(intlRmsProductMapper.selectByProjectCodeFuzzy(projectCode)).thenReturn(Collections.emptyList());

        // When - 测试Service层
        List<IntlRmsProduct> serviceResult = intlRmsProductService.searchByProjectCodeFuzzy(projectCode);

        // Then - 验证Service层结果
        assertNotNull(serviceResult);
        assertTrue(serviceResult.isEmpty());

        // When - 测试API Service层
        CommonApiResponse<List<ProjectInfoDto>> apiResponse = intlRmsProductApiService.searchProductsByProjectCode(req);

        // Then - 验证API Service层结果
        assertNotNull(apiResponse);
        assertEquals(200, apiResponse.getCode());
        assertEquals("success", apiResponse.getMessage());
        
        List<ProjectInfoDto> apiResult = apiResponse.getData();
        assertNotNull(apiResult);
        assertTrue(apiResult.isEmpty());

        // 验证Mapper被调用了两次
        verify(intlRmsProductMapper, times(2)).selectByProjectCodeFuzzy(projectCode);
    }

    @Test
    void integrationTest_SearchProductsByProjectCode_InvalidInput() {
        // Given
        String projectCode = null;
        SearchProductReq req = new SearchProductReq();
        req.setKeyword(projectCode);

        // When - 测试Service层（应该返回空列表）
        List<IntlRmsProduct> serviceResult = intlRmsProductService.searchByProjectCodeFuzzy(projectCode);

        // Then - 验证Service层结果
        assertNotNull(serviceResult);
        assertTrue(serviceResult.isEmpty());

        // When - 测试API Service层
        CommonApiResponse<List<ProjectInfoDto>> apiResponse = intlRmsProductApiService.searchProductsByProjectCode(req);

        // Then - 验证API Service层结果
        assertNotNull(apiResponse);
        assertEquals(200, apiResponse.getCode());
        assertEquals("success", apiResponse.getMessage());
        
        List<ProjectInfoDto> apiResult = apiResponse.getData();
        assertNotNull(apiResult);
        assertTrue(apiResult.isEmpty());

        // 验证Mapper没有被调用（因为参数验证在Service层就拦截了）
        verify(intlRmsProductMapper, never()).selectByProjectCodeFuzzy(anyString());
        verify(intlRmsProductMapper, never()).selectByProjectCodeExact(anyString());
    }

    @Test
    void integrationTest_SearchProductsByProjectCode_ExceptionHandling() {
        // Given
        String projectCode = "MI";
        SearchProductReq req = new SearchProductReq();
        req.setKeyword(projectCode);
        req.setSearchType("FUZZY");
        when(intlRmsProductMapper.selectByProjectCodeFuzzy(projectCode)).thenThrow(new RuntimeException("Database error"));

        // When - 测试Service层
        List<IntlRmsProduct> serviceResult = intlRmsProductService.searchByProjectCodeFuzzy(projectCode);

        // Then - 验证Service层结果（异常被捕获，返回空列表）
        assertNotNull(serviceResult);
        assertTrue(serviceResult.isEmpty());

        // When - 测试API Service层
        CommonApiResponse<List<ProjectInfoDto>> apiResponse = intlRmsProductApiService.searchProductsByProjectCode(req);

        // Then - 验证API Service层结果
        assertNotNull(apiResponse);
        assertEquals(500, apiResponse.getCode());
        assertTrue(apiResponse.getMessage().contains("查询失败"));
        assertNull(apiResponse.getData());

        // 验证Mapper被调用了两次
        verify(intlRmsProductMapper, times(2)).selectByProjectCodeFuzzy(projectCode);
    }

    @Test
    void integrationTest_SearchProductsByProjectCode_TrimmedInput() {
        // Given
        String projectCode = "  MI  ";
        String trimmedProjectCode = "MI";
        SearchProductReq req = new SearchProductReq();
        req.setKeyword(projectCode);
        req.setSearchType("FUZZY");
        List<IntlRmsProduct> mockProducts = Arrays.asList(mockProduct1, mockProduct2);
        when(intlRmsProductMapper.selectByProjectCodeFuzzy(trimmedProjectCode)).thenReturn(mockProducts);

        // When - 测试Service层
        List<IntlRmsProduct> serviceResult = intlRmsProductService.searchByProjectCodeFuzzy(projectCode);

        // Then - 验证Service层结果
        assertNotNull(serviceResult);
        assertEquals(2, serviceResult.size());

        // When - 测试API Service层
        CommonApiResponse<List<ProjectInfoDto>> apiResponse = intlRmsProductApiService.searchProductsByProjectCode(req);

        // Then - 验证API Service层结果
        assertNotNull(apiResponse);
        assertEquals(200, apiResponse.getCode());
        assertEquals("success", apiResponse.getMessage());
        
        List<ProjectInfoDto> apiResult = apiResponse.getData();
        assertNotNull(apiResult);
        assertEquals(2, apiResult.size());

        // 验证Mapper被调用了两次，都使用trimmed的projectCode
        verify(intlRmsProductMapper, times(2)).selectByProjectCodeFuzzy(trimmedProjectCode);
    }

    @Test
    void integrationTest_SearchProductsByProjectCode_MaxResults() {
        // Given
        String projectCode = "MI";
        SearchProductReq req = new SearchProductReq();
        req.setKeyword(projectCode);
        req.setSearchType("FUZZY");
        List<IntlRmsProduct> mockProducts = Arrays.asList(mockProduct1, mockProduct2);
        when(intlRmsProductMapper.selectByProjectCodeFuzzy(projectCode)).thenReturn(mockProducts);

        // When - 测试Service层
        List<IntlRmsProduct> serviceResult = intlRmsProductService.searchByProjectCodeFuzzy(projectCode);

        // Then - 验证Service层结果
        assertNotNull(serviceResult);
        assertEquals(2, serviceResult.size());

        // When - 测试API Service层
        CommonApiResponse<List<ProjectInfoDto>> apiResponse = intlRmsProductApiService.searchProductsByProjectCode(req);

        // Then - 验证API Service层结果
        assertNotNull(apiResponse);
        assertEquals(200, apiResponse.getCode());
        assertEquals("success", apiResponse.getMessage());
        
        List<ProjectInfoDto> apiResult = apiResponse.getData();
        assertNotNull(apiResult);
        assertEquals(2, apiResult.size());

        // 验证结果按projectCode排序
        assertEquals("MI13", apiResult.get(0).getProjectCode());
        assertEquals("MI13PRO", apiResult.get(1).getProjectCode());

        // 验证Mapper被调用了两次
        verify(intlRmsProductMapper, times(2)).selectByProjectCodeFuzzy(projectCode);
    }

    @Test
    void integrationTest_SearchProductsByProjectCode_ProjectCodeOnlyFuzzySearch() {
        // Given - 验证新的查询逻辑：只按project_code字段进行模糊匹配，不再匹配project_name_cn和project_name_en
        String projectCode = "MI13";
        SearchProductReq req = new SearchProductReq();
        req.setKeyword(projectCode);
        req.setSearchType("FUZZY");
        
        // 模拟数据：只有project_code包含"MI13"的产品会被返回
        List<IntlRmsProduct> mockProducts = Arrays.asList(mockProduct1);
        when(intlRmsProductMapper.selectByProjectCodeFuzzy(projectCode)).thenReturn(mockProducts);

        // When - 测试Service层
        List<IntlRmsProduct> serviceResult = intlRmsProductService.searchByProjectCodeFuzzy(projectCode);

        // Then - 验证Service层结果
        assertNotNull(serviceResult);
        assertEquals(1, serviceResult.size());
        assertEquals("MI001", serviceResult.get(0).getGoodsId());
        assertEquals("MI13", serviceResult.get(0).getProjectCode());
        
        // 验证返回的产品确实是通过project_code匹配的，而不是通过project_name_cn或project_name_en
        IntlRmsProduct returnedProduct = serviceResult.get(0);
        assertTrue(returnedProduct.getProjectCode().contains(projectCode), 
            "返回的产品应该通过project_code字段匹配");
        assertNotNull(returnedProduct.getProjectNameCn(), "project_name_cn字段应该存在但不参与匹配");
        assertNotNull(returnedProduct.getProjectNameEn(), "project_name_en字段应该存在但不参与匹配");

        // When - 测试API Service层
        CommonApiResponse<List<ProjectInfoDto>> apiResponse = intlRmsProductApiService.searchProductsByProjectCode(req);

        // Then - 验证API Service层结果
        assertNotNull(apiResponse);
        assertEquals(200, apiResponse.getCode());
        assertEquals("success", apiResponse.getMessage());
        
        List<ProjectInfoDto> apiResult = apiResponse.getData();
        assertNotNull(apiResult);
        assertEquals(1, apiResult.size());
        assertEquals("MI001", apiResult.get(0).getGoodsId());
        assertEquals("MI13", apiResult.get(0).getProjectCode());

        // 验证Mapper被调用了两次
        verify(intlRmsProductMapper, times(2)).selectByProjectCodeFuzzy(projectCode);
    }
}
