package com.mi.info.intl.retail.org.app.service.material;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mi.info.intl.retail.exception.RetailRunTimeException;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.*;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.model.PageResponse;
import com.mi.info.intl.retail.org.domain.material.service.NewInspectionConfigDomainService;
import com.xiaomi.com.i18n.area.Area;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import org.springframework.test.util.ReflectionTestUtils;

@ExtendWith(MockitoExtension.class)
public class NewProductInspectionServiceImplTest {

    private static final Logger log = LoggerFactory.getLogger(NewProductInspectionServiceImplTest.class);

    @InjectMocks
    private NewProductInspectionServiceImpl newProductInspectionService;

    @Mock
    private NewInspectionConfigDomainService newInspectionConfigDomainService;

    private MockedStatic<Area> areaMockedStatic;

    @BeforeEach
    public void setUp() {
        // Mock Area类的静态方法
        areaMockedStatic = Mockito.mockStatic(Area.class);
    }

    @AfterEach
    public void tearDown() {
        // 释放mock的静态方法
        if (areaMockedStatic != null) {
            areaMockedStatic.close();
        }
    }

    /**
     * 测试 getEnumByType 方法 - 正常情况
     */
    @Test
    public void testGetEnumByType_Normal() {
        // 准备测试数据
        InspectionTaskConfigurationPageRequest request = new InspectionTaskConfigurationPageRequest();
        request.setType("1");

        List<Map<String, Object>> expectedResult = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();
        map.put("code", "code");
        map.put("name", "name");
        expectedResult.add(map);

        // Mock依赖方法
        when(newInspectionConfigDomainService.getEnumByType(request)).thenReturn(expectedResult);

        // 执行测试
        CommonApiResponse<List<Map<String, Object>>> response = newProductInspectionService.getEnumByType(request);

        // 验证结果
        assertNotNull(response);
        assertEquals(0, response.getCode()); // 成功状态码为0
        assertEquals(expectedResult, response.getData());
        verify(newInspectionConfigDomainService).getEnumByType(request);
    }

    /**
     * 测试 getEnumByType 方法 - 输入为null
     */
    @Test
    public void testGetEnumByType_NullInput() {
        // Mock依赖方法
        when(newInspectionConfigDomainService.getEnumByType(null)).thenReturn(new ArrayList<>());

        // 执行测试
        CommonApiResponse<List<Map<String, Object>>> response = newProductInspectionService.getEnumByType(null);

        // 验证结果
        assertNotNull(response);
        assertEquals(0, response.getCode());
        assertNotNull(response.getData());
        verify(newInspectionConfigDomainService).getEnumByType(null);
    }

    /**
     * 测试 findInspectionTaskPage 方法
     */
    @Test
    public void testFindInspectionTaskPage() {
        // 准备测试数据
        InspectionTaskConfigurationPageRequest request = new InspectionTaskConfigurationPageRequest();
        request.setPageNum(1L);
        request.setPageSize(10L);

        Page<InspectionTaskConfigurationPageResponse> pageResult = new Page<>(1, 10);
        pageResult.setTotal(100);
        List<InspectionTaskConfigurationPageResponse> records = new ArrayList<>();
        InspectionTaskConfigurationPageResponse record = new InspectionTaskConfigurationPageResponse();
        record.setId(1L);
        records.add(record);
        pageResult.setRecords(records);

        // Mock依赖方法
        when(newInspectionConfigDomainService.findInspectionTaskPage(any(Page.class), any(InspectionTaskConfigurationPageRequest.class)))
                .thenReturn(pageResult);

        // 执行测试
        CommonApiResponse<PageResponse<InspectionTaskConfigurationPageResponse>> response =
                newProductInspectionService.findInspectionTaskPage(request);

        // 验证结果
        assertNotNull(response);
        assertEquals(0, response.getCode());
        PageResponse<InspectionTaskConfigurationPageResponse> pageResponse = response.getData();
        assertNotNull(pageResponse);
        assertEquals(100, pageResponse.getTotalCount());
        assertEquals(1, pageResponse.getPageNum());
        assertEquals(10, pageResponse.getPageSize());
        assertEquals(1, pageResponse.getList().size());
        verify(newInspectionConfigDomainService).findInspectionTaskPage(any(Page.class), any(InspectionTaskConfigurationPageRequest.class));
    }

    /**
     * 测试 getInspectionTaskInfo 方法
     */
    @Test
    public void testGetInspectionTaskInfo() {
        // 准备测试数据
        InspectionTaskConfigurationRequest request = new InspectionTaskConfigurationRequest();
        request.setId(1L);
        request.setEnableTaskFlag("enable");

        InspectionTaskConfigurationInfoVO expectedVO = new InspectionTaskConfigurationInfoVO();
        expectedVO.setId(1L);
        expectedVO.setTaskName("testTask");

        // Mock依赖方法
        when(newInspectionConfigDomainService.getInspectionTaskInfo(request)).thenReturn(expectedVO);

        // 执行测试
        CommonApiResponse<InspectionTaskConfigurationInfoVO> response =
                newProductInspectionService.getInspectionTaskInfo(request);

        // 验证结果
        assertNotNull(response);
        assertEquals(0, response.getCode());
        assertEquals(expectedVO, response.getData());
        verify(newInspectionConfigDomainService).getInspectionTaskInfo(request);
    }

    /**
     * 测试 saveInspectionTask 方法 - 正常情况
     */
    @Test
    public void testSaveInspectionTask_Normal() {
        // 准备测试数据
        InspectionTaskConfigurationDTO dto = new InspectionTaskConfigurationDTO();
        dto.setTaskName("testTask");
        dto.setCountry("CN");

        InspectionTaskConfigurationInfoVO expectedVO = new InspectionTaskConfigurationInfoVO();
        expectedVO.setId(1L);
        expectedVO.setTaskName("testTask");

        // Mock依赖方法
        when(newInspectionConfigDomainService.insertOrUpdate(dto)).thenReturn(expectedVO);

        // 执行测试
        CommonApiResponse<InspectionTaskConfigurationInfoVO> response =
                newProductInspectionService.saveInspectionTask(dto);

        // 验证结果
        assertNotNull(response);
        assertEquals(0, response.getCode());
        assertEquals(expectedVO, response.getData());
        verify(newInspectionConfigDomainService).insertOrUpdate(dto);
    }

    /**
     * 测试 saveInspectionTask 方法 - 输入为null
     */
    @Test
    public void testSaveInspectionTask_NullInput() {
        // 执行测试
        CommonApiResponse<InspectionTaskConfigurationInfoVO> response =
                newProductInspectionService.saveInspectionTask(null);

        // 验证结果
        assertNotNull(response);
        assertEquals(0, response.getCode());
        assertNull(response.getData());
        verify(newInspectionConfigDomainService, never()).insertOrUpdate(any());
    }

    /**
     * 测试 saveInspectionTask 方法 - RetailRunTimeException异常情况
     */
    @Test
    public void testSaveInspectionTask_RetailRunTimeException() {
        // 准备测试数据
        InspectionTaskConfigurationDTO dto = new InspectionTaskConfigurationDTO();
        dto.setTaskName("testTask");

        // Mock依赖方法抛出异常
        when(newInspectionConfigDomainService.insertOrUpdate(dto))
                .thenThrow(new RetailRunTimeException("业务异常"));

        // 执行测试
        CommonApiResponse<InspectionTaskConfigurationInfoVO> response =
                newProductInspectionService.saveInspectionTask(dto);

        // 验证结果
        assertNotNull(response);
        assertEquals(500, response.getCode());
        assertEquals("业务异常", response.getMessage());
        verify(newInspectionConfigDomainService).insertOrUpdate(dto);
    }

    /**
     * 测试 saveInspectionTask 方法 - 其他Exception异常情况
     */
    @Test
    public void testSaveInspectionTask_OtherException() {
        // 准备测试数据
        InspectionTaskConfigurationDTO dto = new InspectionTaskConfigurationDTO();
        dto.setTaskName("testTask");

        // Mock依赖方法抛出异常
        when(newInspectionConfigDomainService.insertOrUpdate(dto))
                .thenThrow(new RuntimeException("系统异常"));

        // 执行测试
        CommonApiResponse<InspectionTaskConfigurationInfoVO> response =
                newProductInspectionService.saveInspectionTask(dto);

        // 验证结果
        assertNotNull(response);
        assertEquals(500, response.getCode());
        assertEquals("保存巡检配置异常", response.getMessage());
        verify(newInspectionConfigDomainService).insertOrUpdate(dto);
    }

    /**
     * 测试 submitInspectionTask 方法 - 正常情况
     */
    @Test
    public void testSubmitInspectionTask_Normal() {
        // 准备测试数据
        InspectionTaskConfigurationDTO dto = new InspectionTaskConfigurationDTO();
        dto.setTaskName("testTask");

        // Mock依赖方法
        doNothing().when(newInspectionConfigDomainService).submitInspectionTaskConfiguration(dto);

        // 执行测试
        CommonApiResponse response = newProductInspectionService.submitInspectionTask(dto);

        // 验证结果
        assertNotNull(response);
        assertEquals(0, response.getCode());
        verify(newInspectionConfigDomainService).submitInspectionTaskConfiguration(dto);
    }

    /**
     * 测试 submitInspectionTask 方法 - RetailRunTimeException异常情况
     */
    @Test
    public void testSubmitInspectionTask_RetailRunTimeException() {
        // 准备测试数据
        InspectionTaskConfigurationDTO dto = new InspectionTaskConfigurationDTO();
        dto.setTaskName("testTask");

        // Mock依赖方法抛出异常
        doThrow(new RetailRunTimeException("业务异常"))
                .when(newInspectionConfigDomainService).submitInspectionTaskConfiguration(dto);

        // 执行测试
        CommonApiResponse response = newProductInspectionService.submitInspectionTask(dto);

        // 验证结果
        assertNotNull(response);
        assertEquals(500, response.getCode());
        assertEquals("业务异常", response.getMessage());
        verify(newInspectionConfigDomainService).submitInspectionTaskConfiguration(dto);
    }

    /**
     * 测试 submitInspectionTask 方法 - 其他Exception异常情况
     */
    @Test
    public void testSubmitInspectionTask_OtherException() {
        // 准备测试数据
        InspectionTaskConfigurationDTO dto = new InspectionTaskConfigurationDTO();
        dto.setTaskName("testTask");

        // Mock依赖方法抛出异常
        doThrow(new RuntimeException("系统异常"))
                .when(newInspectionConfigDomainService).submitInspectionTaskConfiguration(dto);

        // 执行测试
        CommonApiResponse response = newProductInspectionService.submitInspectionTask(dto);

        // 验证结果
        assertNotNull(response);
        assertEquals(500, response.getCode());
        assertEquals("启用巡检配置异常", response.getMessage());
        verify(newInspectionConfigDomainService).submitInspectionTaskConfiguration(dto);
    }

    /**
     * 测试 startOrStopInspectionTask 方法 - 正常情况
     */
    @Test
    public void testStartOrStopInspectionTask_Normal() {
        // 准备测试数据
        InspectionTaskConfigurationRequest request = new InspectionTaskConfigurationRequest();
        request.setId(1L);
        request.setEnableTaskFlag("enable");

        // Mock依赖方法
        doNothing().when(newInspectionConfigDomainService).startOrStopInspectionTask(request);

        // 执行测试
        CommonApiResponse response = newProductInspectionService.startOrStopInspectionTask(request);

        // 验证结果
        assertNotNull(response);
        assertEquals(0, response.getCode());
        verify(newInspectionConfigDomainService).startOrStopInspectionTask(request);
    }

    /**
     * 测试 startOrStopInspectionTask 方法 - RetailRunTimeException异常情况
     */
    @Test
    public void testStartOrStopInspectionTask_RetailRunTimeException() {
        // 准备测试数据
        InspectionTaskConfigurationRequest request = new InspectionTaskConfigurationRequest();
        request.setId(1L);
        request.setEnableTaskFlag("enable");

        // Mock依赖方法抛出异常
        doThrow(new RetailRunTimeException("业务异常"))
                .when(newInspectionConfigDomainService).startOrStopInspectionTask(request);

        // 执行测试
        CommonApiResponse response = newProductInspectionService.startOrStopInspectionTask(request);

        // 验证结果
        assertNotNull(response);
        assertEquals(500, response.getCode());
        assertEquals("业务异常", response.getMessage());
        verify(newInspectionConfigDomainService).startOrStopInspectionTask(request);
    }

    /**
     * 测试 startOrStopInspectionTask 方法 - 其他Exception异常情况
     */
    @Test
    public void testStartOrStopInspectionTask_OtherException() {
        // 准备测试数据
        InspectionTaskConfigurationRequest request = new InspectionTaskConfigurationRequest();
        request.setId(1L);
        request.setEnableTaskFlag("enable");

        // Mock依赖方法抛出异常
        doThrow(new RuntimeException("系统异常"))
                .when(newInspectionConfigDomainService).startOrStopInspectionTask(request);

        // 执行测试
        CommonApiResponse response = newProductInspectionService.startOrStopInspectionTask(request);

        // 验证结果
        assertNotNull(response);
        assertEquals(500, response.getCode());
        assertEquals("启停巡检配置异常", response.getMessage());
        verify(newInspectionConfigDomainService).startOrStopInspectionTask(request);
    }

    /**
     * 测试 exportInspectionTask 方法
     */
    @Test
    public void testExportInspectionTask() {
        // 准备测试数据
        InspectionTaskConfigurationPageRequest request = new InspectionTaskConfigurationPageRequest();
        request.setTaskName("testTask");

        // Mock依赖方法
        doNothing().when(newInspectionConfigDomainService).exportInspectionTask(request);

        // 执行测试
        CommonApiResponse response = newProductInspectionService.exportInspectionTask(request);

        // 验证结果
        assertNotNull(response);
        assertEquals(0, response.getCode());
        verify(newInspectionConfigDomainService).exportInspectionTask(request);
    }

    /**
     * 测试 uploadAssignedStore 方法
     */
    @Test
    public void testUploadAssignedStore() {
        // 准备测试数据
        InspectionTaskConfigurationRequest request = new InspectionTaskConfigurationRequest();
        request.setId(1L);

        InspectionTaskConfigurationDTO expectedDTO = new InspectionTaskConfigurationDTO();
        expectedDTO.setId(1L);
        expectedDTO.setTaskName("testTask");

        // Mock依赖方法
        when(newInspectionConfigDomainService.uploadAssignedStore(request)).thenReturn(expectedDTO);

        // 执行测试
        CommonApiResponse<InspectionTaskConfigurationDTO> response =
                newProductInspectionService.uploadAssignedStore(request);

        // 验证结果
        assertNotNull(response);
        assertEquals(0, response.getCode());
        assertEquals(expectedDTO, response.getData());
        verify(newInspectionConfigDomainService).uploadAssignedStore(request);
    }

    /**
     * 测试 listArea 方法
     */
    @Test
    public void testListArea() {
        // 准备测试数据
        List<Area> expectedAreas = new ArrayList<>();
        Area area = new Area();
        expectedAreas.add(area);

        // Mock静态方法
        areaMockedStatic.when(Area::all).thenReturn(expectedAreas);

        // 执行测试
        CommonApiResponse<List<Area>> response = newProductInspectionService.listArea();

        // 验证结果
        assertNotNull(response);
        assertEquals(0, response.getCode());
        assertEquals(expectedAreas, response.getData());
        areaMockedStatic.verify(Area::all);
    }

    /**
     * 测试用例1：正常返回模板URL
     */
    @Test
    public void testGetImportTemplate_Normal() {
        // 准备数据：设置 newAssignStore 字段为有效URL
        String templateURL = "http://example.com/template.xlsx";
        ReflectionTestUtils.setField(newProductInspectionService, "newAssignStore", templateURL);

        // 调用被测方法
        CommonApiResponse<String> response = newProductInspectionService.getImportTemplate();

        // 验证结果
        assertNotNull(response);
        assertEquals(0, response.getCode());
        assertEquals(templateURL, response.getData());
    }
}
