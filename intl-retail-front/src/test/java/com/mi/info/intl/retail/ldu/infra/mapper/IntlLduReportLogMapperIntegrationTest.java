package com.mi.info.intl.retail.ldu.infra.mapper;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.List;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

/**
 * IntlLduReportLogMapper 集成测试类
 * 用于测试真实的数据库连接
 * 注意：需要配置测试数据库环境
 */
//@SpringBootTest
@ActiveProfiles("test")
@DisplayName("IntlLduReportLogMapper 集成测试")
@Transactional
class IntlLduReportLogMapperIntegrationTest {

    @Autowired
    private IntlLduReportLogMapper intlLduReportLogMapper;

    @Test
    @DisplayName("集成测试：查询所有不重复的 project_code")
    void testSelectDistinctProjectCodes_Integration() {
        // 执行查询
        List<String> projectCodes = intlLduReportLogMapper.selectDistinctProjectCodes();
        
        // 验证结果
        assertNotNull(projectCodes, "返回的列表不应为空");
        
        // 验证去重功能
        long distinctCount = projectCodes.stream().distinct().count();
        assertEquals(projectCodes.size(), distinctCount, "结果应该已经去重");
        
        // 验证排序功能（按字母顺序）
        for (int i = 0; i < projectCodes.size() - 1; i++) {
            assertTrue(projectCodes.get(i).compareTo(projectCodes.get(i + 1)) <= 0, 
                "结果应该按字母顺序排序");
        }
        
        // 验证过滤空值功能
        boolean hasNull = projectCodes.stream().anyMatch(code -> code == null);
        assertFalse(hasNull, "结果不应包含 null 值");
        
        boolean hasEmpty = projectCodes.stream().anyMatch(code -> code.isEmpty());
        assertFalse(hasEmpty, "结果不应包含空字符串");
        
        // 打印结果用于调试
        System.out.println("集成测试 - 查询到的 project_code 数量: " + projectCodes.size());
        if (!projectCodes.isEmpty()) {
            System.out.println("集成测试 - 前5个 project_code: " + projectCodes.subList(0, Math.min(5, projectCodes.size())));
        }
    }

    @Test
    @DisplayName("集成测试：验证查询性能")
    void testSelectDistinctProjectCodes_Performance() {
        // 记录开始时间
        long startTime = System.currentTimeMillis();
        
        // 执行查询
        List<String> projectCodes = intlLduReportLogMapper.selectDistinctProjectCodes();
        
        // 记录结束时间
        long endTime = System.currentTimeMillis();
        long executionTime = endTime - startTime;
        
        // 验证结果
        assertNotNull(projectCodes, "返回的列表不应为空");
        
        // 性能验证（查询应该在合理时间内完成）
        assertTrue(executionTime < 10000, "查询应该在10秒内完成，实际耗时: " + executionTime + "ms");
        
        System.out.println("集成测试 - 查询耗时: " + executionTime + "ms");
        System.out.println("集成测试 - 查询结果数量: " + projectCodes.size());
    }

    @Test
    @DisplayName("集成测试：验证查询结果的一致性")
    void testSelectDistinctProjectCodes_Consistency() {
        // 执行多次查询
        List<String> firstResult = intlLduReportLogMapper.selectDistinctProjectCodes();
        List<String> secondResult = intlLduReportLogMapper.selectDistinctProjectCodes();
        List<String> thirdResult = intlLduReportLogMapper.selectDistinctProjectCodes();
        
        // 验证结果一致性
        assertNotNull(firstResult, "第一次查询结果不应为空");
        assertNotNull(secondResult, "第二次查询结果不应为空");
        assertNotNull(thirdResult, "第三次查询结果不应为空");
        
        assertEquals(firstResult.size(), secondResult.size(), "查询结果数量应该一致");
        assertEquals(secondResult.size(), thirdResult.size(), "查询结果数量应该一致");
        
        // 验证内容一致性
        assertEquals(firstResult, secondResult, "查询结果内容应该一致");
        assertEquals(secondResult, thirdResult, "查询结果内容应该一致");
        
        System.out.println("集成测试 - 查询结果一致性验证通过，结果数量: " + firstResult.size());
    }
} 