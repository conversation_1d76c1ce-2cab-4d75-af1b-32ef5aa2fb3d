package com.mi.info.intl.retail.org.infra.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.InspectionSummaryDTO;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.PositionInspectionRequest;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.UnRemindedTaskDTO;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.InspectionBusinessTypeEnum;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.DisapproveReasonEnum;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.InspectionStatusEnum;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.TaskStatusEnum;
import com.mi.info.intl.retail.org.domain.InspectionRecordDomain;
import com.mi.info.intl.retail.org.domain.dto.InspectionRecordDTO;
import com.mi.info.intl.retail.org.infra.entity.InspectionRecord;
import com.mi.info.intl.retail.org.infra.mapper.InspectionRecordMapper;
import com.mi.info.intl.retail.org.infra.mapper.read.InspectionRecordReadMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * InspectionRecordRepositoryImpl 单元测试类
 */
@ExtendWith(MockitoExtension.class)
class InspectionRecordRepositoryImplTest {

    @InjectMocks
    private InspectionRecordRepositoryImpl inspectionRecordRepository;

    @Mock
    private InspectionRecordMapper inspectionRecordMapper;

    @Mock
    private InspectionRecordReadMapper inspectionRecordReadMapper;

    private InspectionRecordDomain testDomain;
    private InspectionRecord testEntity;
    private InspectionRecordDTO testDto;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        testDomain = new InspectionRecordDomain();
        testDomain.setId(1L);
        testDomain.setRuleCode("RULE_001");
        testDomain.setPositionCode("POS_001");
        testDomain.setCountry("CN");
        testDomain.setTaskStatus(TaskStatusEnum.NOT_COMPLETED);
        testDomain.setInspectionStatus(InspectionStatusEnum.TO_BE_VERIFIED);
        testDomain.setInspectionOwner("testUser");
        testDomain.setInspectionOwnerMiId(12345L);

        testEntity = new InspectionRecord();
        testEntity.setId(1L);
        testEntity.setRuleCode("RULE_001");
        testEntity.setBusinessCode("POS_001");
        testEntity.setTaskStatus(TaskStatusEnum.NOT_COMPLETED.getCode());
        testEntity.setInspectionStatus(InspectionStatusEnum.TO_BE_VERIFIED.getCode());
        testEntity.setInspectionOwner("testUser");
        testEntity.setInspectionOwnerMiId(12345L);

        testDto = new InspectionRecordDTO();
        testDto.setId(1L);
        testDto.setRuleCode("RULE_001");
        testDto.setBusinessCode("POS_001");
        testDto.setCountry("CN");
        testDto.setTaskStatus(TaskStatusEnum.NOT_COMPLETED.getCode());
        testDto.setInspectionStatus(InspectionStatusEnum.TO_BE_VERIFIED.getCode());
        testDto.setInspectionOwner("testUser");
        testDto.setInspectionOwnerMiId(12345L);
    }

    @Test
    @DisplayName("保存巡检记录 - 成功")
    void save_Success() {
        // 准备数据
        when(inspectionRecordMapper.insert(any(InspectionRecord.class))).thenReturn(1);

        // 执行测试
        boolean result = inspectionRecordRepository.save(testDomain);

        // 验证结果
        assertTrue(result);
        verify(inspectionRecordMapper).insert(any(InspectionRecord.class));
    }

    @Test
    @DisplayName("保存巡检记录 - 失败")
    void save_Failure() {
        // 准备数据
        when(inspectionRecordMapper.insert(any(InspectionRecord.class))).thenReturn(0);

        // 执行测试
        boolean result = inspectionRecordRepository.save(testDomain);

        // 验证结果
        assertFalse(result);
        verify(inspectionRecordMapper).insert(any(InspectionRecord.class));
    }

    @Test
    @DisplayName("根据ID获取巡检记录 - 成功")
    void getById_Success() {
        // 准备数据
        when(inspectionRecordReadMapper.selectById(1L)).thenReturn(testEntity);

        // 执行测试
        InspectionRecordDomain result = inspectionRecordRepository.getById(1L);

        // 验证结果
        assertNotNull(result);
        assertEquals(1L, result.getId());
        assertEquals("RULE_001", result.getRuleCode());
        assertEquals("POS_001", result.getPositionCode());
        verify(inspectionRecordReadMapper).selectById(1L);
    }

    @Test
    @DisplayName("根据ID获取巡检记录 - 不存在")
    void getById_NotFound() {
        // 准备数据
        when(inspectionRecordReadMapper.selectById(999L)).thenReturn(null);

        // 执行测试
        InspectionRecordDomain result = inspectionRecordRepository.getById(999L);

        // 验证结果
        assertNull(result);
        verify(inspectionRecordReadMapper).selectById(999L);
    }

    @Test
    @DisplayName("根据业务代码获取巡检记录列表 - 成功")
    void getByBusinessCode_Success() {
        // 准备数据
        List<InspectionRecord> entities = Arrays.asList(testEntity);
        when(inspectionRecordReadMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(entities);

        // 执行测试
        List<InspectionRecordDomain> result = inspectionRecordRepository.getByBusinessCode("POS_001");

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("POS_001", result.get(0).getPositionCode());
        verify(inspectionRecordReadMapper).selectList(any(LambdaQueryWrapper.class));
    }

    @Test
    @DisplayName("根据业务代码获取巡检记录列表 - 空结果")
    void getByBusinessCode_EmptyResult() {
        // 准备数据
        when(inspectionRecordReadMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // 执行测试
        List<InspectionRecordDomain> result = inspectionRecordRepository.getByBusinessCode("POS_001");

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(inspectionRecordReadMapper).selectList(any(LambdaQueryWrapper.class));
    }

    @Test
    @DisplayName("根据业务代码列表获取巡检记录 - 成功")
    void getByBusinessCodeList_Success() {
        // 准备数据
        List<String> businessCodes = Arrays.asList("POS_001", "POS_002");
        List<InspectionRecord> entities = Arrays.asList(testEntity);
        when(inspectionRecordReadMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(entities);

        // 执行测试
        List<InspectionRecordDomain> result = inspectionRecordRepository.getByBusinessCodeList(businessCodes);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        verify(inspectionRecordReadMapper).selectList(any(LambdaQueryWrapper.class));
    }

    @Test
    @DisplayName("获取待审批的巡检记录 - 成功")
    void getPendingApprovalByBusinessCode_Success() {
        // 准备数据
        List<InspectionRecord> entities = Arrays.asList(testEntity);
        when(inspectionRecordReadMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(entities);

        // 执行测试
        List<InspectionRecordDomain> result = inspectionRecordRepository.getPendingApprovalByBusinessCode("POS_001");

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        verify(inspectionRecordReadMapper).selectList(any(LambdaQueryWrapper.class));
    }

    @Test
    @DisplayName("更新巡检记录 - 成功")
    void update_Success() {
        // 准备数据
        when(inspectionRecordMapper.updateById(any(InspectionRecord.class))).thenReturn(1);

        // 执行测试
        boolean result = inspectionRecordRepository.update(testDomain);

        // 验证结果
        assertTrue(result);
        verify(inspectionRecordMapper).updateById(any(InspectionRecord.class));
    }

    @Test
    @DisplayName("更新巡检记录 - 失败")
    void update_Failure() {
        // 准备数据
        when(inspectionRecordMapper.updateById(any(InspectionRecord.class))).thenReturn(0);

        // 执行测试
        boolean result = inspectionRecordRepository.update(testDomain);

        // 验证结果
        assertFalse(result);
        verify(inspectionRecordMapper).updateById(any(InspectionRecord.class));
    }

    @Test
    @DisplayName("根据规则代码和业务代码获取巡检记录 - 成功")
    void getByRuleCodeAndBusinessCode_Success() {
        // 准备数据
        when(inspectionRecordReadMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(testEntity);

        // 执行测试
        InspectionRecordDomain result = inspectionRecordRepository.getByRuleCodeAndBusinessCode("RULE_001", "POS_001");

        // 验证结果
        assertNotNull(result);
        assertEquals("RULE_001", result.getRuleCode());
        assertEquals("POS_001", result.getPositionCode());
        verify(inspectionRecordReadMapper).selectOne(any(LambdaQueryWrapper.class));
    }

    @Test
    @DisplayName("根据规则代码和业务代码获取巡检记录 - 不存在")
    void getByRuleCodeAndBusinessCode_NotFound() {
        // 准备数据
        when(inspectionRecordReadMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // 执行测试
        InspectionRecordDomain result = inspectionRecordRepository.getByRuleCodeAndBusinessCode("RULE_001", "POS_001");

        // 验证结果
        assertNull(result);
        verify(inspectionRecordReadMapper).selectOne(any(LambdaQueryWrapper.class));
    }

    @Test
    @DisplayName("根据国家列表获取待巡检记录 - 成功")
    void getPendingInspectionsByCountries_Success() {
        // 准备数据
        List<String> countries = Arrays.asList("CN", "US");
        List<InspectionRecordDTO> dtos = Arrays.asList(testDto);
        when(inspectionRecordReadMapper.selectPendingInspectionsByCountries(countries, InspectionBusinessTypeEnum.getPostionInspectionCodeList())).thenReturn(dtos);

        // 执行测试
        List<InspectionRecordDTO> result = inspectionRecordRepository.getPendingInspectionsByCountries(countries);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        verify(inspectionRecordReadMapper).selectPendingInspectionsByCountries(countries, InspectionBusinessTypeEnum.getPostionInspectionCodeList());
    }

    @Test
    @DisplayName("根据国家列表获取待巡检记录 - 空国家列表")
    void getPendingInspectionsByCountries_EmptyCountries() {
        // 执行测试
        List<InspectionRecordDTO> result = inspectionRecordRepository.getPendingInspectionsByCountries(Collections.emptyList());

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(inspectionRecordReadMapper, never()).selectPendingInspectionsByCountries(any(), any());
    }

    @Test
    @DisplayName("根据国家列表获取待巡检记录 - null国家列表")
    void getPendingInspectionsByCountries_NullCountries() {
        // 执行测试
        List<InspectionRecordDTO> result = inspectionRecordRepository.getPendingInspectionsByCountries(null);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(inspectionRecordReadMapper, never()).selectPendingInspectionsByCountries(any(), any());
    }

    @Test
    @DisplayName("根据业务代码和施工行为代码获取巡检记录 - 成功")
    void getByBusinessCodeAndConstructionActionCode_Success() {
        // 准备数据
        when(inspectionRecordReadMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(testEntity);

        // 执行测试
        InspectionRecordDomain result = inspectionRecordRepository.getByBusinessCodeAndConstructionActionCode("POS_001", "ACTION_001");

        // 验证结果
        assertNotNull(result);
        assertEquals("POS_001", result.getPositionCode());
        verify(inspectionRecordReadMapper).selectOne(any(LambdaQueryWrapper.class));
    }

    @Test
    @DisplayName("根据业务代码和施工行为代码获取巡检记录 - 不存在")
    void getByBusinessCodeAndConstructionActionCode_NotFound() {
        // 准备数据
        when(inspectionRecordReadMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // 执行测试
        InspectionRecordDomain result = inspectionRecordRepository.getByBusinessCodeAndConstructionActionCode("POS_001", "ACTION_001");

        // 验证结果
        assertNull(result);
        verify(inspectionRecordReadMapper).selectOne(any(LambdaQueryWrapper.class));
    }

    // @Test
    @DisplayName("检查巡检负责人是否有未完成任务 - 有未完成任务")
    void existsUnCompletedTaskByOwner_HasUncompletedTask() {
        // 准备数据
        when(inspectionRecordReadMapper.exists(any(LambdaQueryWrapper.class))).thenReturn(true);

        // 执行测试
        boolean result = inspectionRecordRepository.existsUnCompletedTaskByOwner("testUser");

        // 验证结果
        assertTrue(result);
        verify(inspectionRecordReadMapper).exists(any(LambdaQueryWrapper.class));
    }

    // @Test
    @DisplayName("检查巡检负责人是否有未完成任务 - 无未完成任务")
    void existsUnCompletedTaskByOwner_NoUncompletedTask() {
        // 准备数据
        when(inspectionRecordReadMapper.exists(any(LambdaQueryWrapper.class))).thenReturn(false);

        // 执行测试
        boolean result = inspectionRecordRepository.existsUnCompletedTaskByOwner("testUser");

        // 验证结果
        assertFalse(result);
        verify(inspectionRecordReadMapper).exists(any(LambdaQueryWrapper.class));
    }

    @Test
    @DisplayName("查找激活任务和激活规则 - 成功")
    void findActiveTasksWithActiveRules_Success() {
        // 准备数据
        List<UnRemindedTaskDTO> dtos = Arrays.asList(new UnRemindedTaskDTO());
        when(inspectionRecordReadMapper.findActiveTasksWithActiveRules()).thenReturn(dtos);

        // 执行测试
        List<UnRemindedTaskDTO> result = inspectionRecordRepository.findActiveTasksWithActiveRules();

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        verify(inspectionRecordReadMapper).findActiveTasksWithActiveRules();
    }

    @Test
    @DisplayName("获取巡检汇总信息 - 成功")
    void getInspectionSummary_Success() {
        // 准备数据
        PositionInspectionRequest request = new PositionInspectionRequest();
        request.setCreateTimeStart(1640995200L); // 2022-01-01 00:00:00
        request.setCreateTimeEnd(1641081600L);   // 2022-01-02 00:00:00
        
        InspectionSummaryDTO summaryDTO = new InspectionSummaryDTO();
        when(inspectionRecordReadMapper.getInspectionSummary(any(PositionInspectionRequest.class))).thenReturn(summaryDTO);

        // 执行测试
        InspectionSummaryDTO result = inspectionRecordRepository.getInspectionSummary(request);

        // 验证结果
        assertNotNull(result);
        assertEquals(1640995200000L, request.getCreateTimeStart()); // 验证时间戳转换
        assertEquals(1641168000000L, request.getCreateTimeEnd());   // 验证时间戳转换（+24小时）
        verify(inspectionRecordReadMapper).getInspectionSummary(any(PositionInspectionRequest.class));
    }

    @Test
    @DisplayName("获取巡检汇总信息 - 时间戳为null")
    void getInspectionSummary_NullTimestamps() {
        // 准备数据
        PositionInspectionRequest request = new PositionInspectionRequest();
        request.setCreateTimeStart(null);
        request.setCreateTimeEnd(null);
        
        InspectionSummaryDTO summaryDTO = new InspectionSummaryDTO();
        when(inspectionRecordReadMapper.getInspectionSummary(any(PositionInspectionRequest.class))).thenReturn(summaryDTO);

        // 执行测试
        InspectionSummaryDTO result = inspectionRecordRepository.getInspectionSummary(request);

        // 验证结果
        assertNotNull(result);
        assertNull(request.getCreateTimeStart());
        assertNull(request.getCreateTimeEnd());
        verify(inspectionRecordReadMapper).getInspectionSummary(any(PositionInspectionRequest.class));
    }

    @Test
    @DisplayName("转换领域对象到实体对象 - 成功")
    void convertToEntity_Success() {
        // 准备数据
        InspectionRecordDomain domain = new InspectionRecordDomain();
        domain.setId(1L);
        domain.setRuleCode("RULE_001");
        domain.setPositionCode("POS_001");
        domain.setTaskStatus(TaskStatusEnum.NOT_COMPLETED);
        domain.setInspectionStatus(InspectionStatusEnum.TO_BE_VERIFIED);

        // 执行测试 - 通过公共方法间接测试转换逻辑
        when(inspectionRecordMapper.insert(any(InspectionRecord.class))).thenReturn(1);
        inspectionRecordRepository.save(domain);

        // 验证转换逻辑通过其他测试方法已经得到验证
        verify(inspectionRecordMapper).insert(any(InspectionRecord.class));
    }

    @Test
    @DisplayName("转换实体对象到领域对象 - 成功")
    void convertToDomain_FromEntity_Success() {
        // 准备数据
        InspectionRecord entity = new InspectionRecord();
        entity.setId(1L);
        entity.setRuleCode("RULE_001");
        entity.setBusinessCode("POS_001");
        entity.setTaskStatus(TaskStatusEnum.NOT_COMPLETED.getCode());
        entity.setInspectionStatus(InspectionStatusEnum.TO_BE_VERIFIED.getCode());

        // 执行测试 - 通过公共方法间接测试转换逻辑
        when(inspectionRecordReadMapper.selectById(1L)).thenReturn(entity);
        InspectionRecordDomain result = inspectionRecordRepository.getById(1L);

        // 验证结果
        assertNotNull(result);
        assertEquals(1L, result.getId());
        assertEquals("RULE_001", result.getRuleCode());
        assertEquals("POS_001", result.getPositionCode());
        assertEquals(TaskStatusEnum.NOT_COMPLETED, result.getTaskStatus());
        assertEquals(InspectionStatusEnum.TO_BE_VERIFIED, result.getInspectionStatus());
    }
} 