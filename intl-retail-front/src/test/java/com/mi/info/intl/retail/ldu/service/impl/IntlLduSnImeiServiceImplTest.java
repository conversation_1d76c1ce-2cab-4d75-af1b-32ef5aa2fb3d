package com.mi.info.intl.retail.ldu.service.impl;

import com.google.common.collect.Lists;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.*;
import com.mi.info.intl.retail.ldu.infra.entity.IntlRmsProduct;
import com.mi.info.intl.retail.ldu.infra.repository.IProductQueryService;
import com.mi.info.intl.retail.ldu.infra.repository.ISnImeiQueryService;
import com.mi.info.intl.retail.ldu.service.IntlRmsProductService;
import com.mi.info.intl.retail.utils.SnImeiValidateUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * IntlLduSnImeiServiceImpl 单元测试类
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("LDU SN/IMEI服务实现类测试")
class IntlLduSnImeiServiceImplTest {

    @InjectMocks
    private IntlLduSnImeiServiceImpl intlLduSnImeiService;

    @Mock
    private ISnImeiQueryService snImeiQueryService;

    @Mock
    private IntlRmsProductService intlRmsProductService;

    @Mock
    private IProductQueryService productQueryService;

    private SnImeiQueryDto testQueryDto;
    private SnImeiInfoDto testSnImeiInfo;
    private IntlRmsProduct testProduct;
    private SnImeiValidationDto testValidationDto;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        testQueryDto = new SnImeiQueryDto();
        testQueryDto.setSnList(Arrays.asList("SN123456", "SN789012"));
        testQueryDto.setImeiList(Arrays.asList("IMEI123456", "IMEI789012"));
        testQueryDto.setSixNineCodeList(Arrays.asList("6901234567890", "6901234567891"));

        testSnImeiInfo = new SnImeiInfoDto();
        testSnImeiInfo.setSn("SN123456");
        testSnImeiInfo.setImei("IMEI123456");
        testSnImeiInfo.setGoodsId("1001");
        testSnImeiInfo.setSku("SKU123456");

        testProduct = new IntlRmsProduct();
        testProduct.setId(1L);
        testProduct.setGoodsId("1001");
        testProduct.setName("测试商品");
        testProduct.setProductLine("手机");
        testProduct.setCode69("6901234567890");

        testValidationDto = new SnImeiValidationDto();
        testValidationDto.setSnImei("SN123456");
        testValidationDto.setIsValid(true);
        testValidationDto.setMessage("验证通过");
    }

    @Test
    @DisplayName("查询SN/IMEI信息 - 成功场景")
    void querySnImeiInfo_Success() {
        // 准备数据
        List<SnImeiInfoDto> mockSnImeiInfos = Arrays.asList(testSnImeiInfo);
        when(snImeiQueryService.querySnImeiInfoBySns(anyList())).thenReturn(mockSnImeiInfos);
        when(snImeiQueryService.querySnImeiInfoByImeis(anyList())).thenReturn(Collections.emptyList());

        // 执行测试
        List<SnImeiInfoDto> result = intlLduSnImeiService.querySnImeiInfo(testQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("SN123456", result.get(0).getSn());
        assertEquals("IMEI123456", result.get(0).getImei());
        assertEquals("1001", result.get(0).getGoodsId());

        // 验证方法调用
        verify(snImeiQueryService).querySnImeiInfoBySns(anyList());
        verify(snImeiQueryService).querySnImeiInfoByImeis(anyList());
    }

    @Test
    @DisplayName("查询SN/IMEI信息 - 空结果")
    void querySnImeiInfo_EmptyResult() {
        // 准备数据
        when(snImeiQueryService.querySnImeiInfoBySns(anyList())).thenReturn(Collections.emptyList());
        when(snImeiQueryService.querySnImeiInfoByImeis(anyList())).thenReturn(Collections.emptyList());

        // 执行测试
        List<SnImeiInfoDto> result = intlLduSnImeiService.querySnImeiInfo(testQueryDto);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());

        // 验证方法调用
        verify(snImeiQueryService).querySnImeiInfoBySns(anyList());
        verify(snImeiQueryService).querySnImeiInfoByImeis(anyList());
    }

    @Test
    @DisplayName("验证SN/IMEI信息 - 成功场景")
    void validateSnImeiInfo_Success() {
        // 准备数据
        List<SnImeiInfoDto> mockSnImeiInfos = Arrays.asList(testSnImeiInfo);
        when(snImeiQueryService.querySnImeiInfoBySns(anyList())).thenReturn(mockSnImeiInfos);
        when(snImeiQueryService.querySnImeiInfoByImeis(anyList())).thenReturn(Collections.emptyList());

        List<IntlRmsProduct> mockProducts = Arrays.asList(testProduct);
        when(intlRmsProductService.queryByGoodsIdList(anyList()))
                .thenReturn(mockProducts);
        when(intlRmsProductService.queryByCode69List(anyList()))
                .thenReturn(mockProducts);

        // 执行测试
        List<SnImeiValidationDto> result = intlLduSnImeiService.validateSnImeiInfo(testQueryDto);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isEmpty());

        // 验证方法调用
        verify(snImeiQueryService).querySnImeiInfoBySns(anyList());
        verify(snImeiQueryService).querySnImeiInfoByImeis(anyList());
        verify(intlRmsProductService).queryByGoodsIdList(anyList());
        verify(intlRmsProductService).queryByCode69List(anyList());
    }

    @Test
    @DisplayName("验证SN/IMEI信息 - 只有SN列表")
    void validateSnImeiInfo_OnlySnList() {
        // 准备数据
        testQueryDto.setImeiList(null);
        testQueryDto.setSixNineCodeList(null);

        List<SnImeiInfoDto> mockSnImeiInfos = Arrays.asList(testSnImeiInfo);
        when(snImeiQueryService.querySnImeiInfoBySns(anyList())).thenReturn(mockSnImeiInfos);
        when(snImeiQueryService.querySnImeiInfoByImeis(anyList())).thenReturn(Collections.emptyList());

        List<IntlRmsProduct> mockProducts = Arrays.asList(testProduct);
        when(intlRmsProductService.queryByGoodsIdList(anyList()))
                .thenReturn(mockProducts);

        // 执行测试
        List<SnImeiValidationDto> result = intlLduSnImeiService.validateSnImeiInfo(testQueryDto);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isEmpty());

        // 验证方法调用
        verify(snImeiQueryService).querySnImeiInfoBySns(anyList());
        verify(snImeiQueryService).querySnImeiInfoByImeis(anyList());
        verify(intlRmsProductService).queryByGoodsIdList(anyList());
        verify(intlRmsProductService, never()).queryByCode69List(anyList());
    }

    @Test
    @DisplayName("验证SN/IMEI信息 - 只有IMEI列表")
    void validateSnImeiInfo_OnlyImeiList() {
        // 准备数据
        testQueryDto.setSnList(null);
        testQueryDto.setSixNineCodeList(null);

        List<SnImeiInfoDto> mockSnImeiInfos = Arrays.asList(testSnImeiInfo);
        when(snImeiQueryService.querySnImeiInfoBySns(anyList())).thenReturn(Collections.emptyList());
        when(snImeiQueryService.querySnImeiInfoByImeis(anyList())).thenReturn(mockSnImeiInfos);

        List<IntlRmsProduct> mockProducts = Arrays.asList(testProduct);
        when(intlRmsProductService.queryByGoodsIdList(anyList()))
                .thenReturn(mockProducts);

        // 执行测试
        List<SnImeiValidationDto> result = intlLduSnImeiService.validateSnImeiInfo(testQueryDto);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isEmpty());

        // 验证方法调用
        verify(snImeiQueryService).querySnImeiInfoBySns(anyList());
        verify(snImeiQueryService).querySnImeiInfoByImeis(anyList());
        verify(intlRmsProductService).queryByGoodsIdList(anyList());
        verify(intlRmsProductService, never()).queryByCode69List(anyList());
    }

    @Test
    @DisplayName("验证SN/IMEI信息 - 只有69码列表")
    void validateSnImeiInfo_OnlyCode69List() {
        // 准备数据
        testQueryDto.setSnList(null);
        testQueryDto.setImeiList(null);

        when(snImeiQueryService.querySnImeiInfoBySns(anyList())).thenReturn(Collections.emptyList());
        when(snImeiQueryService.querySnImeiInfoByImeis(anyList())).thenReturn(Collections.emptyList());

        List<IntlRmsProduct> mockProducts = Arrays.asList(testProduct);
        when(intlRmsProductService.queryByCode69List(anyList()))
                .thenReturn(mockProducts);

        // 执行测试
        List<SnImeiValidationDto> result = intlLduSnImeiService.validateSnImeiInfo(testQueryDto);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isEmpty());

        // 验证方法调用
        verify(snImeiQueryService).querySnImeiInfoBySns(anyList());
        verify(snImeiQueryService).querySnImeiInfoByImeis(anyList());
        verify(intlRmsProductService, never()).queryByGoodsIdList(anyList());
        verify(intlRmsProductService).queryByCode69List(anyList());
    }

    @Test
    @DisplayName("验证SN/IMEI信息 - 空查询条件")
    void validateSnImeiInfo_EmptyQuery() {
        // 准备数据
        testQueryDto.setSnList(null);
        testQueryDto.setImeiList(null);
        testQueryDto.setSixNineCodeList(null);

        when(snImeiQueryService.querySnImeiInfoBySns(anyList())).thenReturn(Collections.emptyList());
        when(snImeiQueryService.querySnImeiInfoByImeis(anyList())).thenReturn(Collections.emptyList());

        // 执行测试
        List<SnImeiValidationDto> result = intlLduSnImeiService.validateSnImeiInfo(testQueryDto);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());

        // 验证方法调用
        verify(snImeiQueryService).querySnImeiInfoBySns(anyList());
        verify(snImeiQueryService).querySnImeiInfoByImeis(anyList());
        verify(intlRmsProductService, never()).queryByGoodsIdList(anyList());
        verify(intlRmsProductService, never()).queryByCode69List(anyList());
    }

    @Test
    @DisplayName("根据SN/IMEI查询商品信息 - 成功场景")
    void queryGoodsInfoBySnImeis_Success() {
        // 准备数据
        List<SnImeiInfoDto> mockSnImeiInfos = Arrays.asList(testSnImeiInfo);
        when(snImeiQueryService.querySnImeiInfoBySns(anyList())).thenReturn(mockSnImeiInfos);
        when(snImeiQueryService.querySnImeiInfoByImeis(anyList())).thenReturn(Collections.emptyList());

        List<IntlRmsProduct> mockProducts = Arrays.asList(testProduct);
        when(intlRmsProductService.queryByGoodsIdList(anyList()))
                .thenReturn(mockProducts);
        when(intlRmsProductService.queryByCode69List(anyList()))
                .thenReturn(mockProducts);

        // 执行测试
        List<SnImeiGoodsInfoDto> result = intlLduSnImeiService.queryGoodsInfoBySnImeis(testQueryDto);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isEmpty());

        // 验证方法调用
        verify(snImeiQueryService).querySnImeiInfoBySns(anyList());
        verify(snImeiQueryService).querySnImeiInfoByImeis(anyList());
        verify(intlRmsProductService).queryByGoodsIdList(anyList());
        verify(intlRmsProductService).queryByCode69List(anyList());
    }

    @Test
    @DisplayName("根据SN/IMEI查询商品信息 - 空结果")
    void queryGoodsInfoBySnImeis_EmptyResult() {
        // 准备数据
        when(snImeiQueryService.querySnImeiInfoBySns(anyList())).thenReturn(Collections.emptyList());
        when(snImeiQueryService.querySnImeiInfoByImeis(anyList())).thenReturn(Collections.emptyList());

        when(intlRmsProductService.queryByGoodsIdList(anyList()))
                .thenReturn(Collections.emptyList());
        when(intlRmsProductService.queryByCode69List(anyList()))
                .thenReturn(Collections.emptyList());

        // 执行测试
        List<SnImeiGoodsInfoDto> result = intlLduSnImeiService.queryGoodsInfoBySnImeis(testQueryDto);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());

        // 验证方法调用
        verify(snImeiQueryService).querySnImeiInfoBySns(anyList());
        verify(snImeiQueryService).querySnImeiInfoByImeis(anyList());
        verify(intlRmsProductService).queryByGoodsIdList(anyList());
        verify(intlRmsProductService).queryByCode69List(anyList());
    }

    @Test
    @DisplayName("根据商品ID列表查询商品信息 - 成功场景")
    void queryGoodsInfoByGoodsIds_Success() {
        // 准备数据
        List<String> goodsIds = Arrays.asList("1001", "1002");
        List<IntlRmsProduct> mockProducts = Arrays.asList(testProduct);
        when(intlRmsProductService.queryByGoodsIdList(goodsIds))
                .thenReturn(mockProducts);

        // 执行测试
        List<SnImeiGoodsInfoDto> result = intlLduSnImeiService.queryGoodsInfoByGoodsIds(goodsIds);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("1001", result.get(0).getGoodsId());
        assertEquals("测试商品", result.get(0).getGoodsName());

        // 验证方法调用
        verify(intlRmsProductService).queryByGoodsIdList(goodsIds);
    }

    @Test
    @DisplayName("根据商品ID列表查询商品信息 - 空列表")
    void queryGoodsInfoByGoodsIds_EmptyList() {
        // 准备数据
        List<String> goodsIds = Collections.emptyList();
        when(intlRmsProductService.queryByGoodsIdList(goodsIds))
                .thenReturn(Collections.emptyList());

        // 执行测试
        List<SnImeiGoodsInfoDto> result = intlLduSnImeiService.queryGoodsInfoByGoodsIds(goodsIds);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());

        // 验证方法调用
        verify(intlRmsProductService).queryByGoodsIdList(goodsIds);
    }

    @Test
    @DisplayName("根据商品ID列表查询商品信息 - null列表")
    void queryGoodsInfoByGoodsIds_NullList() {
        // 执行测试
        List<SnImeiGoodsInfoDto> result = intlLduSnImeiService.queryGoodsInfoByGoodsIds(null);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());

        // 验证方法调用
        verify(intlRmsProductService, never()).queryByGoodsIdList(anyList());
    }

    @Test
    @DisplayName("验证单个SN/IMEI - 成功场景")
    void validateSnImei_Success() throws Exception {
        // 准备数据
        String sn = "SN123456";
        Map<String, SnImeiInfoDto> snMap = new HashMap<>();
        snMap.put("SN123456", testSnImeiInfo);

        Map<String, IntlRmsProduct> goodsMap = new HashMap<>();
        goodsMap.put("1001", testProduct);

        // 使用反射调用私有方法
        Method validateSnImeiMethod = IntlLduSnImeiServiceImpl.class.getDeclaredMethod(
                "validateSnImei", String.class, Map.class, Map.class);
        validateSnImeiMethod.setAccessible(true);

        // 执行测试
        SnImeiValidationDto result = (SnImeiValidationDto) validateSnImeiMethod.invoke(
                intlLduSnImeiService, sn, snMap, goodsMap);

        // 验证结果
        assertNotNull(result);
        assertEquals("SN123456", result.getSnImei());
        assertTrue(result.getIsValid());
    }

    @Test
    @DisplayName("验证单个SN/IMEI - SN不存在")
    void validateSnImei_SnNotExists() throws Exception {
        // 准备数据
        String sn = "SN999999";
        Map<String, SnImeiInfoDto> snMap = new HashMap<>();
        Map<String, IntlRmsProduct> goodsMap = new HashMap<>();

        // 使用反射调用私有方法
        Method validateSnImeiMethod = IntlLduSnImeiServiceImpl.class.getDeclaredMethod(
                "validateSnImei", String.class, Map.class, Map.class);
        validateSnImeiMethod.setAccessible(true);

        // 执行测试
        SnImeiValidationDto result = (SnImeiValidationDto) validateSnImeiMethod.invoke(
                intlLduSnImeiService, sn, snMap, goodsMap);

        // 验证结果
        assertNotNull(result);
        assertEquals("SN999999", result.getSnImei());
        assertFalse(result.getIsValid());
        assertTrue(result.getMessage().contains("不存在"));
    }

    @Test
    @DisplayName("验证单个SN/IMEI - 商品不存在")
    void validateSnImei_GoodsNotExists() throws Exception {
        // 准备数据
        String sn = "SN123456";
        Map<String, SnImeiInfoDto> snMap = new HashMap<>();
        snMap.put("SN123456", testSnImeiInfo);

        Map<String, IntlRmsProduct> goodsMap = new HashMap<>();

        // 使用反射调用私有方法
        Method validateSnImeiMethod = IntlLduSnImeiServiceImpl.class.getDeclaredMethod(
                "validateSnImei", String.class, Map.class, Map.class);
        validateSnImeiMethod.setAccessible(true);

        // 执行测试
        SnImeiValidationDto result = (SnImeiValidationDto) validateSnImeiMethod.invoke(
                intlLduSnImeiService, sn, snMap, goodsMap);

        // 验证结果
        assertNotNull(result);
        assertEquals("SN123456", result.getSnImei());
        assertFalse(result.getIsValid());
        assertTrue(result.getMessage().contains("商品信息不存在"));
    }

    @Test
    @DisplayName("验证69码 - 成功场景")
    void validateCode69_Success() throws Exception {
        // 准备数据
        String code69 = "6901234567890";
        Map<String, IntlRmsProduct> code69GoodsMap = new HashMap<>();
        code69GoodsMap.put("6901234567890", testProduct);

        // 使用反射调用私有方法
        Method validateCode69Method = IntlLduSnImeiServiceImpl.class.getDeclaredMethod(
                "validateCode69", String.class, Map.class);
        validateCode69Method.setAccessible(true);

        // 执行测试
        SnImeiValidationDto result = (SnImeiValidationDto) validateCode69Method.invoke(
                intlLduSnImeiService, code69, code69GoodsMap);

        // 验证结果
        assertNotNull(result);
        assertEquals("6901234567890", result.getSnImei());
        assertTrue(result.getIsValid());
    }

    @Test
    @DisplayName("验证69码 - 商品不存在")
    void validateCode69_GoodsNotExists() throws Exception {
        // 准备数据
        String code69 = "6901234567899";
        Map<String, IntlRmsProduct> code69GoodsMap = new HashMap<>();

        // 使用反射调用私有方法
        Method validateCode69Method = IntlLduSnImeiServiceImpl.class.getDeclaredMethod(
                "validateCode69", String.class, Map.class);
        validateCode69Method.setAccessible(true);

        // 执行测试
        SnImeiValidationDto result = (SnImeiValidationDto) validateCode69Method.invoke(
                intlLduSnImeiService, code69, code69GoodsMap);

        // 验证结果
        assertNotNull(result);
        assertEquals("6901234567899", result.getSnImei());
        assertFalse(result.getIsValid());
        assertTrue(result.getMessage().contains("商品信息不存在"));
    }

    @Test
    @DisplayName("查询所有SN/IMEI信息 - 成功场景")
    void queryAllSnImeiInfo_Success() throws Exception {
        // 准备数据
        List<SnImeiInfoDto> mockSnImeiInfos = Arrays.asList(testSnImeiInfo);
        when(snImeiQueryService.querySnImeiInfoBySns(anyList())).thenReturn(mockSnImeiInfos);
        when(snImeiQueryService.querySnImeiInfoByImeis(anyList())).thenReturn(Collections.emptyList());

        // 使用反射调用私有方法
        Method queryAllSnImeiInfoMethod = IntlLduSnImeiServiceImpl.class.getDeclaredMethod(
                "queryAllSnImeiInfo", SnImeiQueryDto.class);
        queryAllSnImeiInfoMethod.setAccessible(true);

        // 执行测试
        List<SnImeiInfoDto> result = (List<SnImeiInfoDto>) queryAllSnImeiInfoMethod.invoke(
                intlLduSnImeiService, testQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("SN123456", result.get(0).getSn());

        // 验证方法调用
        verify(snImeiQueryService).querySnImeiInfoBySns(anyList());
        verify(snImeiQueryService).querySnImeiInfoByImeis(anyList());
    }

    @Test
    @DisplayName("查询所有SN/IMEI信息 - 空结果")
    void queryAllSnImeiInfo_EmptyResult() throws Exception {
        // 准备数据
        when(snImeiQueryService.querySnImeiInfoBySns(anyList())).thenReturn(Collections.emptyList());
        when(snImeiQueryService.querySnImeiInfoByImeis(anyList())).thenReturn(Collections.emptyList());

        // 使用反射调用私有方法
        Method queryAllSnImeiInfoMethod = IntlLduSnImeiServiceImpl.class.getDeclaredMethod(
                "queryAllSnImeiInfo", SnImeiQueryDto.class);
        queryAllSnImeiInfoMethod.setAccessible(true);

        // 执行测试
        List<SnImeiInfoDto> result = (List<SnImeiInfoDto>) queryAllSnImeiInfoMethod.invoke(
                intlLduSnImeiService, testQueryDto);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());

        // 验证方法调用
        verify(snImeiQueryService).querySnImeiInfoBySns(anyList());
        verify(snImeiQueryService).querySnImeiInfoByImeis(anyList());
    }

    @Test
    @DisplayName("验证SN/IMEI信息 - 混合查询条件")
    void validateSnImeiInfo_MixedQuery() {
        // 准备数据
        testQueryDto.setSnList(Arrays.asList("SN123456"));
        testQueryDto.setImeiList(Arrays.asList("IMEI123456"));
        testQueryDto.setSixNineCodeList(Arrays.asList("6901234567890"));

        List<SnImeiInfoDto> mockSnImeiInfos = Arrays.asList(testSnImeiInfo);
        when(snImeiQueryService.querySnImeiInfoBySns(anyList())).thenReturn(mockSnImeiInfos);
        when(snImeiQueryService.querySnImeiInfoByImeis(anyList())).thenReturn(Collections.emptyList());

        List<IntlRmsProduct> mockProducts = Arrays.asList(testProduct);
        when(intlRmsProductService.queryByGoodsIdList(anyList()))
                .thenReturn(mockProducts);
        when(intlRmsProductService.queryByCode69List(anyList()))
                .thenReturn(mockProducts);

        // 执行测试
        List<SnImeiValidationDto> result = intlLduSnImeiService.validateSnImeiInfo(testQueryDto);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isEmpty());

        // 验证方法调用
        verify(snImeiQueryService).querySnImeiInfoBySns(anyList());
        verify(snImeiQueryService).querySnImeiInfoByImeis(anyList());
        verify(intlRmsProductService).queryByGoodsIdList(anyList());
        verify(intlRmsProductService).queryByCode69List(anyList());
    }

    @Test
    @DisplayName("验证SN/IMEI信息 - 商品信息为空")
    void validateSnImeiInfo_EmptyGoodsInfo() {
        // 准备数据
        List<SnImeiInfoDto> mockSnImeiInfos = Arrays.asList(testSnImeiInfo);
        when(snImeiQueryService.querySnImeiInfoBySns(anyList())).thenReturn(mockSnImeiInfos);
        when(snImeiQueryService.querySnImeiInfoByImeis(anyList())).thenReturn(Collections.emptyList());

        when(intlRmsProductService.queryByGoodsIdList(anyList()))
                .thenReturn(Collections.emptyList());
        when(intlRmsProductService.queryByCode69List(anyList()))
                .thenReturn(Collections.emptyList());

        // 执行测试
        List<SnImeiValidationDto> result = intlLduSnImeiService.validateSnImeiInfo(testQueryDto);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isEmpty());

        // 验证方法调用
        verify(snImeiQueryService).querySnImeiInfoBySns(anyList());
        verify(snImeiQueryService).querySnImeiInfoByImeis(anyList());
        verify(intlRmsProductService).queryByGoodsIdList(anyList());
        verify(intlRmsProductService).queryByCode69List(anyList());
    }
}
