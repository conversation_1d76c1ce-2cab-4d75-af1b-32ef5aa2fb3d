package com.mi.info.intl.retail.ldu.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.IntlLduTargetDto;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.IntlLduTargetReq;
import com.mi.info.intl.retail.ldu.infra.entity.IntlLduTarget;
import com.mi.info.intl.retail.ldu.infra.entity.IntlRmsProduct;
import com.mi.info.intl.retail.ldu.infra.mapper.IntlLduTargetMapper;
import com.mi.info.intl.retail.ldu.service.IntlRmsProductService;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.utils.time.DateTimeUtil;
import org.apache.dubbo.rpc.RpcContext;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Method;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * IntlLduTargetServiceImpl 单元测试类
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("LDU目标服务实现类测试")
class IntlLduTargetServiceImplTest {

    @InjectMocks
    private IntlLduTargetServiceImpl intlLduTargetService;

    @Mock
    private IntlLduTargetMapper intlLduTargetMapper;

    @Mock
    private IntlRmsProductService intlRmsProductService;

    private IntlLduTargetReq testRequest;
    private IntlLduTarget testEntity;
    private IntlLduTargetDto testDto;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        testRequest = new IntlLduTargetReq();
        testRequest.setPageNum(1L);
        testRequest.setPageSize(10L);
        testRequest.setProductLine("手机");
        testRequest.setProjectName("测试项目");
        testRequest.setProjectCode("TEST001");
        testRequest.setCountryCode(Arrays.asList("CN", "US"));

        testEntity = new IntlLduTarget();
        testEntity.setId(1);
        testEntity.setProductLine("手机");
        testEntity.setProjectName("测试项目");
        testEntity.setProjectCode("TEST001");
        testEntity.setCountryCode("CN");
        testEntity.setTargetCreateDate(System.currentTimeMillis());

        testDto = new IntlLduTargetDto();
        testDto.setId(1L);
        testDto.setProductLine("手机");
        testDto.setProjectName("测试项目");
        testDto.setProjectCode("TEST001");
        testDto.setCountryCode("CN");
        testDto.setTargetCreateDate(System.currentTimeMillis());
    }

    @Test
    @DisplayName("分页查询LDU目标 - 成功场景")
    void pageListTarget_Success() {
        // 准备数据
        Page<IntlLduTarget> mockPage = new Page<>(1, 10);
        mockPage.setRecords(Arrays.asList(testEntity));
        mockPage.setTotal(1L);
        mockPage.setPages(1L);

        when(intlLduTargetMapper.selectPage(any(Page.class), any(LambdaQueryWrapper.class)))
                .thenReturn(mockPage);

        try (MockedStatic<RpcContext> rpcContextMock = mockStatic(RpcContext.class)) {
            RpcContext mockContext = mock(RpcContext.class);
            rpcContextMock.when(RpcContext::getContext).thenReturn(mockContext);
            when(mockContext.getAttachment("$area_id")).thenReturn("CN");

            // 执行测试
            IPage<IntlLduTargetDto> result = intlLduTargetService.pageListTarget(testRequest);

            // 验证结果
            assertNotNull(result);
            assertEquals(1, result.getRecords().size());
            assertEquals(1L, result.getTotal());

            // 验证方法调用
            verify(intlLduTargetMapper).selectPage(any(Page.class), any(LambdaQueryWrapper.class));
        }
    }

    @Test
    @DisplayName("分页查询LDU目标 - 空结果")
    void pageListTarget_EmptyResult() {
        // 准备数据
        Page<IntlLduTarget> mockPage = new Page<>(1, 10);
        mockPage.setRecords(Collections.emptyList());
        mockPage.setTotal(0L);
        mockPage.setPages(0L);

        when(intlLduTargetMapper.selectPage(any(Page.class), any(LambdaQueryWrapper.class)))
                .thenReturn(mockPage);

        try (MockedStatic<RpcContext> rpcContextMock = mockStatic(RpcContext.class)) {
            RpcContext mockContext = mock(RpcContext.class);
            rpcContextMock.when(RpcContext::getContext).thenReturn(mockContext);
            when(mockContext.getAttachment("$area_id")).thenReturn("CN");

            // 执行测试
            IPage<IntlLduTargetDto> result = intlLduTargetService.pageListTarget(testRequest);

            // 验证结果
            assertNotNull(result);
            assertTrue(result.getRecords().isEmpty());
            assertEquals(0L, result.getTotal());

            // 验证方法调用
            verify(intlLduTargetMapper).selectPage(any(Page.class), any(LambdaQueryWrapper.class));
        }
    }

    @Test
    @DisplayName("分页查询LDU目标 - GLOBAL区域")
    void pageListTarget_GlobalArea() {
        // 准备数据
        testRequest.setCountryCode(null);
        Page<IntlLduTarget> mockPage = new Page<>(1, 10);
        mockPage.setRecords(Arrays.asList(testEntity));
        mockPage.setTotal(1L);
        mockPage.setPages(1L);

        when(intlLduTargetMapper.selectPage(any(Page.class), any(LambdaQueryWrapper.class)))
                .thenReturn(mockPage);

        try (MockedStatic<RpcContext> rpcContextMock = mockStatic(RpcContext.class)) {
            RpcContext mockContext = mock(RpcContext.class);
            rpcContextMock.when(RpcContext::getContext).thenReturn(mockContext);
            when(mockContext.getAttachment("$area_id")).thenReturn("GLOBAL");

            // 执行测试
            IPage<IntlLduTargetDto> result = intlLduTargetService.pageListTarget(testRequest);

            // 验证结果
            assertNotNull(result);
            assertEquals(1, result.getRecords().size());

            // 验证方法调用
            verify(intlLduTargetMapper).selectPage(any(Page.class), any(LambdaQueryWrapper.class));
        }
    }

    @Test
    @DisplayName("分页查询LDU目标 - 特定区域")
    void pageListTarget_SpecificArea() {
        // 准备数据
        testRequest.setCountryCode(null);
        Page<IntlLduTarget> mockPage = new Page<>(1, 10);
        mockPage.setRecords(Arrays.asList(testEntity));
        mockPage.setTotal(1L);
        mockPage.setPages(1L);

        when(intlLduTargetMapper.selectPage(any(Page.class), any(LambdaQueryWrapper.class)))
                .thenReturn(mockPage);

        try (MockedStatic<RpcContext> rpcContextMock = mockStatic(RpcContext.class)) {
            RpcContext mockContext = mock(RpcContext.class);
            rpcContextMock.when(RpcContext::getContext).thenReturn(mockContext);
            when(mockContext.getAttachment("$area_id")).thenReturn("US");

            // 执行测试
            IPage<IntlLduTargetDto> result = intlLduTargetService.pageListTarget(testRequest);

            // 验证结果
            assertNotNull(result);
            assertEquals(1, result.getRecords().size());

            // 验证方法调用
            verify(intlLduTargetMapper).selectPage(any(Page.class), any(LambdaQueryWrapper.class));
        }
    }

    @Test
    @DisplayName("创建LDU目标 - 成功场景")
    void create_Success() {
        // 准备数据
        when(intlLduTargetMapper.queryByProjectCode(anyString(), anyString())).thenReturn(null);
        doNothing().when(intlLduTargetMapper).batchInsert(anyList());

        // 执行测试
        CommonApiResponse<String> result = intlLduTargetService.create(testDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("success", result.getData());
        assertEquals(0, result.getCode());

        // 验证方法调用
        verify(intlLduTargetMapper).queryByProjectCode(anyString(), anyString());
        verify(intlLduTargetMapper).batchInsert(anyList());
    }

    @Test
    @DisplayName("创建LDU目标 - 失败场景")
    void create_Failure() {
        // 准备数据
        when(intlLduTargetMapper.queryByProjectCode(anyString(), anyString())).thenReturn(testEntity);

        // 执行测试
        CommonApiResponse<String> result = intlLduTargetService.create(testDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(-1, result.getCode());

        // 验证方法调用
        verify(intlLduTargetMapper).queryByProjectCode(anyString(), anyString());
        verify(intlLduTargetMapper, never()).batchInsert(anyList());
    }

    @Test
    @DisplayName("修改LDU目标 - 成功场景")
    void modify_Success() {
        // 准备数据
        when(intlLduTargetMapper.updateById(any(IntlLduTarget.class))).thenReturn(1);

        try (MockedStatic<RpcContext> rpcContextMock = mockStatic(RpcContext.class)) {
            RpcContext mockContext = mock(RpcContext.class);
            rpcContextMock.when(RpcContext::getContext).thenReturn(mockContext);
            when(mockContext.getAttachments()).thenReturn(new HashMap<String, String>() {{
                put("$upc_miID", "USER001");
                put("$upc_userName", "测试用户");
            }});

            // 执行测试
            CommonApiResponse<String> result = intlLduTargetService.modify(testDto);

            // 验证结果
            assertNotNull(result);
            assertEquals("success", result.getData());
            assertEquals(0, result.getCode());

            // 验证方法调用
            verify(intlLduTargetMapper).updateById(any(IntlLduTarget.class));
        }
    }

    @Test
    @DisplayName("修改LDU目标 - 失败场景")
    void modify_Failure() {
        // 准备数据
        when(intlLduTargetMapper.updateById(any(IntlLduTarget.class))).thenReturn(0);

        try (MockedStatic<RpcContext> rpcContextMock = mockStatic(RpcContext.class)) {
            RpcContext mockContext = mock(RpcContext.class);
            rpcContextMock.when(RpcContext::getContext).thenReturn(mockContext);
            when(mockContext.getAttachments()).thenReturn(new HashMap<String, String>() {{
                put("$upc_miID", "USER001");
                put("$upc_userName", "测试用户");
            }});

            // 执行测试
            CommonApiResponse<String> result = intlLduTargetService.modify(testDto);

            // 验证结果
            assertNotNull(result);
            assertEquals("success", result.getData());

            // 验证方法调用
            verify(intlLduTargetMapper).updateById(any(IntlLduTarget.class));
        }
    }

    @Test
    @DisplayName("分页查询LDU目标 - 不同过滤条件组合")
    void pageListTarget_DifferentFilters() {
        // 准备数据 - 测试不同的过滤条件
        testRequest.setProductLine("平板");
        testRequest.setProjectName("平板项目");
        testRequest.setProjectCode("TABLET001");
        testRequest.setCountryCode(Arrays.asList("US", "EU"));

        Page<IntlLduTarget> mockPage = new Page<>(1, 10);
        mockPage.setRecords(Arrays.asList(testEntity));
        mockPage.setTotal(1L);
        mockPage.setPages(1L);

        when(intlLduTargetMapper.selectPage(any(Page.class), any(LambdaQueryWrapper.class)))
                .thenReturn(mockPage);

        try (MockedStatic<RpcContext> rpcContextMock = mockStatic(RpcContext.class)) {
            RpcContext mockContext = mock(RpcContext.class);
            rpcContextMock.when(RpcContext::getContext).thenReturn(mockContext);
            when(mockContext.getAttachment("$area_id")).thenReturn("US");

            // 执行测试
            IPage<IntlLduTargetDto> result = intlLduTargetService.pageListTarget(testRequest);

            // 验证结果
            assertNotNull(result);
            assertEquals(1, result.getRecords().size());

            // 验证方法调用
            verify(intlLduTargetMapper).selectPage(any(Page.class), any(LambdaQueryWrapper.class));
        }
    }

    @Test
    @DisplayName("分页查询LDU目标 - 空区域ID")
    void pageListTarget_NullAreaId() {
        // 准备数据
        testRequest.setCountryCode(null);
        Page<IntlLduTarget> mockPage = new Page<>(1, 10);
        mockPage.setRecords(Arrays.asList(testEntity));
        mockPage.setTotal(1L);
        mockPage.setPages(1L);

        when(intlLduTargetMapper.selectPage(any(Page.class), any(LambdaQueryWrapper.class)))
                .thenReturn(mockPage);

        try (MockedStatic<RpcContext> rpcContextMock = mockStatic(RpcContext.class)) {
            RpcContext mockContext = mock(RpcContext.class);
            rpcContextMock.when(RpcContext::getContext).thenReturn(mockContext);
            when(mockContext.getAttachment("$area_id")).thenReturn(null);

            // 执行测试
            IPage<IntlLduTargetDto> result = intlLduTargetService.pageListTarget(testRequest);

            // 验证结果
            assertNotNull(result);
            assertEquals(1, result.getRecords().size());

            // 验证方法调用
            verify(intlLduTargetMapper).selectPage(any(Page.class), any(LambdaQueryWrapper.class));
        }
    }

    @Test
    @DisplayName("转换为DTO - 成功场景")
    void convertToDto_Success() throws Exception {
        // 准备数据
        IntlRmsProduct testProduct = new IntlRmsProduct();
        testProduct.setId(1L);
        testProduct.setProjectCode("TEST001");
        testProduct.setShortname("测试项目");
        testProduct.setProductLine("手机");
        testProduct.setProductLineCode(1L);
        testProduct.setProductLineEn("Phone");

        // 使用反射调用私有方法
        Method convertToDtoMethod = IntlLduTargetServiceImpl.class.getDeclaredMethod(
                "convertToDto", IntlRmsProduct.class);
        convertToDtoMethod.setAccessible(true);

        // 执行测试
        Object result = convertToDtoMethod.invoke(intlLduTargetService, testProduct);

        // 验证结果
        assertNotNull(result);
    }

    @Test
    @DisplayName("时区转换 - 成功场景")
    void timeZoneConvert_Success() throws Exception {
        // 准备数据
        testDto.setTargetCreateDate(System.currentTimeMillis());

        try (MockedStatic<DateTimeUtil> dateTimeUtilMock = mockStatic(DateTimeUtil.class)) {
            dateTimeUtilMock.when(() -> DateTimeUtil.formatTimestamp(anyLong()))
                    .thenReturn("2024-01-01 12:00:00");

            // 使用反射调用私有方法
            Method timeZoneConvertMethod = IntlLduTargetServiceImpl.class.getDeclaredMethod(
                    "timeZoneConvert", IntlLduTargetDto.class, String.class);
            timeZoneConvertMethod.setAccessible(true);

            // 执行测试
            timeZoneConvertMethod.invoke(intlLduTargetService, testDto, "CN");

            // 验证方法调用
            dateTimeUtilMock.verify(() -> DateTimeUtil.formatTimestamp(anyLong()));
        }
    }

    @Test
    @DisplayName("中英文转换 - 成功场景")
    void cntoEn_Success() throws Exception {
        // 使用反射调用私有方法
        Method cntoEnMethod = IntlLduTargetServiceImpl.class.getDeclaredMethod(
                "cntoEn", IntlLduTargetDto.class);
        cntoEnMethod.setAccessible(true);

        // 执行测试
        cntoEnMethod.invoke(intlLduTargetService, testDto);

        // 验证结果 - 方法执行无异常
        assertNotNull(testDto);
    }
}
