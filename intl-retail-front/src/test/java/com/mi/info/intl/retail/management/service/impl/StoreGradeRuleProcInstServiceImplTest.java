package com.mi.info.intl.retail.management.service.impl;

import com.mi.info.intl.retail.intlretail.service.api.bpm.dto.BpmCallBackParamDto;
import com.mi.info.intl.retail.intlretail.service.api.bpm.dto.BpmUser;
import com.mi.info.intl.retail.intlretail.service.api.management.StoreGradeService;
import com.mi.info.intl.retail.management.entity.CommonApproveLog;
import com.mi.info.intl.retail.management.entity.StoreGradeRelation;
import com.mi.info.intl.retail.management.entity.StoreGradeRule;
import com.mi.info.intl.retail.management.mapper.CommonApproveLogMapper;
import com.mi.info.intl.retail.management.mapper.CommonChangeLogMapper;
import com.mi.info.intl.retail.management.mapper.StoreGradeRelationMapper;
import com.mi.info.intl.retail.management.mapper.StoreGradeRuleMapper;
import com.mi.info.intl.retail.management.rpc.MainDataRpc;
import com.mi.info.intl.retail.management.service.enums.BpmCallBackNodeEnum;
import com.mi.info.intl.retail.management.service.enums.ProcessReviewStatus;
import com.mi.info.intl.retail.management.service.enums.TieringModificationMethodEnum;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.List;

class StoreGradeRuleProcInstServiceImplTest {

    @InjectMocks
    private StoreGradeRuleProcInstServiceImpl storeGradeRuleProcInstService;

    @Mock
    private StoreGradeRuleMapper storeGradeRuleMapper;

    @Mock
    private CommonApproveLogMapper commonApproveLogMapper;

    @Mock
    private StoreGradeService storeGradeService;

    @Mock
    private StoreGradeRelationMapper storeGradeRelationMapper;

    @Mock
    private CommonChangeLogMapper commonChangeLogMapper;

    @Mock
    private MainDataRpc mainDataRpc;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * 测试用例：状态为 REJECTED，应调用 rejected 方法
     */
    @Test
    void testDoCallback_WhenStatusIsRejected_ShouldCallRejected() {
        // 准备数据
        BpmCallBackParamDto response = BpmCallBackParamDto.builder()
                .status(ProcessReviewStatus.REJECTED.getDescEn())
                .build();

        // 执行方法
        storeGradeRuleProcInstService.doCallback(response);

        // 验证是否调用了 rejected 方法（通过验证 updateStatus 是否被调用）
        verify(commonApproveLogMapper, times(1)).selectOne(any());
    }

    /**
     * 测试用例：状态为 PROCESS_COMPLETED 且节点为 HQ_STORE_TEAM_NODE，应调用 approve 方法
     */
    @Test
    void testDoCallback_WhenStatusIsProcessCompletedAndNodeIsHqStoreTeam_ShouldCallApprove() {
        // 准备数据
        BpmCallBackParamDto response = BpmCallBackParamDto.builder()
                .status(ProcessReviewStatus.PROCESS_COMPLETED.getCode())
                .taskDefinitionKey(BpmCallBackNodeEnum.HQ_STORE_TEAM_NODE.getNode())
                .businessKey("testBusinessKey")
                .build();
        BpmUser assignee = BpmUser.builder()
                .userName("testUserName")
                .displayName("testDisplayName")
                .personId("testPersonId")
                .build();
        response.setAssignee(assignee);

        CommonApproveLog commonApproveLog = new CommonApproveLog();
        commonApproveLog.setBusinessId("1");
        when(commonApproveLogMapper.selectOne(any())).thenReturn(commonApproveLog);

        StoreGradeRule storeGradeRule = StoreGradeRule.builder()
                .method(TieringModificationMethodEnum.SYSTEM_CALCULATION.getKey())
                .build();
        storeGradeRule.setId(1L);
        when(storeGradeRuleMapper.selectById(1L)).thenReturn(storeGradeRule);

        // 执行方法
        storeGradeRuleProcInstService.doCallback(response);

        // 验证是否调用了 approve 方法（通过验证 ruleChangeTrigger 是否被调用）
        verify(storeGradeService, times(1)).ruleChangeTrigger(any());
    }

    /**
     * 测试用例：状态为 PROCESS_COMPLETED 且节点为 HQ_STORE_TEAM_NODE，应调用 approve 方法
     */
    @Test
    void testDoCallback_WhenStatusIsProcessCompletedAndNodeIsHqStoreTeam_ShouldCallApprove2() {
        // 准备数据
        BpmCallBackParamDto response = BpmCallBackParamDto.builder()
                .status(ProcessReviewStatus.PROCESS_COMPLETED.getCode())
                .taskDefinitionKey(BpmCallBackNodeEnum.HQ_STORE_TEAM_NODE.getNode())
                .businessKey("testBusinessKey")
                .build();
        BpmUser assignee = BpmUser.builder()
                .userName("testUserName")
                .displayName("testDisplayName")
                .personId("testPersonId")
                .build();
        response.setAssignee(assignee);

        CommonApproveLog commonApproveLog = new CommonApproveLog();
        commonApproveLog.setBusinessId("1");
        when(commonApproveLogMapper.selectOne(any())).thenReturn(commonApproveLog);

        StoreGradeRule storeGradeRule = StoreGradeRule.builder()
                .method(TieringModificationMethodEnum.MANUAL_UPLOAD.getKey())
                .build();
        storeGradeRule.setId(1L);
        when(storeGradeRuleMapper.selectById(1L)).thenReturn(storeGradeRule);
        List<StoreGradeRelation> storeGradeRelations = new ArrayList<>();
        StoreGradeRelation storeGradeRelation = new StoreGradeRelation();
        storeGradeRelation.setStoreCode("testStoreCode");
        storeGradeRelation.setStoreGrade("A");
        storeGradeRelations.add(storeGradeRelation);
        when(storeGradeRelationMapper.selectList(any())).thenReturn(storeGradeRelations);

        // 执行方法
        storeGradeRuleProcInstService.doCallback(response);

        // 添加断言验证
        // 验证是否调用了 storeGradeRelationMapper.selectList 方法
        verify(storeGradeRelationMapper, times(1)).selectList(any());

        // 验证是否调用了 commonChangeLogMapper.insert 方法
        verify(commonChangeLogMapper, times(1)).insert(any());

        // 验证是否调用了 mainDataRpc.pushEditStoreBeta 方法
        verify(mainDataRpc, times(1)).pushEditStoreBeta(any());
    }

    /**
     * 测试用例：状态为 PROCESS_COMPLETED 但节点不是 HQ_STORE_TEAM_NODE，不应调用任何方法
     */
    @Test
    void testDoCallback_WhenStatusIsProcessCompletedButNodeNotHqStoreTeam_ShouldNotCallAnyMethod() {
        // 准备数据
        BpmCallBackParamDto response = BpmCallBackParamDto.builder()
                .status(ProcessReviewStatus.PROCESS_COMPLETED.getCode())
                .taskDefinitionKey("otherNode")
                .build();

        // 执行方法
        storeGradeRuleProcInstService.doCallback(response);

        // 验证没有调用任何方法
        verifyNoInteractions(storeGradeRuleMapper, commonApproveLogMapper, storeGradeService, storeGradeRelationMapper,
                commonChangeLogMapper, mainDataRpc);
    }

    /**
     * 测试用例：状态既不是 REJECTED 也不是 PROCESS_COMPLETED，不应调用任何方法
     */
    @Test
    void testDoCallback_WhenStatusIsNeitherRejectedNorProcessCompleted_ShouldNotCallAnyMethod() {
        // 准备数据
        BpmCallBackParamDto response = BpmCallBackParamDto.builder()
                .status("unknownStatus")
                .build();

        // 执行方法
        storeGradeRuleProcInstService.doCallback(response);

        // 验证没有调用任何方法
        verifyNoInteractions(storeGradeRuleMapper, commonApproveLogMapper, storeGradeService, storeGradeRelationMapper,
                commonChangeLogMapper, mainDataRpc);
    }

}
