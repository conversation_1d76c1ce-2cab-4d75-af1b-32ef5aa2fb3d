package com.mi.info.intl.retail.ldu.service.impl;

import com.mi.info.intl.retail.ldu.dto.UpcGoodsInfoDto;
import com.mi.info.intl.retail.ldu.dto.UpcGoodsPagingQueryDto;
import com.mi.info.intl.retail.ldu.dto.UpcGoodsPagingResultDto;
import com.mi.info.intl.retail.ldu.dto.UpcGoodsSyncReqDto;
import com.mi.info.intl.retail.ldu.infra.entity.IntlRmsProduct;
import com.mi.info.intl.retail.ldu.infra.repository.IProductQueryService;
import com.mi.info.intl.retail.ldu.service.IntlRmsProductService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class IntlProductSyncServiceImplTest {

    @InjectMocks
    private IntlProductSyncServiceImpl intlProductSyncService;

    @Mock
    private IProductQueryService productQueryService;

    @Mock
    private IntlRmsProductService intlRmsProductService;

    private UpcGoodsSyncReqDto testSyncReq;
    private UpcGoodsPagingResultDto testPagingResult;
    private UpcGoodsInfoDto testGoodsInfo;

    @BeforeEach
    void setUp() {
        testSyncReq = new UpcGoodsSyncReqDto();
        testSyncReq.setPageSize(100);
        testSyncReq.setStart("2024-01-01 00:00:00");
        testSyncReq.setEnd("2024-01-01 23:59:59");
        testSyncReq.setFilterYouPin(1);
        testSyncReq.setQueryType("ALL");

        testPagingResult = new UpcGoodsPagingResultDto();
        testPagingResult.setTotal(1);
        testPagingResult.setPage(1);
        testPagingResult.setPageSize(100);

        testGoodsInfo = new UpcGoodsInfoDto();
        testGoodsInfo.setGoodsId("TEST001");
        testGoodsInfo.setSpuNameCn("Test Product");
        testGoodsInfo.setSku("123456789012");
        testPagingResult.setData(Arrays.asList(testGoodsInfo));
    }

    @DisplayName("测试全量同步商品信息 - 成功场景")
    @Test
    void testSyncAllProductInfo_Success() {
        // Given
        when(productQueryService.queryUpcGoodsInfosPaging(any(UpcGoodsPagingQueryDto.class)))
                .thenReturn(testPagingResult);
        doNothing().when(intlRmsProductService).batchSaveOrUpdate(anyList());

        // When
        intlProductSyncService.syncAllProductInfo(testSyncReq);

        // Then
        verify(productQueryService, atLeastOnce()).queryUpcGoodsInfosPaging(any(UpcGoodsPagingQueryDto.class));
        verify(intlRmsProductService, atLeastOnce()).batchSaveOrUpdate(anyList());
    }

    @DisplayName("测试全量同步商品信息 - 空数据")
    @Test
    void testSyncAllProductInfo_EmptyData() {
        // Given
        testPagingResult.setData(Collections.emptyList());
        testPagingResult.setTotal(0);
        when(productQueryService.queryUpcGoodsInfosPaging(any(UpcGoodsPagingQueryDto.class)))
                .thenReturn(testPagingResult);

        // When
        intlProductSyncService.syncAllProductInfo(testSyncReq);

        // Then
        verify(productQueryService, atLeastOnce()).queryUpcGoodsInfosPaging(any(UpcGoodsPagingQueryDto.class));
        verify(intlRmsProductService, never()).batchSaveOrUpdate(anyList());
    }

    @DisplayName("测试全量同步商品信息 - 多页数据")
    @Test
    void testSyncAllProductInfo_MultiplePages() {
        // Given
        testPagingResult.setTotal(250);
        testPagingResult.setPage(1);
        testPagingResult.setPageSize(100);
        
        UpcGoodsPagingResultDto secondPage = new UpcGoodsPagingResultDto();
        secondPage.setTotal(250);
        secondPage.setPage(2);
        secondPage.setPageSize(100);
        secondPage.setData(Arrays.asList(testGoodsInfo));

        when(productQueryService.queryUpcGoodsInfosPaging(argThat(query -> query.getPage() == 1)))
                .thenReturn(testPagingResult);
        when(productQueryService.queryUpcGoodsInfosPaging(argThat(query -> query.getPage() == 2)))
                .thenReturn(secondPage);
        doNothing().when(intlRmsProductService).batchSaveOrUpdate(anyList());

        // When
        intlProductSyncService.syncAllProductInfo(testSyncReq);

        // Then
        verify(productQueryService, times(2)).queryUpcGoodsInfosPaging(any(UpcGoodsPagingQueryDto.class));
        verify(intlRmsProductService, times(2)).batchSaveOrUpdate(anyList());
    }
}
