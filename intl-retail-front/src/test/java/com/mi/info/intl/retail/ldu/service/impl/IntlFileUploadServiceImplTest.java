package com.mi.info.intl.retail.ldu.service.impl;

import com.mi.info.intl.retail.exception.RetailRunTimeException;
import com.mi.info.intl.retail.intlretail.service.api.upload.FileUploadInfo;
import com.mi.info.intl.retail.ldu.enums.FileUploadEnum;
import com.mi.info.intl.retail.ldu.infra.mapper.IntlFileUploadMapper;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.api.so.upload.IntlSoImeiApiService;
import com.mi.info.intl.retail.api.so.upload.IntlSoQtyApiService;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@ExtendWith(MockitoExtension.class)
class IntlFileUploadServiceImplTest {
    @Mock
    private IntlFileUploadMapper intlFileUploadMapper;

    @Mock
    private IntlSoImeiApiService intlSoImeiApiService;

    @Mock
    private IntlSoQtyApiService intlSoQtyApiService;

    @InjectMocks
    private IntlFileUploadServiceImpl intlFileUploadService;

    private FileUploadInfo buildFileUploadInfo() {
        FileUploadInfo.MetaData metaData = new FileUploadInfo.MetaData("guid-123", Collections.singletonList("http://fds.com/file1.jpg"));
        FileUploadInfo info = new FileUploadInfo();
        info.setMetaDataList(Collections.singletonList(metaData));
        info.setModuleName(FileUploadEnum.LDU_INSPECTION.getCode());
        info.setUploaderName("tester");
        info.setOfflineUpload(false);
        info.setUploadedToBlob(false);
        info.setNoWatermark(false);
        info.setRelatedId(100L);
        return info;
    }

    @Test
    public void testSave_parameter_missing(){
        FileUploadInfo fileUploadInfo1 = buildFileUploadInfo();
        FileUploadInfo fileUploadInfo = new FileUploadInfo();
        RetailRunTimeException exp =
                assertThrows(RetailRunTimeException.class, () -> intlFileUploadService.save(fileUploadInfo));
        assertEquals("metaDataList can not be null",exp.getMessage());
        fileUploadInfo.setMetaDataList(fileUploadInfo1.getMetaDataList());
        exp = assertThrows(RetailRunTimeException.class, () -> intlFileUploadService.save(fileUploadInfo));
        assertEquals("uploaderName can not be null",exp.getMessage());
        fileUploadInfo.setUploaderName("tester");
        exp = assertThrows(RetailRunTimeException.class, () -> intlFileUploadService.save(fileUploadInfo));
        assertEquals("unsupported module name",exp.getMessage());
    }

    @Test
    void testSave() {
        when(intlFileUploadMapper.batchInsertDatas(any())).thenReturn(1);
        FileUploadInfo info = buildFileUploadInfo();
        CommonApiResponse<Object> response = intlFileUploadService.save(info);
        assertEquals(0, response.getCode());
        verify(intlFileUploadMapper, times(1)).batchInsertDatas(any());
    }

    @Test
    void testSaveSimple() {
        when(intlFileUploadMapper.batchInsertDatas(any())).thenReturn(1);
        FileUploadInfo.MetaData metaData = new FileUploadInfo.MetaData("guid-456", Collections.singletonList("http://fds.com/file2.jpg"));
        CommonApiResponse<Object> response = intlFileUploadService.saveSimple(Collections.singletonList(metaData), FileUploadEnum.LDU_INSPECTION, "tester");
        assertEquals(0, response.getCode());
        verify(intlFileUploadMapper, times(1)).batchInsertDatas(any());
    }

    @Test
    void testSaveSimple_multi_images() {
        when(intlFileUploadMapper.batchInsertDatas(any())).thenReturn(1);
        FileUploadInfo.MetaData metaData = new FileUploadInfo.MetaData("guid-456",
                Stream.generate(() -> "http://fds.com/file2" +
                        ".jpg").limit(300).collect(Collectors.toList()));
        CommonApiResponse<Object> response = intlFileUploadService.saveSimple(Collections.singletonList(metaData), FileUploadEnum.LDU_INSPECTION, "tester");
        assertEquals(0, response.getCode());
        verify(intlFileUploadMapper, times(2)).batchInsertDatas(any());
    }

    @Test
    void testSaveWithImeiUpload() {
        // 准备测试数据
        FileUploadInfo fileUploadInfo = new FileUploadInfo();
        fileUploadInfo.setModuleName("imei_upload");
        fileUploadInfo.setUploaderName("testUser");

        FileUploadInfo.MetaData metaData1 = new FileUploadInfo.MetaData("guid1", Arrays.asList("http://example.com/photo1.jpg"));
        FileUploadInfo.MetaData metaData2 = new FileUploadInfo.MetaData("guid2", Arrays.asList("http://example.com/photo2.jpg"));
        fileUploadInfo.setMetaDataList(Arrays.asList(metaData1, metaData2));

        // Mock 依赖
        doNothing().when(intlFileUploadMapper).batchInsertDatas(anyList());
        when(intlSoImeiApiService.updateIsPhotoExistByDetailIds(anyList())).thenReturn(2);

        // 执行测试
        CommonApiResponse<Object> result = intlFileUploadService.save(fileUploadInfo);

        // 验证结果
        assertEquals(0, result.getCode());

        // 验证调用
        verify(intlFileUploadMapper, times(1)).batchInsertDatas(anyList());
        verify(intlSoImeiApiService, times(1)).updateIsPhotoExistByDetailIds(Arrays.asList("guid1", "guid2"));
        verify(intlSoQtyApiService, never()).updateIsPhotoExistByDetailIds(anyList());
    }

    @Test
    void testSaveWithQtyUpload() {
        // 准备测试数据
        FileUploadInfo fileUploadInfo = new FileUploadInfo();
        fileUploadInfo.setModuleName("qty_upload");
        fileUploadInfo.setUploaderName("testUser");

        FileUploadInfo.MetaData metaData1 = new FileUploadInfo.MetaData("guid3", Arrays.asList("http://example.com/photo3.jpg"));
        FileUploadInfo.MetaData metaData2 = new FileUploadInfo.MetaData("guid4", Arrays.asList("http://example.com/photo4.jpg"));
        fileUploadInfo.setMetaDataList(Arrays.asList(metaData1, metaData2));

        // Mock 依赖
        doNothing().when(intlFileUploadMapper).batchInsertDatas(anyList());
        when(intlSoQtyApiService.updateIsPhotoExistByDetailIds(anyList())).thenReturn(2);

        // 执行测试
        CommonApiResponse<Object> result = intlFileUploadService.save(fileUploadInfo);

        // 验证结果
        assertEquals(0, result.getCode());

        // 验证调用
        verify(intlFileUploadMapper, times(1)).batchInsertDatas(anyList());
        verify(intlSoQtyApiService, times(1)).updateIsPhotoExistByDetailIds(Arrays.asList("guid3", "guid4"));
        verify(intlSoImeiApiService, never()).updateIsPhotoExistByDetailIds(anyList());
    }
}