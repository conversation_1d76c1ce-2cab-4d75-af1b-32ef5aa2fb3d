package com.mi.info.intl.retail.ldu.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mi.info.intl.retail.exception.RetailRunTimeException;
import com.mi.info.intl.retail.intlretail.service.api.ldu.IntlLduSnService;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.*;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.RmsUserBaseDataResponse;
import com.mi.info.intl.retail.ldu.dto.StoreInfoDTO;
import com.mi.info.intl.retail.ldu.enums.ResultCodeEnum;
import com.mi.info.intl.retail.ldu.infra.mapper.IntlLduReportLogMapper;
import com.mi.info.intl.retail.ldu.infra.repository.IProductQueryService;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.org.infra.entity.IntlRmsCountryTimezone;
import com.mi.info.intl.retail.org.infra.entity.IntlRmsStore;
import com.mi.info.intl.retail.org.infra.mapper.IntlRmsCountryTimezoneMapper;
import com.mi.info.intl.retail.org.infra.mapper.IntlRmsPositionMapper;
import com.mi.info.intl.retail.org.infra.mapper.IntlRmsStoreMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.*;
import com.mi.info.intl.retail.org.infra.mapper.read.InspectionRecordReadMapper;
import com.mi.info.intl.retail.api.front.position.IntlPositionApiService;
import com.mi.info.intl.retail.api.front.dto.RmsPositionInfoRes;
import org.apache.dubbo.rpc.RpcContext;
import org.mockito.MockedStatic;
import java.math.BigDecimal;

@ExtendWith(MockitoExtension.class)
class IntlLduReportLogServiceImplTest {

    @InjectMocks
    private IntlLduReportLogServiceImpl intlLduReportLogService;

    @Mock
    private IntlLduReportLogMapper statisticsMapper;

    @Mock
    private IProductQueryService productQueryService;

    @Mock
    private IntlRmsPositionMapper intlRmsPositionMapper;

    @Mock
    private IntlRmsStoreMapper intlRmsStoreMapper;

    @Mock
    private IntlRmsCountryTimezoneMapper intlRmsCountryTimezoneMapper;

    @Mock
    private IntlLduSnService intlLduSnService;

    @Mock
    private InspectionRecordReadMapper inspectionRecordReadMapper;

    @Mock
    private IntlPositionApiService intlPositionApiService;

    private IntlLduReportReq testRequest;

    @BeforeEach
    void setUp() {
        testRequest = new IntlLduReportReq();
        testRequest.setPageNum(1L);
        testRequest.setPageSize(10L);
        testRequest.setCountryCode(Arrays.asList("ID"));
    }

    @DisplayName("测试分页查询 - 成功场景")
    @Test
    void testPageList_Success() {
        // Given
        when(statisticsMapper.pageList(any(IntlLduReportReq.class))).thenReturn(Collections.emptyList());
        when(statisticsMapper.pageListCount(any(IntlLduReportReq.class))).thenReturn(0);

        // When
        CommonResponse<Page<IntlLduReportLogDto>> result = intlLduReportLogService.pageList(testRequest);

        // Then
        assertNotNull(result);
        assertNotNull(result.getData());
        assertEquals(0, result.getData().getTotal());
        assertEquals(0, result.getData().getRecords().size());
    }

    @DisplayName("测试分页查询 - 带产品线过滤")
    @Test
    void testPageList_WithProductLine() {
        // Given
        testRequest.setProductLine("1001");
        ProductLineDto productLineDto = new ProductLineDto();
        productLineDto.setEnName("Mobile");

        when(productQueryService.getProductLineById("1001")).thenReturn(productLineDto);
        when(statisticsMapper.pageList(any(IntlLduReportReq.class))).thenReturn(Collections.emptyList());
        when(statisticsMapper.pageListCount(any(IntlLduReportReq.class))).thenReturn(0);

        // When
        CommonResponse<Page<IntlLduReportLogDto>> result = intlLduReportLogService.pageList(testRequest);

        // Then
        assertNotNull(result);
        assertNotNull(result.getData());
    }

    @DisplayName("测试历史记录查询 - 成功场景")
    @Test
    void testHistoryList_Success() {
        // Given
        when(statisticsMapper.historyList(any(IntlLduReportReq.class))).thenReturn(Collections.emptyList());
        when(statisticsMapper.historyListCount(any(IntlLduReportReq.class))).thenReturn(0);

        // When
        CommonApiResponse<Page<IntlLduReportLogDto>> result = intlLduReportLogService.historyList(testRequest);

        // Then
        assertNotNull(result);
        assertNotNull(result.getData());
        assertEquals(0, result.getData().getTotal());
        assertEquals(0, result.getData().getRecords().size());
    }

    // ==================== getStoreInfo 方法测试 ====================

    @DisplayName("测试获取门店信息 - 成功场景")
    @Test
    void testGetStoreInfo_Success() {
        // Given - 准备数据
        RmsPositionReq request = new RmsPositionReq();
        request.setPositionCode("POS001");
        request.setStoreCode("STORE001");

        IntlRmsStore mockStore = new IntlRmsStore();
        mockStore.setStoreId("STORE001");
        mockStore.setName("小米授权店");
        mockStore.setChannelType(1);
        mockStore.setChannelTypeName("直营店");

        // Mock - 设置模拟行为
        when(intlRmsPositionMapper.getStoreInfo(request)).thenReturn(mockStore);

        // When - 执行测试
        CommonResponse<StroeInfoDto> result = intlLduReportLogService.getStoreInfo(request);

        // Then - 验证结果
        assertNotNull(result);
        assertEquals(0, result.getCode()); // code为0表示成功
        assertNotNull(result.getData());

        StroeInfoDto storeInfo = result.getData();
        assertEquals("STORE001", storeInfo.getStoreId());
        assertEquals("小米授权店", storeInfo.getStoreName());
        assertEquals(1, storeInfo.getChannelType());
        assertEquals("直营店", storeInfo.getChannelTypeDesc());
    }

    @DisplayName("测试获取门店信息 - 阵地编号为空")
    @Test
    void testGetStoreInfo_PositionCodeEmpty() {
        // Given - 准备数据
        RmsPositionReq request = new RmsPositionReq();
        request.setPositionCode("");
        request.setStoreCode("STORE001");

        // When & Then - 执行测试并验证异常
        RetailRunTimeException exception = assertThrows(RetailRunTimeException.class,
                () -> intlLduReportLogService.getStoreInfo(request));

        assertEquals("Sales point information does not exist", exception.getMessage());
    }

    @DisplayName("测试获取门店信息 - 阵地编号为null")
    @Test
    void testGetStoreInfo_PositionCodeNull() {
        // Given - 准备数据
        RmsPositionReq request = new RmsPositionReq();
        request.setPositionCode(null);
        request.setStoreCode("STORE001");

        // When & Then - 执行测试并验证异常
        RetailRunTimeException exception = assertThrows(RetailRunTimeException.class,
                () -> intlLduReportLogService.getStoreInfo(request));

        assertEquals("Sales point information does not exist", exception.getMessage());
    }

    @DisplayName("测试获取门店信息 - 门店信息不存在")
    @Test
    void testGetStoreInfo_StoreNotFound() {
        // Given - 准备数据
        RmsPositionReq request = new RmsPositionReq();
        request.setPositionCode("POS001");
        request.setStoreCode("STORE001");

        // Mock - 设置模拟行为，返回null表示门店不存在
        when(intlRmsPositionMapper.getStoreInfo(request)).thenReturn(null);

        // When & Then - 执行测试并验证异常
        RetailRunTimeException exception = assertThrows(RetailRunTimeException.class,
                () -> intlLduReportLogService.getStoreInfo(request));

        assertEquals("Sales point information does not exist", exception.getMessage());
    }

    @DisplayName("测试获取门店信息 - 门店信息完整")
    @Test
    void testGetStoreInfo_CompleteStoreInfo() {
        // Given - 准备数据
        RmsPositionReq request = new RmsPositionReq();
        request.setPositionCode("POS001");
        request.setStoreCode("STORE001");

        IntlRmsStore mockStore = new IntlRmsStore();
        mockStore.setStoreId("STORE001");
        mockStore.setName("小米授权体验店");
        mockStore.setChannelType(2);
        mockStore.setChannelTypeName("授权店");
        mockStore.setCode("STORE001");
        mockStore.setType(1);
        mockStore.setTypeName("体验店");

        // Mock - 设置模拟行为
        when(intlRmsPositionMapper.getStoreInfo(request)).thenReturn(mockStore);

        // When - 执行测试
        CommonResponse<StroeInfoDto> result = intlLduReportLogService.getStoreInfo(request);

        // Then - 验证结果
        assertNotNull(result);
        assertEquals(0, result.getCode()); // code为0表示成功
        assertNotNull(result.getData());

        StroeInfoDto storeInfo = result.getData();
        assertEquals("STORE001", storeInfo.getStoreId());
        assertEquals("小米授权体验店", storeInfo.getStoreName());
        assertEquals(2, storeInfo.getChannelType());
        assertEquals("授权店", storeInfo.getChannelTypeDesc());
    }

    @DisplayName("测试获取门店信息 - 边界值测试")
    @Test
    void testGetStoreInfo_BoundaryValues() {
        // Given - 准备数据，测试边界值
        RmsPositionReq request = new RmsPositionReq();
        request.setPositionCode("A"); // 最短阵地编号
        request.setStoreCode("STORE001");

        IntlRmsStore mockStore = new IntlRmsStore();
        mockStore.setStoreId("STORE001");
        mockStore.setName(""); // 空门店名称
        mockStore.setChannelType(0); // 最小渠道类型值
        mockStore.setChannelTypeName(""); // 空渠道类型名称

        // Mock - 设置模拟行为
        when(intlRmsPositionMapper.getStoreInfo(request)).thenReturn(mockStore);

        // When - 执行测试
        CommonResponse<StroeInfoDto> result = intlLduReportLogService.getStoreInfo(request);

        // Then - 验证结果
        assertNotNull(result);
        assertEquals(0, result.getCode()); // code为0表示成功
        assertNotNull(result.getData());

        StroeInfoDto storeInfo = result.getData();
        assertEquals("STORE001", storeInfo.getStoreId());
        assertEquals("", storeInfo.getStoreName());
        assertEquals(0, storeInfo.getChannelType());
        assertEquals("", storeInfo.getChannelTypeDesc());
    }

    @DisplayName("测试获取门店信息 - 特殊字符阵地编号")
    @Test
    void testGetStoreInfo_SpecialCharacters() {
        // Given - 准备数据，测试特殊字符
        RmsPositionReq request = new RmsPositionReq();
        request.setPositionCode("POS_001-ABC");
        request.setStoreCode("STORE001");

        IntlRmsStore mockStore = new IntlRmsStore();
        mockStore.setStoreId("STORE001");
        mockStore.setName("小米授权店");
        mockStore.setChannelType(1);
        mockStore.setChannelTypeName("直营店");

        // Mock - 设置模拟行为
        when(intlRmsPositionMapper.getStoreInfo(request)).thenReturn(mockStore);

        // When - 执行测试
        CommonResponse<StroeInfoDto> result = intlLduReportLogService.getStoreInfo(request);

        // Then - 验证结果
        assertNotNull(result);
        assertEquals(0, result.getCode()); // code为0表示成功
        assertNotNull(result.getData());

        StroeInfoDto storeInfo = result.getData();
        assertEquals("STORE001", storeInfo.getStoreId());
        assertEquals("小米授权店", storeInfo.getStoreName());
        assertEquals(1, storeInfo.getChannelType());
        assertEquals("直营店", storeInfo.getChannelTypeDesc());
    }

    @DisplayName("测试获取门店信息 - 验证Mapper调用")
    @Test
    void testGetStoreInfo_VerifyMapperCall() {
        // Given - 准备数据
        RmsPositionReq request = new RmsPositionReq();
        request.setPositionCode("POS001");
        request.setStoreCode("STORE001");

        IntlRmsStore mockStore = new IntlRmsStore();
        mockStore.setStoreId("STORE001");
        mockStore.setName("小米授权店");
        mockStore.setChannelType(1);
        mockStore.setChannelTypeName("直营店");

        // Mock - 设置模拟行为
        when(intlRmsPositionMapper.getStoreInfo(request)).thenReturn(mockStore);

        // When - 执行测试
        intlLduReportLogService.getStoreInfo(request);

        // Then - 验证Mapper方法被正确调用
        // 注意：由于我们使用的是Mockito，这里主要是验证方法能够正常执行
        // 如果需要验证具体的调用次数和参数，可以使用verify方法
        assertDoesNotThrow(() -> intlLduReportLogService.getStoreInfo(request));
    }

    // ==================== submit 方法测试 ====================



    @DisplayName("测试submit - 成功提交")
    @Test
    void testSubmit_Success() {
        // Given - 准备数据
        StoreInfoDTO storeInfo = new StoreInfoDTO();
        storeInfo.setCountryId("1");
        storeInfo.setStoreName("Test Store");

        IntlLduReportSubmitReq intlLduReportDataReq = new IntlLduReportSubmitReq();
        RmsUserBaseDataResponse userBaseInfo = new RmsUserBaseDataResponse();

        intlLduReportDataReq.setSubmitType(1);
        userBaseInfo.setStoreName("Test Store");

        IntlRmsCountryTimezone countryTimezone = new IntlRmsCountryTimezone();
        countryTimezone.setCountryCode("ID");
        countryTimezone.setCountryId("1");

        // Mock - 设置模拟行为
        when(intlRmsStoreMapper.getStoreByPositionCode("POS001")).thenReturn(storeInfo);
        when(intlRmsCountryTimezoneMapper.selectByCountryId("1")).thenReturn(countryTimezone);
        when(intlLduSnService.selectBySnAndCountryCode(any(), any())).thenReturn(Collections.emptyList());

        // When - 执行测试
        CommonResponse<Integer> result = intlLduReportLogService.submit(intlLduReportDataReq, userBaseInfo);

        // Then - 验证结果
        assertNotNull(result);
        assertEquals(0, result.getCode()); // 0表示成功
    }

    @DisplayName("测试submit - 提交类型为空")
    @Test
    void testSubmit_SubmitTypeNull() {
        // Given - 准备数据
        IntlLduReportSubmitReq testSubmitRequest = new IntlLduReportSubmitReq();
        RmsUserBaseDataResponse testUserBaseInfo = new RmsUserBaseDataResponse();

        testSubmitRequest.setSubmitType(1);
        testUserBaseInfo.setStoreName("Test Store");

        testSubmitRequest.setSubmitType(null);

        // When - 执行测试
        CommonResponse<Integer> result = intlLduReportLogService.submit(testSubmitRequest, testUserBaseInfo);

        // Then - 验证结果
        assertNotNull(result);
        assertNotEquals(0, result.getCode());
        assertEquals("Submission type cannot be empty", result.getMessage());
    }

    @DisplayName("测试submit - 商品列表为空")
    @Test
    void testSubmit_GoodsListNull() {
        IntlLduReportSubmitReq testSubmitRequest = new IntlLduReportSubmitReq();
        RmsUserBaseDataResponse testUserBaseInfo = new RmsUserBaseDataResponse();

        testSubmitRequest.setSubmitType(1);
        testUserBaseInfo.setStoreName("Test Store");

        // When - 执行测试
        CommonResponse<Integer> result = intlLduReportLogService.submit(testSubmitRequest, testUserBaseInfo);

        // Then - 验证结果
        assertNotNull(result);
        assertNotEquals(0, result.getCode());
        assertEquals("Product list cannot be empty", result.getMessage());
    }

    @DisplayName("测试submit - 阵地编号为空")
    @Test
    void testSubmit_PositionCodeBlank() {
        IntlLduReportSubmitReq testSubmitRequest = new IntlLduReportSubmitReq();
        RmsUserBaseDataResponse testUserBaseInfo = new RmsUserBaseDataResponse();

        testSubmitRequest.setSubmitType(1);
        testUserBaseInfo.setStoreName("Test Store");
        // When - 执行测试
        CommonResponse<Integer> result = intlLduReportLogService.submit(testSubmitRequest, testUserBaseInfo);

        // Then - 验证结果
        assertNotNull(result);
        assertNotEquals(0, result.getCode());
        assertEquals("Sales point information does not exist", result.getMessage());
    }

    @DisplayName("测试submit - 门店信息不存在")
    @Test
    void testSubmit_StoreNotFound() {
        IntlLduReportSubmitReq testSubmitRequest = new IntlLduReportSubmitReq();
        RmsUserBaseDataResponse testUserBaseInfo = new RmsUserBaseDataResponse();

        testSubmitRequest.setSubmitType(1);
        testUserBaseInfo.setStoreName("Test Store");
        // Mock - 设置模拟行为，返回null表示门店不存在
        when(intlRmsStoreMapper.getStoreByPositionCode("POS001")).thenReturn(null);

        // When - 执行测试
        CommonResponse<Integer> result = intlLduReportLogService.submit(testSubmitRequest, testUserBaseInfo);

        // Then - 验证结果
        assertNotNull(result);
        assertNotEquals(0, result.getCode());
        assertEquals("Country code does not have a corresponding region name", result.getMessage());
    }

    // ==================== getTaskTypeSummary 方法测试 ====================

    @DisplayName("测试getTaskTypeSummary - 参数验证失败：开始时间为空")
    @Test
    void testGetTaskTypeSummary_TaskStartTimeNull() {
        // Given
        IntInspectionReq query = new IntInspectionReq();
        query.setTaskStartTime(null);
        query.setTaskEndTime(System.currentTimeMillis());

        // When
        CommonResponse<Map<String, CoverageStatisticsResultDto>> result = intlLduReportLogService.getTaskTypeSummary(query);

        // Then
        assertNotNull(result);
        assertEquals(ResultCodeEnum.PARAM_ERROR.getCode(), result.getCode());
        assertEquals("Start time or end time cannot be empty", result.getMessage());
    }

    @DisplayName("测试getTaskTypeSummary - 参数验证失败：结束时间为空")
    @Test
    void testGetTaskTypeSummary_TaskEndTimeNull() {
        // Given
        IntInspectionReq query = new IntInspectionReq();
        query.setTaskStartTime(System.currentTimeMillis());
        query.setTaskEndTime(null);

        // When
        CommonResponse<Map<String, CoverageStatisticsResultDto>> result = intlLduReportLogService.getTaskTypeSummary(query);

        // Then
        assertNotNull(result);
        assertEquals(ResultCodeEnum.PARAM_ERROR.getCode(), result.getCode());
        assertEquals("Start time or end time cannot be empty", result.getMessage());
    }

    @DisplayName("测试getTaskTypeSummary - 参数验证失败：开始和结束时间都为空")
    @Test
    void testGetTaskTypeSummary_BothTimesNull() {
        // Given
        IntInspectionReq query = new IntInspectionReq();
        query.setTaskStartTime(null);
        query.setTaskEndTime(null);

        // When
        CommonResponse<Map<String, CoverageStatisticsResultDto>> result = intlLduReportLogService.getTaskTypeSummary(query);

        // Then
        assertNotNull(result);
        assertEquals(ResultCodeEnum.PARAM_ERROR.getCode(), result.getCode());
        assertEquals("Start time or end time cannot be empty", result.getMessage());
    }

    @DisplayName("测试getTaskTypeSummary - 成功场景：返回空结果")
    @Test
    void testGetTaskTypeSummary_EmptyResult() {
        // Given
        IntInspectionReq query = new IntInspectionReq();
        query.setTaskStartTime(System.currentTimeMillis() - 86400000L); // 昨天
        query.setTaskEndTime(System.currentTimeMillis());
        query.setCountryCode(Arrays.asList("US", "CN"));

        // Mock RpcContext to avoid null pointer exception
        try (MockedStatic<RpcContext> mockedRpcContext = mockStatic(RpcContext.class)) {
            RpcContext mockContext = mock(RpcContext.class);
            mockedRpcContext.when(RpcContext::getContext).thenReturn(mockContext);
            when(mockContext.getAttachment("$area_id")).thenReturn("US");

            // Mock mapper to return empty list
            when(inspectionRecordReadMapper.getTaskTypeSummary(any(IntInspectionReq.class)))
                    .thenReturn(Collections.emptyList());

            // When
            CommonResponse<Map<String, CoverageStatisticsResultDto>> result = intlLduReportLogService.getTaskTypeSummary(query);

            // Then
            assertNotNull(result);
            assertEquals(0, result.getCode()); // 成功
            assertNotNull(result.getData());
            assertTrue(result.getData().isEmpty());
        }
    }

    @DisplayName("测试getTaskTypeSummary - 成功场景：有数据返回")
    @Test
    void testGetTaskTypeSummary_WithData() {
        // Given
        IntInspectionReq query = new IntInspectionReq();
        query.setTaskStartTime(System.currentTimeMillis() - 86400000L);
        query.setTaskEndTime(System.currentTimeMillis());
        query.setCountryCode(Arrays.asList("US"));

        // 准备测试数据
        List<CoverageStatisticsDto> mockData = createMockCoverageStatisticsData();

        // Mock RpcContext
        try (MockedStatic<RpcContext> mockedRpcContext = mockStatic(RpcContext.class)) {
            RpcContext mockContext = mock(RpcContext.class);
            mockedRpcContext.when(RpcContext::getContext).thenReturn(mockContext);
            when(mockContext.getAttachment("$area_id")).thenReturn("US");

            // Mock mapper
            when(inspectionRecordReadMapper.getTaskTypeSummary(any(IntInspectionReq.class)))
                    .thenReturn(mockData);

            // Mock position API service for getBestPositions calls
            when(intlPositionApiService.getBestPositions(any()))
                    .thenReturn(Collections.emptyList());

            // When
            CommonResponse<Map<String, CoverageStatisticsResultDto>> result = intlLduReportLogService.getTaskTypeSummary(query);

            // Then
            assertNotNull(result);
            assertEquals(0, result.getCode());
            assertNotNull(result.getData());
            // 验证有数据返回
            assertFalse(result.getData().isEmpty());
            
            // 验证mapper被调用
            verify(inspectionRecordReadMapper, times(1)).getTaskTypeSummary(any(IntInspectionReq.class));
        }
    }

    @DisplayName("测试getTaskTypeSummary - 成功场景：自动设置区域代码")
    @Test
    void testGetTaskTypeSummary_AutoSetAreaId() {
        // Given
        IntInspectionReq query = new IntInspectionReq();
        query.setTaskStartTime(System.currentTimeMillis() - 86400000L);
        query.setTaskEndTime(System.currentTimeMillis());
        // 不设置countryCode，让方法自动从RpcContext获取

        // Mock RpcContext
        try (MockedStatic<RpcContext> mockedRpcContext = mockStatic(RpcContext.class)) {
            RpcContext mockContext = mock(RpcContext.class);
            mockedRpcContext.when(RpcContext::getContext).thenReturn(mockContext);
            when(mockContext.getAttachment("$area_id")).thenReturn("EU");

            // Mock mapper
            when(inspectionRecordReadMapper.getTaskTypeSummary(any(IntInspectionReq.class)))
                    .thenReturn(Collections.emptyList());

            // When
            CommonResponse<Map<String, CoverageStatisticsResultDto>> result = intlLduReportLogService.getTaskTypeSummary(query);

            // Then
            assertNotNull(result);
            assertEquals(0, result.getCode());
            // 验证countryCode被自动设置
            assertEquals(Collections.singletonList("EU"), query.getCountryCode());
        }
    }

    @DisplayName("测试getTaskTypeSummary - 成功场景：GLOBAL区域不自动设置")
    @Test
    void testGetTaskTypeSummary_GlobalAreaNotSet() {
        // Given
        IntInspectionReq query = new IntInspectionReq();
        query.setTaskStartTime(System.currentTimeMillis() - 86400000L);
        query.setTaskEndTime(System.currentTimeMillis());
        // 不设置countryCode

        // Mock RpcContext
        try (MockedStatic<RpcContext> mockedRpcContext = mockStatic(RpcContext.class)) {
            RpcContext mockContext = mock(RpcContext.class);
            mockedRpcContext.when(RpcContext::getContext).thenReturn(mockContext);
            when(mockContext.getAttachment("$area_id")).thenReturn("GLOBAL");

            // Mock mapper
            when(inspectionRecordReadMapper.getTaskTypeSummary(any(IntInspectionReq.class)))
                    .thenReturn(Collections.emptyList());

            // When
            CommonResponse<Map<String, CoverageStatisticsResultDto>> result = intlLduReportLogService.getTaskTypeSummary(query);

            // Then
            assertNotNull(result);
            assertEquals(0, result.getCode());
            // 验证countryCode没有被自动设置
            assertNull(query.getCountryCode());
        }
    }

    @DisplayName("测试getTaskTypeSummary - 异常处理")
    @Test
    void testGetTaskTypeSummary_ExceptionHandling() {
        // Given
        IntInspectionReq query = new IntInspectionReq();
        query.setTaskStartTime(System.currentTimeMillis() - 86400000L);
        query.setTaskEndTime(System.currentTimeMillis());
        query.setCountryCode(Arrays.asList("US"));

        // Mock RpcContext
        try (MockedStatic<RpcContext> mockedRpcContext = mockStatic(RpcContext.class)) {
            RpcContext mockContext = mock(RpcContext.class);
            mockedRpcContext.when(RpcContext::getContext).thenReturn(mockContext);
            when(mockContext.getAttachment("$area_id")).thenReturn("US");

            // Mock mapper to throw exception
            when(inspectionRecordReadMapper.getTaskTypeSummary(any(IntInspectionReq.class)))
                    .thenThrow(new RuntimeException("Database error"));

            // When & Then
            assertThrows(RuntimeException.class, () -> {
                intlLduReportLogService.getTaskTypeSummary(query);
            });
        }
    }

    @DisplayName("测试getTaskTypeSummary - 验证方法调用")
    @Test
    void testGetTaskTypeSummary_VerifyMethodCalls() {
        // Given
        IntInspectionReq query = new IntInspectionReq();
        query.setTaskStartTime(System.currentTimeMillis() - 86400000L);
        query.setTaskEndTime(System.currentTimeMillis());
        query.setCountryCode(Arrays.asList("CN"));

        // 准备测试数据
        List<CoverageStatisticsDto> mockData = createMockCoverageStatisticsData();

        // Mock RpcContext
        try (MockedStatic<RpcContext> mockedRpcContext = mockStatic(RpcContext.class)) {
            RpcContext mockContext = mock(RpcContext.class);
            mockedRpcContext.when(RpcContext::getContext).thenReturn(mockContext);
            when(mockContext.getAttachment("$area_id")).thenReturn("CN");

            // Mock mapper
            when(inspectionRecordReadMapper.getTaskTypeSummary(any(IntInspectionReq.class)))
                    .thenReturn(mockData);

            // Mock position API service
            when(intlPositionApiService.getBestPositions(any()))
                    .thenReturn(Arrays.asList(new RmsPositionInfoRes(), new RmsPositionInfoRes()));

            // When
            intlLduReportLogService.getTaskTypeSummary(query);

            // Then - 验证方法被正确调用
            verify(inspectionRecordReadMapper, times(1)).getTaskTypeSummary(query);
            // 验证intlPositionApiService.getBestPositions被调用（根据数据量可能多次）
            verify(intlPositionApiService, atLeastOnce()).getBestPositions(any());
        }
    }

    /**
     * 创建模拟的覆盖统计数据
     */
    private List<CoverageStatisticsDto> createMockCoverageStatisticsData() {
        List<CoverageStatisticsDto> mockData = new ArrayList<>();
        
        // 创建第一个任务类型的数据
        CoverageStatisticsDto dto1 = new CoverageStatisticsDto();
        dto1.setTaskType("401"); // DUMMY
        dto1.setCountry("US");
        dto1.setRegion("North America");
        dto1.setProductLine("Mobile");
        dto1.setProjectCode("PRJ001");
        dto1.setTargetType("1");
        dto1.setRuleId(1L);
        dto1.setTargetId("1");
        dto1.setDummyStoreCoverage(BigDecimal.valueOf(100));
        dto1.setPosmStoreCoverage(BigDecimal.valueOf(50));
        dto1.setPriceTagCoverageTarget(BigDecimal.valueOf(80));
        dto1.setLduStoreCoverage(BigDecimal.valueOf(60));
        dto1.setIntlRmsPositionDtos(new ArrayList<>());
        mockData.add(dto1);
        
        // 创建第二个任务类型的数据
        CoverageStatisticsDto dto2 = new CoverageStatisticsDto();
        dto2.setTaskType("501"); // POSM
        dto2.setCountry("US");
        dto2.setRegion("North America");
        dto2.setProductLine("Laptop");
        dto2.setProjectCode("PRJ002");
        dto2.setTargetType("2");
        dto2.setRuleId(2L);
        dto2.setTargetId("2");
        dto2.setDummyStoreCoverage(BigDecimal.valueOf(70));
        dto2.setPosmStoreCoverage(BigDecimal.valueOf(90));
        dto2.setPriceTagCoverageTarget(BigDecimal.valueOf(75));
        dto2.setLduStoreCoverage(BigDecimal.valueOf(85));
        dto2.setIntlRmsPositionDtos(new ArrayList<>());
        mockData.add(dto2);
        
        return mockData;
    }
}
