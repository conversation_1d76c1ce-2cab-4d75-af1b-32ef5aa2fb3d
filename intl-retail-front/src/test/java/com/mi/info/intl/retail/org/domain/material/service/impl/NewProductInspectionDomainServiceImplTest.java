package com.mi.info.intl.retail.org.domain.material.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.mi.info.intl.retail.cooperation.task.domain.RmsCountryTimezoneService;
import com.mi.info.intl.retail.cooperation.task.dto.dto.IntlRmsUserDto;
import com.mi.info.intl.retail.cooperation.task.inspection.IntlRmsUserService;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.InspectionRecordPageItem;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.InspectionRecordPageRequest;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialInspectionDetailRequest;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialInspectionDetailResponse;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialInspectionItem;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialInspectionOperationHistoryResponse;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialInspectionRecordPageDTO;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialInspectionRecordSelectorItem;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialInspectionReq;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialInspectionVerifyRequest;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialPhotoGroup;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialSampleRequest;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialSampleResponse;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.SubmitMaterialInspectionRequest;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.TaskRemindDTO;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.UploadMaterialData;
import com.mi.info.intl.retail.intlretail.service.api.material.enums.BusinessTypeEnum;
import com.mi.info.intl.retail.intlretail.service.api.material.enums.TargetTypeEnum;
import com.mi.info.intl.retail.intlretail.service.api.material.enums.TaskTypeEnum;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.TaskCenterFinishReq;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.TaskCenterTaskReq;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.MaterialDisapproveReasonEnum;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.MaterialInspectionStatus;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.TaskStatusEnum;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.VerifyActionEnum;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.taskcenter.TaskCenterTaskNumReq;
import com.mi.info.intl.retail.ldu.dto.LduReportSimple;
import com.mi.info.intl.retail.ldu.infra.repository.LduReportRepository;
import com.mi.info.intl.retail.ldu.service.IntlFileUploadService;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.model.PageResponse;
import com.mi.info.intl.retail.model.UserInfo;
import com.mi.info.intl.retail.org.domain.InspectionHistoryDomain;
import com.mi.info.intl.retail.org.domain.InspectionRecordDomain;
import com.mi.info.intl.retail.org.domain.IntlStoreMaterialStatusDomain;
import com.mi.info.intl.retail.org.domain.MaterialInspectionDomain;
import com.mi.info.intl.retail.org.domain.PositionDomain;
import com.mi.info.intl.retail.org.domain.RuleConfigDomain;
import com.mi.info.intl.retail.org.domain.repository.InspectionHistoryRepository;
import com.mi.info.intl.retail.org.domain.repository.InspectionRecordRepository;
import com.mi.info.intl.retail.org.domain.repository.IntlStoreMaterialStatusRepository;
import com.mi.info.intl.retail.org.domain.repository.NewProductInspectionRepository;
import com.mi.info.intl.retail.org.domain.repository.PositionRepository;
import com.mi.info.intl.retail.org.domain.repository.RuleConfigRepository;
import com.mi.info.intl.retail.org.domain.service.RuleConfigService;
import com.mi.info.intl.retail.org.infra.mapper.IntlLduInspectionInfoMapper;
import com.mi.info.intl.retail.org.infra.mapper.IntlStoreMaterialStatusMapper;
import com.mi.info.intl.retail.org.infra.rpc.TaskCenterServiceRpc;
import com.mi.info.intl.retail.utils.UserInfoUtil;
import com.xiaomi.cnzone.maindataapi.model.dto.common.ConfigKV2;
import com.xiaomi.cnzone.storems.api.api.StoreRelateProvider;
import com.xiaomi.cnzone.storems.api.model.dto.store.CommonConfigDTO2;
import com.xiaomi.cnzone.storems.api.model.req.store.CommonConfigReq;
import com.xiaomi.nr.eiam.api.provider.UserProvider;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.SneakyThrows;
import org.junit.jupiter.api.AfterEach;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.isNull;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadPoolExecutor;
import javax.validation.Validator;

@ExtendWith(MockitoExtension.class)
class NewProductInspectionDomainServiceImplTest {
    @Mock
    private StoreRelateProvider storeRelateProvider;
    @Mock
    private NewProductInspectionRepository newProductInspectionRepository;
    @Mock
    private InspectionRecordRepository inspectionRecordRepository;
    @Mock
    private PositionRepository positionRepository;
    @Mock
    private InspectionHistoryRepository inspectionHistoryRepository;
    @Mock
    private IntlStoreMaterialStatusRepository intlStoreMaterialStatusRepository;
    @Mock
    private RmsCountryTimezoneService rmsCountryTimezoneService;
    @Mock
    private IntlRmsUserService intlRmsUserService;
    @Mock
    private ThreadPoolTaskExecutor batchQueryExecutor;
    @Mock
    private ThreadPoolExecutor pool;
    @Mock
    private IntlFileUploadService fileUploadService;
    @Mock
    private LduReportRepository lduReportRepository;
    @Mock
    private UserProvider userProvider;
    @Mock
    private RuleConfigRepository ruleConfigRepository;
    @Mock
    private Validator validator;
    @Mock
    private TaskCenterServiceRpc taskCenterServiceRpc;
    @Mock
    private RuleConfigService ruleConfigService;
    @Mock
    private IntlLduInspectionInfoMapper intlLduInspectionInfoMapper;
    @Mock
    private ObjectMapper objectMapper;
    @Mock
    private IntlStoreMaterialStatusMapper intlStoreMaterialStatusMapper;
    @InjectMocks
    private NewProductInspectionDomainServiceImpl service;

    private MockedStatic<UserInfoUtil> userInfoUtilMockedStatic;

    private MaterialInspectionVerifyRequest verifyRequest;
    private MaterialInspectionDomain inspectionDomain;
    private RuleConfigDomain ruleConfig;
    private UserInfo userInfo;

    @BeforeEach
    void setUp() {
        verifyRequest = new MaterialInspectionVerifyRequest();
        verifyRequest.setId(1L);
        verifyRequest.setVerifyStatus(VerifyActionEnum.APPROVE.getCode());

        inspectionDomain = new MaterialInspectionDomain();
        inspectionDomain.setId(1L);
        inspectionDomain.setRuleConfigId(100L);
        inspectionDomain.setInspectionStatus(
                MaterialInspectionStatus.VERIFYING);
        inspectionDomain.setTaskStatus(TaskStatusEnum.NOT_COMPLETED);

        ruleConfig = new RuleConfigDomain();
        ruleConfig.setNeedInspection(1); // BoolEnum.YES
        ruleConfig.setTargetType(TargetTypeEnum.FIRST_SALES_PERIOD);

        userInfo = new UserInfo();
        userInfo.setMiID(123L);
        userInfo.setUserName("testUser");

        userInfoUtilMockedStatic = mockStatic(UserInfoUtil.class);
    }

    @AfterEach
    void teardown() {
        if (userInfoUtilMockedStatic != null) {
            userInfoUtilMockedStatic.close();
        }
    }

    private static CommonConfigDTO2 getCommonConfigDTO2() {
        CommonConfigDTO2 dto = new CommonConfigDTO2();
        ConfigKV2 channel = new ConfigKV2();
        channel.setKey("1");
        channel.setValue("渠道1");
        dto.setChannelType(Collections.singletonList(channel));
        ConfigKV2 positionType = new ConfigKV2();
        positionType.setKey("2");
        positionType.setValue("类型2");
        dto.setPositionType(Collections.singletonList(positionType));
        ConfigKV2 storeGrade = new ConfigKV2();
        storeGrade.setKey("3");
        storeGrade.setValue("等级3");
        dto.setStoreGradings(Collections.singletonList(storeGrade));
        return dto;
    }

    @Test
    @DisplayName("获取选择器项列表 - 应返回有效项")
    void testGetSelectorItemList_shouldReturnValidItem() {
        when(storeRelateProvider.get3CCommonConfig(any(CommonConfigReq.class))).thenReturn(
                Result.success(getCommonConfigDTO2()));
        MaterialInspectionRecordSelectorItem item = service.getSelectorItemList();
        assertNotNull(item);
        assertNotNull(item.getChannel());
        assertFalse(item.getChannel().isEmpty());
        assertEquals("渠道1", item.getChannel().get(0).getKey());
        assertEquals("渠道1", item.getChannel().get(0).getValue());
        assertNotNull(item.getPositionType());
        assertFalse(item.getPositionType().isEmpty());
        assertEquals("2", item.getPositionType().get(0).getKey());
        assertEquals("类型2", item.getPositionType().get(0).getValue());
        assertNotNull(item.getStoreGrade());
        assertFalse(item.getStoreGrade().isEmpty());
        assertEquals("等级3", item.getStoreGrade().get(0).getKey());
        assertEquals("等级3", item.getStoreGrade().get(0).getValue());
        assertNotNull(item.getTaskStatus());
        assertNotNull(item.getTaskType());
        assertNotNull(item.getDisapproveReason());
        assertNotNull(item.getStoreStatus());
        assertNotNull(item.getInspectionStatus());
        assertNotNull(item.getStoreLimitedRange());
        assertNotNull(item.getWhetherTakePhoto());
        assertNotNull(item.getMaterialCovered());
    }

    @Test
    @DisplayName("获取物料巡检记录分页项 - 无记录时应返回空")
    void testGetMaterialInspectionRecordPageItem_shouldReturnEmptyWhenNoRecords() {
        InspectionRecordPageRequest req = new InspectionRecordPageRequest();
        req.setPageNum(1L);
        req.setPageSize(10L);
        Page<InspectionRecordPageItem> page = new Page<>(1, 10, 0);
        page.setRecords(Collections.emptyList());
        when(newProductInspectionRepository.getMaterialInspectionRecordPageItem(
                any(InspectionRecordPageRequest.class))).thenReturn(page);
        PageResponse<MaterialInspectionRecordPageDTO> resp = service.getMaterialInspectionRecordPageItem(req);
        assertNotNull(resp);
        assertEquals(0, resp.getTotalCount());
        assertNotNull(resp.getList());
        assertTrue(resp.getList().isEmpty());
    }

    @Test
    @DisplayName("获取物料巡检记录分页项 - 应正确映射记录")
    void testGetMaterialInspectionRecordPageItem_shouldMapRecords() {
        InspectionRecordPageRequest req = new InspectionRecordPageRequest();
        req.setPageNum(1L);
        req.setPageSize(10L);
        InspectionRecordPageItem record = new InspectionRecordPageItem();
        record.setId(100L);
        record.setTaskName("T");
        record.setPositionName("P");
        record.setPositionCode("PC");
        record.setStoreName("SN");
        record.setStoreCode("SC");
        record.setStoreGrade("SG");
        record.setReportDistance(1.23);
        record.setStoreStatus(1);
        record.setTaskStatus(TaskStatusEnum.COMPLETED.getCode());
        record.setChannel("ch");
        record.setCovered(1);
        record.setStoreLimitedRange(1);
        record.setTaskType(TaskTypeEnum.LDU.getCode());
        record.setOnSaleProjectCode("[\"p1\",\"p2\"]");
        record.setProjectCode("[\"p0\"]");
        record.setCountryCode("CN");
        record.setRegionCode("REG");
        record.setVerifierMiId(1L);
        record.setVerifier("ver");
        record.setCreatedTime(System.currentTimeMillis());
        record.setUploadData(
                "[{\"materialValue\":{\"guid\":\"111\",\"images\":[]},\"lduReportLogId\":1,\"businessStatusCode\":0}," +
                        "{\"materialValue\":{\"guid\":\"222\",\"images\":[]},\"lduReportLogId\":1034,\"businessStatusCode\":0}]");
        Page<InspectionRecordPageItem> page = new Page<>(1, 10, 1);
        page.setRecords(Collections.singletonList(record));
        when(newProductInspectionRepository.getMaterialInspectionRecordPageItem(
                any(InspectionRecordPageRequest.class))).thenReturn(page);
        when(rmsCountryTimezoneService.getCountryAreaMapByCountryCodes(anyList())).thenReturn(
                Collections.singletonMap("CN", "中国"));
        when(intlRmsUserService.getIntlRmsUserByMiIds(anyList())).thenReturn(
                Collections.singletonList(IntlRmsUserDto.builder().miId(1L).englishName("E").domainName("D").build()));
        Map<String, List<String>> map =
                ImmutableMap.of("111", ImmutableList.of("1.jpg"), "222", ImmutableList.of("2.jpg"));
        when(fileUploadService.getUrlsByModuleAndGuids(any(), anyList())).thenReturn(map);
        when(batchQueryExecutor.getThreadPoolExecutor()).thenReturn(pool);
        when(userProvider.listUserInfo(any())).thenReturn(Result.success(Collections.emptyList()));
        LduReportSimple simple = new LduReportSimple();
        simple.setId(1L);
        simple.setSn("ABC");
        simple.setCode69("abc");
        simple.setReportRole("ReportRole");
        when(lduReportRepository.findSnByIds(anyList())).thenReturn(Collections.singletonList(simple));
        doAnswer(invocation -> {
            Runnable task = invocation.getArgument(0);
            task.run(); // 执行任务
            return null;
        }).when(pool).execute(any(Runnable.class));
        when(storeRelateProvider.get3CCommonConfig(any(CommonConfigReq.class))).thenReturn(
                Result.success(getCommonConfigDTO2()));

        PageResponse<MaterialInspectionRecordPageDTO> resp = service.getMaterialInspectionRecordPageItem(req);
        assertNotNull(resp);
        assertEquals(1, resp.getTotalCount());
        assertNotNull(resp.getList());
        assertEquals(1, resp.getList().size());
        MaterialInspectionRecordPageDTO dto = resp.getList().get(0);
        assertEquals(100L, dto.getId());
        assertEquals("中国", dto.getCountry());
        assertNotNull(dto.getStoreStatus());
        assertNotNull(dto.getTaskType());
        assertNotNull(dto.getTaskStatus());
    }

    @Test
    @DisplayName("验证物料巡检 - 成功场景")
    void testVerifyMaterialInspection_Success() {
        when(newProductInspectionRepository.getById(anyLong())).thenReturn(inspectionDomain);
        when(ruleConfigRepository.getById(anyLong())).thenReturn(ruleConfig);
        userInfoUtilMockedStatic.when(UserInfoUtil::getUserContext).thenReturn(userInfo);
        when(newProductInspectionRepository.updateVerifyStatus(any())).thenReturn(1);

        service.verifyMaterialInspection(verifyRequest);

        verify(newProductInspectionRepository, times(1)).updateVerifyStatus(inspectionDomain);
        assertEquals(MaterialInspectionStatus.PASSED, inspectionDomain.getInspectionStatus());
    }

    @Test
    @DisplayName("验证物料巡检 - 拒绝场景")
    void testVerifyMaterialInspection_Rejected() {

        verifyRequest.setVerifyStatus(VerifyActionEnum.DISAPPROVE.getCode());
        verifyRequest.setDisapproveReason(MaterialDisapproveReasonEnum.INCORRECT_PRODUCT.getCode());
        when(newProductInspectionRepository.getById(anyLong())).thenReturn(inspectionDomain);
        when(ruleConfigRepository.getById(anyLong())).thenReturn(ruleConfig);
        when(UserInfoUtil.getUserContext()).thenReturn(userInfo);
        when(newProductInspectionRepository.updateVerifyStatus(any())).thenReturn(1);
        userInfoUtilMockedStatic.when(UserInfoUtil::getUserContext).thenReturn(userInfo);

        service.verifyMaterialInspection(verifyRequest);

        verify(newProductInspectionRepository, times(1)).updateVerifyStatus(inspectionDomain);
        assertEquals(MaterialInspectionStatus.REJECTED, inspectionDomain.getInspectionStatus());
        assertEquals(TaskStatusEnum.NOT_COMPLETED, inspectionDomain.getTaskStatus());
    }

    @Test
    @DisplayName("验证物料巡检 - 拒绝场景(首销期)")
    void testVerifyMaterialInspection_RejectedFirstSalesPeriod() {

        verifyRequest.setVerifyStatus(VerifyActionEnum.DISAPPROVE.getCode());
        verifyRequest.setDisapproveReason(MaterialDisapproveReasonEnum.INCORRECT_PRODUCT.getCode());
        inspectionDomain.setTaskBatchId(1L);
        inspectionDomain.setTaskInstanceId(1L);
        inspectionDomain.setPeriodStartTimeStamp(1L);
        inspectionDomain.setPeriodEndTimeStamp(System.currentTimeMillis() + 3600000);
        when(newProductInspectionRepository.getById(anyLong())).thenReturn(inspectionDomain);
        ruleConfig.setTargetType(TargetTypeEnum.FIRST_SALES_PERIOD);
        when(ruleConfigRepository.getById(anyLong())).thenReturn(ruleConfig);
        when(UserInfoUtil.getUserContext()).thenReturn(userInfo);
        when(newProductInspectionRepository.updateVerifyStatus(any())).thenReturn(1);
        userInfoUtilMockedStatic.when(UserInfoUtil::getUserContext).thenReturn(userInfo);

        service.verifyMaterialInspection(verifyRequest);
        verify(taskCenterServiceRpc, times(1)).reloadTaskStatus(any(TaskCenterTaskReq.class));
    }

    @Test
    @DisplayName("分页查询物料巡检 - 成功场景")
    void testPageMaterialInspection_Success() {
        MaterialInspectionReq request = new MaterialInspectionReq();
        request.setPageNum(1L);
        request.setPageSize(10L);

        Page<MaterialInspectionItem> expectedPage = new Page<>(1, 10, 5);
        List<MaterialInspectionItem> items = Collections.singletonList(new MaterialInspectionItem());
        expectedPage.setRecords(items);

        when(newProductInspectionRepository.pageMaterialInspection(any(Page.class), any(MaterialInspectionReq.class)))
                .thenReturn(expectedPage);

        Page<MaterialInspectionItem> result = service.pageMaterialInspection(request);

        assertNotNull(result);
        assertEquals(5L, result.getTotal());
        assertEquals(1, result.getRecords().size());
        assertNotNull(request.getBusinessTypes());
        assertEquals(4, request.getBusinessTypes().size());
        verify(newProductInspectionRepository, times(1)).pageMaterialInspection(any(Page.class),
                any(MaterialInspectionReq.class));
    }

    @Test
    @DisplayName("检查是否有未完成任务 - 有未完成任务")
    void testHasUnCompletedTask_HasUnCompleted() {
        String account = "testAccount";
        when(newProductInspectionRepository.existsUnCompletedTaskByOwner(account)).thenReturn(true);

        boolean result = service.hasUnCompletedTask(account);

        assertTrue(result);
        verify(newProductInspectionRepository, times(1)).existsUnCompletedTaskByOwner(account);
    }

    @Test
    @DisplayName("检查是否有未完成任务 - 无未完成任务")
    void testHasUnCompletedTask_NoUnCompleted() {
        String account = "testAccount";
        when(newProductInspectionRepository.existsUnCompletedTaskByOwner(account)).thenReturn(false);

        boolean result = service.hasUnCompletedTask(account);

        assertFalse(result);
        verify(newProductInspectionRepository, times(1)).existsUnCompletedTaskByOwner(account);
    }

    @Test
    @DisplayName("获取物料巡检详情 - 巡检记录不存在")
    void testGetMaterialInspectionDetail_RecordNotFound() {
        MaterialInspectionDetailRequest request = new MaterialInspectionDetailRequest();
        request.setMaterialInspectionId(1L);

        when(inspectionRecordRepository.getById(1L)).thenReturn(null);

        CommonApiResponse<MaterialInspectionDetailResponse> result = service.getMaterialInspectionDetail(request);

        assertNotNull(result);
        assertEquals(500, result.getCode());
        assertEquals("新品物料巡检记录不存在", result.getMessage());
    }

    @Test
    @DisplayName("获取物料巡检详情 - 业务类型错误")
    void testGetMaterialInspectionDetail_InvalidBusinessType() {
        MaterialInspectionDetailRequest request = new MaterialInspectionDetailRequest();
        request.setMaterialInspectionId(1L);

        InspectionRecordDomain inspectionRecord = new InspectionRecordDomain();
        inspectionRecord.setBusinessType(999); // 无效的业务类型
        inspectionRecord.setPositionCode("POS001");

        when(inspectionRecordRepository.getById(1L)).thenReturn(inspectionRecord);

        CommonApiResponse<MaterialInspectionDetailResponse> result = service.getMaterialInspectionDetail(request);

        assertNotNull(result);
        assertEquals(500, result.getCode());
        assertEquals("业务类型错误", result.getMessage());
    }

    @Test
    @DisplayName("获取物料巡检详情 - 阵地信息不存在")
    void testGetMaterialInspectionDetail_PositionNotFound() {
        MaterialInspectionDetailRequest request = new MaterialInspectionDetailRequest();
        request.setMaterialInspectionId(1L);

        InspectionRecordDomain inspectionRecord = new InspectionRecordDomain();
        inspectionRecord.setBusinessType(BusinessTypeEnum.NEW_PRODUCT_INSPECTION_TOUR_DUMMY.getCode());
        inspectionRecord.setPositionCode("POS001");

        when(inspectionRecordRepository.getById(1L)).thenReturn(inspectionRecord);
        when(positionRepository.getByCode("POS001")).thenReturn(null);

        CommonApiResponse<MaterialInspectionDetailResponse> result = service.getMaterialInspectionDetail(request);

        assertNotNull(result);
        assertEquals(500, result.getCode());
        assertEquals("阵地信息不存在", result.getMessage());
    }

    @Test
    @DisplayName("获取物料样本 - 巡检记录不存在")
    void testGetMaterialSample_RecordNotFound() {
        MaterialSampleRequest request = new MaterialSampleRequest();
        request.setMaterialInspectionId(1L);

        when(inspectionRecordRepository.getById(1L)).thenReturn(null);

        MaterialSampleResponse result = service.getMaterialSample(request);

        assertNotNull(result);
        // 默认返回空的响应对象
    }

    @Test
    @DisplayName("获取物料样本 - 规则配置不存在")
    void testGetMaterialSample_RuleConfigNotFound() {
        MaterialSampleRequest request = new MaterialSampleRequest();
        request.setMaterialInspectionId(1L);

        InspectionRecordDomain inspectionRecord = new InspectionRecordDomain();
        inspectionRecord.setRuleCode("RULE001");
        inspectionRecord.setBusinessType(BusinessTypeEnum.NEW_PRODUCT_INSPECTION_TOUR_POSM.getCode());

        when(inspectionRecordRepository.getById(1L)).thenReturn(inspectionRecord);
        when(ruleConfigRepository.getByRuleCode("RULE001")).thenReturn(null);

        MaterialSampleResponse result = service.getMaterialSample(request);

        assertNotNull(result);
        // 默认返回空的响应对象
    }

    @Test
    @DisplayName("获取物料巡检操作历史 - 记录不存在")
    void testGetMaterialInspectionOperationHistory_RecordNotFound() {
        Long materialInspectionId = 1L;
        when(inspectionRecordRepository.getById(materialInspectionId)).thenReturn(null);

        List<MaterialInspectionOperationHistoryResponse> result =
                service.getMaterialInspectionOperationHistory(materialInspectionId);

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    @DisplayName("获取物料巡检操作历史 - 成功场景")
    void testGetMaterialInspectionOperationHistory_Success() {
        Long materialInspectionId = 1L;

        InspectionRecordDomain record = new InspectionRecordDomain();
        record.setBusinessType(BusinessTypeEnum.NEW_PRODUCT_INSPECTION_TOUR_POSM.getCode());

        InspectionHistoryDomain history = new InspectionHistoryDomain();
        history.setUploadData("[{\"materialKey\":\"key1\",\"materialValue\":{\"guid\":\"guid1\",\"images\":[]}}]");

        when(inspectionRecordRepository.getById(materialInspectionId)).thenReturn(record);
        when(inspectionHistoryRepository.getByInspectionRecordId(materialInspectionId))
                .thenReturn(Collections.singletonList(history));
        RuleConfigDomain ruleConfigDomain = new RuleConfigDomain();
        ruleConfigDomain.setPosmMaterials("{\"posmMaterials\":[1,2,3]}");
        when(ruleConfigRepository.getByRuleCode(isNull())).thenReturn(ruleConfigDomain);

        List<MaterialInspectionOperationHistoryResponse> result =
                service.getMaterialInspectionOperationHistory(materialInspectionId);

        assertNotNull(result);
        assertEquals(1, result.size());
        assertFalse(result.get(0).getSingleColumnShow()); // POSM类型为false
    }

    @Test
    @DisplayName("获取未完成列表 - 成功场景")
    void testGetNoCompletedList_Success() {
        Long miId = 123L;
        List<TaskRemindDTO> expectedList = Collections.singletonList(new TaskRemindDTO());

        when(newProductInspectionRepository.getNoCompletedList(miId)).thenReturn(expectedList);

        List<TaskRemindDTO> result = service.getNoCompletedList(miId);

        assertNotNull(result);
        assertEquals(1, result.size());
        verify(newProductInspectionRepository, times(1)).getNoCompletedList(miId);
    }

    @Test
    @DisplayName("批量保存物料巡检 - 成功场景")
    void testBatchSaveMaterialInspections_Success() {
        List<MaterialInspectionDomain> inspectionRecords = Collections.singletonList(new MaterialInspectionDomain());
        List<IntlStoreMaterialStatusDomain> storeMaterials =
                Collections.singletonList(new IntlStoreMaterialStatusDomain());

        service.batchSaveMaterialInspections(inspectionRecords, storeMaterials);

        verify(newProductInspectionRepository, times(1)).batchSave(inspectionRecords);
        verify(intlStoreMaterialStatusRepository, times(1)).batchSave(storeMaterials);
    }

    @Test
    @DisplayName("获取门店模型列表 - 成功场景")
    void testGetStoreModelList_Success() {
        String positionCode = "POS001";
        List<String> expectedList = Arrays.asList("Model1", "Model2");

        when(newProductInspectionRepository.getStoreModelList(positionCode)).thenReturn(expectedList);

        List<String> result = service.getStoreModelList(positionCode);

        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("Model1", result.get(0));
        assertEquals("Model2", result.get(1));
        verify(newProductInspectionRepository, times(1)).getStoreModelList(positionCode);
    }

    @Test
    @DisplayName("检查新品巡检 - 成功场景")
    void testCheckNewProductInspection_Success() {
        TaskCenterTaskNumReq req = new TaskCenterTaskNumReq();
        when(newProductInspectionRepository.checkNewProductInspection(any(TaskCenterTaskNumReq.class), anyList()))
                .thenReturn(true);

        boolean result = service.checkNewProductInspection(req);

        assertTrue(result);
        verify(newProductInspectionRepository, times(1)).checkNewProductInspection(any(TaskCenterTaskNumReq.class),
                anyList());
    }

    @Test
    @DisplayName("提交物料巡检 - 巡检记录不存在")
    void testSubmitMaterialInspection_RecordNotFound() {
        SubmitMaterialInspectionRequest request = new SubmitMaterialInspectionRequest();
        request.setMaterialInspectionId(1L);
        request.setOwner("testOwner");

        when(inspectionRecordRepository.getById(1L)).thenReturn(null);

        CommonApiResponse<String> result = service.submitMaterialInspection(request);

        assertNotNull(result);
        assertEquals(500, result.getCode());
        assertEquals("新品物料巡检记录不存在", result.getMessage());
    }

    @Test
    @DisplayName("提交物料巡检 - 未获取到登录人信息")
    void testSubmitMaterialInspection_UserNotFound() {
        SubmitMaterialInspectionRequest request = new SubmitMaterialInspectionRequest();
        request.setMaterialInspectionId(1L);
        request.setOwner("testOwner");

        InspectionRecordDomain inspectionRecord = new InspectionRecordDomain();
        inspectionRecord.setInspectionOwnerMiId(123L);

        when(inspectionRecordRepository.getById(1L)).thenReturn(inspectionRecord);
        when(intlRmsUserService.getIntlRmsUserByDomainName("testOwner")).thenReturn(null);

        try {
            service.submitMaterialInspection(request);
        } catch (Exception e) {
            // 预期抛出异常
            assertNotNull(e);
        }
    }

    @Test
    @DisplayName("提交物料巡检 - 位置信息不完整")
    void testSubmitMaterialInspection_IncompleteLocationInfo() {
        SubmitMaterialInspectionRequest request = new SubmitMaterialInspectionRequest();
        request.setMaterialInspectionId(1L);
        request.setOwner("testOwner");
        request.setLatitude(null); // 缺少位置信息

        InspectionRecordDomain inspectionRecord = new InspectionRecordDomain();
        inspectionRecord.setInspectionOwnerMiId(123L);

        IntlRmsUserDto userDto = IntlRmsUserDto.builder().miId(123L).build();

        when(inspectionRecordRepository.getById(1L)).thenReturn(inspectionRecord);
        when(intlRmsUserService.getIntlRmsUserByDomainName("testOwner")).thenReturn(userDto);

        CommonApiResponse<String> result = service.submitMaterialInspection(request);

        assertNotNull(result);
        assertEquals(500, result.getCode());
        assertEquals("位置信息不完整", result.getMessage());
    }

    @Test
    @DisplayName("提交物料巡检 - 阵地信息不存在")
    void testSubmitMaterialInspection_PositionNotFound() {
        SubmitMaterialInspectionRequest request = new SubmitMaterialInspectionRequest();
        request.setMaterialInspectionId(1L);
        request.setOwner("testOwner");
        request.setLatitude(1.0);
        request.setLongitude(1.0);
        request.setPositionLatitude(1.0);
        request.setPositionLongitude(1.0);
        request.setSections(Collections.emptyList());

        InspectionRecordDomain inspectionRecord = new InspectionRecordDomain();
        inspectionRecord.setInspectionOwnerMiId(123L);
        inspectionRecord.setPositionCode("POS001");

        IntlRmsUserDto userDto = IntlRmsUserDto.builder().miId(123L).build();

        when(inspectionRecordRepository.getById(1L)).thenReturn(inspectionRecord);
        when(intlRmsUserService.getIntlRmsUserByDomainName("testOwner")).thenReturn(userDto);
        when(positionRepository.getByCode("POS001")).thenReturn(null);

        CommonApiResponse<String> result = service.submitMaterialInspection(request);

        assertNotNull(result);
        assertEquals(500, result.getCode());
        assertEquals("阵地信息不存在", result.getMessage());
    }

    @Test
    @DisplayName("获取物料巡检详情 - 成功场景（测试私有方法）")
    void testGetMaterialInspectionDetail_Success_TestPrivateMethods() {
        MaterialInspectionDetailRequest request = new MaterialInspectionDetailRequest();
        request.setMaterialInspectionId(1L);

        InspectionRecordDomain inspectionRecord = new InspectionRecordDomain();
        inspectionRecord.setBusinessType(BusinessTypeEnum.NEW_PRODUCT_INSPECTION_TOUR_DUMMY.getCode());
        inspectionRecord.setPositionCode("POS001");
        inspectionRecord.setUploadData(
                "[{\"materialKey\":\"dummy\",\"materialValue\":{\"guid\":\"test-guid\",\"images\":[\"image1.jpg\"]}}]");

        PositionDomain position = new PositionDomain();
        position.setStoreName("测试门店");
        position.setPositionName("测试阵地");

        when(inspectionRecordRepository.getById(1L)).thenReturn(inspectionRecord);
        when(positionRepository.getByCode("POS001")).thenReturn(position);
        when(fileUploadService.getUrlsByModuleAndGuids(any(), anyList()))
                .thenReturn(Collections.singletonMap("test-guid", Arrays.asList("http://example.com/image1.jpg")));
        when(ruleConfigService.getRuleConfigByRuleCode(isNull())).thenReturn(new RuleConfigDomain());

        CommonApiResponse<MaterialInspectionDetailResponse> result = service.getMaterialInspectionDetail(request);

        assertNotNull(result);
        assertEquals(0, result.getCode()); // 成功
        assertNotNull(result.getData());
        assertNotNull(result.getData().getStoreInfo());
        assertEquals("测试门店", result.getData().getStoreInfo().getStoreName());
        assertEquals("测试阵地", result.getData().getStoreInfo().getFrontName());

        // 验证sections数据处理（测试processUploadDataImages和getPhotoMap私有方法）
        assertNotNull(result.getData().getSections());
        assertEquals(1, result.getData().getSections().size());
        assertEquals("dummy", result.getData().getSections().get(0).getMaterialKey());
    }

    @Test
    @DisplayName("提交物料巡检 - 成功场景（非LDU类型）")
    void testSubmitMaterialInspection_Success_NonLDU() {
        SubmitMaterialInspectionRequest request = new SubmitMaterialInspectionRequest();
        request.setMaterialInspectionId(1L);
        request.setOwner("testOwner");
        request.setLatitude(1.0);
        request.setLongitude(1.0);
        request.setPositionLatitude(1.0);
        request.setPositionLongitude(1.0);
        request.setCheckInDistance(200.0);
        UploadMaterialData uploadMaterialData = new UploadMaterialData();
        uploadMaterialData.setMaterialKey("k");
        MaterialPhotoGroup materialPhotoGroup = new MaterialPhotoGroup();
        materialPhotoGroup.setGuid("guid");
        materialPhotoGroup.setImages(Collections.singletonList("1.jpg"));
        uploadMaterialData.setMaterialValue(materialPhotoGroup);
        request.setSections(Collections.singletonList(uploadMaterialData));

        InspectionRecordDomain inspectionRecord = new InspectionRecordDomain();
        inspectionRecord.setId(1L);
        inspectionRecord.setInspectionOwnerMiId(123L);
        inspectionRecord.setInspectionOwner("testOwner");
        inspectionRecord.setPositionCode("POS001");
        inspectionRecord.setBusinessType(BusinessTypeEnum.NEW_PRODUCT_INSPECTION_TOUR_DUMMY.getCode());
        inspectionRecord.setTaskBatchId(1L);
        inspectionRecord.setTaskInstanceId(1L);

        PositionDomain position = new PositionDomain();
        position.setPositionCode("POS001");

        IntlRmsUserDto userDto = IntlRmsUserDto.builder().miId(123L).build();

        when(inspectionRecordRepository.getById(1L)).thenReturn(inspectionRecord);
        when(intlRmsUserService.getIntlRmsUserByDomainName("testOwner")).thenReturn(userDto);
        when(positionRepository.getByCode("POS001")).thenReturn(position);
        when(inspectionRecordRepository.update(any())).thenReturn(true);
        when(inspectionHistoryRepository.save(any())).thenReturn(true);
        when(ruleConfigRepository.getByRuleCode(isNull())).thenReturn(new RuleConfigDomain());

        CommonApiResponse<String> result = service.submitMaterialInspection(request);

        assertNotNull(result);
        assertEquals("", result.getData());
        verify(fileUploadService, times(1)).saveSimple(anyList(), any(), any());
        verify(taskCenterServiceRpc, times(1)).outerTaskFinish(any(TaskCenterFinishReq.class));
        verify(inspectionHistoryRepository, times(1)).save(any());
    }

    @Test
    @DisplayName("提交物料巡检 - 成功场景（LDU类型）")
    void testSubmitMaterialInspection_Success_LDU() {
        SubmitMaterialInspectionRequest request = new SubmitMaterialInspectionRequest();
        request.setMaterialInspectionId(1L);
        request.setOwner("testOwner");
        request.setLatitude(1.0);
        request.setLongitude(1.0);
        request.setPositionLatitude(1.0);
        request.setPositionLongitude(1.0);
        request.setCheckInDistance(200.0);

        UploadMaterialData section = new UploadMaterialData();
        section.setMaterialValue(new MaterialPhotoGroup("", "guid1", Collections.singletonList("image1.jpg")));
        section.setLduReportLogId(1L);
        section.setBusinessStatusCode(0);
        request.setSections(Collections.singletonList(section));

        InspectionRecordDomain inspectionRecord = new InspectionRecordDomain();
        inspectionRecord.setId(1L);
        inspectionRecord.setInspectionOwnerMiId(123L);
        inspectionRecord.setInspectionOwner("testOwner");
        inspectionRecord.setPositionCode("POS001");
        inspectionRecord.setBusinessType(BusinessTypeEnum.NEW_PRODUCT_INSPECTION_TOUR_LDU.getCode());
        inspectionRecord.setTaskBatchId(1L);
        inspectionRecord.setTaskInstanceId(1L);

        PositionDomain position = new PositionDomain();
        position.setPositionCode("POS001");

        IntlRmsUserDto userDto = IntlRmsUserDto.builder().miId(123L).build();

        when(inspectionRecordRepository.getById(1L)).thenReturn(inspectionRecord);
        when(intlRmsUserService.getIntlRmsUserByDomainName("testOwner")).thenReturn(userDto);
        when(positionRepository.getByCode("POS001")).thenReturn(position);
        when(inspectionRecordRepository.update(any())).thenReturn(true);
        when(inspectionHistoryRepository.save(any())).thenReturn(true);
        when(intlLduInspectionInfoMapper.batchInsert(anyList())).thenReturn(1);
        when(ruleConfigRepository.getByRuleCode(isNull())).thenReturn(new RuleConfigDomain());

        CommonApiResponse<String> result = service.submitMaterialInspection(request);

        assertNotNull(result);
        assertEquals("", result.getData());
        verify(intlLduInspectionInfoMapper, times(1)).batchInsert(anyList());
    }


    @Test
    @DisplayName("提交物料巡检 - 文件上传失败")
    void testSubmitMaterialInspection_FileUploadFailed() {
        SubmitMaterialInspectionRequest request = new SubmitMaterialInspectionRequest();
        request.setMaterialInspectionId(1L);
        request.setOwner("testOwner");
        request.setLatitude(1.0);
        request.setLongitude(1.0);
        request.setPositionLatitude(1.0);
        request.setPositionLongitude(1.0);
        request.setCheckInDistance(200.0);
        request.setSections(Collections.singletonList(new UploadMaterialData()));

        InspectionRecordDomain inspectionRecord = new InspectionRecordDomain();
        inspectionRecord.setId(1L);
        inspectionRecord.setInspectionOwnerMiId(123L);
        inspectionRecord.setInspectionOwner("testOwner");
        inspectionRecord.setPositionCode("POS001");
        inspectionRecord.setBusinessType(BusinessTypeEnum.NEW_PRODUCT_INSPECTION_TOUR_DUMMY.getCode());

        PositionDomain position = new PositionDomain();
        position.setPositionCode("POS001");

        IntlRmsUserDto userDto = IntlRmsUserDto.builder().miId(123L).build();

        when(inspectionRecordRepository.getById(1L)).thenReturn(inspectionRecord);
        when(intlRmsUserService.getIntlRmsUserByDomainName("testOwner")).thenReturn(userDto);
        when(positionRepository.getByCode("POS001")).thenReturn(position);
        when(ruleConfigRepository.getByRuleCode(isNull())).thenReturn(new RuleConfigDomain());

        CommonApiResponse<String> result = service.submitMaterialInspection(request);

        assertNotNull(result);
        assertEquals("", result.getData()); // 主流程仍应成功
        verify(inspectionRecordRepository, times(1)).update(any());
    }

    @SneakyThrows
    @Test
    @DisplayName("提交物料巡检 - JSON序列化失败")
    void testSubmitMaterialInspection_JsonSerializationFailed() {
        SubmitMaterialInspectionRequest request = new SubmitMaterialInspectionRequest();
        request.setMaterialInspectionId(1L);
        request.setOwner("testOwner");
        request.setLatitude(1.0);
        request.setLongitude(1.0);
        request.setPositionLatitude(1.0);
        request.setPositionLongitude(1.0);
        request.setCheckInDistance(200.0);
        request.setSections(Collections.singletonList(new UploadMaterialData()));

        InspectionRecordDomain inspectionRecord = new InspectionRecordDomain();
        inspectionRecord.setId(1L);
        inspectionRecord.setInspectionOwnerMiId(123L);
        inspectionRecord.setInspectionOwner("testOwner");
        inspectionRecord.setPositionCode("POS001");
        inspectionRecord.setBusinessType(BusinessTypeEnum.NEW_PRODUCT_INSPECTION_TOUR_DUMMY.getCode());

        PositionDomain position = new PositionDomain();
        position.setPositionCode("POS001");

        IntlRmsUserDto userDto = IntlRmsUserDto.builder().miId(123L).build();

        when(inspectionRecordRepository.getById(1L)).thenReturn(inspectionRecord);
        when(intlRmsUserService.getIntlRmsUserByDomainName("testOwner")).thenReturn(userDto);
        when(positionRepository.getByCode("POS001")).thenReturn(position);
        when(objectMapper.writeValueAsString(any())).thenThrow(new RuntimeException("JSON failed"));
        when(ruleConfigRepository.getByRuleCode(isNull())).thenReturn(new RuleConfigDomain());

        CommonApiResponse<String> result = service.submitMaterialInspection(request);

        assertNotNull(result);
        assertEquals(500, result.getCode());
        assertEquals("数据处理失败", result.getMessage());
    }

    @Test
    @DisplayName("提交物料巡检 - 任务中心调用失败")
    void testSubmitMaterialInspection_TaskCenterFailed() {
        SubmitMaterialInspectionRequest request = new SubmitMaterialInspectionRequest();
        request.setMaterialInspectionId(1L);
        request.setOwner("testOwner");
        request.setLatitude(1.0);
        request.setLongitude(1.0);
        request.setPositionLatitude(1.0);
        request.setPositionLongitude(1.0);
        request.setCheckInDistance(200.0);
        request.setSections(Collections.singletonList(new UploadMaterialData()));

        InspectionRecordDomain inspectionRecord = new InspectionRecordDomain();
        inspectionRecord.setId(1L);
        inspectionRecord.setInspectionOwnerMiId(123L);
        inspectionRecord.setInspectionOwner("testOwner");
        inspectionRecord.setPositionCode("POS001");
        inspectionRecord.setBusinessType(BusinessTypeEnum.NEW_PRODUCT_INSPECTION_TOUR_DUMMY.getCode());
        inspectionRecord.setTaskBatchId(1L);
        inspectionRecord.setTaskInstanceId(1L);

        PositionDomain position = new PositionDomain();
        position.setPositionCode("POS001");

        IntlRmsUserDto userDto = IntlRmsUserDto.builder().miId(123L).build();

        when(inspectionRecordRepository.getById(1L)).thenReturn(inspectionRecord);
        when(intlRmsUserService.getIntlRmsUserByDomainName("testOwner")).thenReturn(userDto);
        when(positionRepository.getByCode("POS001")).thenReturn(position);
        when(ruleConfigRepository.getByRuleCode(isNull())).thenReturn(new RuleConfigDomain());
        doThrow(new RuntimeException("Task center failed")).when(taskCenterServiceRpc).outerTaskFinish(any(TaskCenterFinishReq.class));

        assertThrows(RuntimeException.class, () -> service.submitMaterialInspection(request));
    }

    @Test
    @DisplayName("获取物料样本 - 成功场景（测试parsePosmMaterials私有方法）")
    void testGetMaterialSample_Success_TestParsePosmMaterials() {
        MaterialSampleRequest request = new MaterialSampleRequest();
        request.setMaterialInspectionId(1L);

        InspectionRecordDomain inspectionRecord = new InspectionRecordDomain();
        inspectionRecord.setRuleCode("RULE001");
        inspectionRecord.setBusinessType(BusinessTypeEnum.NEW_PRODUCT_INSPECTION_TOUR_POSM.getCode());

        RuleConfigDomain ruleConfig = new RuleConfigDomain();
        ruleConfig.setAllowPhotoFromGallery(true);
        ruleConfig.setTaskType(TaskTypeEnum.DUMMY);
        ruleConfig.setPosmMaterials("{\"posmMaterials\":[\"DUMMY\",\"PRICE_TAG\"]}");

        when(inspectionRecordRepository.getById(1L)).thenReturn(inspectionRecord);
        when(ruleConfigRepository.getByRuleCode("RULE001")).thenReturn(ruleConfig);

        MaterialSampleResponse result = service.getMaterialSample(request);

        assertNotNull(result);
        assertEquals(1, result.getAllowPhotoFromGallery());
        assertEquals(TaskTypeEnum.DUMMY, result.getTaskType());
        assertNotNull(result.getPosmMaterials());
        // parsePosmMaterials私有方法被间接测试
    }
}