package com.mi.info.intl.retail.ldu.service.impl;

import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.SearchRetailerReqDto;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.SearchRetailerResponseDto;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.retailer.mapper.read.IntlRmsRetailerReadMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class IntlLduSearchRetailServiceImplTest {

    @InjectMocks
    private IntlLduSearchRetailServiceImpl intlLduSearchRetailService;

    @Mock
    private IntlRmsRetailerReadMapper intlRmsRetailerReadMapper;

    private SearchRetailerReqDto testRequest;
    private SearchRetailerResponseDto testResponse;

    @BeforeEach
    void setUp() {
        testRequest = new SearchRetailerReqDto();
        testRequest.setKeyword("Test Retailer");

        testResponse = new SearchRetailerResponseDto();
        testResponse.setCode("RETAILER001");
        testResponse.setName("Test Retailer Store");
        testResponse.setCountryCode("ID");

    }

    @DisplayName("测试搜索零售商 - 成功场景")
    @Test
    void testSearchRetailer_Success() {
        // Given
        List<SearchRetailerResponseDto> expectedResults = Arrays.asList(testResponse);
        when(intlRmsRetailerReadMapper.queryRetailerByNameOrCode(anyInt(), any(SearchRetailerReqDto.class)))
                .thenReturn(expectedResults);

        // When
        CommonApiResponse<List<SearchRetailerResponseDto>> result = intlLduSearchRetailService.searchRetailer(testRequest);

        // Then
        assertNotNull(result);
        assertNotNull(result.getData());
        assertEquals(1, result.getData().size());
        assertEquals("RETAILER001", result.getData().get(0).getCode());
        assertEquals("Test Retailer Store", result.getData().get(0).getName());
    }

    @DisplayName("测试搜索零售商 - 空结果")
    @Test
    void testSearchRetailer_EmptyResult() {
        // Given
        when(intlRmsRetailerReadMapper.queryRetailerByNameOrCode(anyInt(), any(SearchRetailerReqDto.class)))
                .thenReturn(Collections.emptyList());

        // When
        CommonApiResponse<List<SearchRetailerResponseDto>> result = intlLduSearchRetailService.searchRetailer(testRequest);

        // Then
        assertNotNull(result);
        assertNotNull(result.getData());
        assertTrue(result.getData().isEmpty());
    }

    @DisplayName("测试搜索零售商 - 请求参数为空")
    @Test
    void testSearchRetailer_NullRequest() {
        // When
        CommonApiResponse<List<SearchRetailerResponseDto>> result = intlLduSearchRetailService.searchRetailer(null);

        // Then
        assertNotNull(result);
        assertNotNull(result.getData());
        assertTrue(result.getData().isEmpty());
    }

    @DisplayName("测试搜索零售商 - 关键词为空")
    @Test
    void testSearchRetailer_EmptyKeyword() {
        // Given
        testRequest.setKeyword("");

        // When
        CommonApiResponse<List<SearchRetailerResponseDto>> result = intlLduSearchRetailService.searchRetailer(testRequest);

        // Then
        assertNotNull(result);
        assertNotNull(result.getData());
        assertTrue(result.getData().isEmpty());
    }

    @DisplayName("测试搜索零售商 - 关键词为null")
    @Test
    void testSearchRetailer_NullKeyword() {
        // Given
        testRequest.setKeyword(null);

        // When
        CommonApiResponse<List<SearchRetailerResponseDto>> result = intlLduSearchRetailService.searchRetailer(testRequest);

        // Then
        assertNotNull(result);
        assertNotNull(result.getData());
        assertTrue(result.getData().isEmpty());
    }

    @DisplayName("测试搜索零售商 - 多个结果")
    @Test
    void testSearchRetailer_MultipleResults() {
        // Given
        SearchRetailerResponseDto secondResponse = new SearchRetailerResponseDto();
        secondResponse.setCode("RETAILER002");
        secondResponse.setName("Second Test Retailer");
        secondResponse.setCountryCode("ID");


        List<SearchRetailerResponseDto> expectedResults = Arrays.asList(testResponse, secondResponse);
        when(intlRmsRetailerReadMapper.queryRetailerByNameOrCode(anyInt(), any(SearchRetailerReqDto.class)))
                .thenReturn(expectedResults);

        // When
        CommonApiResponse<List<SearchRetailerResponseDto>> result = intlLduSearchRetailService.searchRetailer(testRequest);

        // Then
        assertNotNull(result);
        assertNotNull(result.getData());
        assertEquals(2, result.getData().size());
        assertEquals("RETAILER001", result.getData().get(0).getCode());
        assertEquals("RETAILER002", result.getData().get(1).getCode());
    }

    @DisplayName("测试搜索零售商 - 带国家代码过滤")
    @Test
    void testSearchRetailer_WithCountryCode() {
        // Given
        testRequest.setCountryCode(Arrays.asList("ID"));
        List<SearchRetailerResponseDto> expectedResults = Arrays.asList(testResponse);
        when(intlRmsRetailerReadMapper.queryRetailerByNameOrCode(anyInt(), any(SearchRetailerReqDto.class)))
                .thenReturn(expectedResults);

        // When
        CommonApiResponse<List<SearchRetailerResponseDto>> result = intlLduSearchRetailService.searchRetailer(testRequest);

        // Then
        assertNotNull(result);
        assertNotNull(result.getData());
        assertEquals(1, result.getData().size());
        assertEquals("ID", result.getData().get(0).getCountryCode());
    }


}
