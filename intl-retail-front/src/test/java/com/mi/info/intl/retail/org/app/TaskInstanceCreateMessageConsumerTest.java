package com.mi.info.intl.retail.org.app;

import cn.hutool.json.JSONUtil;
import com.mi.info.intl.retail.cooperation.task.dto.dto.IntlRmsUserDto;
import com.mi.info.intl.retail.cooperation.task.inspection.IntlRmsUserService;
import com.mi.info.intl.retail.exception.ReconsumeRuntimeException;
import com.mi.info.intl.retail.intlretail.service.api.mq.dto.TaskInstanceMessageBody;
import com.mi.info.intl.retail.org.domain.PositionDomain;
import com.mi.info.intl.retail.org.domain.RuleConfigDomain;
import com.mi.info.intl.retail.org.domain.material.service.NewProductInspectionDomainService;
import com.mi.info.intl.retail.org.domain.repository.IntlStoreMaterialStatusRepository;
import com.mi.info.intl.retail.org.domain.repository.NewProductInspectionRepository;
import com.mi.info.intl.retail.org.domain.repository.PositionRepository;
import com.mi.info.intl.retail.org.domain.repository.RuleConfigRepository;
import com.mi.info.intl.retail.org.infra.config.TaskInstanceMqBusinessTypeConfig;
import org.apache.rocketmq.common.message.MessageExt;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.nio.charset.StandardCharsets;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TaskInstanceCreateMessageConsumerTest {

    @Mock
    private TaskInstanceMqBusinessTypeConfig taskInstanceMqBusinessTypeConfig;
    @Mock
    private PositionRepository positionRepository;
    @Mock
    private RuleConfigRepository ruleConfigRepository;
    @Mock
    private IntlRmsUserService rmsUserService;
    @Mock
    private NewProductInspectionRepository newProductInspectionRepository;
    @Mock
    private IntlStoreMaterialStatusRepository intlStoreMaterialStatusRepository;
    @Mock
    private NewProductInspectionDomainService newProductInspectionDomainService;

    @InjectMocks
    private TaskInstanceCreateMessageConsumer consumer;

    @BeforeEach
    void setUp() {
        // no-op
    }

    @Test
    void handleTaskInstanceCreated_happyPath_shouldSaveRecordsAndReturnEmptyUnProceed() {
        TaskInstanceMessageBody body = new TaskInstanceMessageBody();
        TaskInstanceMessageBody.TaskInstanceData item = new TaskInstanceMessageBody.TaskInstanceData();
        item.setId(1L);
        item.setOrgId("ORG1");
        item.setTaskDefinitionId(100L);
        item.setMid(200L);
        item.setBusinessTypeId(402L);
        item.setStatus(1); // COMPLETED
        item.setCreateTimeStamp(System.currentTimeMillis());
        item.setPeriodStartTimeStamp(System.currentTimeMillis() - 1000);
        item.setPeriodEndTimeStamp(System.currentTimeMillis() + 1000);
        item.setDeadlineStamp(System.currentTimeMillis() + 2000);
        body.setData(Collections.singletonList(item));

        PositionDomain position = new PositionDomain();
        position.setPositionCode("ORG1");
        position.setPositionTypeName("TYPE");

        RuleConfigDomain rule = new RuleConfigDomain();
        rule.setId(10L);
        rule.setTaskDefId(100L);
        rule.setRuleCode("RC");
        rule.setNeedInspection(1);

        IntlRmsUserDto user = IntlRmsUserDto.builder()
                .miId(200L)
                .domainName("user")
                .build();

        when(positionRepository.getByCodes(anyList())).thenReturn(Collections.singletonList(position));
        when(ruleConfigRepository.getByTaskDefineIds(anyList())).thenReturn(Collections.singletonList(rule));
        when(rmsUserService.getIntlRmsUserByMiIds(anyList())).thenReturn(Collections.singletonList(user));
        when(newProductInspectionRepository.getUnexistsTaskInstanceIds(anyList())).thenReturn(Collections.singletonList(1L));
        when(intlStoreMaterialStatusRepository.findNotExistBusinessCodes(anyList())).thenReturn(Collections.singletonList("ORG1"));

        Set<Long> result = consumer.handleTaskInstanceCreated(body, "401");
        assertTrue(result.isEmpty());

        ArgumentCaptor<List> inspectionsCaptor = ArgumentCaptor.forClass(List.class);
        ArgumentCaptor<List> statusCaptor = ArgumentCaptor.forClass(List.class);
        verify(newProductInspectionDomainService, times(1))
                .batchSaveMaterialInspections(inspectionsCaptor.capture(), statusCaptor.capture());

        List<?> inspections = inspectionsCaptor.getValue();
        List<?> statuses = statusCaptor.getValue();
        assertEquals(1, inspections.size());
        // TaskTypeEnum has 4, excluding LDU -> 3
        assertEquals(3, statuses.size());
    }

    @Test
    void onMessage_invalidTag_shouldShortCircuit() {
        MessageExt msg = mock(MessageExt.class);
        when(msg.getTags()).thenReturn("abc"); // 非数字，直接返回
        when(msg.getBody()).thenReturn("{}".getBytes(StandardCharsets.UTF_8));

        consumer.onMessage(msg);
        verifyNoInteractions(positionRepository, ruleConfigRepository, rmsUserService,
                newProductInspectionRepository, intlStoreMaterialStatusRepository, newProductInspectionDomainService);
    }

    @Test
    void onMessage_unProceedTasksAndReconsumeTimesLE2_shouldThrow() {
        // 使用 spy 来桩掉 handle 方法，聚焦 onMessage 的重试逻辑
        TaskInstanceCreateMessageConsumer spyConsumer = Mockito.spy(consumer);

        when(taskInstanceMqBusinessTypeConfig.shouldProcessMqMessage(401L)).thenReturn(true);

        MessageExt msg = mock(MessageExt.class);
        when(msg.getTags()).thenReturn("401");
        String body = JSONUtil.createObj().set("data", Collections.singletonList(Collections.singletonMap("id", 1L))).toString();
        when(msg.getBody()).thenReturn(body.getBytes(StandardCharsets.UTF_8));
        when(msg.getReconsumeTimes()).thenReturn(0);
        when(msg.getMsgId()).thenReturn("mid-1");

        doReturn(new HashSet<>(Collections.singletonList(1L)))
                .when(spyConsumer).handleTaskInstanceCreated(any(TaskInstanceMessageBody.class), anyString());

        assertThrows(ReconsumeRuntimeException.class, () -> spyConsumer.onMessage(msg));
    }

    @Test
    void onMessage_unProceedTasksAndReconsumeTimesGT2_shouldNotThrow() {
        TaskInstanceCreateMessageConsumer spyConsumer = Mockito.spy(consumer);

        when(taskInstanceMqBusinessTypeConfig.shouldProcessMqMessage(401L)).thenReturn(true);

        MessageExt msg = mock(MessageExt.class);
        when(msg.getTags()).thenReturn("401");
        String body = JSONUtil.createObj().set("data", Collections.singletonList(Collections.singletonMap("id", 1L))).toString();
        when(msg.getBody()).thenReturn(body.getBytes(StandardCharsets.UTF_8));
        when(msg.getReconsumeTimes()).thenReturn(3);

        doReturn(new HashSet<>(Collections.singletonList(1L)))
                .when(spyConsumer).handleTaskInstanceCreated(any(TaskInstanceMessageBody.class), anyString());

        assertDoesNotThrow(() -> spyConsumer.onMessage(msg));
    }

    @Test
    void onMessage_blankBody_shouldShortCircuit() {
        when(taskInstanceMqBusinessTypeConfig.shouldProcessMqMessage(401L)).thenReturn(true);

        MessageExt msg = mock(MessageExt.class);
        when(msg.getTags()).thenReturn("401");
        when(msg.getBody()).thenReturn("  ".getBytes(StandardCharsets.UTF_8));

        consumer.onMessage(msg);
        verifyNoInteractions(positionRepository, ruleConfigRepository, rmsUserService,
                newProductInspectionRepository, intlStoreMaterialStatusRepository, newProductInspectionDomainService);
    }
} 