package com.mi.info.intl.retail.org.app;

import com.mi.info.intl.retail.intlretail.service.api.mq.dto.RmsDbContentRequest;
import com.mi.info.intl.retail.intlretail.service.api.mq.dto.RmsDbRequest;
import com.mi.info.intl.retail.org.domain.RmsSyncDbManager;
import com.mi.info.intl.retail.utils.JsonUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * RmsSyncDbConsumer 单元测试类
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("RMS数据同步消费者测试")
class RmsSyncDbConsumerTest {

    @InjectMocks
    private RmsSyncDbConsumer rmsSyncDbConsumer;

    @Mock
    private RmsSyncDbManager rmsSyncDbManager;

    private String validMessage;
    private String invalidMessage;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        RmsDbRequest testRequest = new RmsDbRequest();
        RmsDbContentRequest contentRequest = new RmsDbContentRequest();
        contentRequest.setTable("intl_rms_store");
        contentRequest.setUuid("test-uuid-001");
        
        Map<String, Object> content = new HashMap<>();
        content.put("storeId", "STORE001");
        content.put("storeName", "测试门店");
        contentRequest.setContent(content);
        
        testRequest.setRmsDBContentList(Arrays.asList(contentRequest));
        
        validMessage = JsonUtil.bean2json(testRequest);
        invalidMessage = "invalid json message";
    }

    @Test
    @DisplayName("处理有效消息 - 成功场景")
    void testOnMessage_ValidMessage() {
        // Given
        doNothing().when(rmsSyncDbManager).editDb(any(RmsDbRequest.class));

        // When
        assertDoesNotThrow(() -> rmsSyncDbConsumer.onMessage(validMessage));

        // Then
        verify(rmsSyncDbManager, times(1)).editDb(any(RmsDbRequest.class));
    }

    @Test
    @DisplayName("处理门店数据消息 - 成功场景")
    void testOnMessage_StoreDataMessage() {
        // Given
        RmsDbRequest storeRequest = new RmsDbRequest();
        RmsDbContentRequest storeContent = new RmsDbContentRequest();
        storeContent.setTable("intl_rms_store");
        storeContent.setUuid("store-uuid");
        
        Map<String, Object> content = new HashMap<>();
        content.put("storeId", "STORE001");
        content.put("storeName", "测试门店");
        content.put("countryCode", "ID");
        storeContent.setContent(content);
        
        storeRequest.setRmsDBContentList(Arrays.asList(storeContent));
        String storeMessage = JsonUtil.bean2json(storeRequest);
        
        doNothing().when(rmsSyncDbManager).editDb(any(RmsDbRequest.class));

        // When
        assertDoesNotThrow(() -> rmsSyncDbConsumer.onMessage(storeMessage));

        // Then
        verify(rmsSyncDbManager, times(1)).editDb(any(RmsDbRequest.class));
    }

    @Test
    @DisplayName("处理用户数据消息 - 成功场景")
    void testOnMessage_UserDataMessage() {
        // Given
        RmsDbRequest userRequest = new RmsDbRequest();
        RmsDbContentRequest userContent = new RmsDbContentRequest();
        userContent.setTable("intl_rms_user");
        userContent.setUuid("user-uuid");
        
        Map<String, Object> content = new HashMap<>();
        content.put("rmsUserid", "USER001");
        content.put("userName", "测试用户");
        content.put("email", "<EMAIL>");
        userContent.setContent(content);
        
        userRequest.setRmsDBContentList(Arrays.asList(userContent));
        String userMessage = JsonUtil.bean2json(userRequest);
        
        doNothing().when(rmsSyncDbManager).editDb(any(RmsDbRequest.class));

        // When
        assertDoesNotThrow(() -> rmsSyncDbConsumer.onMessage(userMessage));

        // Then
        verify(rmsSyncDbManager, times(1)).editDb(any(RmsDbRequest.class));
    }

    @Test
    @DisplayName("处理职位数据消息 - 成功场景")
    void testOnMessage_PositionDataMessage() {
        // Given
        RmsDbRequest positionRequest = new RmsDbRequest();
        RmsDbContentRequest positionContent = new RmsDbContentRequest();
        positionContent.setTable("intl_rms_position");
        positionContent.setUuid("position-uuid");
        
        Map<String, Object> content = new HashMap<>();
        content.put("positionId", "POS001");
        content.put("positionName", "测试职位");
        content.put("department", "技术部");
        positionContent.setContent(content);
        
        positionRequest.setRmsDBContentList(Arrays.asList(positionContent));
        String positionMessage = JsonUtil.bean2json(positionRequest);
        
        doNothing().when(rmsSyncDbManager).editDb(any(RmsDbRequest.class));

        // When
        assertDoesNotThrow(() -> rmsSyncDbConsumer.onMessage(positionMessage));

        // Then
        verify(rmsSyncDbManager, times(1)).editDb(any(RmsDbRequest.class));
    }

    @Test
    @DisplayName("处理时区数据消息 - 成功场景")
    void testOnMessage_TimezoneDataMessage() {
        // Given
        RmsDbRequest timezoneRequest = new RmsDbRequest();
        RmsDbContentRequest timezoneContent = new RmsDbContentRequest();
        timezoneContent.setTable("intl_rms_country_timezone");
        timezoneContent.setUuid("timezone-uuid");
        
        Map<String, Object> content = new HashMap<>();
        content.put("countryCode", "ID");
        content.put("timezone", "Asia/Jakarta");
        content.put("utcOffset", "+7");
        timezoneContent.setContent(content);
        
        timezoneRequest.setRmsDBContentList(Arrays.asList(timezoneContent));
        String timezoneMessage = JsonUtil.bean2json(timezoneRequest);
        
        doNothing().when(rmsSyncDbManager).editDb(any(RmsDbRequest.class));

        // When
        assertDoesNotThrow(() -> rmsSyncDbConsumer.onMessage(timezoneMessage));

        // Then
        verify(rmsSyncDbManager, times(1)).editDb(any(RmsDbRequest.class));
    }

    @Test
    @DisplayName("处理零售商数据消息 - 成功场景")
    void testOnMessage_RetailerDataMessage() {
        // Given
        RmsDbRequest retailerRequest = new RmsDbRequest();
        RmsDbContentRequest retailerContent = new RmsDbContentRequest();
        retailerContent.setTable("intl_rms_retailer");
        retailerContent.setUuid("retailer-uuid");
        
        Map<String, Object> content = new HashMap<>();
        content.put("retailerId", "RETAILER001");
        content.put("retailerName", "测试零售商");
        content.put("address", "测试地址");
        retailerContent.setContent(content);
        
        retailerRequest.setRmsDBContentList(Arrays.asList(retailerContent));
        String retailerMessage = JsonUtil.bean2json(retailerRequest);
        
        doNothing().when(rmsSyncDbManager).editDb(any(RmsDbRequest.class));

        // When
        assertDoesNotThrow(() -> rmsSyncDbConsumer.onMessage(retailerMessage));

        // Then
        verify(rmsSyncDbManager, times(1)).editDb(any(RmsDbRequest.class));
    }

    @Test
    @DisplayName("处理签到规则数据消息 - 成功场景")
    void testOnMessage_SignRuleDataMessage() {
        // Given
        RmsDbRequest signRuleRequest = new RmsDbRequest();
        RmsDbContentRequest signRuleContent = new RmsDbContentRequest();
        signRuleContent.setTable("intl_rms_sign_rule");
        signRuleContent.setUuid("signrule-uuid");
        
        Map<String, Object> content = new HashMap<>();
        content.put("signRuleId", "RULE001");
        content.put("ruleName", "测试规则");
        content.put("ruleType", "daily");
        signRuleContent.setContent(content);
        
        signRuleRequest.setRmsDBContentList(Arrays.asList(signRuleContent));
        String signRuleMessage = JsonUtil.bean2json(signRuleRequest);
        
        doNothing().when(rmsSyncDbManager).editDb(any(RmsDbRequest.class));

        // When
        assertDoesNotThrow(() -> rmsSyncDbConsumer.onMessage(signRuleMessage));

        // Then
        verify(rmsSyncDbManager, times(1)).editDb(any(RmsDbRequest.class));
    }

    @Test
    @DisplayName("处理人员职位关联数据消息 - 成功场景")
    void testOnMessage_PersonnelPositionDataMessage() {
        // Given
        RmsDbRequest personnelPositionRequest = new RmsDbRequest();
        RmsDbContentRequest personnelPositionContent = new RmsDbContentRequest();
        personnelPositionContent.setTable("intl_rms_personnel_position");
        personnelPositionContent.setUuid("personnel-uuid");
        
        Map<String, Object> content = new HashMap<>();
        content.put("associationId", "ASSOC001");
        content.put("userId", "USER001");
        content.put("positionId", "POS001");
        content.put("startDate", "2024-01-01");
        personnelPositionContent.setContent(content);
        
        personnelPositionRequest.setRmsDBContentList(Arrays.asList(personnelPositionContent));
        String personnelPositionMessage = JsonUtil.bean2json(personnelPositionRequest);
        
        doNothing().when(rmsSyncDbManager).editDb(any(RmsDbRequest.class));

        // When
        assertDoesNotThrow(() -> rmsSyncDbConsumer.onMessage(personnelPositionMessage));

        // Then
        verify(rmsSyncDbManager, times(1)).editDb(any(RmsDbRequest.class));
    }

    @Test
    @DisplayName("处理多个数据类型的消息 - 成功场景")
    void testOnMessage_MultipleDataTypesMessage() {
        // Given
        RmsDbRequest multiRequest = new RmsDbRequest();
        
        RmsDbContentRequest storeContent = new RmsDbContentRequest();
        storeContent.setTable("intl_rms_store");
        storeContent.setUuid("store-uuid");
        Map<String, Object> storeData = new HashMap<>();
        storeData.put("storeId", "STORE001");
        storeContent.setContent(storeData);
        
        RmsDbContentRequest userContent = new RmsDbContentRequest();
        userContent.setTable("intl_rms_user");
        userContent.setUuid("user-uuid");
        Map<String, Object> userData = new HashMap<>();
        userData.put("rmsUserid", "USER001");
        userContent.setContent(userData);
        
        multiRequest.setRmsDBContentList(Arrays.asList(storeContent, userContent));
        String multiMessage = JsonUtil.bean2json(multiRequest);
        
        doNothing().when(rmsSyncDbManager).editDb(any(RmsDbRequest.class));

        // When
        assertDoesNotThrow(() -> rmsSyncDbConsumer.onMessage(multiMessage));

        // Then
        verify(rmsSyncDbManager, times(1)).editDb(any(RmsDbRequest.class));
    }

    @Test
    @DisplayName("处理无效JSON消息 - 异常场景")
    void testOnMessage_InvalidJsonMessage() {
        // Given
        String invalidJsonMessage = "{ invalid json }";

        // When
        assertDoesNotThrow(() -> rmsSyncDbConsumer.onMessage(invalidJsonMessage));

        // Then
        verify(rmsSyncDbManager, never()).editDb(any(RmsDbRequest.class));
    }

    @Test
    @DisplayName("处理空消息 - 异常场景")
    void testOnMessage_EmptyMessage() {
        // Given
        String emptyMessage = "";

        // When
        assertDoesNotThrow(() -> rmsSyncDbConsumer.onMessage(emptyMessage));

        // Then
        verify(rmsSyncDbManager, never()).editDb(any(RmsDbRequest.class));
    }

    @Test
    @DisplayName("处理null消息 - 异常场景")
    void testOnMessage_NullMessage() {
        // Given
        String nullMessage = null;

        // When
        assertDoesNotThrow(() -> rmsSyncDbConsumer.onMessage(nullMessage));

        // Then
        verify(rmsSyncDbManager, never()).editDb(any(RmsDbRequest.class));
    }

    @Test
    @DisplayName("处理RmsSyncDbManager异常 - 异常场景")
    void testOnMessage_RmsSyncDbManagerException() {
        // Given
        doThrow(new RuntimeException("Database error")).when(rmsSyncDbManager).editDb(any(RmsDbRequest.class));

        // When
        assertDoesNotThrow(() -> rmsSyncDbConsumer.onMessage(validMessage));

        // Then
        verify(rmsSyncDbManager, times(1)).editDb(any(RmsDbRequest.class));
        // 异常被捕获并记录日志，不会抛出
    }

    @Test
    @DisplayName("处理空内容列表消息 - 成功场景")
    void testOnMessage_EmptyContentListMessage() {
        // Given
        RmsDbRequest emptyRequest = new RmsDbRequest();
        emptyRequest.setRmsDBContentList(Arrays.asList());
        String emptyContentMessage = JsonUtil.bean2json(emptyRequest);
        
        doNothing().when(rmsSyncDbManager).editDb(any(RmsDbRequest.class));

        // When
        assertDoesNotThrow(() -> rmsSyncDbConsumer.onMessage(emptyContentMessage));

        // Then
        verify(rmsSyncDbManager, times(1)).editDb(any(RmsDbRequest.class));
    }
}
