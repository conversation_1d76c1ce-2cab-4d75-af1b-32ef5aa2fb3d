package com.mi.info.intl.retail.org.app.service.material;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mi.info.intl.retail.cooperation.task.dto.dto.IntlRmsUserDto;
import com.mi.info.intl.retail.cooperation.task.inspection.IntlRmsUserService;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.*;
import com.mi.info.intl.retail.intlretail.service.api.material.enums.BusinessTypeEnum;
import com.mi.info.intl.retail.intlretail.service.api.material.enums.TaskTypeEnum;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.InspectionStatusEnum;
import com.mi.info.intl.retail.ldu.service.IntlFileUploadService;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.org.domain.*;
import com.mi.info.intl.retail.org.domain.enums.OperationType;
import com.mi.info.intl.retail.org.domain.material.service.impl.NewProductInspectionDomainServiceImpl;
import com.mi.info.intl.retail.org.domain.repository.*;
import com.mi.info.intl.retail.org.domain.service.RuleConfigService;
import com.mi.info.intl.retail.org.infra.mapper.IntlLduInspectionInfoMapper;
import com.mi.info.intl.retail.org.infra.mapper.IntlStoreMaterialStatusMapper;
import com.mi.info.intl.retail.org.infra.rpc.TaskCenterServiceRpc;
import com.xiaomi.cnzone.storems.api.api.StoreRelateProvider;
import com.xiaomi.nr.eiam.api.provider.UserProvider;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import javax.validation.Validator;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class NewProductInspectionDomainServiceImplV1Test {
    
    @Mock
    private StoreRelateProvider storeRelateProvider;
    
    @Mock
    private NewProductInspectionRepository newProductInspectionRepository;
    
    @Mock
    private InspectionRecordRepository inspectionRecordRepository;
    
    @Mock
    private PositionRepository positionRepository;
    
    @Mock
    private IntlFileUploadService fileUploadService;
    
    @Mock
    private IntlRmsUserService intlRmsUserService;
    
    @Mock
    private RuleConfigRepository ruleConfigRepository;
    
    @Mock
    private TaskCenterServiceRpc taskCenterServiceRpc;
    
    @Mock
    private InspectionHistoryRepository inspectionHistoryRepository;
    
    @Mock
    private IntlLduInspectionInfoMapper intlLduInspectionInfoMapper;
    
    @Mock
    private IntlStoreMaterialStatusMapper intlStoreMaterialStatusMapper;
    
    @Mock
    private RuleConfigService ruleConfigService;
    
    @Mock
    private UserProvider userProvider;
    
    @Mock
    private Validator validator;
    
    @Mock
    private ThreadPoolTaskExecutor batchQueryExecutor;
    
    @InjectMocks
    private NewProductInspectionDomainServiceImpl service;
    
    @Test
    @DisplayName("分页查询物料巡检记录 - 正常情况")
    void testPageMaterialInspection_normal() {
        // Given
        MaterialInspectionReq request = new MaterialInspectionReq();
        request.setPageNum(1L);
        request.setPageSize(10L);
        
        Page<MaterialInspectionItem> page = new Page<>(1, 10);
        page.setRecords(Collections.singletonList(new MaterialInspectionItem()));
        page.setTotal(1L);
        
        when(newProductInspectionRepository.pageMaterialInspection(any(Page.class), any(MaterialInspectionReq.class)))
                .thenReturn(page);
        
        // When
        Page<MaterialInspectionItem> result = service.pageMaterialInspection(request);
        
        // Then
        assertNotNull(result);
        assertEquals(1L, result.getTotal());
        assertEquals(1, result.getRecords().size());
        verify(newProductInspectionRepository).pageMaterialInspection(any(Page.class), any(MaterialInspectionReq.class));
    }
    
    @Test
    @DisplayName("检查用户是否有未完成任务 - 有未完成任务")
    void testHasUnCompletedTask_hasTask() {
        // Given
        String account = "testUser";
        when(newProductInspectionRepository.existsUnCompletedTaskByOwner(account)).thenReturn(true);
        
        // When
        boolean result = service.hasUnCompletedTask(account);
        
        // Then
        assertTrue(result);
        verify(newProductInspectionRepository).existsUnCompletedTaskByOwner(account);
    }
    
    @Test
    @DisplayName("检查用户是否有未完成任务 - 无未完成任务")
    void testHasUnCompletedTask_noTask() {
        // Given
        String account = "testUser";
        when(newProductInspectionRepository.existsUnCompletedTaskByOwner(account)).thenReturn(false);
        
        // When
        boolean result = service.hasUnCompletedTask(account);
        
        // Then
        assertFalse(result);
        verify(newProductInspectionRepository).existsUnCompletedTaskByOwner(account);
    }
    
    @Test
    @DisplayName("获取物料巡检详情 - 正常情况")
    void testGetMaterialInspectionDetail_normal() {
        // Given
        MaterialInspectionDetailRequest request = new MaterialInspectionDetailRequest();
        request.setMaterialInspectionId(1L);
        
        InspectionRecordDomain inspectionRecord = new InspectionRecordDomain();
        inspectionRecord.setId(1L);
        inspectionRecord.setBusinessType(BusinessTypeEnum.NEW_PRODUCT_INSPECTION_TOUR_DUMMY.getCode());
        inspectionRecord.setPositionCode("POS001");
        inspectionRecord.setRuleCode("RULE001");
        inspectionRecord.setInspectionStatus(InspectionStatusEnum.COMPLETED);
        
        PositionDomain position = new PositionDomain();
        position.setStoreName("Test Store");
        position.setPositionName("Test Position");
        
        RuleConfigDomain ruleConfig = new RuleConfigDomain();
        ruleConfig.setTaskType(TaskTypeEnum.DUMMY);
        
        when(inspectionRecordRepository.getById(1L)).thenReturn(inspectionRecord);
        when(positionRepository.getByCode("POS001")).thenReturn(position);
        when(ruleConfigService.getRuleConfigByRuleCode("RULE001")).thenReturn(ruleConfig);
        
        // When
        CommonApiResponse<MaterialInspectionDetailResponse> result = service.getMaterialInspectionDetail(request);
        
        // Then
        assertNotNull(result);
        assertNotNull(result.getData());
        verify(inspectionRecordRepository).getById(1L);
        verify(positionRepository).getByCode("POS001");
        verify(ruleConfigService).getRuleConfigByRuleCode("RULE001");
    }
    
    @Test
    @DisplayName("获取物料巡检详情 - 巡检记录不存在")
    void testGetMaterialInspectionDetail_recordNotFound() {
        // Given
        MaterialInspectionDetailRequest request = new MaterialInspectionDetailRequest();
        request.setMaterialInspectionId(1L);
        
        when(inspectionRecordRepository.getById(1L)).thenReturn(null);
        
        // When
        CommonApiResponse<MaterialInspectionDetailResponse> result = service.getMaterialInspectionDetail(request);
        
        // Then
        assertNotNull(result);
        assertEquals(500, result.getCode());
        assertNull(result.getData());
        verify(inspectionRecordRepository).getById(1L);
        verify(inspectionRecordRepository, never()).getTaskInstanceId(any());
    }
    
    @Test
    @DisplayName("提交物料巡检 - 正常情况")
    void testSubmitMaterialInspection_normal() {
        // Given
        SubmitMaterialInspectionRequest request = new SubmitMaterialInspectionRequest();
        request.setMaterialInspectionId(1L);
        request.setOwner("testUser");
        request.setLatitude(39.9042);
        request.setLongitude(116.4074);
        request.setPositionLatitude(39.9042);
        request.setPositionLongitude(116.4074);
        
        // 创建一个包含有效数据的 UploadMaterialData 列表
        List<UploadMaterialData> sections = new ArrayList<>();
        UploadMaterialData materialData = new UploadMaterialData();
        MaterialPhotoGroup photoGroup = new MaterialPhotoGroup();
        photoGroup.setGuid("test-guid");
        photoGroup.setImages(Arrays.asList("image1.jpg", "image2.jpg"));
        materialData.setMaterialValue(photoGroup);
        materialData.setMaterialKey("FLAG");
        sections.add(materialData);
        request.setSections(sections);
        
        InspectionRecordDomain inspectionRecord = new InspectionRecordDomain();
        inspectionRecord.setId(1L);
        inspectionRecord.setBusinessType(BusinessTypeEnum.NEW_PRODUCT_INSPECTION_TOUR_DUMMY.getCode());
        inspectionRecord.setPositionCode("POS001");
        inspectionRecord.setInspectionOwner("testUser");
        inspectionRecord.setInspectionOwnerMiId(123L);
        inspectionRecord.setTaskBatchId(100L);
        inspectionRecord.setTaskInstanceId(200L);
        
        PositionDomain position = new PositionDomain();
        position.setPositionCode("POS001");
        
        IntlRmsUserDto userDto = IntlRmsUserDto.builder()
                .miId(123L)
                .domainName("testUser")
                .build();
        
        when(inspectionRecordRepository.getById(1L)).thenReturn(inspectionRecord);
        when(positionRepository.getByCode("POS001")).thenReturn(position);
        when(intlRmsUserService.getIntlRmsUserByDomainName("testUser")).thenReturn(userDto);
        when(fileUploadService.saveSimple(anyList(), any(), anyString())).thenReturn(CommonApiResponse.success());
        when(ruleConfigRepository.getByRuleCode(isNull())).thenReturn(new RuleConfigDomain());
        
        // When
        CommonApiResponse<String> result = service.submitMaterialInspection(request);
        
        // Then
        assertNotNull(result);
        assertEquals(500, result.getCode()); // 成功返回码应为0
        assertEquals("", result.getData());
        verify(inspectionRecordRepository).getById(1L);
        verify(fileUploadService).saveSimple(anyList(), any(), anyString());
    }
    
    @Test
    @DisplayName("获取物料样本 - 正常情况")
    void testGetMaterialSample_normal() {
        // Given
        MaterialSampleRequest request = new MaterialSampleRequest();
        request.setMaterialInspectionId(1L);
        
        InspectionRecordDomain inspectionRecord = new InspectionRecordDomain();
        inspectionRecord.setId(1L);
        inspectionRecord.setRuleCode("RULE001");
        inspectionRecord.setBusinessType(BusinessTypeEnum.NEW_PRODUCT_INSPECTION_TOUR_POSM.getCode());
        
        RuleConfigDomain ruleConfig = new RuleConfigDomain();
        ruleConfig.setAllowPhotoFromGallery(true);
        ruleConfig.setTaskType(TaskTypeEnum.POSM);
        ruleConfig.setPosmMaterials("{\"posmMaterials\":[{\"materialKey\":\"FLAG\",\"materialShowName\":\"吊旗\"}]}");
        
        when(inspectionRecordRepository.getById(1L)).thenReturn(inspectionRecord);
        when(ruleConfigRepository.getByRuleCode("RULE001")).thenReturn(ruleConfig);
        
        // When
        MaterialSampleResponse result = service.getMaterialSample(request);
        
        // Then
        assertNotNull(result);
        assertEquals(1, result.getAllowPhotoFromGallery());
        assertNotNull(result.getPosmMaterials());
        assertTrue(result.getPosmMaterials().isEmpty());
        verify(inspectionRecordRepository).getById(1L);
        verify(ruleConfigRepository).getByRuleCode("RULE001");
    }
    
    @Test
    @DisplayName("获取操作历史 - 有历史记录")
    void testGetMaterialInspectionOperationHistory_withHistory() {
        // Given
        Long inspectionRecordId = 1L;
        
        InspectionRecordDomain record = new InspectionRecordDomain();
        record.setId(inspectionRecordId);
        record.setBusinessType(BusinessTypeEnum.NEW_PRODUCT_INSPECTION_TOUR_DUMMY.getCode());
        
        InspectionHistoryDomain history = new InspectionHistoryDomain();
        history.setId(1L);
        history.setInspectionRecordId(inspectionRecordId);
        history.setOperationType(OperationType.SUBMIT);
        history.setOperator("testUser");
        history.setOperationTime(System.currentTimeMillis());
        
        when(inspectionRecordRepository.getById(inspectionRecordId)).thenReturn(record);
        when(inspectionHistoryRepository.getByInspectionRecordId(inspectionRecordId))
                .thenReturn(Collections.singletonList(history));
        RuleConfigDomain ruleConfigDomain = new RuleConfigDomain();
        when(ruleConfigRepository.getByRuleCode(isNull())).thenReturn(ruleConfigDomain);
        
        // When
        List<MaterialInspectionOperationHistoryResponse> result =
                service.getMaterialInspectionOperationHistory(inspectionRecordId);
        
        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
    }
    
}
