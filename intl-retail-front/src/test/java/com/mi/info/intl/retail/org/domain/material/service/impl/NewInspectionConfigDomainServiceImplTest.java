package com.mi.info.intl.retail.org.domain.material.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Sets;
import com.mi.info.intl.retail.cooperation.downloadcenter.example.JobTriggerHelper;
import com.mi.info.intl.retail.exception.RetailRunTimeException;
import com.mi.info.intl.retail.intlretail.service.api.fds.FdsService;
import com.mi.info.intl.retail.intlretail.service.api.fds.dto.FdsUploadResult;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.CommonResponse;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.InspectionTaskConfigurationDTO;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.InspectionTaskConfigurationInfoVO;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.InspectionTaskConfigurationPageRequest;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.InspectionTaskConfigurationPageResponse;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.InspectionTaskConfigurationRequest;
import com.mi.info.intl.retail.intlretail.service.api.material.enums.BrainPlatformOperateTypeEnum;
import com.mi.info.intl.retail.intlretail.service.api.material.enums.CycleTypeEnum;
import com.mi.info.intl.retail.intlretail.service.api.material.enums.InspectionRuleStautsEnum;
import com.mi.info.intl.retail.intlretail.service.api.material.enums.TargetTypeEnum;
import com.mi.info.intl.retail.intlretail.service.api.material.enums.TaskTypeEnum;
import com.mi.info.intl.retail.intlretail.service.api.material.enums.YESNOEnum;
import com.mi.info.intl.retail.intlretail.service.api.newproducttarget.dto.NewProductTargetProjectReq;
import com.mi.info.intl.retail.intlretail.service.api.newproducttarget.dto.NewProductTargetProjectResp;
import com.mi.info.intl.retail.org.app.service.material.convert.NewProductInspectionConvert;
import com.mi.info.intl.retail.org.domain.material.service.IntlInspectionRuleRelationService;
import com.mi.info.intl.retail.org.domain.repository.NewProductInspectionRepository;
import com.mi.info.intl.retail.org.domain.service.RuleConfigService;
import com.mi.info.intl.retail.org.domain.util.ContextUtil;
import com.mi.info.intl.retail.org.infra.entity.IntlRmsCountryTimezone;
import com.mi.info.intl.retail.org.infra.entity.RuleConfig;
import com.mi.info.intl.retail.org.infra.mapper.IntlNewProductTargetMapper;
import com.mi.info.intl.retail.org.infra.mapper.IntlRmsCountryTimezoneMapper;
import com.xiaomi.cnzone.brain.platform.api.model.resp.admin.AddTaskDefinitionResp;
import com.xiaomi.cnzone.brain.platform.api.provider.BrainPlatformOuterProvider;
import com.xiaomi.cnzone.maindataapi.model.dto.common.ConfigKV2;
import com.xiaomi.cnzone.storems.api.api.StoreRelateProvider;
import com.xiaomi.cnzone.storems.api.model.dto.store.CommonConfigDTO2;
import com.xiaomi.com.i18n.area.Area;
import com.xiaomi.youpin.infra.rpc.Result;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.AfterEach;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.io.TempDir;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyList;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import java.io.File;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.validation.ConstraintViolation;
import javax.validation.Validator;

@ExtendWith(MockitoExtension.class)
public class NewInspectionConfigDomainServiceImplTest {

    @InjectMocks
    private NewInspectionConfigDomainServiceImpl service;

    @Mock
    private StoreRelateProvider storeRelateProvider;

    @Mock
    private RuleConfigService ruleConfigService;

    @Mock
    private IntlRmsCountryTimezoneMapper intlRmsCountryTimezoneMapper;

    @Mock
    private IntlNewProductTargetMapper intlNewProductTargetMapper;

    @Mock
    private StringRedisTemplate stringRedisTemplate;

    @Mock
    private BrainPlatformOuterProvider brainPlatformOuterProvider;

    @Mock
    private FdsService fdsService;

    @Mock
    private Validator validator;

    @Mock
    private IntlInspectionRuleRelationService inspectionRuleRelationService;

    @Mock
    private ValueOperations<String, String> valueOperations;

    @Mock
    private JobTriggerHelper jobTriggerHelper;

    @Mock
    private NewProductInspectionRepository mockRepository;

    @TempDir
    Path tempDir;
    private InspectionTaskConfigurationPageRequest request;
    private MockedStatic<Area> mockedArea;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        request = new InspectionTaskConfigurationPageRequest();

        mockedArea = Mockito.mockStatic(Area.class);
        initializeAreaMockData();
    }

    @AfterEach
    void tearDown() {
        mockedArea.close();
    }

    /**
     * 初始化Area的mock数据，预设一些常用国家的时区信息
     */
    private void initializeAreaMockData() {
        // 预设一些常用国家的时区数据
        Map<String, String> countryTimezoneMap = new HashMap<>();
        countryTimezoneMap.put("CN", "Asia/Shanghai");
        countryTimezoneMap.put("HK", "Asia/Hong_Kong");
        countryTimezoneMap.put("TW", "Asia/Taipei");
        countryTimezoneMap.put("IN", "Asia/Kolkata");
        countryTimezoneMap.put("ID", "Asia/Jakarta");
        countryTimezoneMap.put("TH", "Asia/Bangkok");
        countryTimezoneMap.put("VN", "Asia/Ho_Chi_Minh");
        countryTimezoneMap.put("MY", "Asia/Kuala_Lumpur");
        countryTimezoneMap.put("SG", "Asia/Singapore");
        countryTimezoneMap.put("PH", "Asia/Manila");
        countryTimezoneMap.put("KR", "Asia/Seoul");
        countryTimezoneMap.put("JP", "Asia/Tokyo");

        // 使用通用的answer方式处理所有国家代码
        mockedArea.when(() -> Area.of(Mockito.anyString())).thenAnswer(invocation -> {
            String countryCode = invocation.getArgument(0);
            // 创建mock的Area对象
            Area mockArea = mock(Area.class);
            String timezone = countryTimezoneMap.getOrDefault(countryCode, "UTC");
            lenient().when(mockArea.getTimezone()).thenReturn(timezone);
            return mockArea;
        });
    }

    @Test
    void testGetEnumByType_TaskType() {
        InspectionTaskConfigurationPageRequest request = new InspectionTaskConfigurationPageRequest();
        request.setType("1");
        List<Map<String, Object>> result = service.getEnumByType(request);
        assertNotNull(result);
    }

    @Test
    void testGetEnumByType_CycleType() {
        InspectionTaskConfigurationPageRequest request = new InspectionTaskConfigurationPageRequest();
        request.setType("2");
        List<Map<String, Object>> result = service.getEnumByType(request);
        assertNotNull(result);
    }

    @Test
    void testGetEnumByType_PosmMaterial() {
        InspectionTaskConfigurationPageRequest request = new InspectionTaskConfigurationPageRequest();
        request.setType("3");
        List<Map<String, Object>> result = service.getEnumByType(request);
        assertNotNull(result);
    }

    @Test
    void testGetEnumByType_TargetType() {
        InspectionTaskConfigurationPageRequest request = new InspectionTaskConfigurationPageRequest();
        request.setType("4");
        List<Map<String, Object>> result = service.getEnumByType(request);
        assertNotNull(result);
    }

    @Test
    void testGetEnumByType_RuleStatus() {
        InspectionTaskConfigurationPageRequest request = new InspectionTaskConfigurationPageRequest();
        request.setType("5");
        List<Map<String, Object>> result = service.getEnumByType(request);
        assertNotNull(result);
    }

    @Test
    void testGetEnumByType_Store() {
        InspectionTaskConfigurationPageRequest request = new InspectionTaskConfigurationPageRequest();
        request.setType("6");
        CommonConfigDTO2 mockDTO = new CommonConfigDTO2();
        List<ConfigKV2> list = new ArrayList<>();
        ConfigKV2 configKV2 = new ConfigKV2();
        configKV2.setValue("Xiaomi Store");
        ConfigKV2 configKV2_1 = new ConfigKV2();
        configKV2.setValue("IR Store");
        list.add(configKV2);
        list.add(configKV2_1);
        mockDTO.setStoreLevels(list);
        when(storeRelateProvider.get3CCommonConfig(any())).thenReturn(Result.success(mockDTO));
        List<Map<String, Object>> result = service.getEnumByType(request);
        assertNotNull(result);
        assertFalse(result.isEmpty());

    }

    @Test
    void testGetEnumByType_NullType_DefaultToTaskType() {
        InspectionTaskConfigurationPageRequest request = new InspectionTaskConfigurationPageRequest();
        request.setType(null);
        List<Map<String, Object>> result = service.getEnumByType(request);
        assertNotNull(result);
    }

    @Test
    public void testFindInspectionTaskPage_Success() {
        // 准备测试数据
        Page<InspectionTaskConfigurationPageResponse> page = new Page<>(1, 10);
        InspectionTaskConfigurationPageRequest request = new InspectionTaskConfigurationPageRequest();

        // 创建模拟的响应数据
        InspectionTaskConfigurationPageResponse response1 = new InspectionTaskConfigurationPageResponse();
        response1.setId(1L);
        response1.setRuleCode("RULE001");
        response1.setRuleName("Test Rule");
        response1.setRuleStatus(1);
        response1.setTaskType(401);
        response1.setTaskName("Test Task");
        response1.setProject(new String[] {"PROJ001", "PROJ002"});
        response1.setCountry("IN");
        response1.setCycleType(1);
        response1.setCustomCycleDays(0);
        response1.setAllowPhotoFromGallery(1);
        response1.setStartTime(System.currentTimeMillis() - 86400000L); // 1天前
        response1.setEndTime(System.currentTimeMillis() + 86400000L * 30); // 30天后
        response1.setCreationTime(System.currentTimeMillis() - 86400000L * 2); // 2天前
        response1.setModificationTime(System.currentTimeMillis() - 86400000L); // 1天前

        InspectionTaskConfigurationPageResponse response2 = new InspectionTaskConfigurationPageResponse();
        response2.setId(2L);
        response2.setRuleCode("RULE002");
        response2.setRuleName("Test Rule 2");
        response2.setRuleStatus(0);
        response2.setTaskType(402);
        response2.setTaskName("Test Task 2");
        response2.setProject(new String[] {"PROJ003"});
        response2.setCountry("TH");
        response2.setCycleType(2);
        response2.setCustomCycleDays(0);
        response2.setAllowPhotoFromGallery(0);
        response2.setStartTime(System.currentTimeMillis() - 86400000L * 2); // 2天前
        response2.setEndTime(System.currentTimeMillis() + 86400000L * 60); // 60天后
        response2.setCreationTime(System.currentTimeMillis() - 86400000L * 3); // 3天前
        response2.setModificationTime(System.currentTimeMillis() - 86400000L * 2); // 2天前

        List<InspectionTaskConfigurationPageResponse> records = Arrays.asList(response1, response2);
        Page<InspectionTaskConfigurationPageResponse> mockPageResult = new Page<>(1, 10);
        mockPageResult.setRecords(records);
        mockPageResult.setTotal(2L);

        // Mock依赖服务
        when(ruleConfigService.find4Page(any(), any())).thenReturn(mockPageResult);

        // Mock国家时区数据
        IntlRmsCountryTimezone timezone1 = new IntlRmsCountryTimezone();
        timezone1.setCountryCode("IN");
        timezone1.setCountryName("India");
        timezone1.setArea("Asia");

        IntlRmsCountryTimezone timezone2 = new IntlRmsCountryTimezone();
        timezone2.setCountryCode("TH");
        timezone2.setCountryName("Thailand");
        timezone2.setArea("Asia");

        when(intlRmsCountryTimezoneMapper.selectList(any())).thenReturn(Arrays.asList(timezone1, timezone2));

        // 执行测试
        Page<InspectionTaskConfigurationPageResponse> result = service.findInspectionTaskPage(page, request);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.getTotal());
        assertEquals(2, result.getRecords().size());

        // 验证第一条记录
        InspectionTaskConfigurationPageResponse result1 = result.getRecords().get(0);
        assertEquals("Dummy", result1.getTaskTypeDesc());
        assertEquals("日", result1.getCycleTypeDesc());
        assertEquals("启用", result1.getRuleStatusDesc());
        assertEquals("是", result1.getAllowPhotoFromGalleryDesc());
        assertEquals("Asia", result1.getRegionDesc());
        assertEquals("India", result1.getCountryDesc());
        assertNotNull(result1.getCreationTimeDesc());
        assertNotNull(result1.getModificationTimeDesc());
        assertNotNull(result1.getStartTimeDesc());
        assertNotNull(result1.getEndTimeDesc());
        assertEquals("PROJ001,PROJ002", result1.getProjectDesc());

        // 验证第二条记录
        InspectionTaskConfigurationPageResponse result2 = result.getRecords().get(1);
        assertEquals("POSM", result2.getTaskTypeDesc());
        assertEquals("周", result2.getCycleTypeDesc());
        assertEquals("草稿", result2.getRuleStatusDesc());
        assertEquals("否", result2.getAllowPhotoFromGalleryDesc());
        assertEquals("Asia", result2.getRegionDesc());
        assertEquals("Thailand", result2.getCountryDesc());
        assertNotNull(result2.getCreationTimeDesc());
        assertNotNull(result2.getModificationTimeDesc());
        assertNotNull(result2.getStartTimeDesc());
        assertNotNull(result2.getEndTimeDesc());
        assertEquals("PROJ003", result2.getProjectDesc());
    }

    @Test
    public void testFindInspectionTaskPage_EmptyResult() {
        // 准备测试数据
        Page<InspectionTaskConfigurationPageResponse> page = new Page<>(1, 10);
        InspectionTaskConfigurationPageRequest request = new InspectionTaskConfigurationPageRequest();

        // Mock空结果
        Page<InspectionTaskConfigurationPageResponse> mockPageResult = new Page<>(1, 10);
        mockPageResult.setRecords(new ArrayList<>());
        mockPageResult.setTotal(0L);

        when(ruleConfigService.find4Page(any(), any())).thenReturn(mockPageResult);

        // 执行测试
        Page<InspectionTaskConfigurationPageResponse> result = service.findInspectionTaskPage(page, request);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getTotal());
        assertEquals(0, result.getRecords().size());
    }

    @Test
    public void testFindInspectionTaskPage_NullCountry() {
        // 准备测试数据
        Page<InspectionTaskConfigurationPageResponse> page = new Page<>(1, 10);
        InspectionTaskConfigurationPageRequest request = new InspectionTaskConfigurationPageRequest();

        // 创建模拟的响应数据，国家为空
        InspectionTaskConfigurationPageResponse response = new InspectionTaskConfigurationPageResponse();
        response.setId(1L);
        response.setRuleCode("RULE001");
        response.setRuleName("Test Rule");
        response.setRuleStatus(1);
        response.setTaskType(401);
        response.setTaskName("Test Task");
        response.setCountry("HK"); // 国家为空
        response.setRegion("HMT");
        response.setStartTime(System.currentTimeMillis());
        response.setEndTime(System.currentTimeMillis() + 86400000L);

        List<InspectionTaskConfigurationPageResponse> records = Arrays.asList(response);
        Page<InspectionTaskConfigurationPageResponse> mockPageResult = new Page<>(1, 10);
        mockPageResult.setRecords(records);
        mockPageResult.setTotal(1L);

        // Mock依赖服务
        when(ruleConfigService.find4Page(any(), any())).thenReturn(mockPageResult);
        IntlRmsCountryTimezone countryTimezone = new IntlRmsCountryTimezone();
        countryTimezone.setCountryCode("HK");
        countryTimezone.setArea("港澳台");
        countryTimezone.setCountryName("Hong Kong");
        countryTimezone.setAreaCode("HMT");
        when(intlRmsCountryTimezoneMapper.selectList(any())).thenReturn(Lists.newArrayList(countryTimezone));

        // 执行测试
        Page<InspectionTaskConfigurationPageResponse> result = service.findInspectionTaskPage(page, request);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getTotal());
        assertEquals(1, result.getRecords().size());

        // 验证记录
        InspectionTaskConfigurationPageResponse resultRecord = result.getRecords().get(0);
        assertEquals("Dummy", resultRecord.getTaskTypeDesc());
        assertEquals("启用", resultRecord.getRuleStatusDesc());
        assertEquals("港澳台", resultRecord.getRegionDesc());
        assertEquals("Hong Kong", resultRecord.getCountryDesc());
    }

    @Test
    public void testGetInspectionTaskInfo_Success() {
        // Given
        InspectionTaskConfigurationRequest request = new InspectionTaskConfigurationRequest();
        request.setId(1L);

        RuleConfig ruleConfig = new RuleConfig();
        ruleConfig.setId(1L);
        ruleConfig.setTaskType(401);
        ruleConfig.setCycleType(1);
        ruleConfig.setRuleStatus(1);
        ruleConfig.setTargetType("1");
        ruleConfig.setCountry("IN");
        long currentTime = 1756127523444L;
        ruleConfig.setStartTime(currentTime - 86400000L); // 1 day ago
        ruleConfig.setEndTime(currentTime + 86400000L); // 1 day later
        ruleConfig.setSuggestedTimeRangeStart(currentTime - 3600000L); // 1 hour ago
        ruleConfig.setSuggestedTimeRangeEnd(currentTime + 3600000L); // 1 hour later
        ruleConfig.setCreationTime(currentTime - 172800000L); // 2 days ago
        ruleConfig.setModificationTime(currentTime - 86400000L); // 1 day ago
        ruleConfig.setProject(new String[] {"A11"});
        ruleConfig.setPosmMaterials("{\"posmMaterials\": [\"Lightbox\"]}");

        IntlRmsCountryTimezone countryTimezone = new IntlRmsCountryTimezone();
        countryTimezone.setCountryCode("IN");
        countryTimezone.setArea("Asia");
        countryTimezone.setCountryName("India");
        countryTimezone.setAreaCode("AS");

        NewProductTargetProjectResp projectResp = new NewProductTargetProjectResp();
        projectResp.setProject("A11");
        projectResp.setCountry(
                "MX,NI,IN,PA,PA,PE,PE,PY,PY,SV,SV,AZ,AZ,BY,BY,KZ,KZ,RU,RU,UZ,UZ,AR,AR,BO,BO,BR,BR,CL,CL,CO,CO,CR,CR,DO,DO,EC,EC,GT,GT,HN,HN,MX");
        projectResp.setCountryList(Arrays.asList("MX", "NI", "IN", "PA", "PA", "PE", "PE", "PY", "PY", "SV", "SV",
                "AZ", "AZ", "BY", "BY", "KZ", "KZ", "RU", "RU", "UZ", "UZ", "AR", "AR", "BO", "BO", "BR", "BR", "CL",
                "CL", "CO", "CO", "CR", "CR", "DO", "DO", "EC", "EC", "GT", "GT"));
        projectResp.setFinishTime(1758211199999L);
        projectResp.setBeginTime(1755187200000L);

        // Mocks
        when(ruleConfigService.getById(1L)).thenReturn(ruleConfig);

        // Mock converter
        InspectionTaskConfigurationInfoVO infoVO = new InspectionTaskConfigurationInfoVO();
        infoVO.setId(1L);
        infoVO.setTaskType(401);
        infoVO.setCycleType(1);
        infoVO.setRuleStatus(1);
        infoVO.setTargetType("1");
        infoVO.setCountry("IN");
        infoVO.setProject(Lists.newArrayList("A11"));
        infoVO.setPosmMaterials(Lists.newArrayList("Lightbox"));

        // Mock timezone mapper
        when(intlRmsCountryTimezoneMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Arrays.asList(countryTimezone));

        // Mock project mapper
        when(intlNewProductTargetMapper.listByProject(any(NewProductTargetProjectReq.class)))
                .thenReturn(Arrays.asList(projectResp));
        // When
        InspectionTaskConfigurationInfoVO result = service.getInspectionTaskInfo(request);

        // Then
        assertNotNull(result);
        assertEquals(1L, result.getId());
        assertEquals("Dummy", result.getTaskTypeDesc());
        assertEquals("日", result.getCycleTypeDesc());
        assertEquals("启用", result.getRuleStatusDesc());
        assertEquals("首销期", result.getTargetTypeDesc());
        assertEquals("Asia", result.getRegionDesc());
        assertEquals("India", result.getCountryDesc());
        assertNotNull(result.getStartTimeDesc());
        assertNotNull(result.getEndTimeDesc());
        assertNotNull(result.getSuggestedTimeRangeStartDesc());
        assertNotNull(result.getSuggestedTimeRangeEndDesc());
        assertNotNull(result.getCreationTimeDesc());
        assertNotNull(result.getModificationTimeDesc());
        assertNotNull(result.getNewProductTargetProjectRespList());
        assertNotNull(result.getNewProductTargetProjectRespList());
        assertEquals(1, result.getNewProductTargetProjectRespList().size());
        assertEquals("A11", result.getNewProductTargetProjectRespList().get(0).getProject());
        assertNotNull(result.getPosmMaterialsDesc());
        assertEquals(1, result.getPosmMaterialsDesc().size());
        assertEquals("店外灯箱", result.getPosmMaterialsDesc().get(0));

    }

    @Test
    public void testGetInspectionTaskInfo_RuleConfigNotFound() {
        // Given
        InspectionTaskConfigurationRequest request = new InspectionTaskConfigurationRequest();
        request.setId(1L);

        when(ruleConfigService.getById(1L)).thenReturn(null);

        // When & Then
        RetailRunTimeException exception = assertThrows(RetailRunTimeException.class,
                () -> service.getInspectionTaskInfo(request));
        assertTrue(exception.getMessage().contains("the inspection task configuration does not exist"));
    }

    @Test
    public void testGetInspectionTaskInfo_WithEmptyProject() {
        // Given
        InspectionTaskConfigurationRequest request = new InspectionTaskConfigurationRequest();
        request.setId(1L);
        try (MockedStatic<NewProductInspectionConvert> mockedConvert = mockStatic(NewProductInspectionConvert.class)) {
            RuleConfig ruleConfig = new RuleConfig();
            ruleConfig.setId(1L);
            ruleConfig.setTaskType(401);
            ruleConfig.setCycleType(1);
            ruleConfig.setRuleStatus(1);
            ruleConfig.setTargetType("1");
            ruleConfig.setCountry("IN");
            long currentTimeMillis = 1756110093053L;
            ruleConfig.setStartTime(currentTimeMillis - 86400000L); // 1 day ago
            ruleConfig.setEndTime(currentTimeMillis + 86400000L); // 1 day later
            ruleConfig.setSuggestedTimeRangeStart(currentTimeMillis - 3600000L); // 1 hour ago
            ruleConfig.setSuggestedTimeRangeEnd(currentTimeMillis + 3600000L); // 1 hour later
            ruleConfig.setCreationTime(currentTimeMillis - 172800000L); // 2 days ago
            ruleConfig.setModificationTime(currentTimeMillis - 86400000L); // 1 day ago
            ruleConfig.setProject(new String[] {"A11"});
            ruleConfig.setPosmMaterials("{\"posmMaterials\": [\"Lightbox\"]}");

            IntlRmsCountryTimezone countryTimezone = new IntlRmsCountryTimezone();
            countryTimezone.setCountryCode("IN");
            countryTimezone.setArea("Asia");
            countryTimezone.setCountryName("India");
            countryTimezone.setAreaCode("AS");

            NewProductTargetProjectResp projectResp = new NewProductTargetProjectResp();
            projectResp.setProject("A11");
            projectResp.setCountry(
                    "MX,NI,IN,PA,PA,PE,PE,PY,PY,SV,SV,AZ,AZ,BY,BY,KZ,KZ,RU,RU,UZ,UZ,AR,AR,BO,BO,BR,BR,CL,CL,CO,CO,CR,CR,DO,DO,EC,EC,GT,GT,HN,HN,MX");
            projectResp.setCountryList(Arrays.asList("MX", "NI", "IN", "PA", "PA", "PE", "PE", "PY", "PY", "SV", "SV",
                    "AZ", "AZ", "BY", "BY", "KZ", "KZ", "RU", "RU", "UZ", "UZ", "AR", "AR", "BO", "BO", "BR", "BR",
                    "CL", "CL", "CO", "CO", "CR", "CR", "DO", "DO", "EC", "EC", "GT", "GT"));
            projectResp.setFinishTime(1758211199999L);
            projectResp.setBeginTime(1755187200000L);

            // Mocks
            when(ruleConfigService.getById(1L)).thenReturn(ruleConfig);

            // Mock timezone mapper
            when(intlRmsCountryTimezoneMapper.selectList(any(LambdaQueryWrapper.class)))
                    .thenReturn(Arrays.asList(countryTimezone));

            // Mock project mapper
            when(intlNewProductTargetMapper.listByProject(any(NewProductTargetProjectReq.class)))
                    .thenReturn(Arrays.asList(projectResp));
            // When
            InspectionTaskConfigurationInfoVO result = service.getInspectionTaskInfo(request);

            // Then
            assertNotNull(result);
            assertEquals(1L, result.getId());
            assertEquals("Dummy", result.getTaskTypeDesc());
            assertEquals("日", result.getCycleTypeDesc());
            assertEquals("启用", result.getRuleStatusDesc());
            assertEquals("首销期", result.getTargetTypeDesc());
            assertEquals("Asia", result.getRegionDesc());
            assertEquals("India", result.getCountryDesc());
            assertEquals("2025-08-24", result.getStartTimeDesc());
            assertEquals("2025-08-26", result.getEndTimeDesc());
            assertEquals("12:51", result.getSuggestedTimeRangeStartDesc());
            assertEquals("14:51", result.getSuggestedTimeRangeEndDesc());
            assertEquals("2025-08-23 16:21:33", result.getCreationTimeDesc());
            assertEquals("2025-08-24 16:21:33", result.getModificationTimeDesc());
            assertNotNull(result.getNewProductTargetProjectRespList());
            assertEquals(1, result.getNewProductTargetProjectRespList().size());
            assertEquals("A11", result.getNewProductTargetProjectRespList().get(0).getProject());
            assertNotNull(result.getPosmMaterialsDesc());
            assertEquals(1, result.getPosmMaterialsDesc().size());
            assertEquals("店外灯箱", result.getPosmMaterialsDesc().get(0));
        }
    }

    private InspectionTaskConfigurationDTO createValidDTO() {
        InspectionTaskConfigurationDTO dto = new InspectionTaskConfigurationDTO();
        dto.setId(null);
        dto.setTaskName("测试任务");
        dto.setCountry("HK");
        dto.setStartTime(System.currentTimeMillis() + 86400000L); // 明天
        dto.setEndTime(System.currentTimeMillis() + 172800000L); // 后天
        dto.setTaskType(TaskTypeEnum.POSM.getCode());
        dto.setCycleType(CycleTypeEnum.DAILY.getCode());
        dto.setNeedInspection(YESNOEnum.YES.getCode());
        dto.setTargetType(TargetTypeEnum.FIRST_SALES_PERIOD.getCode());
        dto.setProject(Arrays.asList("P001"));
        dto.setCustomCycleDays(1);
        dto.setReminderDays(1);
        dto.setSuggestedTimeRangeStart(System.currentTimeMillis());
        dto.setSuggestedTimeRangeEnd(System.currentTimeMillis() + 3600000L);
        return dto;
    }

    private RuleConfig createValidRuleConfig() {
        RuleConfig ruleConfig = new RuleConfig();
        ruleConfig.setId(1L);
        ruleConfig.setTaskName("测试任务");
        ruleConfig.setCountry("HK");
        ruleConfig.setRegion("HMT");
        ruleConfig.setStartTime(System.currentTimeMillis() + 86400000L);
        ruleConfig.setEndTime(System.currentTimeMillis() + 172800000L);
        ruleConfig.setTaskType(TaskTypeEnum.POSM.getCode());
        ruleConfig.setCycleType(CycleTypeEnum.DAILY.getCode());
        ruleConfig.setNeedInspection(YESNOEnum.YES.getCode());
        ruleConfig.setTargetType(TargetTypeEnum.FIRST_SALES_PERIOD.getCode());
        ruleConfig.setProject(new String[] {"P001"});
        ruleConfig.setReminderDays(1);
        ruleConfig.setSuggestedTimeRangeStart(System.currentTimeMillis());
        ruleConfig.setSuggestedTimeRangeEnd(System.currentTimeMillis() + 3600000L);
        ruleConfig.setRuleStatus(InspectionRuleStautsEnum.DRAFT.getCode());
        return ruleConfig;
    }

    @Test
    void insertOrUpdate_insert_success() {
        // 准备数据
        InspectionTaskConfigurationDTO dto = createValidDTO();
        when(stringRedisTemplate.opsForValue()).thenReturn(valueOperations);

        try (MockedStatic<ContextUtil> mockedContextUtil = mockStatic(ContextUtil.class)) {
            mockedContextUtil.when(ContextUtil::getUserName).thenReturn("testUser");

            // Mock validator验证结果为空集合（无验证错误）
            Set<ConstraintViolation<InspectionTaskConfigurationDTO>> violations = new HashSet<>();
            when(validator.validate(dto)).thenReturn(violations);

            // Mock依赖
            IntlRmsCountryTimezone timezone = new IntlRmsCountryTimezone();
            timezone.setAreaCode("HMT");
            timezone.setArea("港澳台");
            timezone.setCountryName("Hong Kong");
            timezone.setCountryCode("HK");
            when(intlRmsCountryTimezoneMapper.selectList(any())).thenReturn(Arrays.asList(timezone));

            // 修复：使用具体参数而不是any()
            when(ruleConfigService.save(any(RuleConfig.class))).thenAnswer(invocation -> {
                RuleConfig rule = invocation.getArgument(0);
                rule.setId(1L);
                return true;
            });

            RuleConfig ruleConfig = createValidRuleConfig();
            ruleConfig.setId(1L);
            // 修复：使用具体参数而不是any()
            when(ruleConfigService.getById(1L)).thenReturn(ruleConfig);

            doNothing().when(inspectionRuleRelationService).batchInsert(anyList());

            // 修复：正确mock listByProject方法
            when(intlNewProductTargetMapper.listByProject(any(NewProductTargetProjectReq.class)))
                    .thenReturn(new ArrayList<>());

            // 执行测试
            InspectionTaskConfigurationInfoVO result = service.insertOrUpdate(dto);

            // 验证结果
            assertNotNull(result);
            assertEquals(1L, result.getId());
            verify(ruleConfigService).save(any(RuleConfig.class));
            verify(inspectionRuleRelationService).batchInsert(anyList());
        }
    }

    @Test
    void insertOrUpdate_update_success() {
        // 准备数据
        InspectionTaskConfigurationDTO dto = createValidDTO();
        dto.setId(1L);
        try (MockedStatic<ContextUtil> mockedContextUtil = mockStatic(ContextUtil.class)) {
            mockedContextUtil.when(ContextUtil::getUserName).thenReturn("testUser");
            // Mock validator验证结果为空集合（无验证错误）
            Set<ConstraintViolation<InspectionTaskConfigurationDTO>> violations = new HashSet<>();
            when(validator.validate(dto)).thenReturn(violations);
            // Mock依赖
            RuleConfig existingRuleConfig = createValidRuleConfig();
            existingRuleConfig.setId(1L);
            when(ruleConfigService.getById(1L)).thenReturn(existingRuleConfig);

            IntlRmsCountryTimezone timezone = new IntlRmsCountryTimezone();
            timezone.setAreaCode("HMT");
            timezone.setArea("港澳台");
            timezone.setCountryName("Hong Kong");
            timezone.setCountryCode("HK");
            when(intlRmsCountryTimezoneMapper.selectList(any())).thenReturn(Arrays.asList(timezone));

            when(ruleConfigService.update(any(RuleConfig.class), any(LambdaQueryWrapper.class))).thenReturn(true);

            when(inspectionRuleRelationService.selectByRuleId(1L)).thenReturn(new ArrayList<>());

            // 执行测试
            InspectionTaskConfigurationInfoVO result = service.insertOrUpdate(dto);

            // 验证结果
            assertNotNull(result);
            assertEquals(1L, result.getId());
            verify(ruleConfigService, times(2)).getById(1L);
            verify(ruleConfigService).update(any(RuleConfig.class), any(LambdaQueryWrapper.class));
        }
    }

    @Test
    void insertOrUpdate_insert_enable_success() {
        // 准备数据
        InspectionTaskConfigurationDTO dto = createValidDTO();
        dto.setEnableFlag(BrainPlatformOperateTypeEnum.ENABLE.getCode());
        when(stringRedisTemplate.opsForValue()).thenReturn(valueOperations);

        try (MockedStatic<ContextUtil> mockedContextUtil = mockStatic(ContextUtil.class)) {
            mockedContextUtil.when(ContextUtil::getUserName).thenReturn("testUser");
            // Mock validator验证结果为空集合（无验证错误）
            Set<ConstraintViolation<InspectionTaskConfigurationDTO>> violations = new HashSet<>();
            when(validator.validate(dto)).thenReturn(violations);

            // Mock依赖
            IntlRmsCountryTimezone timezone = new IntlRmsCountryTimezone();
            timezone.setAreaCode("HMT");
            timezone.setArea("港澳台");
            timezone.setCountryName("Hong Kong");
            timezone.setCountryCode("HK");
            when(intlRmsCountryTimezoneMapper.selectList(any())).thenReturn(Arrays.asList(timezone));

            // 修复：使用具体参数而不是any()
            when(ruleConfigService.save(any(RuleConfig.class))).thenAnswer(invocation -> {
                RuleConfig rule = invocation.getArgument(0);
                rule.setId(1L);
                return true;
            });

            RuleConfig ruleConfig = createValidRuleConfig();
            ruleConfig.setId(1L);
            // 修复：使用具体参数而不是any()
            when(ruleConfigService.getById(1L)).thenReturn(ruleConfig);

            doNothing().when(inspectionRuleRelationService).batchInsert(anyList());

            // 修复：正确mock listByProject方法
            when(intlNewProductTargetMapper.listByProject(any(NewProductTargetProjectReq.class)))
                    .thenReturn(new ArrayList<>());
            AddTaskDefinitionResp addTaskDefinitionResp = new AddTaskDefinitionResp();
            addTaskDefinitionResp.setId(5L);
            Result<AddTaskDefinitionResp> bpReturn = Result.success(addTaskDefinitionResp);

            when(brainPlatformOuterProvider.addOrEditTaskDefinitionReturnIdForWlXunjian(any())).thenReturn(bpReturn);

            // 执行测试
            InspectionTaskConfigurationInfoVO result = service.insertOrUpdate(dto);

            // 验证结果
            assertNotNull(result);
            assertEquals(1L, result.getId());
            verify(ruleConfigService).save(any(RuleConfig.class));
            verify(inspectionRuleRelationService).batchInsert(anyList());
        }
    }

    @Test
    void updateInspectionTaskConfiguration_ruleConfigNotInDraftStatus_ThrowsException() {
        // 准备数据：规则配置状态不是草稿
        InspectionTaskConfigurationDTO dto = new InspectionTaskConfigurationDTO();
        dto.setId(1L);

        RuleConfig existingRuleConfig = new RuleConfig();
        existingRuleConfig.setId(1L);
        existingRuleConfig.setRuleStatus(InspectionRuleStautsEnum.ACTIVE.getCode()); // 设置为启用状态而非草稿状态

        // Mock依赖
        when(ruleConfigService.getById(1L)).thenReturn(existingRuleConfig);

        // 执行测试并验证异常
        RetailRunTimeException exception = assertThrows(RetailRunTimeException.class, () -> {
            service.updateInspectionTaskConfiguration(dto);
        });

        // 验证异常消息
        assertTrue(exception.getMessage()
                .contains("The inspection task configuration is not in the draft state, so it cannot be saved."));

    }

    /**
     * 测试周期类型为NONE的情况
     */
    @Test
    void testSubmitInspectionTaskConfigurationWithNoneCycleType() {
        RuleConfig mockRuleConfig = new RuleConfig();
        mockRuleConfig.setId(1L);
        mockRuleConfig.setRuleStatus(InspectionRuleStautsEnum.DRAFT.getCode());
        mockRuleConfig.setCountry("CN");
        mockRuleConfig.setProject(new String[] {"Project1"});
        mockRuleConfig.setStoreType(new String[] {"Project1"});
        mockRuleConfig.setAssignedStore(new String[] {"Project1"});
        mockRuleConfig.setStartTime(System.currentTimeMillis());
        mockRuleConfig.setEndTime(System.currentTimeMillis() + 86400000); // 1天后
        mockRuleConfig.setSuggestedTimeRangeStart(System.currentTimeMillis());
        mockRuleConfig.setSuggestedTimeRangeEnd(System.currentTimeMillis() + 3600000); // 1小时后
        mockRuleConfig.setTaskType(TaskTypeEnum.POSM.getCode());
        mockRuleConfig.setNeedInspection(0);
        InspectionTaskConfigurationDTO mockDTO = new InspectionTaskConfigurationDTO();
        mockDTO.setId(1L);
        try (MockedStatic<ContextUtil> mockedContextUtil = mockStatic(ContextUtil.class)) {
            mockedContextUtil.when(ContextUtil::getUserName).thenReturn("testUser");
            // Arrange
            mockRuleConfig.setCycleType(CycleTypeEnum.NONE.getCode());

            AddTaskDefinitionResp mockResp = new AddTaskDefinitionResp();
            mockResp.setId(5L);
            Result<AddTaskDefinitionResp> mockResult = Result.success(mockResp);

            when(ruleConfigService.getById(anyLong())).thenReturn(mockRuleConfig);
            when(brainPlatformOuterProvider.addOrEditTaskDefinitionReturnIdForWlXunjian(any())).thenReturn(mockResult);
            when(ruleConfigService.update(any(RuleConfig.class), any())).thenReturn(true);

            // Act
            service.submitInspectionTaskConfiguration(mockDTO);

            // Assert
            verify(brainPlatformOuterProvider).addOrEditTaskDefinitionReturnIdForWlXunjian(any());
            verify(ruleConfigService).update(any(RuleConfig.class), any());

        }
    }

    @Test
    void insertOrUpdate_insert_startTimeAfterEndTime_ThrowsException1() {
        // 准备数据：开始时间晚于结束时间
        InspectionTaskConfigurationDTO dto = new InspectionTaskConfigurationDTO();
        dto.setTaskName("测试任务");
        dto.setCountry("CN");
        dto.setStartTime(System.currentTimeMillis() + 86400000L); // 明天
        dto.setEndTime(System.currentTimeMillis()); // 现在
        dto.setTaskType(TaskTypeEnum.POSM.getCode());
        dto.setCycleType(CycleTypeEnum.DAILY.getCode());
        dto.setNeedInspection(YESNOEnum.YES.getCode());

        // Mock validator验证结果为空集合（无验证错误）
        Set<ConstraintViolation<InspectionTaskConfigurationDTO>> violations = new HashSet<>();
        when(validator.validate(dto)).thenReturn(violations);

        // 执行测试并验证异常
        RetailRunTimeException exception = assertThrows(RetailRunTimeException.class, () -> {
            service.insertOrUpdate(dto);
        });

        assertTrue(exception.getMessage().contains("The start time cannot be greater than the end time"));
    }

    @Test
    void insertOrUpdate_insert_startTimeAfterEndTime_ThrowsException2() {
        // 准备数据：开始时间晚于结束时间
        InspectionTaskConfigurationDTO dto = new InspectionTaskConfigurationDTO();
        dto.setTaskName("测试任务");
        dto.setCountry("CN");
        dto.setStartTime(System.currentTimeMillis() - 86400000L);// 昨天
        dto.setEndTime(System.currentTimeMillis() + 86400000L);  // 明天
        dto.setTaskType(TaskTypeEnum.POSM.getCode());
        dto.setCycleType(CycleTypeEnum.DAILY.getCode());
        dto.setNeedInspection(YESNOEnum.YES.getCode());

        // Mock validator验证结果为空集合（无验证错误）
        Set<ConstraintViolation<InspectionTaskConfigurationDTO>> violations = new HashSet<>();
        when(validator.validate(dto)).thenReturn(violations);

        // 执行测试并验证异常
        RetailRunTimeException exception = assertThrows(RetailRunTimeException.class, () -> {
            service.insertOrUpdate(dto);
        });

        assertTrue(exception.getMessage().contains("The start time cannot be less than the current time"));
    }

    @Test
    void insertOrUpdate_insert_startTimeAfterEndTime_ThrowsException3() {
        // 准备数据：开始时间晚于结束时间
        InspectionTaskConfigurationDTO dto = new InspectionTaskConfigurationDTO();
        dto.setTaskName("测试任务");
        dto.setCountry("CN");
        dto.setStartTime(System.currentTimeMillis() - 3600000L);// 1小时前
        dto.setEndTime(System.currentTimeMillis() - 1800000L); // 30分钟前
        dto.setTaskType(TaskTypeEnum.POSM.getCode());
        dto.setCycleType(CycleTypeEnum.DAILY.getCode());
        dto.setNeedInspection(YESNOEnum.YES.getCode());

        // Mock validator验证结果为空集合（无验证错误）
        Set<ConstraintViolation<InspectionTaskConfigurationDTO>> violations = new HashSet<>();
        when(validator.validate(dto)).thenReturn(violations);

        // 执行测试并验证异常
        RetailRunTimeException exception = assertThrows(RetailRunTimeException.class, () -> {
            service.insertOrUpdate(dto);
        });

        assertTrue(exception.getMessage().contains("The end time cannot be less than the current time"));
    }

    /**
     * 测试：参数校验失败时抛出异常
     */
    @Test
    public void testStartOrStopInspectionTask_ValidationFails_ThrowsException() {
        InspectionTaskConfigurationRequest request = new InspectionTaskConfigurationRequest();
        request.setEnableTaskFlag(BrainPlatformOperateTypeEnum.ENABLE.getCode());

        ConstraintViolation<InspectionTaskConfigurationRequest> violation = mock(ConstraintViolation.class);
        when(violation.getMessage()).thenReturn("参数错误");
        Set<ConstraintViolation<InspectionTaskConfigurationRequest>> violations = Sets.newHashSet(violation);

        when(validator.validate(request, InspectionTaskConfigurationRequest.EnableTaskFlag.class))
                .thenReturn(violations);

        assertThrows(RetailRunTimeException.class, () -> {
            service.startOrStopInspectionTask(request);
        });
    }

    /**
     * 测试：查询不到规则配置时抛出异常
     */
    @Test
    public void testStartOrStopInspectionTask_RuleConfigNotFound_ThrowsException() {
        InspectionTaskConfigurationRequest request = new InspectionTaskConfigurationRequest();
        request.setId(999L);
        request.setEnableTaskFlag(BrainPlatformOperateTypeEnum.ENABLE.getCode());

        when(validator.validate(request, InspectionTaskConfigurationRequest.EnableTaskFlag.class))
                .thenReturn(Sets.newHashSet());
        when(ruleConfigService.getById(999L)).thenReturn(null);

        assertThrows(RetailRunTimeException.class, () -> {
            service.startOrStopInspectionTask(request);
        });
    }

    /**
     * 测试：启用任务，状态为草稿，成功启用
     */
    @Test
    public void testStartOrStopInspectionTask_EnableTask_StatusIsDraft_Success() {
        InspectionTaskConfigurationRequest request = new InspectionTaskConfigurationRequest();
        request.setId(1L);
        request.setEnableTaskFlag(BrainPlatformOperateTypeEnum.ENABLE.getCode());

        RuleConfig ruleConfig = createValidRuleConfig();

        ruleConfig.setRuleStatus(InspectionRuleStautsEnum.DRAFT.getCode());

        when(validator.validate(request, InspectionTaskConfigurationRequest.EnableTaskFlag.class))
                .thenReturn(Sets.newHashSet());
        when(ruleConfigService.getById(1L)).thenReturn(ruleConfig);

        // 模拟静态方法
        try (MockedStatic<ContextUtil> mockedContextUtil = mockStatic(ContextUtil.class)) {
            mockedContextUtil.when(ContextUtil::getUserName).thenReturn("testUser");
            mockedContextUtil.when(ContextUtil::getMiID).thenReturn(12345L);

            AddTaskDefinitionResp addTaskDefinitionResp = new AddTaskDefinitionResp();
            addTaskDefinitionResp.setId(5L);
            Result<AddTaskDefinitionResp> bpReturn = Result.success(addTaskDefinitionResp);

            when(brainPlatformOuterProvider.addOrEditTaskDefinitionReturnIdForWlXunjian(any())).thenReturn(bpReturn);

            service.startOrStopInspectionTask(request);

            // 验证是否调用了提交任务的方法（这里简化处理）
            verify(ruleConfigService, times(2)).getById(1L);
        }
    }

    /**
     * 测试：停用任务，状态为启用，成功停用
     */
    @Test
    public void testStartOrStopInspectionTask_DisableTask_StatusIsActive_Success() {
        InspectionTaskConfigurationRequest request = new InspectionTaskConfigurationRequest();
        request.setId(1L);
        request.setEnableTaskFlag(BrainPlatformOperateTypeEnum.DISABLE.getCode());

        RuleConfig ruleConfig = new RuleConfig();
        ruleConfig.setId(1L);
        ruleConfig.setRuleStatus(InspectionRuleStautsEnum.ACTIVE.getCode());
        ruleConfig.setTaskDefId(100L);

        when(validator.validate(request, InspectionTaskConfigurationRequest.EnableTaskFlag.class))
                .thenReturn(Sets.newHashSet());
        when(ruleConfigService.getById(1L)).thenReturn(ruleConfig);

        // 模拟大脑平台调用成功
        when(brainPlatformOuterProvider.toggleTaskDefinition(any())).thenReturn(Result.success(null));

        // 创建 mock 对象
        doNothing().when(mockRepository).disableInspectionRecord(eq(1L), anyString());

        // 模拟静态方法
        try (MockedStatic<ContextUtil> mockedContextUtil = mockStatic(ContextUtil.class)) {
            mockedContextUtil.when(ContextUtil::getUserName).thenReturn("testUser");
            mockedContextUtil.when(ContextUtil::getMiID).thenReturn(12345L);

            service.startOrStopInspectionTask(request);

            // 验证更新状态
            verify(ruleConfigService, times(1)).update(any(RuleConfig.class), any());
        }
    }

    /**
     * 测试：停用任务，但状态不是启用，抛出异常
     */
    @Test
    public void testStartOrStopInspectionTask_DisableTask_StatusNotActive_ThrowsException() {
        InspectionTaskConfigurationRequest request = new InspectionTaskConfigurationRequest();
        request.setId(1L);
        request.setEnableTaskFlag(BrainPlatformOperateTypeEnum.DISABLE.getCode());

        RuleConfig ruleConfig = new RuleConfig();
        ruleConfig.setId(1L);
        ruleConfig.setRuleStatus(InspectionRuleStautsEnum.INACTIVE.getCode());
        when(validator.validate(request, InspectionTaskConfigurationRequest.EnableTaskFlag.class))
                .thenReturn(new HashSet<>());
        when(ruleConfigService.getById(1L)).thenReturn(ruleConfig);

        assertThrows(RetailRunTimeException.class, () -> {
            service.startOrStopInspectionTask(request);
        });
    }

    /**
     * 测试：启用任务，但状态不是草稿，抛出异常
     */
    @Test
    public void testStartOrStopInspectionTask_EnableTask_StatusNotDraft_ThrowsException() {
        InspectionTaskConfigurationRequest request = new InspectionTaskConfigurationRequest();
        request.setId(1L);
        request.setEnableTaskFlag(BrainPlatformOperateTypeEnum.ENABLE.getCode());

        RuleConfig ruleConfig = new RuleConfig();
        ruleConfig.setId(1L);
        ruleConfig.setRuleStatus(InspectionRuleStautsEnum.ACTIVE.getCode());

        when(validator.validate(request, InspectionTaskConfigurationRequest.EnableTaskFlag.class))
                .thenReturn(Sets.newHashSet());
        when(ruleConfigService.getById(1L)).thenReturn(ruleConfig);

        assertThrows(RetailRunTimeException.class, () -> {
            service.startOrStopInspectionTask(request);
        });
    }

    /**
     * 测试：非法操作码，抛出异常
     */
    @Test
    public void testStartOrStopInspectionTask_IllegalOperation_ThrowsException() {
        InspectionTaskConfigurationRequest request = new InspectionTaskConfigurationRequest();
        request.setId(1L);
        request.setEnableTaskFlag("invalid");

        RuleConfig ruleConfig = new RuleConfig();
        ruleConfig.setId(1L);
        ruleConfig.setRuleStatus(InspectionRuleStautsEnum.DRAFT.getCode());

        when(validator.validate(request, InspectionTaskConfigurationRequest.EnableTaskFlag.class))
                .thenReturn(Sets.newHashSet());
        when(ruleConfigService.getById(1L)).thenReturn(ruleConfig);

        assertThrows(RetailRunTimeException.class, () -> {
            service.startOrStopInspectionTask(request);
        });
    }

    @Test
    void exportInspectionTask_success() throws Exception {
        // 准备数据
        Page<InspectionTaskConfigurationPageResponse> page = new Page<>();
        page.setRecords(Collections.emptyList());
        page.setTotal(0);

        try (MockedStatic<ContextUtil> contextUtilMockedStatic = Mockito.mockStatic(ContextUtil.class)) {
            contextUtilMockedStatic.when(ContextUtil::getMiID).thenReturn(123456L);

            // Mock service方法
            when(ruleConfigService.find4Page(any(), any())).thenReturn(page);
            FdsUploadResult uploadResult = new FdsUploadResult();
            uploadResult.setUrl("http://example.com/file.xlsx");
            when(fdsService.upload(anyString(), any(File.class), anyBoolean()))
                    .thenReturn(uploadResult);
            when(jobTriggerHelper.triggerCommonExportJob(anyString(), anyString(), anyString(), anyString()))
                    .thenReturn(new CommonResponse<>(0, "success", "data"));

            // 执行测试
            service.exportInspectionTask(request);

            // 验证
            verify(ruleConfigService, times(1)).find4Page(any(), any());
            verify(fdsService, times(1)).upload(anyString(), any(File.class), anyBoolean());
            verify(jobTriggerHelper, times(1)).triggerCommonExportJob(anyString(), anyString(), anyString(),
                    anyString());
        }
    }

}