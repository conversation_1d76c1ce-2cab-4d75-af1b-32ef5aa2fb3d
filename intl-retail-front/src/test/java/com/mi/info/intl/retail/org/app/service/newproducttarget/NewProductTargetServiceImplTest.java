package com.mi.info.intl.retail.org.app.service.newproducttarget;

import com.mi.info.intl.retail.intlretail.service.api.fds.FdsService;
import com.mi.info.intl.retail.intlretail.service.api.fds.dto.FdsUploadResult;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.CommonResponse;
import com.mi.info.intl.retail.intlretail.service.api.newproducttarget.dto.NewProdcutTargetMetaReq;
import com.mi.info.intl.retail.intlretail.service.api.newproducttarget.dto.NewProdcutTargetMetaResp;
import com.mi.info.intl.retail.intlretail.service.api.newproducttarget.dto.NewProductTargetAddReq;
import com.mi.info.intl.retail.intlretail.service.api.newproducttarget.dto.NewProductTargetImportReq;
import com.mi.info.intl.retail.intlretail.service.api.newproducttarget.dto.NewProductTargetItem;
import com.mi.info.intl.retail.intlretail.service.api.newproducttarget.dto.NewProductTargetProjectReq;
import com.mi.info.intl.retail.intlretail.service.api.newproducttarget.dto.NewProductTargetProjectResp;
import com.mi.info.intl.retail.intlretail.service.api.newproducttarget.dto.NewProductTargetReq;
import com.mi.info.intl.retail.intlretail.service.api.newproducttarget.dto.NewProductTargetUpdateReq;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.model.PageResponse;
import com.mi.info.intl.retail.org.domain.newproducttarget.NewProductTargetDomainService;
import com.mi.info.intl.retail.cooperation.downloadcenter.example.JobTriggerHelper;
import com.xiaomi.cnzone.proretail.newcommon.util.RetailJsonUtil;
import org.apache.dubbo.rpc.RpcContext;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class NewProductTargetServiceImplTest {

    private static final Logger log = LoggerFactory.getLogger(NewProductTargetServiceImplTest.class);

    @InjectMocks
    private NewProductTargetServiceImpl newProductTargetService;

    @Mock
    private NewProductTargetDomainService newProductTargetDomainService;
    
    @Mock
    private FdsService fdsService;
    
    @Mock
    private JobTriggerHelper jobTriggerHelper;

    @Test
    @DisplayName("测试新增新品目标 - 成功场景")
    public void testAddNewProductTarget() {
        // 准备测试数据
        NewProductTargetAddReq req = new NewProductTargetAddReq();
        req.setCategoryCode("CAT001");
        req.setCategoryName("测试品类");
        req.setSeries("测试系列");
        req.setProjects(Arrays.asList("项目1", "项目2"));
        req.setCountryCode(Arrays.asList("SG", "MY"));
        req.setStartTime(System.currentTimeMillis());
        req.setEndTime(System.currentTimeMillis() + 86400000);

        log.info("测试请求参数:{}", RetailJsonUtil.toJson(req));

        // 模拟领域服务返回 - 成功时data列表为空
        List<String> expectedData = Arrays.asList(); // 成功时返回空列表
        CommonApiResponse<List<String>> domainServiceResponse = new CommonApiResponse<>(expectedData);
        when(newProductTargetDomainService.addNewProductTarget(req)).thenReturn(domainServiceResponse);

        // 调用被测试方法
        CommonApiResponse<List<String>> result = newProductTargetService.addNewProductTarget(req);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getCode());
        assertEquals("ok", result.getMessage());
        assertNotNull(result.getData());
        assertEquals(expectedData, result.getData());

        // 验证领域服务被调用了一次
        verify(newProductTargetDomainService, times(1)).addNewProductTarget(req);
    }

//    @Test
    @DisplayName("测试新增新品目标 - 空参数场景")
    public void testAddNewProductTargetWithNullReq() {
        // 测试传入null参数的情况
        assertThrows(NullPointerException.class, () -> {
            newProductTargetService.addNewProductTarget(null);
        });
    }

    @Test
    @DisplayName("测试新增新品目标 - 数据已存在场景")
    public void testAddNewProductTargetWithExistingData() {
        // 准备测试数据
        NewProductTargetAddReq req = new NewProductTargetAddReq();
        req.setCategoryCode("CAT001");
        req.setCategoryName("测试品类");
        req.setSeries("测试系列");
        req.setProjects(Arrays.asList("项目1", "项目2"));
        req.setCountryCode(Arrays.asList("SG", "MY"));
        req.setStartTime(System.currentTimeMillis());
        req.setEndTime(System.currentTimeMillis() + 86400000);

        // 模拟领域服务返回 - 存在重复数据的情况
        List<String> expectedData = Arrays.asList("projectLine PL001; project: 项目1; country: SG; already existed");
        CommonApiResponse<List<String>> domainServiceResponse = new CommonApiResponse<>(expectedData);
        when(newProductTargetDomainService.addNewProductTarget(req)).thenReturn(domainServiceResponse);

        // 调用被测试方法
        CommonApiResponse<List<String>> result = newProductTargetService.addNewProductTarget(req);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getCode());
        assertEquals("ok", result.getMessage());
        assertNotNull(result.getData());
        assertFalse(result.getData().isEmpty());
        assertEquals(1, result.getData().size());
        assertEquals("projectLine PL001; project: 项目1; country: SG; already existed", result.getData().get(0));

        // 验证领域服务被调用了一次
        verify(newProductTargetDomainService, times(1)).addNewProductTarget(req);
    }

    @Test
    @DisplayName("测试查询新品目标列表 - 成功场景")
    public void testListNewProductTarget() {
        // 准备测试数据
        NewProductTargetReq req = new NewProductTargetReq();
        req.setPageNum(1L);
        req.setPageSize(10L);
        req.setProductLines(Arrays.asList("PL001", "PL002"));
        req.setProjects(Arrays.asList("项目1", "项目2"));
        req.setCountryCode(Arrays.asList("SG", "MY"));

        // 模拟领域服务返回
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<NewProductTargetItem> page = 
                new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(1, 10, 2);
        
        NewProductTargetItem item1 = new NewProductTargetItem();
        item1.setId(1L);
        item1.setProductLine("PL001");
        item1.setProject("项目1");
        item1.setCountry("SG");
        
        NewProductTargetItem item2 = new NewProductTargetItem();
        item2.setId(2L);
        item2.setProductLine("PL002");
        item2.setProject("项目2");
        item2.setCountry("MY");
        
        page.setRecords(Arrays.asList(item1, item2));
        
        when(newProductTargetDomainService.listNewProductTarget(req)).thenReturn(page);

        // 调用被测试方法
        PageResponse<NewProductTargetItem> result = newProductTargetService.listNewProductTarget(req);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.getTotalCount());
        assertEquals(1, result.getPageNum());
        assertEquals(10, result.getPageSize());
        assertNotNull(result.getList());
        assertEquals(2, result.getList().size());
        assertEquals("PL001", result.getList().get(0).getProductLine());
        assertEquals("项目1", result.getList().get(0).getProject());
        assertEquals("SG", result.getList().get(0).getCountry());
        assertEquals("PL002", result.getList().get(1).getProductLine());
        assertEquals("项目2", result.getList().get(1).getProject());
        assertEquals("MY", result.getList().get(1).getCountry());

        // 验证领域服务被调用了一次
        verify(newProductTargetDomainService, times(1)).listNewProductTarget(req);
    }

    @Test
    @DisplayName("测试根据ID查询新品目标列表 - 成功场景")
    public void testListNewProductTargetById() {
        // 准备测试数据
        NewProductTargetReq req = new NewProductTargetReq();
        req.setId(1L);
        req.setPageNum(1L);
        req.setPageSize(10L);

        NewProductTargetItem item = new NewProductTargetItem();
        item.setId(1L);
        item.setProductLine("PL001");
        item.setProject("项目1");
        item.setCountry("SG");

        com.baomidou.mybatisplus.extension.plugins.pagination.Page<NewProductTargetItem> page =
                new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(1, 10, 1);

        page.setRecords(Arrays.asList(item));

        when(newProductTargetDomainService.getNewProductTargetItemById(1L)).thenReturn(item);
        when(newProductTargetDomainService.listNewProductTarget(any(NewProductTargetReq.class))).thenReturn(page);

        // 调用被测试方法
        PageResponse<NewProductTargetItem> result = newProductTargetService.listNewProductTarget(req);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getTotalCount());
        assertNotNull(result.getList());
        assertEquals(1, result.getList().size());
        assertEquals("PL001", result.getList().get(0).getProductLine());
        assertEquals("项目1", result.getList().get(0).getProject());
        assertEquals("SG", result.getList().get(0).getCountry());

        // 验证领域服务被调用
        verify(newProductTargetDomainService, times(1)).getNewProductTargetItemById(1L);
        verify(newProductTargetDomainService, times(1)).listNewProductTarget(any(NewProductTargetReq.class));
    }

    @Test
    @DisplayName("测试更新新品目标 - 成功场景")
    public void testUpdateNewProductTarget() {
        // 准备测试数据
        NewProductTargetUpdateReq req = new NewProductTargetUpdateReq();
        NewProductTargetUpdateReq.NewProductTargetUpdateItemReq itemReq = 
                new NewProductTargetUpdateReq.NewProductTargetUpdateItemReq();
        itemReq.setId(1L);
        itemReq.setLduPlanCount(100);
        req.setItems(Arrays.asList(itemReq));

        // 模拟领域服务返回
        CommonApiResponse<String> domainServiceResponse = CommonApiResponse.success("更新成功");
        when(newProductTargetDomainService.updateNewProductTarget(req)).thenReturn(domainServiceResponse);

        // 调用被测试方法
        CommonApiResponse<String> result = newProductTargetService.updateNewProductTarget(req);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getCode());
        assertEquals("ok", result.getMessage());
        assertEquals("更新成功", result.getData());

        // 验证领域服务被调用了一次
        verify(newProductTargetDomainService, times(1)).updateNewProductTarget(req);
    }

    @Test
    @DisplayName("测试更新新品目标 - 空参数场景")
    public void testUpdateNewProductTargetWithEmptyItems() {
        // 准备测试数据
        NewProductTargetUpdateReq req = new NewProductTargetUpdateReq();
        req.setItems(Collections.emptyList());

        // 模拟领域服务返回
        CommonApiResponse<String> domainServiceResponse = CommonApiResponse.success("更新成功");
        when(newProductTargetDomainService.updateNewProductTarget(req)).thenReturn(domainServiceResponse);

        // 调用被测试方法
        CommonApiResponse<String> result = newProductTargetService.updateNewProductTarget(req);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getCode());
        assertEquals("ok", result.getMessage());
        assertEquals("更新成功", result.getData());

        // 验证领域服务被调用了一次
        verify(newProductTargetDomainService, times(1)).updateNewProductTarget(req);
    }

    @Test
    @DisplayName("测试查询元数据 - 成功场景")
    public void testListMeta() {
        // 准备测试数据
        NewProdcutTargetMetaReq req = new NewProdcutTargetMetaReq();

        // 模拟领域服务返回
        NewProdcutTargetMetaResp metaResp = new NewProdcutTargetMetaResp();
        metaResp.setCode("PL001");
        metaResp.setName("产品线1");
        
        CommonApiResponse<List<NewProdcutTargetMetaResp>> domainServiceResponse = CommonApiResponse.success(Arrays.asList(metaResp));
        when(newProductTargetDomainService.listMeta(req)).thenReturn(domainServiceResponse);

        // 调用被测试方法
        CommonApiResponse<List<NewProdcutTargetMetaResp>> result = newProductTargetService.listMeta(req);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getCode());
        assertEquals("ok", result.getMessage());
        assertNotNull(result.getData());
        assertEquals(1, result.getData().size());
        assertEquals("PL001", result.getData().get(0).getCode());
        assertEquals("产品线1", result.getData().get(0).getName());

        // 验证领域服务被调用了一次
        verify(newProductTargetDomainService, times(1)).listMeta(req);
    }

    @Test
    @DisplayName("测试查询元数据 - 空参数场景")
    public void testListMetaWithNullReq() {
        // 准备测试数据
        NewProdcutTargetMetaReq req = null;

        // 模拟领域服务返回
        CommonApiResponse<List<NewProdcutTargetMetaResp>> domainServiceResponse = CommonApiResponse.success(Collections.emptyList());
        when(newProductTargetDomainService.listMeta(req)).thenReturn(domainServiceResponse);

        // 调用被测试方法
        CommonApiResponse<List<NewProdcutTargetMetaResp>> result = newProductTargetService.listMeta(req);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getCode());
        assertEquals("ok", result.getMessage());
        assertNotNull(result.getData());
        assertTrue(result.getData().isEmpty());

        // 验证领域服务被调用了一次
        verify(newProductTargetDomainService, times(1)).listMeta(req);
    }

    @Test
    @DisplayName("测试导入新品目标 - 成功场景")
    public void testImportNewProdcutTargetMetaResp() {
        // 准备测试数据
        NewProductTargetImportReq req = new NewProductTargetImportReq();
        req.setUrl("http://test.com/file.xlsx");

        // 模拟领域服务返回
        CommonApiResponse<String> domainServiceResponse = CommonApiResponse.success("导入成功");
        when(newProductTargetDomainService.importNewProdcutTargetMetaResp(req)).thenReturn(domainServiceResponse);

        // 调用被测试方法
        CommonApiResponse<String> result = newProductTargetService.importNewProdcutTargetMetaResp(req);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getCode());
        assertEquals("ok", result.getMessage());
        assertEquals("导入成功", result.getData());

        // 验证领域服务被调用了一次
        verify(newProductTargetDomainService, times(1)).importNewProdcutTargetMetaResp(req);
    }

    @Test
    @DisplayName("测试下载新品目标模板 - 成功场景")
    public void testDownLoadNewProductTargetTempalte() {
        // 准备测试数据
        NewProductTargetImportReq req = new NewProductTargetImportReq();

        // 调用被测试方法
        CommonApiResponse<String> result = newProductTargetService.downLoadNewProductTargetTempalte(req);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getCode());
        assertEquals("ok", result.getMessage());
        assertNull(result.getData()); // targetUploadUrl 默认为 null

        // 无需验证领域服务调用，因为该方法直接返回结果
    }

    @Test
    @DisplayName("测试查询项目列表 - 成功场景")
    public void testListProject() {
        // 准备测试数据
        NewProductTargetProjectReq req = new NewProductTargetProjectReq();
        req.setProject("PL001");

        // 模拟领域服务返回
        NewProductTargetProjectResp projectResp = new NewProductTargetProjectResp();
        projectResp.setProject("项目1");
        projectResp.setCountry("SG");
        
        List<NewProductTargetProjectResp> projectRespList = Arrays.asList(projectResp);
        when(newProductTargetDomainService.listProject(req)).thenReturn(projectRespList);

        // 调用被测试方法
        CommonApiResponse<List<NewProductTargetProjectResp>> result = newProductTargetService.listProject(req);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getCode());
        assertEquals("ok", result.getMessage());
        assertNotNull(result.getData());
        assertEquals(1, result.getData().size());
        assertEquals("项目1", result.getData().get(0).getProject());
        assertEquals("SG", result.getData().get(0).getCountry());

        // 验证领域服务被调用了一次
        verify(newProductTargetDomainService, times(1)).listProject(req);
    }

    @Test
    @DisplayName("测试网关查询新品目标列表 - 成功场景")
    public void testListNewProductTargetGateWay() {
        // 准备测试数据
        NewProductTargetReq req = new NewProductTargetReq();
        req.setPageNum(1L);
        req.setPageSize(10L);
        req.setProductLines(Arrays.asList("PL001", "PL002"));

        // 模拟内部方法返回
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<NewProductTargetItem> page = 
                new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(1, 10, 1);
        
        NewProductTargetItem item = new NewProductTargetItem();
        item.setId(1L);
        item.setProductLine("PL001");
        item.setProject("项目1");
        item.setCountry("SG");
        
        page.setRecords(Arrays.asList(item));
        
        when(newProductTargetDomainService.listNewProductTarget(req)).thenReturn(page);

        // 调用被测试方法
        CommonApiResponse<PageResponse<NewProductTargetItem>> result = newProductTargetService.listNewProductTargetGateWay(req);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getCode());
        assertEquals("ok", result.getMessage());
        assertNotNull(result.getData());
        assertEquals(1, result.getData().getTotalCount());
        assertEquals(1, result.getData().getList().size());
        assertEquals("PL001", result.getData().getList().get(0).getProductLine());

        // 验证领域服务被调用了一次
        verify(newProductTargetDomainService, times(1)).listNewProductTarget(req);
    }

    @Test
    @DisplayName("测试查询空列表场景")
    public void testListNewProductTargetWithEmptyResult() {
        // 准备测试数据
        NewProductTargetReq req = new NewProductTargetReq();
        req.setPageNum(1L);
        req.setPageSize(10L);

        // 模拟领域服务返回空结果
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<NewProductTargetItem> page = 
                new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(1, 10, 0);
        page.setRecords(Collections.emptyList());
        
        when(newProductTargetDomainService.listNewProductTarget(req)).thenReturn(page);

        // 调用被测试方法
        PageResponse<NewProductTargetItem> result = newProductTargetService.listNewProductTarget(req);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getTotalCount());
        assertEquals(1, result.getPageNum());
        assertEquals(10, result.getPageSize());
        assertNotNull(result.getList());
        assertTrue(result.getList().isEmpty());

        // 验证领域服务被调用了一次
        verify(newProductTargetDomainService, times(1)).listNewProductTarget(req);
    }
    
    @Test
    @DisplayName("测试导出新品目标数据 - 成功场景")
    public void testListNewProductTargetExportSuccess() {
        // 准备测试数据
        NewProductTargetReq req = new NewProductTargetReq();
        req.setPageNum(1L);
        req.setPageSize(10L);
        req.setProductLines(Arrays.asList("PL001", "PL002"));

        // 模拟分页查询结果
        NewProductTargetItem item1 = new NewProductTargetItem();
        item1.setId(1L);
        item1.setProductLine("PL001");
        item1.setProject("项目1");
        item1.setCountry("SG");
        
        NewProductTargetItem item2 = new NewProductTargetItem();
        item2.setId(2L);
        item2.setProductLine("PL002");
        item2.setProject("项目2");
        item2.setCountry("MY");
        
        // 模拟领域服务的返回
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<NewProductTargetItem> page = 
                new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(2, 1, 10);
        page.setRecords(Arrays.asList(item1, item2));
        when(newProductTargetDomainService.listNewProductTarget(any(NewProductTargetReq.class))).thenReturn(page);
        
        // Mock静态方法RpcContext
        try (MockedStatic<RpcContext> mockedRpcContext = Mockito.mockStatic(RpcContext.class)) {
            RpcContext rpcContext = mock(RpcContext.class);
            mockedRpcContext.when(RpcContext::getContext).thenReturn(rpcContext);
            when(rpcContext.getAttachment("$upc_account")).thenReturn("testUser");
            when(rpcContext.getAttachment("$language")).thenReturn("en_US");
            
            // Mock FDS上传服务
            FdsUploadResult fdsUploadResult = new FdsUploadResult();
            fdsUploadResult.setUrl("http://test-fds.com/file.xlsx");
            when(fdsService.upload(anyString(), any(File.class), eq(true))).thenReturn(fdsUploadResult);
            
            // Mock任务触发服务
            CommonResponse<String> jobResponse = new CommonResponse<>("任务已触发");
            when(jobTriggerHelper.triggerCommonExportJob(anyString(), anyString(), anyString(), anyString()))
                    .thenReturn(jobResponse);
            
            // 调用被测试方法
            CommonResponse<String> result = newProductTargetService.listNewProductTargetExport(req);
            
            // 验证结果
            assertNotNull(result);
            assertEquals(0, result.getCode());
            assertEquals("ok", result.getMessage());
            assertEquals("任务已触发", result.getData());
            
            // 验证相关服务被调用
            verify(newProductTargetDomainService, atLeastOnce()).listNewProductTarget(any(NewProductTargetReq.class));
            verify(fdsService, times(1)).upload(anyString(), any(File.class), eq(true));
            verify(jobTriggerHelper, times(1)).triggerCommonExportJob(anyString(), anyString(), anyString(), anyString());
        }
    }
    
//    @Test
    @DisplayName("测试导出新品目标数据 - 无数据场景")
    public void testListNewProductTargetExportNoData() {
        // 准备测试数据
        NewProductTargetReq req = new NewProductTargetReq();
        req.setPageNum(1L);
        req.setPageSize(10L);

        // 模拟空分页查询结果
        PageResponse<NewProductTargetItem> emptyPageResponse = new PageResponse<>(0L, 1L, 10L, Collections.emptyList());
        
        // Mock静态方法RpcContext
        try (MockedStatic<RpcContext> mockedRpcContext = Mockito.mockStatic(RpcContext.class)) {
            RpcContext rpcContext = mock(RpcContext.class);
            mockedRpcContext.when(RpcContext::getContext).thenReturn(rpcContext);
            when(rpcContext.getAttachment("$upc_account")).thenReturn("testUser");
            when(rpcContext.getAttachment("$language")).thenReturn("en_US");
            
            // Mock分页查询方法
            when(newProductTargetService.listNewProductTarget(req)).thenReturn(emptyPageResponse);
            
            // 调用被测试方法
            CommonResponse<String> result = newProductTargetService.listNewProductTargetExport(req);
            
            // 验证结果
            assertNotNull(result);
            assertNotEquals(0, result.getCode()); // 应该返回错误码
            assertNotNull(result.getMessage());
            
            // 验证相关服务未被调用
            verify(newProductTargetService, times(1)).listNewProductTarget(req);
            verify(fdsService, never()).upload(anyString(), any(File.class), anyBoolean());
            verify(jobTriggerHelper, never()).triggerCommonExportJob(anyString(), anyString(), anyString(), anyString());
        }
    }
    
//    @Test
    @DisplayName("测试导出新品目标数据 - 异常场景")
    public void testListNewProductTargetExportException() {
        // 准备测试数据
        NewProductTargetReq req = new NewProductTargetReq();
        req.setPageNum(1L);
        req.setPageSize(10L);

        // Mock静态方法RpcContext
        try (MockedStatic<RpcContext> mockedRpcContext = Mockito.mockStatic(RpcContext.class)) {
            RpcContext rpcContext = mock(RpcContext.class);
            mockedRpcContext.when(RpcContext::getContext).thenReturn(rpcContext);
            when(rpcContext.getAttachment("$upc_account")).thenReturn("testUser");
            when(rpcContext.getAttachment("$language")).thenReturn("en_US");
            
            // Mock分页查询方法抛出异常
            when(newProductTargetService.listNewProductTarget(req)).thenThrow(new RuntimeException("测试异常"));
            
            // 调用被测试方法
            CommonResponse<String> result = newProductTargetService.listNewProductTargetExport(req);
            
            // 验证结果
            assertNotNull(result);
            assertNotEquals(0, result.getCode()); // 应该返回错误码
            assertNotNull(result.getMessage());
            
            // 验证相关服务未被调用
            verify(newProductTargetService, times(1)).listNewProductTarget(req);
            verify(fdsService, never()).upload(anyString(), any(File.class), anyBoolean());
            verify(jobTriggerHelper, never()).triggerCommonExportJob(anyString(), anyString(), anyString(), anyString());
        }
    }
}