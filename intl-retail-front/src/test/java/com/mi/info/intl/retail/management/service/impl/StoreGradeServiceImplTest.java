package com.mi.info.intl.retail.management.service.impl;

import com.mi.info.intl.retail.intlretail.service.api.management.dto.RuleQueryReq;
import com.mi.info.intl.retail.management.mapper.StoreGradeMapper;
import com.mi.info.intl.retail.management.mapper.StoreGradeRuleMapper;
import com.mi.info.intl.retail.management.mapper.StoreGradeRelationMapper;
import com.mi.info.intl.retail.management.entity.StoreGradeRule;
import com.mi.info.intl.retail.management.config.GlobalDataAccessConfig;
import com.mi.info.intl.retail.model.ChannelTypeStatistics;
import com.mi.info.intl.retail.model.RetailerChannelGradeCount;
import com.mi.info.intl.retail.model.StoreGradeCompleteCount;
import com.mi.info.intl.retail.model.StoreGradeCompleteStatistics;
import com.mi.info.intl.retail.intlretail.service.api.retailer.dto.CommonResponse;
import com.mi.info.intl.retail.utils.RpcContextUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.mockStatic;
import org.mockito.MockedStatic;

/**
 * StoreGradeServiceImpl 单元测试
 * 
 * <AUTHOR>
 * @date 2024/12/19
 */
@ExtendWith(MockitoExtension.class)
class StoreGradeServiceImplTest {

    @Mock
    private StoreGradeMapper storeGradeMapper;

    @Mock
    private StoreGradeRuleMapper storeGradeRuleMapper;

    @Mock
    private StoreGradeRelationMapper storeGradeRelationMapper;

    @Mock
    private GlobalDataAccessConfig globalDataAccessConfig;

    @InjectMocks
    private StoreGradeServiceImpl storeGradeService;

    @BeforeEach
    void setUp() {
        // 初始化测试环境
    }

    @Test
    void testGetChannelTypeStatistics_WithValidData() {
        // 准备测试数据
        List<RetailerChannelGradeCount> mockData = Arrays.asList(
            new RetailerChannelGradeCount("ONLINE", "1", 10L),
            new RetailerChannelGradeCount("ONLINE", "0", 5L),
            new RetailerChannelGradeCount("OFFLINE", "1", 8L),
            new RetailerChannelGradeCount("OFFLINE", "0", 2L)
        );

        when(storeGradeMapper.selectRetailerChannelGradeCount(any())).thenReturn(mockData);

        // 执行测试
        CommonResponse<List<ChannelTypeStatistics>> response = storeGradeService.getChannelTypeStatistics();
        List<ChannelTypeStatistics> result = response.getData();

        // 验证结果
        assertNotNull(result);
        assertEquals(3, result.size()); // ALL + ONLINE + OFFLINE

        // 验证总计统计
        ChannelTypeStatistics totalStats = result.get(0);
        assertEquals("ALL", totalStats.getChannelType());
        assertEquals(18, totalStats.getCompleteCount()); // 10 + 8
        assertEquals(7, totalStats.getNotCompleteCount()); // 5 + 2

        verify(storeGradeMapper, times(1)).selectRetailerChannelGradeCount(any());
    }

    @Test
    void testGetChannelTypeStatistics_WithEmptyData() {
        when(storeGradeMapper.selectRetailerChannelGradeCount(any())).thenReturn(Arrays.asList());

        CommonResponse<List<ChannelTypeStatistics>> response = storeGradeService.getChannelTypeStatistics();
        List<ChannelTypeStatistics> result = response.getData();

        assertNotNull(result);
        assertEquals(1, result.size()); // 只有 ALL
        assertEquals(0, result.get(0).getCompleteCount());
        assertEquals(0, result.get(0).getNotCompleteCount());

        verify(storeGradeMapper, times(1)).selectRetailerChannelGradeCount(any());
    }

    @Test
    void testGetStoreGradeCompleteStatistics_WithCurrentMethod() {
        // 准备测试数据
        Long ruleId = 1L;
        RuleQueryReq ruleQueryReq = new RuleQueryReq();
        ruleQueryReq.setRuleId(ruleId);
        StoreGradeRule mockRule = StoreGradeRule.builder()
            .channelType(111)
            .retailerCode("RETAILER001")
            .method(1)
            .build();

        List<StoreGradeCompleteCount> mockData = Arrays.asList(
            new StoreGradeCompleteCount("S", 10L),
            new StoreGradeCompleteCount("A", 20L)
        );

        when(storeGradeRuleMapper.selectById(ruleId)).thenReturn(mockRule);
        when(storeGradeMapper.selectStoreGradeCompleteCount(any(), eq("ONLINE"), eq("RETAILER001"))).thenReturn(mockData);

        // 执行测试
        CommonResponse<List<StoreGradeCompleteStatistics>> response = storeGradeService.getStoreGradeCompleteStatistics(ruleQueryReq);
        List<StoreGradeCompleteStatistics> result = response.getData();

        // 验证结果
        assertNotNull(result);
        assertEquals(3, result.size()); // ALL + S + A
        assertEquals(30, result.get(0).getCount()); // 总计

        verify(storeGradeRuleMapper, times(1)).selectById(ruleId);
        verify(storeGradeMapper, times(1)).selectStoreGradeCompleteCount(any(), eq("ONLINE"), eq("RETAILER001"));
    }

    @Test
    void testGetStoreGradeCompleteStatistics_WithRelationMethod() {
        // 准备测试数据
        Long ruleId = 1L;
        RuleQueryReq ruleQueryReq = new RuleQueryReq();
        ruleQueryReq.setRuleId(ruleId);
        StoreGradeRule mockRule = StoreGradeRule.builder()
            .channelType(111)
            .retailerCode("RETAILER001")
            .method(2) // 修改为2，对应MANUAL_UPLOAD，使用关系表统计
            .build();

        List<StoreGradeCompleteCount> mockData = Arrays.asList(
            new StoreGradeCompleteCount("S", 15L),
            new StoreGradeCompleteCount("A", 25L)
        );

        when(storeGradeRuleMapper.selectById(ruleId)).thenReturn(mockRule);
        when(storeGradeRelationMapper.selectStoreGradeCountByRuleId(any(), any())).thenReturn(mockData);

        // 执行测试
        CommonResponse<List<StoreGradeCompleteStatistics>> response = storeGradeService.getStoreGradeCompleteStatistics(ruleQueryReq);
        List<StoreGradeCompleteStatistics> result = response.getData();

        // 验证结果
        assertNotNull(result);
        assertEquals(3, result.size()); // ALL + S + A
        assertEquals(40, result.get(0).getCount()); // 总计

        verify(storeGradeRuleMapper, times(1)).selectById(ruleId);
        verify(storeGradeRelationMapper, times(1)).selectStoreGradeCountByRuleId(any(), any());
    }

    @Test
    void testGetStoreGradeCompleteStatistics_WithInvalidRuleId() {
        Long ruleId = 999L;
        RuleQueryReq ruleQueryReq = new RuleQueryReq();
        ruleQueryReq.setRuleId(ruleId);
        when(storeGradeRuleMapper.selectById(ruleId)).thenReturn(null);

        CommonResponse<List<StoreGradeCompleteStatistics>> response = storeGradeService.getStoreGradeCompleteStatistics(ruleQueryReq);
        List<StoreGradeCompleteStatistics> result = response.getData();

        assertNotNull(result);
        assertTrue(result.isEmpty());

        verify(storeGradeRuleMapper, times(1)).selectById(ruleId);
        verify(storeGradeMapper, never()).selectStoreGradeCompleteCount(any(), any(), any());
        verify(storeGradeRelationMapper, never()).selectStoreGradeCountByRuleId(any(), any());
    }

    @Test
    void testGetStoreGradeCompleteStatistics_WithUnsupportedMethod() {
        Long ruleId = 1L;
        RuleQueryReq ruleQueryReq = new RuleQueryReq();
        ruleQueryReq.setRuleId(ruleId);
        StoreGradeRule mockRule = StoreGradeRule.builder()
            .channelType(111)
            .retailerCode("RETAILER001")
            .method(99) // 修改为不支持的method值，测试未实现的计算方法逻辑
            .build();

        when(storeGradeRuleMapper.selectById(ruleId)).thenReturn(mockRule);

        CommonResponse<List<StoreGradeCompleteStatistics>> response = storeGradeService.getStoreGradeCompleteStatistics(ruleQueryReq);
        List<StoreGradeCompleteStatistics> result = response.getData();

        assertNotNull(result);
        assertTrue(result.isEmpty()); // 期望返回空列表，因为是不支持的计算方法

        verify(storeGradeRuleMapper, times(1)).selectById(ruleId);
        verify(storeGradeMapper, never()).selectStoreGradeCompleteCount(any(), any(), any());
        verify(storeGradeRelationMapper, never()).selectStoreGradeCountByRuleId(any(), any());
    }

    @Test
    @DisplayName("测试getChannelTypeStatistics - GLOBAL环境且支持全球数据访问（如生产环境）")
    void testGetChannelTypeStatistics_GlobalWithDataAccessEnabled() {
        // 准备数据
        String countryCode = "GLOBAL";
        List<RetailerChannelGradeCount> mockData = Arrays.asList(
            new RetailerChannelGradeCount("1", "1", 10L),
            new RetailerChannelGradeCount("1", "0", 5L),
            new RetailerChannelGradeCount("2", "1", 8L)
        );

        try (MockedStatic<RpcContextUtil> mockedStatic = mockStatic(RpcContextUtil.class)) {
            mockedStatic.when(RpcContextUtil::getCurrentAreaId).thenReturn(countryCode);
            when(storeGradeMapper.selectRetailerChannelGradeCount(countryCode)).thenReturn(mockData);
            when(globalDataAccessConfig.isEnabled()).thenReturn(true); // 生产环境配置为true（启用）

            // 执行测试
            CommonResponse<List<ChannelTypeStatistics>> response = storeGradeService.getChannelTypeStatistics();

            // 验证结果
            assertNotNull(response);
            assertNotNull(response.getData());
            assertTrue(response.getData().size() > 0);
            
            // 验证调用了数据库查询
            verify(storeGradeMapper, times(1)).selectRetailerChannelGradeCount(countryCode);
            // 验证检查了全球数据访问配置
            verify(globalDataAccessConfig, times(1)).isEnabled();
        }
    }

    @Test
    @DisplayName("测试getChannelTypeStatistics - GLOBAL环境但不支持全球数据访问（如欧洲环境或默认情况）")
    void testGetChannelTypeStatistics_GlobalWithDataAccessDisabled() {
        // 准备数据
        String countryCode = "GLOBAL";
        List<RetailerChannelGradeCount> mockData = Arrays.asList(
            new RetailerChannelGradeCount("1", "1", 10L),
            new RetailerChannelGradeCount("1", "0", 5L)
        );

        try (MockedStatic<RpcContextUtil> mockedStatic = mockStatic(RpcContextUtil.class)) {
            mockedStatic.when(RpcContextUtil::getCurrentAreaId).thenReturn(countryCode);
            when(storeGradeMapper.selectRetailerChannelGradeCount(countryCode)).thenReturn(mockData);
            when(globalDataAccessConfig.isEnabled()).thenReturn(false); // 欧洲环境为false（不启用）

            // 执行测试
            CommonResponse<List<ChannelTypeStatistics>> response = storeGradeService.getChannelTypeStatistics();

            // 验证结果
            assertNotNull(response);
            assertNotNull(response.getData());
            assertTrue(response.getData().size() > 0);
            
            // 验证调用了数据库查询
            verify(storeGradeMapper, times(1)).selectRetailerChannelGradeCount(countryCode);
            // 验证检查了全球数据访问配置
            verify(globalDataAccessConfig, times(1)).isEnabled();
        }
    }

    @Test
    @DisplayName("测试getChannelTypeStatistics - 非GLOBAL环境")
    void testGetChannelTypeStatistics_NonGlobal() {
        // 准备数据
        String countryCode = "SG";
        List<RetailerChannelGradeCount> mockData = Arrays.asList(
            new RetailerChannelGradeCount("1", "1", 10L),
            new RetailerChannelGradeCount("1", "0", 5L)
        );

        try (MockedStatic<RpcContextUtil> mockedStatic = mockStatic(RpcContextUtil.class)) {
            mockedStatic.when(RpcContextUtil::getCurrentAreaId).thenReturn(countryCode);
            when(storeGradeMapper.selectRetailerChannelGradeCount(countryCode)).thenReturn(mockData);

            // 执行测试
            CommonResponse<List<ChannelTypeStatistics>> response = storeGradeService.getChannelTypeStatistics();

            // 验证结果
            assertNotNull(response);
            assertNotNull(response.getData());
            assertTrue(response.getData().size() > 0);
            
            // 验证调用了数据库查询
            verify(storeGradeMapper, times(1)).selectRetailerChannelGradeCount(countryCode);
            // 验证没有检查全球数据访问配置（因为不是GLOBAL环境）
            verify(globalDataAccessConfig, never()).isEnabled();
        }
    }

    @Test
    @DisplayName("测试getChannelTypeStatistics - 默认配置值（false，不启用）")
    void testGetChannelTypeStatistics_DefaultConfig() {
        // 准备数据
        String countryCode = "GLOBAL";
        List<RetailerChannelGradeCount> mockData = Arrays.asList(
            new RetailerChannelGradeCount("1", "1", 10L),
            new RetailerChannelGradeCount("1", "0", 5L)
        );

        try (MockedStatic<RpcContextUtil> mockedStatic = mockStatic(RpcContextUtil.class)) {
            mockedStatic.when(RpcContextUtil::getCurrentAreaId).thenReturn(countryCode);
            when(storeGradeMapper.selectRetailerChannelGradeCount(countryCode)).thenReturn(mockData);
            // 不设置globalDataAccessConfig.isEnabled()的mock，使用默认值false

            // 执行测试
            CommonResponse<List<ChannelTypeStatistics>> response = storeGradeService.getChannelTypeStatistics();

            // 验证结果
            assertNotNull(response);
            assertNotNull(response.getData());
            assertTrue(response.getData().size() > 0);
            
            // 验证调用了数据库查询
            verify(storeGradeMapper, times(1)).selectRetailerChannelGradeCount(countryCode);
            // 验证检查了全球数据访问配置（默认值为false）
            verify(globalDataAccessConfig, times(1)).isEnabled();
        }
    }
} 