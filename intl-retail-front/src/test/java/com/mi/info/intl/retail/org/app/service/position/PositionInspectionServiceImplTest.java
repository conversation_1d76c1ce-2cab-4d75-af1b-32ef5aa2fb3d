package com.mi.info.intl.retail.org.app.service.position;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mi.info.intl.retail.cooperation.task.dto.dto.IntlRmsUserDto;
import com.mi.info.intl.retail.cooperation.task.inspection.IntlRmsUserService;
import com.mi.info.intl.retail.intlretail.service.api.fds.FdsService;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.*;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.AbnormalReasonEnum;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.I18nDesc;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.taskcenter.TaskCenterFinishTaskReq;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.PositionInspectionResponsiblePersonDTO;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.model.PageResponse;
import com.mi.info.intl.retail.org.domain.InspectionRecordDomain;
import com.mi.info.intl.retail.org.domain.position.service.PositionInspectionDomainService;


import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * PositionInspectionServiceImpl单元测试类
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("阵地巡检服务实现类测试")
class PositionInspectionServiceImplTest {

    @InjectMocks
    private PositionInspectionServiceImpl positionInspectionService;

    @Mock
    private PositionInspectionDomainService positionInspectionDomainService;

    @Mock
    private IntlRmsUserService intlRmsUserService;

    @Mock
    private FdsService fdsService;

    private PositionInspectionRequest mockRequest;
    private PositionInspectionItem mockItem;
    private Page<PositionInspectionItem> mockPage;
    private PositionSelectorItemList mockSelectorList;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        mockRequest = new PositionInspectionRequest();
        mockRequest.setPageNum(1L);
        mockRequest.setPageSize(10L);
        mockRequest.setLatitude(1.0);
        mockRequest.setLongitude(1.0);

        mockItem = new PositionInspectionItem();
        mockItem.setId(1L);
        mockItem.setOwner("<EMAIL>");
        mockItem.setPositionCategory("[\"category1\",\"category2\"]");
        mockItem.setPositonLocation("location1");
        mockItem.setDisplayStandardization("standard1");
        mockItem.setPositionLatitude(2.0);
        mockItem.setPositionLongitude(2.0);

        List<PositionInspectionItem> records = Arrays.asList(mockItem);
        mockPage = new Page<>();
        mockPage.setRecords(records);
        mockPage.setTotal(1L);

        mockSelectorList = new PositionSelectorItemList();
        List<OptionalItem<String>> positionLocationList = Arrays.asList(
                new OptionalItem<>("location1", "Location 1"),
                new OptionalItem<>("location2", "Location 2")
        );
        List<OptionalItem<String>> displayStandardizationList = Arrays.asList(
                new OptionalItem<>("standard1", "Standard 1"),
                new OptionalItem<>("standard2", "Standard 2")
        );
        List<OptionalItem<String>> positionCategoryList = Arrays.asList(
                new OptionalItem<>("category1", "Category 1"),
                new OptionalItem<>("category2", "Category 2")
        );
        mockSelectorList.setPositionLocation(positionLocationList);
        mockSelectorList.setDisplayStandardization(displayStandardizationList);
        mockSelectorList.setPositionCategory(positionCategoryList);
    }

    @Test
    @DisplayName("分页查询阵地巡检信息 - 成功场景")
    void listPositionInspection_Success() {
        // 准备数据
        when(positionInspectionDomainService.pagePositionInspection(any(PositionInspectionRequest.class)))
                .thenReturn(mockPage);
        when(positionInspectionDomainService.getSelectorList(any(PositionItemListRequest.class)))
                .thenReturn(mockSelectorList);

        IntlRmsUserDto mockUserDto = IntlRmsUserDto.builder()
                .domainName("<EMAIL>")
                .englishName("Test User")
                .build();
        when(intlRmsUserService.getIntlRmsUserByDomainNames(anyList()))
                .thenReturn(Arrays.asList(mockUserDto));

        // 执行测试
        PageResponse<PositionInspectionItem> result = positionInspectionService.listPositionInspection(mockRequest);

        // 验证结果
        assertNotNull(result);
        assertEquals(1L, result.getTotalCount());
        assertEquals(1, result.getList().size());
        assertEquals("Test User (<EMAIL>)", result.getList().get(0).getOwner());
        assertEquals("Category 1,Category 2", result.getList().get(0).getPositionCategory());
        assertEquals("Location 1", result.getList().get(0).getPositonLocation());
        assertEquals("Standard 1", result.getList().get(0).getDisplayStandardization());
        assertNotNull(result.getList().get(0).getDistance());

        // 验证方法调用
        verify(positionInspectionDomainService).pagePositionInspection(any(PositionInspectionRequest.class));
        verify(positionInspectionDomainService).getSelectorList(any(PositionItemListRequest.class));
        verify(intlRmsUserService).getIntlRmsUserByDomainNames(anyList());
    }

    @Test
    @DisplayName("分页查询阵地巡检信息 - 空数据场景")
    void listPositionInspection_EmptyData() {
        // 准备数据
        Page<PositionInspectionItem> emptyPage = new Page<>();
        emptyPage.setRecords(Collections.emptyList());
        emptyPage.setTotal(0L);

        when(positionInspectionDomainService.pagePositionInspection(any(PositionInspectionRequest.class)))
                .thenReturn(emptyPage);
        when(positionInspectionDomainService.getSelectorList(any(PositionItemListRequest.class)))
                .thenReturn(mockSelectorList);

        // 执行测试
        PageResponse<PositionInspectionItem> result = positionInspectionService.listPositionInspection(mockRequest);

        // 验证结果
        assertNotNull(result);
        assertEquals(0L, result.getTotalCount());
        assertTrue(result.getList().isEmpty());
    }

    @Test
    @DisplayName("审批阵地巡检 - 成功场景")
    void approvePositionInspection_Success() {
        // 准备数据
        PositionInspectionApproveRequest request = new PositionInspectionApproveRequest();
        request.setPositionInspectionId(1L);
        request.setVerifyAction(1);

        when(positionInspectionDomainService.approvePositionInspection(any(PositionInspectionApproveRequest.class)))
                .thenReturn("审批成功");

        // 执行测试
        CommonApiResponse<String> result = positionInspectionService.approvePositionInspection(request);

        // 验证结果
        assertNotNull(result);
        assertEquals("审批成功", result.getData());
        assertEquals(0, result.getCode());

        // 验证方法调用
        verify(positionInspectionDomainService).approvePositionInspection(any(PositionInspectionApproveRequest.class));
    }

    @Test
    @DisplayName("提交阵地巡检 - 成功场景")
    void submitPositionInspection_Success() {
        // 准备数据
        PositionInspectionSubmitRequest request = new PositionInspectionSubmitRequest();
        request.setPositionInspectionId(1L);

        CommonApiResponse<String> expectedResponse = new CommonApiResponse<>("提交成功");
        when(positionInspectionDomainService.submitPositionInspection(any(PositionInspectionSubmitRequest.class)))
                .thenReturn(expectedResponse);

        // 执行测试
        CommonApiResponse<String> result = positionInspectionService.submitPositionInspection(request);

        // 验证结果
        assertNotNull(result);
        assertEquals(expectedResponse, result);

        // 验证方法调用
        verify(positionInspectionDomainService).submitPositionInspection(any(PositionInspectionSubmitRequest.class));
    }

    @Test
    @DisplayName("获取阵地巡检详情 - 成功场景")
    void getPositionInspectionDetail_Success() {
        // 准备数据
        PositionInspectionDetailRequest request = new PositionInspectionDetailRequest();
        request.setPositionInspectionId(1L);

        PositionInspectionDetailResponse mockResponse = new PositionInspectionDetailResponse();
        CommonApiResponse<PositionInspectionDetailResponse> expectedResponse = new CommonApiResponse<>(mockResponse);
        when(positionInspectionDomainService.getPositionInspectionDetail(any(PositionInspectionDetailRequest.class)))
                .thenReturn(expectedResponse);

        // 执行测试
        CommonApiResponse<PositionInspectionDetailResponse> result = positionInspectionService.getPositionInspectionDetail(request);

        // 验证结果
        assertNotNull(result);
        assertEquals(expectedResponse, result);

        // 验证方法调用
        verify(positionInspectionDomainService).getPositionInspectionDetail(any(PositionInspectionDetailRequest.class));
    }

    @Test
    @DisplayName("获取筛选项列表 - 成功场景")
    void getSelectorList_Success() {
        // 准备数据
        PositionItemListRequest request = new PositionItemListRequest();
        request.setBusinessScene("channelRetail");

        when(positionInspectionDomainService.getSelectorList(any(PositionItemListRequest.class)))
                .thenReturn(mockSelectorList);

        // 执行测试
        CommonApiResponse<PositionSelectorItemList> result = positionInspectionService.getSelectorList(request);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getCode());
        assertEquals(mockSelectorList, result.getData());

        // 验证方法调用
        verify(positionInspectionDomainService).getSelectorList(any(PositionItemListRequest.class));
    }

    @Test
    @DisplayName("获取筛选项列表 - 异常场景")
    void getSelectorList_ThrowsException() {
        // 准备数据
        PositionItemListRequest request = new PositionItemListRequest();
        request.setBusinessScene("channelRetail");

        when(positionInspectionDomainService.getSelectorList(any(PositionItemListRequest.class)))
                .thenThrow(new RuntimeException("获取筛选项失败"));

        // 执行测试
        CommonApiResponse<PositionSelectorItemList> result = positionInspectionService.getSelectorList(request);

        // 验证结果
        assertNotNull(result);
        assertEquals(500, result.getCode());
        assertEquals("获取筛选项失败", result.getMessage());

        // 验证方法调用
        verify(positionInspectionDomainService).getSelectorList(any(PositionItemListRequest.class));
    }

    @Test
    @DisplayName("获取巡检记录汇总数据 - 成功场景")
    void summary_Success() {
        // 准备数据
        InspectionSummaryDTO mockSummary = new InspectionSummaryDTO();
        when(positionInspectionDomainService.getInspectionSummary(any(PositionInspectionRequest.class)))
                .thenReturn(mockSummary);

        // 执行测试
        InspectionSummaryDTO result = positionInspectionService.summary(mockRequest);

        // 验证结果
        assertNotNull(result);
        assertEquals(mockSummary, result);

        // 验证方法调用
        verify(positionInspectionDomainService).getInspectionSummary(any(PositionInspectionRequest.class));
    }

    @Test
    @DisplayName("获取操作历史 - 成功场景")
    void operationHistory_Success() {
        // 准备数据
        PositionInspectionHistoryRequest request = new PositionInspectionHistoryRequest();
        request.setPositionInspectionId(1L);

        List<PositionInspectionHistoryItem> mockHistoryItems = Arrays.asList(
                new PositionInspectionHistoryItem(),
                new PositionInspectionHistoryItem()
        );

        when(positionInspectionDomainService.operationHistory(anyLong()))
                .thenReturn(mockHistoryItems);

        // 执行测试
        CommonApiResponse<List<PositionInspectionHistoryItem>> result = positionInspectionService.operationHistory(request);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getCode());
        assertEquals(mockHistoryItems, result.getData());
        assertEquals(2, result.getData().size());

        // 验证方法调用
        verify(positionInspectionDomainService).operationHistory(anyLong());
    }

    @Test
    @DisplayName("标记任务为无需完成 - 成功场景")
    void noNeedCompleteTask_Success() {
        // 准备数据
        TaskCenterFinishTaskReq request = new TaskCenterFinishTaskReq();

        when(positionInspectionDomainService.noNeedCompleteTask(any(TaskCenterFinishTaskReq.class)))
                .thenReturn("标记成功");

        // 执行测试
        CommonApiResponse<String> result = positionInspectionService.noNeedCompleteTask(request);

        // 验证结果
        assertNotNull(result);
        assertEquals("标记成功", result.getData());
        assertEquals(0, result.getCode());

        // 验证方法调用
        verify(positionInspectionDomainService).noNeedCompleteTask(any(TaskCenterFinishTaskReq.class));
    }

    @Test
    @DisplayName("完成用户当前任务动作 - 成功场景")
    void outerTaskFinish_Success() {
        // 准备数据
        TaskCenterFinishTaskReq request = new TaskCenterFinishTaskReq();

        when(positionInspectionDomainService.outerTaskFinish(any(TaskCenterFinishTaskReq.class)))
                .thenReturn("任务完成");

        // 执行测试
        CommonApiResponse<String> result = positionInspectionService.outerTaskFinish(request);

        // 验证结果
        assertNotNull(result);
        assertEquals("任务完成", result.getData());
        assertEquals(0, result.getCode());

        // 验证方法调用
        verify(positionInspectionDomainService).outerTaskFinish(any(TaskCenterFinishTaskReq.class));
    }

    @Test
    @DisplayName("根据阵地编码创建巡检记录 - 成功场景")
    void createInspectionByPositionCode_Success() {
        // 准备数据
        String areaId = "AREA001";
        String positionCode = "POS001";
        String operatorId = "OP001";

        when(positionInspectionDomainService.createInspectionByPositionCode(anyString(), anyString(), anyString()))
                .thenReturn("创建成功");

        // 执行测试
        CommonApiResponse<String> result = positionInspectionService.createInspectionByPositionCode(areaId, positionCode, operatorId);

        // 验证结果
        assertNotNull(result);
        assertEquals("创建成功", result.getData());
        assertEquals(0, result.getCode());

        // 验证方法调用
        verify(positionInspectionDomainService).createInspectionByPositionCode(anyString(), anyString(), anyString());
    }

    @Test
    @DisplayName("根据阵地编码创建巡检记录 - 异常场景")
    void createInspectionByPositionCode_ThrowsException() {
        // 准备数据
        String areaId = "AREA001";
        String positionCode = "POS001";
        String operatorId = "OP001";

        when(positionInspectionDomainService.createInspectionByPositionCode(anyString(), anyString(), anyString()))
                .thenThrow(new RuntimeException("创建失败"));

        // 执行测试
        CommonApiResponse<String> result = positionInspectionService.createInspectionByPositionCode(areaId, positionCode, operatorId);

        // 验证结果
        assertNotNull(result);
        assertEquals(500, result.getCode());
        assertTrue(result.getMessage().contains("创建阵地巡检记录失败"));

        // 验证方法调用
        verify(positionInspectionDomainService).createInspectionByPositionCode(anyString(), anyString(), anyString());
    }

    @Test
    @DisplayName("阵地巡检任务下发 - 成功场景")
    void dispatchInspectionTasks_Success() {
        // 准备数据
        Integer regularTime = 0;

        when(positionInspectionDomainService.dispatchInspectionTasks(anyInt()))
                .thenReturn(new CommonApiResponse<>("下发成功"));

        // 执行测试
        CommonApiResponse<String> result = positionInspectionService.dispatchInspectionTasks(regularTime);

        // 验证结果
        assertNotNull(result);
        assertEquals("下发成功", result.getData());
        assertEquals(0, result.getCode());

        // 验证方法调用
        verify(positionInspectionDomainService).dispatchInspectionTasks(anyInt());
    }

    // @Test
    // @DisplayName("获取异常原因列表 - 成功场景")
    // void getAbnormalReason_Success() {
    //     // 执行测试
    //     CommonApiResponse<List<OptionalItem<Integer>>> result = positionInspectionService.getAbnormalReason();

    //     // 验证结果
    //     assertNotNull(result);
    //     assertEquals(0, result.getCode());
    //     assertNotNull(result.getData());
    //     assertFalse(result.getData().isEmpty());

    //     // 验证数据来源 - 检查返回的数据是否包含所有AbnormalReasonEnum的值
    //     List<OptionalItem<Integer>> expectedItems = I18nDesc.toOptionalItems(AbnormalReasonEnum.class);
    //     assertEquals(expectedItems.size(), result.getData().size());
        
    //     // 验证返回的数据包含所有异常原因的代码
    //     List<Integer> expectedCodes = Arrays.asList(1, 2, 3, 4, 5);
    //     List<Integer> actualCodes = result.getData().stream()
    //             .map(OptionalItem::getKey)
    //             .collect(java.util.stream.Collectors.toList());
        
    //     assertTrue(actualCodes.containsAll(expectedCodes));
    //     assertTrue(expectedCodes.containsAll(actualCodes));
    // }

    @Test
    @DisplayName("根据业务代码获取照片链接 - 成功场景")
    void getPhotosByBusinessCode_Success() {
        // 准备数据
        String businessCode = "BUS001";

        List<InspectionRecordDomain> mockRecords = Arrays.asList(new InspectionRecordDomain());
        List<ImageCenterDto> mockImageDtos = Arrays.asList(new ImageCenterDto());

        when(positionInspectionDomainService.getInspectionRecordsByBusinessCode(anyString()))
                .thenReturn(mockRecords);
        when(positionInspectionDomainService.getImageCenterData(anyList()))
                .thenReturn(mockImageDtos);

        // 执行测试
        CommonApiResponse<List<ImageCenterDto>> result = positionInspectionService.getPhotosByBusinessCode(businessCode);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getCode());
        assertEquals(mockImageDtos, result.getData());

        // 验证方法调用
        verify(positionInspectionDomainService).getInspectionRecordsByBusinessCode(anyString());
        verify(positionInspectionDomainService).getImageCenterData(anyList());
    }

    @Test
    @DisplayName("根据业务代码获取照片链接 - 业务代码为空")
    void getPhotosByBusinessCode_EmptyBusinessCode() {
        // 执行测试
        CommonApiResponse<List<ImageCenterDto>> result = positionInspectionService.getPhotosByBusinessCode(null);

        // 验证结果
        assertNotNull(result);
        assertEquals(500, result.getCode());
        assertEquals("业务代码不能为空", result.getMessage());
        assertNull(result.getData());
    }

    @Test
    @DisplayName("根据业务代码获取照片链接 - 无巡检记录")
    void getPhotosByBusinessCode_NoRecords() {
        // 准备数据
        String businessCode = "BUS001";

        when(positionInspectionDomainService.getInspectionRecordsByBusinessCode(anyString()))
                .thenReturn(Collections.emptyList());

        // 执行测试
        CommonApiResponse<List<ImageCenterDto>> result = positionInspectionService.getPhotosByBusinessCode(businessCode);

        // 验证结果
        assertNotNull(result);
        assertEquals(500, result.getCode());
        assertEquals("无对应巡检记录", result.getMessage());
        assertNull(result.getData());

        // 验证方法调用
        verify(positionInspectionDomainService).getInspectionRecordsByBusinessCode(anyString());
        verify(positionInspectionDomainService, never()).getImageCenterData(anyList());
    }

    @Test
    @DisplayName("查询阵地家具列表 - 成功场景")
    void getPositionFurnitureList_Success() {
        // 准备数据
        PositionFurnitureRequest request = new PositionFurnitureRequest();
        request.setPositionCode("POS001");

        List<OptionalItem<Integer>> mockFurnitureList = Arrays.asList(
                new OptionalItem<>(1, "家具1"),
                new OptionalItem<>(2, "家具2")
        );

        when(positionInspectionDomainService.getPositionFurnitureList(any(PositionFurnitureRequest.class)))
                .thenReturn(mockFurnitureList);

        // 执行测试
        CommonApiResponse<List<OptionalItem<Integer>>> result = positionInspectionService.getPositionFurnitureList(request);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getCode());
        assertEquals(mockFurnitureList, result.getData());
        assertEquals(2, result.getData().size());

        // 验证方法调用
        verify(positionInspectionDomainService).getPositionFurnitureList(any(PositionFurnitureRequest.class));
    }

    @Test
    @DisplayName("定时任务提醒 - 成功场景")
    void taskReminder_Success() {
        // 准备数据
        CommonApiResponse<String> expectedResponse = new CommonApiResponse<>("提醒成功");
        when(positionInspectionDomainService.taskReminder())
                .thenReturn(expectedResponse);

        // 执行测试
        CommonApiResponse<String> result = positionInspectionService.taskReminder();

        // 验证结果
        assertNotNull(result);
        assertEquals(expectedResponse, result);

        // 验证方法调用
        verify(positionInspectionDomainService).taskReminder();
    }

    @Test
    @DisplayName("获取巡检记录汇总数据网关 - 成功场景")
    void summaryGateWay_Success() {
        // 准备数据
        InspectionSummaryDTO mockSummary = new InspectionSummaryDTO();

        when(positionInspectionDomainService.getInspectionSummary(any(PositionInspectionRequest.class)))
                .thenReturn(mockSummary);

        // 执行测试
        CommonApiResponse<InspectionSummaryDTO> result = positionInspectionService.summaryGateWay(mockRequest);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getCode());
        assertEquals(mockSummary, result.getData());

        // 验证方法调用
        verify(positionInspectionDomainService).getInspectionSummary(any(PositionInspectionRequest.class));
    }

    @Test
    @DisplayName("分页查询阵地巡检信息网关 - 成功场景")
    void listPositionInspectionGateWay_Success() {
        // 准备数据
        when(positionInspectionDomainService.pagePositionInspection(any(PositionInspectionRequest.class)))
                .thenReturn(mockPage);
        when(positionInspectionDomainService.getSelectorList(any(PositionItemListRequest.class)))
                .thenReturn(mockSelectorList);

        IntlRmsUserDto mockUserDto = IntlRmsUserDto.builder()
                .domainName("<EMAIL>")
                .englishName("Test User")
                .build();
        when(intlRmsUserService.getIntlRmsUserByDomainNames(anyList()))
                .thenReturn(Arrays.asList(mockUserDto));

        // 执行测试
        CommonApiResponse<PageResponse<PositionInspectionItemDTO>> result = positionInspectionService.listPositionInspectionGateWay(mockRequest);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getCode());
        assertNotNull(result.getData());

        // 验证方法调用
        verify(positionInspectionDomainService).pagePositionInspection(any(PositionInspectionRequest.class));
        verify(positionInspectionDomainService).getSelectorList(any(PositionItemListRequest.class));
        verify(intlRmsUserService).getIntlRmsUserByDomainNames(anyList());
    }

    @Test
    @DisplayName("转换阵地类别 - 成功场景")
    void convertPositionCategory_Success() {
        // 准备数据
        String positionCategory = "[\"category1\",\"category2\"]";
        Map<String, String> positionCategoryMap = new HashMap<>();
        positionCategoryMap.put("category1", "Category 1");
        positionCategoryMap.put("category2", "Category 2");

        // 使用反射调用私有方法进行测试
        try {
            java.lang.reflect.Method method = PositionInspectionServiceImpl.class.getDeclaredMethod(
                    "convertPositionCategory", String.class, Map.class);
            method.setAccessible(true);
            String result = (String) method.invoke(positionInspectionService, positionCategory, positionCategoryMap);

            // 验证结果
            assertEquals("Category 1,Category 2", result);
        } catch (Exception e) {
            fail("反射调用失败: " + e.getMessage());
        }
    }

    @Test
    @DisplayName("转换阵地类别 - 空值场景")
    void convertPositionCategory_EmptyValue() {
        // 准备数据
        String positionCategory = null;
        Map<String, String> positionCategoryMap = new HashMap<>();
        positionCategoryMap.put("category1", "Category 1");

        // 使用反射调用私有方法进行测试
        try {
            java.lang.reflect.Method method = PositionInspectionServiceImpl.class.getDeclaredMethod(
                    "convertPositionCategory", String.class, Map.class);
            method.setAccessible(true);
            String result = (String) method.invoke(positionInspectionService, positionCategory, positionCategoryMap);

            // 验证结果
            assertNull(result);
        } catch (Exception e) {
            fail("反射调用失败: " + e.getMessage());
        }
    }
} 