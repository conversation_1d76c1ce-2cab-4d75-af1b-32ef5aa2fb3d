package com.mi.info.intl.retail.ldu.app.service;

import com.google.common.collect.Lists;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.SnImeiInfoDto;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.SnImeiQueryDto;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.SnImeiValidationDto;
import com.mi.info.intl.retail.intlretail.service.api.retailer.dto.CommonResponse;
import com.mi.info.intl.retail.ldu.app.service.impl.SnImeiServiceImpl;
import com.mi.info.intl.retail.ldu.dto.UpcGoodsInfoDto;
import com.mi.info.intl.retail.ldu.infra.repository.IProductQueryService;
import com.mi.info.intl.retail.ldu.infra.repository.ISnImeiQueryService;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date 2025/7/9 11:25
 */
@ExtendWith(MockitoExtension.class)
public class SnImeiServiceImplTest {

    @InjectMocks
    private SnImeiServiceImpl snImeiServiceImpl;

    @Mock
    private ISnImeiQueryService snImeiQueryService;

    @Mock
    private IProductQueryService productQueryService;

    @DisplayName("测试查询SN/IMEI信息，参数为空，返回空列表")
    @Test
    public void testQuerySnImeiInfo_params_empty() {
        SnImeiQueryDto snImeiQueryDto = new SnImeiQueryDto();
        CommonResponse<List<SnImeiInfoDto>> listCommonResponse = snImeiServiceImpl.querySnImeiInfo(snImeiQueryDto);
        Assertions.assertTrue(CollectionUtils.isEmpty(listCommonResponse.getData()));
    }

    @DisplayName("测试查询SN/IMEI信息，SN列表参数不为空，返回预期结果")
    @Test
    public void testQuerySnImeiInfo_params_sns() {
        SnImeiQueryDto snImeiQueryDto = new SnImeiQueryDto();
        snImeiQueryDto.setSnList(Lists.newArrayList("SN1001"));
        // 生成SnImeiInfoDto预期结果
        SnImeiInfoDto snImeiInfoDto = new SnImeiInfoDto();
        snImeiInfoDto.setSn("SN1001");
        snImeiInfoDto.setImei("IMEI1001-1");
        snImeiInfoDto.setImei2("IMEI1001-2");
        snImeiInfoDto.setSku("SKU1001");
        snImeiInfoDto.setGoodsId("1001");
        List<SnImeiInfoDto> expectSnImeiInfos = Lists.newArrayList(snImeiInfoDto);
        when(snImeiQueryService.querySnImeiInfoBySns(snImeiQueryDto.getSnList())).thenReturn(expectSnImeiInfos);
        CommonResponse<List<SnImeiInfoDto>> listCommonResponse = snImeiServiceImpl.querySnImeiInfo(snImeiQueryDto);
        Assertions.assertEquals(expectSnImeiInfos, listCommonResponse.getData());
    }

    @DisplayName("测试查询SN/IMEI信息，IMEI列表参数不为空，返回预期结果")
    @Test
    public void testQuerySnImeiInfo_params_imeis() {
        SnImeiQueryDto snImeiQueryDto = new SnImeiQueryDto();
        snImeiQueryDto.setImeiList(Lists.newArrayList("IMEI1001"));
        // 生成SnImeiInfoDto预期结果
        SnImeiInfoDto snImeiInfoDto = new SnImeiInfoDto();
        snImeiInfoDto.setSn("SN1002");
        snImeiInfoDto.setImei("IMEI1001");
        snImeiInfoDto.setImei2("IMEI1001-2");
        snImeiInfoDto.setSku("SKU1002");
        snImeiInfoDto.setGoodsId("1002");
        List<SnImeiInfoDto> expectSnImeiInfos = Lists.newArrayList(snImeiInfoDto);
        when(snImeiQueryService.querySnImeiInfoByImeis(snImeiQueryDto.getImeiList())).thenReturn(expectSnImeiInfos);
        CommonResponse<List<SnImeiInfoDto>> listCommonResponse = snImeiServiceImpl.querySnImeiInfo(snImeiQueryDto);
        Assertions.assertEquals(expectSnImeiInfos, listCommonResponse.getData());
    }

    @DisplayName("测试查询SN/IMEI信息，SN和IMEI列表参数不为空，返回预期结果")
    @Test
    public void testQuerySnImeiInfo_params_sns_imeis() {
        SnImeiQueryDto snImeiQueryDto = new SnImeiQueryDto();
        snImeiQueryDto.setSnList(Lists.newArrayList("SN1001"));
        snImeiQueryDto.setImeiList(Lists.newArrayList("IMEI1001"));
        // 生成SnImeiInfoDto预期结果
        // SN查询
        SnImeiInfoDto snImeiInfoDto1 = new SnImeiInfoDto();
        snImeiInfoDto1.setSn("SN1001");
        snImeiInfoDto1.setImei("IMEI1001");
        snImeiInfoDto1.setImei2("IMEI1001-2");
        snImeiInfoDto1.setSku("SKU1001");
        snImeiInfoDto1.setGoodsId("1001");

        // IMEI查询
        SnImeiInfoDto snImeiInfoDto2 = new SnImeiInfoDto();
        snImeiInfoDto2.setSn("SN1002");
        snImeiInfoDto2.setImei("IMEI1001");
        snImeiInfoDto2.setImei2("IMEI1001-2");
        snImeiInfoDto2.setSku("SKU1002");
        snImeiInfoDto2.setGoodsId("1002");

        List<SnImeiInfoDto> expectSnImeiInfos = Lists.newArrayList(snImeiInfoDto1, snImeiInfoDto2);
        when(snImeiQueryService.querySnImeiInfoBySns(snImeiQueryDto.getSnList())).thenReturn(Lists.newArrayList(snImeiInfoDto1));
        when(snImeiQueryService.querySnImeiInfoByImeis(snImeiQueryDto.getImeiList())).thenReturn(Lists.newArrayList(snImeiInfoDto2));
        CommonResponse<List<SnImeiInfoDto>> listCommonResponse = snImeiServiceImpl.querySnImeiInfo(snImeiQueryDto);
        Assertions.assertEquals(expectSnImeiInfos, listCommonResponse.getData());
    }

    @DisplayName("测试校验SN/IMEI信息，参数为空，返回空列表")
    @Test
    public void testValidateSnImeiInfo_params_empty() {
        SnImeiQueryDto snImeiQueryDto = new SnImeiQueryDto();
        CommonResponse<List<SnImeiValidationDto>> response = snImeiServiceImpl.validateSnImeiInfo(snImeiQueryDto);
        Assertions.assertTrue(CollectionUtils.isEmpty(response.getData()));
    }

    @DisplayName("测试校验SN/IMEI信息，SN列表参数不为空，返回预期结果")
    @Test
    public void testValidateSnImeiInfo_params_sns() {
        SnImeiQueryDto snImeiQueryDto = new SnImeiQueryDto();
        snImeiQueryDto.setSnList(Lists.newArrayList("SN1001"));

        // 生成SnImeiInfoDto预期结果
        SnImeiInfoDto snImeiInfoDto = new SnImeiInfoDto();
        snImeiInfoDto.setSn("SN1001");
        snImeiInfoDto.setImei("IMEI1001-1");
        snImeiInfoDto.setImei2("IMEI1001-2");
        snImeiInfoDto.setGoodsId("1001");
        List<SnImeiInfoDto> snImeiInfos = Lists.newArrayList(snImeiInfoDto);

        // 生成UpcGoodsInfoDto预期结果
        UpcGoodsInfoDto upcGoodsInfoDto = new UpcGoodsInfoDto();
        upcGoodsInfoDto.setGoodsId("1001");
        upcGoodsInfoDto.setSku("SKU1001");
        upcGoodsInfoDto.setGoodsId("1001");
        upcGoodsInfoDto.setScmName("SCM1001");
        List<UpcGoodsInfoDto> goodsInfos = Lists.newArrayList(upcGoodsInfoDto);

        // Mock查询结果
        when(snImeiQueryService.querySnImeiInfoBySns(snImeiQueryDto.getSnList())).thenReturn(snImeiInfos);
        when(snImeiQueryService.querySnImeiInfoByImeis(any())).thenReturn(Lists.newArrayList());
        when(productQueryService.queryGoodsInfos(Lists.newArrayList("1001"))).thenReturn(goodsInfos);

        // 生成SnImeiValidationDto预期结果
        SnImeiValidationDto snImeiValidationDto = new SnImeiValidationDto();
        snImeiValidationDto.setSnImei("SN1001");
        snImeiValidationDto.setIsValid(true);
        snImeiValidationDto.setSnType("SN");
        List<SnImeiValidationDto> expectValidationInfos = Lists.newArrayList(snImeiValidationDto);

        CommonResponse<List<SnImeiValidationDto>> response = snImeiServiceImpl.validateSnImeiInfo(snImeiQueryDto);
        Assertions.assertEquals(expectValidationInfos, response.getData());
    }

    @DisplayName("测试校验SN/IMEI信息，IMEI列表参数不为空，返回预期结果")
    @Test
    public void testValidateSnImeiInfo_params_imeis() {
        SnImeiQueryDto snImeiQueryDto = new SnImeiQueryDto();
        snImeiQueryDto.setImeiList(Lists.newArrayList("IMEI1002"));

        // 生成SnImeiInfoDto预期结果
        SnImeiInfoDto snImeiInfoDto = new SnImeiInfoDto();
        snImeiInfoDto.setSn("SN1002");
        snImeiInfoDto.setImei("IMEI1002");
        snImeiInfoDto.setImei2("IMEI1002-2");
        snImeiInfoDto.setGoodsId("1002");
        List<SnImeiInfoDto> snImeiInfos = Lists.newArrayList(snImeiInfoDto);

        // 生成UpcGoodsInfoDto预期结果
        UpcGoodsInfoDto upcGoodsInfoDto = new UpcGoodsInfoDto();
        upcGoodsInfoDto.setGoodsId("1002");
        upcGoodsInfoDto.setSku("SKU1002");
        upcGoodsInfoDto.setGoodsId("1002");
        upcGoodsInfoDto.setScmName("SCM1002");
        List<UpcGoodsInfoDto> goodsInfos = Lists.newArrayList(upcGoodsInfoDto);

        // Mock查询结果
        when(snImeiQueryService.querySnImeiInfoBySns(snImeiQueryDto.getSnList())).thenReturn(Lists.newArrayList());
        when(snImeiQueryService.querySnImeiInfoByImeis(snImeiQueryDto.getImeiList())).thenReturn(snImeiInfos); // 这里返回的结果和上面一样
        when(productQueryService.queryGoodsInfos(Lists.newArrayList("1002"))).thenReturn(goodsInfos);

        // 生成SnImeiValidationDto预期结果
        SnImeiValidationDto snImeiValidationDto = new SnImeiValidationDto();
        snImeiValidationDto.setSnImei("IMEI1002");
        snImeiValidationDto.setIsValid(true);
        snImeiValidationDto.setSnType("IMEI");
        List<SnImeiValidationDto> expectValidationInfos = Lists.newArrayList(snImeiValidationDto);

        CommonResponse<List<SnImeiValidationDto>> response = snImeiServiceImpl.validateSnImeiInfo(snImeiQueryDto);
        Assertions.assertEquals(expectValidationInfos, response.getData());
    }


}
