package com.mi.info.intl.retail.org.domain.position.service.impl;

import com.mi.info.intl.retail.intlretail.service.api.position.dto.PositionInspectionAllDetailDTO;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.PositionInspectionDetailRequest;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.InspectionBusinessTypeEnum;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.TaskStatusEnum;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.org.domain.InspectionRecordDomain;
import com.mi.info.intl.retail.org.domain.PositionDomain;
import com.mi.info.intl.retail.org.domain.repository.InspectionRecordRepository;
import com.mi.info.intl.retail.org.domain.repository.InspectionHistoryRepository;
import com.mi.info.intl.retail.org.domain.repository.PositionRepository;
import com.xiaomi.cnzone.storeapi.api.channelbuild.position.BuildChannelPositionProvider;
import com.xiaomi.cnzone.storeapi.model.channelbuild.business.req.PositionDataReq;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import com.xiaomi.nr.global.dev.neptune.T;

import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.mockStatic;

@ExtendWith(MockitoExtension.class)
public class PositionInspectionDomainServiceImpl_GetAllDetailTest {

    @Spy
    @InjectMocks
    private PositionInspectionDomainServiceImpl service;

    @Mock
    private InspectionRecordRepository inspectionRecordRepository;

    @Mock
    private PositionRepository positionRepository;

    @Mock
    private InspectionHistoryRepository inspectionHistoryRepository;

    @Mock
    private BuildChannelPositionProvider buildChannelPositionProvider;

    @Test
    @DisplayName("getPositionInspectionAllDetail 覆盖 buildPositionBasicInfo 行549: positionConstructionType 映射")
    void getPositionInspectionAllDetail_shouldMapPositionConstructionType() {
        Long inspectionId = 123L;
        String positionCode = "P123";

        PositionInspectionDetailRequest req = new PositionInspectionDetailRequest();
        req.setPositionInspectionId(inspectionId);

        InspectionBusinessTypeEnum type = InspectionBusinessTypeEnum.values()[0];
        InspectionRecordDomain record = new InspectionRecordDomain();
        record.setId(inspectionId);
        record.setPositionCode(positionCode);
        record.setPositionInspectionBusinessTypeEnum(type);
        record.setTaskStatus(TaskStatusEnum.NOT_COMPLETED);

        PositionDomain position = new PositionDomain();
        position.setCountryName("CN");
        position.setPositionName("FrontName");
        position.setPositionTypeName("TypeName");

        when(inspectionRecordRepository.getById(inspectionId)).thenReturn(record);
        when(positionRepository.getByPositionCode(positionCode)).thenReturn(position);
        when(buildChannelPositionProvider.imageCenter(any(PositionDataReq.class))).thenReturn(null);
        doReturn(null).when(service).getSelectorList(any());
        doReturn(Collections.emptyList()).when(service).getPositionFurnitureList(any());

        CommonApiResponse<PositionInspectionAllDetailDTO> resp = service.getPositionInspectionAllDetail(req);

        assertNotNull(resp);
        PositionInspectionAllDetailDTO dto = resp.getData();
        assertNotNull(dto);
        assertNotNull(dto.getPositionBasicInfo());
        assertEquals(type.getDesc(), dto.getPositionBasicInfo().getPositionConstructionType());
    }
} 