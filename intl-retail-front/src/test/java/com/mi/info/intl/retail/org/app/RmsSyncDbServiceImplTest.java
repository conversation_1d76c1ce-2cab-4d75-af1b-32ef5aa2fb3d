package com.mi.info.intl.retail.org.app;

import com.mi.info.intl.retail.intlretail.service.api.mq.dto.RmsDbContentRequest;
import com.mi.info.intl.retail.intlretail.service.api.mq.dto.RmsDbRequest;
import com.mi.info.intl.retail.intlretail.service.api.retailer.dto.CommonResponse;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * RmsSyncDbServiceImpl 单元测试类
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("RMS数据同步服务实现类测试")
class RmsSyncDbServiceImplTest {

    @InjectMocks
    private RmsSyncDbServiceImpl rmsSyncDbService;

    @Mock
    private RocketMQTemplate rocketMQTemplate;

    private RmsDbRequest testRequest;
    private static final String TEST_TOPIC = "test-sync-topic";

    @BeforeEach
    void setUp() {
        // 设置私有字段值
        ReflectionTestUtils.setField(rmsSyncDbService, "topic", TEST_TOPIC);
        
        // 初始化测试数据
        testRequest = new RmsDbRequest();
        RmsDbContentRequest contentRequest = new RmsDbContentRequest();
        contentRequest.setTable("intl_rms_store");
        contentRequest.setUuid("test-uuid-001");
        
        Map<String, Object> content = new HashMap<>();
        content.put("storeId", "STORE001");
        content.put("storeName", "测试门店");
        contentRequest.setContent(content);
        
        testRequest.setRmsDBContentList(Arrays.asList(contentRequest));
    }

    @Test
    @DisplayName("同步RMS数据库消息 - 成功场景")
    void testSyncRmsDbMsg_Success() {
        // Given
        doNothing().when(rocketMQTemplate).convertAndSend(eq(TEST_TOPIC), any(RmsDbRequest.class));

        // When
        CommonResponse<String> result = rmsSyncDbService.syncRmsDbMsg(testRequest);

        // Then
        assertNotNull(result);
        assertEquals("success", result.getData());
        verify(rocketMQTemplate, times(1)).convertAndSend(TEST_TOPIC, testRequest);
    }

    @Test
    @DisplayName("同步RMS数据库消息 - 空请求")
    void testSyncRmsDbMsg_EmptyRequest() {
        // Given
        RmsDbRequest emptyRequest = new RmsDbRequest();
        emptyRequest.setRmsDBContentList(Arrays.asList());
        
        doNothing().when(rocketMQTemplate).convertAndSend(eq(TEST_TOPIC), any(RmsDbRequest.class));

        // When
        CommonResponse<String> result = rmsSyncDbService.syncRmsDbMsg(emptyRequest);

        // Then
        assertNotNull(result);
        assertEquals("success", result.getData());
        verify(rocketMQTemplate, times(1)).convertAndSend(TEST_TOPIC, emptyRequest);
    }

    @Test
    @DisplayName("同步RMS数据库消息 - 多个内容")
    void testSyncRmsDbMsg_MultipleContent() {
        // Given
        RmsDbContentRequest contentRequest1 = new RmsDbContentRequest();
        contentRequest1.setTable("intl_rms_store");
        contentRequest1.setUuid("store-uuid");
        
        RmsDbContentRequest contentRequest2 = new RmsDbContentRequest();
        contentRequest2.setTable("intl_rms_user");
        contentRequest2.setUuid("user-uuid");
        
        RmsDbRequest multiRequest = new RmsDbRequest();
        multiRequest.setRmsDBContentList(Arrays.asList(contentRequest1, contentRequest2));
        
        doNothing().when(rocketMQTemplate).convertAndSend(eq(TEST_TOPIC), any(RmsDbRequest.class));

        // When
        CommonResponse<String> result = rmsSyncDbService.syncRmsDbMsg(multiRequest);

        // Then
        assertNotNull(result);
        assertEquals("success", result.getData());
        verify(rocketMQTemplate, times(1)).convertAndSend(TEST_TOPIC, multiRequest);
    }

    @Test
    @DisplayName("同步RMS数据库消息 - 不同表类型")
    void testSyncRmsDbMsg_DifferentTableTypes() {
        // Given
        RmsDbContentRequest userRequest = new RmsDbContentRequest();
        userRequest.setTable("intl_rms_user");
        userRequest.setUuid("user-uuid");
        
        Map<String, Object> userContent = new HashMap<>();
        userContent.put("rmsUserid", "USER001");
        userContent.put("userName", "测试用户");
        userRequest.setContent(userContent);
        
        RmsDbRequest userDbRequest = new RmsDbRequest();
        userDbRequest.setRmsDBContentList(Arrays.asList(userRequest));
        
        doNothing().when(rocketMQTemplate).convertAndSend(eq(TEST_TOPIC), any(RmsDbRequest.class));

        // When
        CommonResponse<String> result = rmsSyncDbService.syncRmsDbMsg(userDbRequest);

        // Then
        assertNotNull(result);
        assertEquals("success", result.getData());
        verify(rocketMQTemplate, times(1)).convertAndSend(TEST_TOPIC, userDbRequest);
    }

    @Test
    @DisplayName("同步RMS数据库消息 - 职位数据")
    void testSyncRmsDbMsg_PositionData() {
        // Given
        RmsDbContentRequest positionRequest = new RmsDbContentRequest();
        positionRequest.setTable("intl_rms_position");
        positionRequest.setUuid("position-uuid");
        
        Map<String, Object> positionContent = new HashMap<>();
        positionContent.put("positionId", "POS001");
        positionContent.put("positionName", "测试职位");
        positionRequest.setContent(positionContent);
        
        RmsDbRequest positionDbRequest = new RmsDbRequest();
        positionDbRequest.setRmsDBContentList(Arrays.asList(positionRequest));
        
        doNothing().when(rocketMQTemplate).convertAndSend(eq(TEST_TOPIC), any(RmsDbRequest.class));

        // When
        CommonResponse<String> result = rmsSyncDbService.syncRmsDbMsg(positionDbRequest);

        // Then
        assertNotNull(result);
        assertEquals("success", result.getData());
        verify(rocketMQTemplate, times(1)).convertAndSend(TEST_TOPIC, positionDbRequest);
    }

    @Test
    @DisplayName("同步RMS数据库消息 - 时区数据")
    void testSyncRmsDbMsg_TimezoneData() {
        // Given
        RmsDbContentRequest timezoneRequest = new RmsDbContentRequest();
        timezoneRequest.setTable("intl_rms_country_timezone");
        timezoneRequest.setUuid("timezone-uuid");
        
        Map<String, Object> timezoneContent = new HashMap<>();
        timezoneContent.put("countryCode", "ID");
        timezoneContent.put("timezone", "Asia/Jakarta");
        timezoneRequest.setContent(timezoneContent);
        
        RmsDbRequest timezoneDbRequest = new RmsDbRequest();
        timezoneDbRequest.setRmsDBContentList(Arrays.asList(timezoneRequest));
        
        doNothing().when(rocketMQTemplate).convertAndSend(eq(TEST_TOPIC), any(RmsDbRequest.class));

        // When
        CommonResponse<String> result = rmsSyncDbService.syncRmsDbMsg(timezoneDbRequest);

        // Then
        assertNotNull(result);
        assertEquals("success", result.getData());
        verify(rocketMQTemplate, times(1)).convertAndSend(TEST_TOPIC, timezoneDbRequest);
    }

    @Test
    @DisplayName("同步RMS数据库消息 - 零售商数据")
    void testSyncRmsDbMsg_RetailerData() {
        // Given
        RmsDbContentRequest retailerRequest = new RmsDbContentRequest();
        retailerRequest.setTable("intl_rms_retailer");
        retailerRequest.setUuid("retailer-uuid");
        
        Map<String, Object> retailerContent = new HashMap<>();
        retailerContent.put("retailerId", "RETAILER001");
        retailerContent.put("retailerName", "测试零售商");
        retailerRequest.setContent(retailerContent);
        
        RmsDbRequest retailerDbRequest = new RmsDbRequest();
        retailerDbRequest.setRmsDBContentList(Arrays.asList(retailerRequest));
        
        doNothing().when(rocketMQTemplate).convertAndSend(eq(TEST_TOPIC), any(RmsDbRequest.class));

        // When
        CommonResponse<String> result = rmsSyncDbService.syncRmsDbMsg(retailerDbRequest);

        // Then
        assertNotNull(result);
        assertEquals("success", result.getData());
        verify(rocketMQTemplate, times(1)).convertAndSend(TEST_TOPIC, retailerDbRequest);
    }

    @Test
    @DisplayName("同步RMS数据库消息 - 签到规则数据")
    void testSyncRmsDbMsg_SignRuleData() {
        // Given
        RmsDbContentRequest signRuleRequest = new RmsDbContentRequest();
        signRuleRequest.setTable("intl_rms_sign_rule");
        signRuleRequest.setUuid("signrule-uuid");
        
        Map<String, Object> signRuleContent = new HashMap<>();
        signRuleContent.put("signRuleId", "RULE001");
        signRuleContent.put("ruleName", "测试规则");
        signRuleRequest.setContent(signRuleContent);
        
        RmsDbRequest signRuleDbRequest = new RmsDbRequest();
        signRuleDbRequest.setRmsDBContentList(Arrays.asList(signRuleRequest));
        
        doNothing().when(rocketMQTemplate).convertAndSend(eq(TEST_TOPIC), any(RmsDbRequest.class));

        // When
        CommonResponse<String> result = rmsSyncDbService.syncRmsDbMsg(signRuleDbRequest);

        // Then
        assertNotNull(result);
        assertEquals("success", result.getData());
        verify(rocketMQTemplate, times(1)).convertAndSend(TEST_TOPIC, signRuleDbRequest);
    }

    @Test
    @DisplayName("同步RMS数据库消息 - 人员职位关联数据")
    void testSyncRmsDbMsg_PersonnelPositionData() {
        // Given
        RmsDbContentRequest personnelPositionRequest = new RmsDbContentRequest();
        personnelPositionRequest.setTable("intl_rms_personnel_position");
        personnelPositionRequest.setUuid("personnel-uuid");
        
        Map<String, Object> personnelPositionContent = new HashMap<>();
        personnelPositionContent.put("associationId", "ASSOC001");
        personnelPositionContent.put("userId", "USER001");
        personnelPositionContent.put("positionId", "POS001");
        personnelPositionRequest.setContent(personnelPositionContent);
        
        RmsDbRequest personnelPositionDbRequest = new RmsDbRequest();
        personnelPositionDbRequest.setRmsDBContentList(Arrays.asList(personnelPositionRequest));
        
        doNothing().when(rocketMQTemplate).convertAndSend(eq(TEST_TOPIC), any(RmsDbRequest.class));

        // When
        CommonResponse<String> result = rmsSyncDbService.syncRmsDbMsg(personnelPositionDbRequest);

        // Then
        assertNotNull(result);
        assertEquals("success", result.getData());
        verify(rocketMQTemplate, times(1)).convertAndSend(TEST_TOPIC, personnelPositionDbRequest);
    }

    @Test
    @DisplayName("同步RMS数据库消息 - 混合数据类型")
    void testSyncRmsDbMsg_MixedDataTypes() {
        // Given
        RmsDbContentRequest storeRequest = new RmsDbContentRequest();
        storeRequest.setTable("intl_rms_store");
        storeRequest.setUuid("store-uuid");
        
        RmsDbContentRequest userRequest = new RmsDbContentRequest();
        userRequest.setTable("intl_rms_user");
        userRequest.setUuid("user-uuid");
        
        RmsDbContentRequest positionRequest = new RmsDbContentRequest();
        positionRequest.setTable("intl_rms_position");
        positionRequest.setUuid("position-uuid");
        
        RmsDbRequest mixedRequest = new RmsDbRequest();
        mixedRequest.setRmsDBContentList(Arrays.asList(storeRequest, userRequest, positionRequest));
        
        doNothing().when(rocketMQTemplate).convertAndSend(eq(TEST_TOPIC), any(RmsDbRequest.class));

        // When
        CommonResponse<String> result = rmsSyncDbService.syncRmsDbMsg(mixedRequest);

        // Then
        assertNotNull(result);
        assertEquals("success", result.getData());
        verify(rocketMQTemplate, times(1)).convertAndSend(TEST_TOPIC, mixedRequest);
    }
}
