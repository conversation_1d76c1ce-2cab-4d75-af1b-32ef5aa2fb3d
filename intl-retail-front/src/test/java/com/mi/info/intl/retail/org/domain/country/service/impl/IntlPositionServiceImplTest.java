package com.mi.info.intl.retail.org.domain.country.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mi.info.intl.retail.api.front.dto.RmsPositionIAndStoreRes;
import com.mi.info.intl.retail.api.front.dto.RmsPositionInfoRes;
import com.mi.info.intl.retail.org.domain.country.service.impl.IntlPositionServiceImpl;
import com.mi.info.intl.retail.org.infra.entity.IntlRmsPosition;
import com.mi.info.intl.retail.org.infra.mapper.IntlRmsPositionMapper;
import com.mi.info.intl.retail.utils.redis.RedisClient;
import com.mi.info.intl.retail.utils.redis.RedisKey;
import com.xiaomi.cnzone.commons.utils.JacksonUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class IntlPositionServiceImplTest {

    @Mock
    private RedisClient redisClient;

    @Mock
    private IntlRmsPositionMapper intlRmsPositionMapper;

    @Spy
    @InjectMocks
    private IntlPositionServiceImpl intlPositionService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        ReflectionTestUtils.setField(intlPositionService, "baseMapper", intlRmsPositionMapper);
    }

    /**
     * 测试getPositionsByPositionIds方法 - 输入为null的情况
     */
    @Test
    void testGetPositionsByPositionIds_withNullInput() {
        List<RmsPositionIAndStoreRes> result = intlPositionService.getPositionsByPositionIds(null);
        assertTrue(CollectionUtils.isEmpty(result));
    }

    /**
     * 测试getPositionsByPositionIds方法 - 输入为空列表的情况
     */
    @Test
    void testGetPositionsByPositionIds_withEmptyList() {
        List<RmsPositionIAndStoreRes> result = intlPositionService.getPositionsByPositionIds(new ArrayList<>());
        assertTrue(CollectionUtils.isEmpty(result));
    }

    /**
     * 测试getPositionsByPositionIds方法 - 所有数据都来自缓存
     */
    @Test
    void testGetPositionsByPositionIds_allFromCache() {
        List<String> positionIds = Arrays.asList("pos1", "pos2");

        // 模拟缓存中有数据
        RmsPositionIAndStoreRes pos1 = new RmsPositionIAndStoreRes();
        pos1.setPositionId("pos1");
        pos1.setPositionCodeRMS("code1");

        RmsPositionIAndStoreRes pos2 = new RmsPositionIAndStoreRes();
        pos2.setPositionId("pos2");
        pos2.setPositionCodeRMS("code2");

        when(redisClient.getObj(any(RedisKey.class), eq(RmsPositionIAndStoreRes.class)))
                .thenReturn(pos1) // 第一次调用返回pos1
                .thenReturn(pos2); // 第二次调用返回pos2

        List<RmsPositionIAndStoreRes> result = intlPositionService.getPositionsByPositionIds(positionIds);

        assertEquals(2, result.size());
        assertEquals("pos1", result.get(0).getPositionId());
        assertEquals("pos2", result.get(1).getPositionId());

        // 验证没有调用数据库查询
        verify(intlRmsPositionMapper, never()).getPositionsByPositionIds(anyList());
    }

    /**
     * 测试getPositionsByPositionIds方法 - 部分数据来自缓存，部分来自数据库
     */
    @Test
    void testGetPositionsByPositionIds_partialFromCache() {
        List<String> positionIds = Arrays.asList("pos1", "pos2", "pos3");

        // 模拟缓存中有部分数据
        RmsPositionIAndStoreRes pos1 = new RmsPositionIAndStoreRes();
        pos1.setPositionId("pos1");
        pos1.setPositionCodeRMS("code1");

        when(redisClient.getObj(any(RedisKey.class), eq(RmsPositionIAndStoreRes.class)))
                .thenReturn(pos1) // pos1在缓存中
                .thenReturn(null) // pos2不在缓存中
                .thenReturn(null); // pos3不在缓存中

        // 模拟数据库查询返回缺失的数据
        RmsPositionIAndStoreRes pos2 = new RmsPositionIAndStoreRes();
        pos2.setPositionId("pos2");
        pos2.setPositionCodeRMS("code2");

        RmsPositionIAndStoreRes pos3 = new RmsPositionIAndStoreRes();
        pos3.setPositionId("pos3");
        pos3.setPositionCodeRMS("code3");

        List<RmsPositionIAndStoreRes> dbResults = Arrays.asList(pos2, pos3);
        when(intlRmsPositionMapper.getPositionsByPositionIds(Arrays.asList("pos2", "pos3")))
                .thenReturn(dbResults);

        try (MockedStatic<JacksonUtil> jacksonUtilMock = mockStatic(JacksonUtil.class)) {
            // 模拟序列化
            jacksonUtilMock.when(() -> JacksonUtil.toStr(any())).thenReturn("serialized");

            List<RmsPositionIAndStoreRes> result = intlPositionService.getPositionsByPositionIds(positionIds);

            assertEquals(3, result.size());
            Set<String> positionIdSet = new HashSet<>();
            for (RmsPositionIAndStoreRes res : result) {
                positionIdSet.add(res.getPositionId());
            }
            assertTrue(positionIdSet.contains("pos1"));
            assertTrue(positionIdSet.contains("pos2"));
            assertTrue(positionIdSet.contains("pos3"));

            // 验证调用了数据库查询
            verify(intlRmsPositionMapper).getPositionsByPositionIds(Arrays.asList("pos2", "pos3"));
        }
    }

    /**
     * 测试getPositionsByPositionIds方法 - 所有数据都来自数据库
     */
    @Test
    void testGetPositionsByPositionIds_allFromDatabase() {
        List<String> positionIds = Arrays.asList("pos1", "pos2");

        // 模拟缓存中没有数据
        when(redisClient.getObj(any(RedisKey.class), eq(RmsPositionIAndStoreRes.class)))
                .thenReturn(null);

        // 模拟数据库查询返回数据
        RmsPositionIAndStoreRes pos1 = new RmsPositionIAndStoreRes();
        pos1.setPositionId("pos1");
        pos1.setPositionCodeRMS("code1");

        RmsPositionIAndStoreRes pos2 = new RmsPositionIAndStoreRes();
        pos2.setPositionId("pos2");
        pos2.setPositionCodeRMS("code2");

        List<RmsPositionIAndStoreRes> dbResults = Arrays.asList(pos1, pos2);
        when(intlRmsPositionMapper.getPositionsByPositionIds(positionIds))
                .thenReturn(dbResults);

        try (MockedStatic<JacksonUtil> jacksonUtilMock = mockStatic(JacksonUtil.class)) {
            // 模拟序列化
            jacksonUtilMock.when(() -> JacksonUtil.toStr(any())).thenReturn("serialized");

            List<RmsPositionIAndStoreRes> result = intlPositionService.getPositionsByPositionIds(positionIds);

            assertEquals(2, result.size());
            assertEquals("pos1", result.get(0).getPositionId());
            assertEquals("pos2", result.get(1).getPositionId());

            // 验证调用了数据库查询
            verify(intlRmsPositionMapper).getPositionsByPositionIds(positionIds);
        }
    }

    /**
     * 测试getCachedPositions方法 - 正常情况
     */
    @Test
    void testGetCachedPositions_normal() {
        List<String> positionIds = Arrays.asList("pos1", "pos2");

        // 模拟缓存中有数据
        RmsPositionIAndStoreRes pos1 = new RmsPositionIAndStoreRes();
        pos1.setPositionId("pos1");
        pos1.setPositionCodeRMS("code1");

        when(redisClient.getObj(any(RedisKey.class), eq(RmsPositionIAndStoreRes.class)))
                .thenReturn(pos1)
                .thenReturn(null); // pos2在缓存中不存在

        List<RmsPositionIAndStoreRes> result = intlPositionService.getCachedPositions(positionIds);

        assertEquals(1, result.size());
        assertEquals("pos1", result.get(0).getPositionId());
    }

    /**
     * 测试getCachedPositions方法 - 缓存中无数据
     */
    @Test
    void testGetCachedPositions_noDataInCache() {
        List<String> positionIds = Arrays.asList("pos1", "pos2");

        // 模拟缓存中没有数据
        when(redisClient.getObj(any(RedisKey.class), eq(RmsPositionIAndStoreRes.class)))
                .thenReturn(null);

        List<RmsPositionIAndStoreRes> result = intlPositionService.getCachedPositions(positionIds);

        assertTrue(result.isEmpty());
    }

    /**
     * 测试cachePositions方法 - 正常情况
     */
    @Test
    void testCachePositions_normal() {
        RmsPositionIAndStoreRes pos1 = new RmsPositionIAndStoreRes();
        pos1.setPositionId("pos1");
        pos1.setPositionCodeRMS("code1");

        RmsPositionIAndStoreRes pos2 = new RmsPositionIAndStoreRes();
        pos2.setPositionId("pos2");
        pos2.setPositionCodeRMS("code2");

        List<RmsPositionIAndStoreRes> positions = Arrays.asList(pos1, pos2);

        try (MockedStatic<JacksonUtil> jacksonUtilMock = mockStatic(JacksonUtil.class)) {
            // 模拟序列化
            jacksonUtilMock.when(() -> JacksonUtil.toStr(any())).thenReturn("serialized");

            // 调用方法
            intlPositionService.cachePositions(positions);

            // 验证redisClient.set被调用了两次
            verify(redisClient, times(2)).set(any(RedisKey.class), anyString());
        }
    }

    /**
     * 测试cachePositions方法 - 空列表
     */
    @Test
    void testCachePositions_emptyList() {
        List<RmsPositionIAndStoreRes> positions = new ArrayList<>();

        intlPositionService.cachePositions(positions);

        // 验证redisClient.set没有被调用
        verify(redisClient, never()).set(any(RedisKey.class), anyString());
    }

    /**
     * 测试 getPositionInfoByPositionCodes 方法 - 输入为 null
     */
    @Test
    void testGetPositionInfoByPositionCodes_withNullInput() {
        Map<String, RmsPositionInfoRes> result = intlPositionService.getPositionInfoByPositionCodes(null);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试 getPositionInfoByPositionCodes 方法 - 输入为空列表
     */
    @Test
    void testGetPositionInfoByPositionCodes_withEmptyList() {
        Map<String, RmsPositionInfoRes> result = intlPositionService.getPositionInfoByPositionCodes(new ArrayList<>());
        assertTrue(result.isEmpty());
    }

}
