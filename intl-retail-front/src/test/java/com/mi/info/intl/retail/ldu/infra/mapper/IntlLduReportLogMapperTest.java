package com.mi.info.intl.retail.ldu.infra.mapper;

import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.StoreMetricsDto;
import com.mi.info.intl.retail.ldu.infra.entity.IntlLduReportLog;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.junit.jupiter.api.DisplayName;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * IntlLduReportLogMapper 单元测试
 * 主要测试 statisticReportLogBatch 方法的SQL查询逻辑
 */
@ExtendWith(MockitoExtension.class)
public class IntlLduReportLogMapperTest {
    
    @Mock
    private IntlLduReportLogMapper intlLduReportLogMapper;
    
    @Test
    @DisplayName("批量统计报告日志 - 成功场景")
    public void statisticReportLogBatch_Success() {
        // 准备数据
        IntlLduReportLog reportLog1 = new IntlLduReportLog();
        reportLog1.setProjectCode("PROJECT_001");
        reportLog1.setCountryCode("CN");
        reportLog1.setStoreCode("STORE_001");
        
        IntlLduReportLog reportLog2 = new IntlLduReportLog();
        reportLog2.setProjectCode("PROJECT_002");
        reportLog2.setCountryCode("US");
        reportLog2.setStoreCode("STORE_002");
        
        List<IntlLduReportLog> reportLogs = Arrays.asList(reportLog1, reportLog2);
        
        // 模拟返回结果
        StoreMetricsDto metrics1 = new StoreMetricsDto();
        metrics1.setProjectCode("PROJECT_001");
        metrics1.setCountryCode("CN");
        metrics1.setActualCoveredStores(5);
        metrics1.setActualDisplayCount(10);
        
        StoreMetricsDto metrics2 = new StoreMetricsDto();
        metrics2.setProjectCode("PROJECT_002");
        metrics2.setCountryCode("US");
        metrics2.setActualCoveredStores(3);
        metrics2.setActualDisplayCount(8);
        
        List<StoreMetricsDto> expectedResults = Arrays.asList(metrics1, metrics2);
        
        // Mock mapper调用
        when(intlLduReportLogMapper.statisticReportLogBatch(reportLogs)).thenReturn(expectedResults);
        
        // 执行测试
        List<StoreMetricsDto> actualResults = intlLduReportLogMapper.statisticReportLogBatch(reportLogs);
        
        // 验证结果
        assertNotNull(actualResults);
        assertEquals(2, actualResults.size());
        
        // 验证第一个结果
        StoreMetricsDto result1 = actualResults.get(0);
        assertEquals("PROJECT_001", result1.getProjectCode());
        assertEquals("CN", result1.getCountryCode());
        assertEquals(5, result1.getActualCoveredStores());
        assertEquals(10, result1.getActualDisplayCount());
        
        // 验证第二个结果
        StoreMetricsDto result2 = actualResults.get(1);
        assertEquals("PROJECT_002", result2.getProjectCode());
        assertEquals("US", result2.getCountryCode());
        assertEquals(3, result2.getActualCoveredStores());
        assertEquals(8, result2.getActualDisplayCount());
        
        // 验证mapper方法被调用
        verify(intlLduReportLogMapper, times(1)).statisticReportLogBatch(reportLogs);
    }
    
    @Test
    @DisplayName("批量统计报告日志 - 空列表场景")
    public void statisticReportLogBatch_EmptyList() {
        // 准备数据
        List<IntlLduReportLog> emptyList = Arrays.asList();
        
        // Mock mapper调用返回空列表
        when(intlLduReportLogMapper.statisticReportLogBatch(emptyList)).thenReturn(Arrays.asList());
        
        // 执行测试
        List<StoreMetricsDto> actualResults = intlLduReportLogMapper.statisticReportLogBatch(emptyList);
        
        // 验证结果
        assertNotNull(actualResults);
        assertTrue(actualResults.isEmpty());
        
        // 验证mapper方法被调用
        verify(intlLduReportLogMapper, times(1)).statisticReportLogBatch(emptyList);
    }
    
    @Test
    @DisplayName("批量统计报告日志 - 单个项目统计场景")
    public void statisticReportLogBatch_SingleProject() {
        // 准备数据
        IntlLduReportLog reportLog = new IntlLduReportLog();
        reportLog.setProjectCode("PROJECT_SINGLE");
        reportLog.setCountryCode("JP");
        reportLog.setStoreCode("STORE_SINGLE");
        
        List<IntlLduReportLog> reportLogs = Arrays.asList(reportLog);
        
        // 模拟返回结果
        StoreMetricsDto metrics = new StoreMetricsDto();
        metrics.setProjectCode("PROJECT_SINGLE");
        metrics.setCountryCode("JP");
        metrics.setActualCoveredStores(1);
        metrics.setActualDisplayCount(1);
        
        List<StoreMetricsDto> expectedResults = Arrays.asList(metrics);
        
        // Mock mapper调用
        when(intlLduReportLogMapper.statisticReportLogBatch(reportLogs)).thenReturn(expectedResults);
        
        // 执行测试
        List<StoreMetricsDto> actualResults = intlLduReportLogMapper.statisticReportLogBatch(reportLogs);
        
        // 验证结果
        assertNotNull(actualResults);
        assertEquals(1, actualResults.size());
        
        StoreMetricsDto result = actualResults.get(0);
        assertEquals("PROJECT_SINGLE", result.getProjectCode());
        assertEquals("JP", result.getCountryCode());
        assertEquals(1, result.getActualCoveredStores());
        assertEquals(1, result.getActualDisplayCount());
        
        // 验证mapper方法被调用
        verify(intlLduReportLogMapper, times(1)).statisticReportLogBatch(reportLogs);
    }
    
    @Test
    @DisplayName("批量统计报告日志 - 相同项目不同国家场景")
    public void statisticReportLogBatch_SameProjectDifferentCountries() {
        // 准备数据
        IntlLduReportLog reportLog1 = new IntlLduReportLog();
        reportLog1.setProjectCode("PROJECT_SAME");
        reportLog1.setCountryCode("CN");
        reportLog1.setStoreCode("STORE_CN_001");
        
        IntlLduReportLog reportLog2 = new IntlLduReportLog();
        reportLog2.setProjectCode("PROJECT_SAME");
        reportLog2.setCountryCode("US");
        reportLog2.setStoreCode("STORE_US_001");
        
        List<IntlLduReportLog> reportLogs = Arrays.asList(reportLog1, reportLog2);
        
        // 模拟返回结果
        StoreMetricsDto metrics1 = new StoreMetricsDto();
        metrics1.setProjectCode("PROJECT_SAME");
        metrics1.setCountryCode("CN");
        metrics1.setActualCoveredStores(2);
        metrics1.setActualDisplayCount(5);
        
        StoreMetricsDto metrics2 = new StoreMetricsDto();
        metrics2.setProjectCode("PROJECT_SAME");
        metrics2.setCountryCode("US");
        metrics2.setActualCoveredStores(1);
        metrics2.setActualDisplayCount(3);
        
        List<StoreMetricsDto> expectedResults = Arrays.asList(metrics1, metrics2);
        
        // Mock mapper调用
        when(intlLduReportLogMapper.statisticReportLogBatch(reportLogs)).thenReturn(expectedResults);
        
        // 执行测试
        List<StoreMetricsDto> actualResults = intlLduReportLogMapper.statisticReportLogBatch(reportLogs);
        
        // 验证结果
        assertNotNull(actualResults);
        assertEquals(2, actualResults.size());
        
        // 验证两个结果的项目代码相同但国家代码不同
        assertEquals("PROJECT_SAME", actualResults.get(0).getProjectCode());
        assertEquals("PROJECT_SAME", actualResults.get(1).getProjectCode());
        assertNotEquals(actualResults.get(0).getCountryCode(), actualResults.get(1).getCountryCode());
        
        // 验证mapper方法被调用
        verify(intlLduReportLogMapper, times(1)).statisticReportLogBatch(reportLogs);
    }
}
