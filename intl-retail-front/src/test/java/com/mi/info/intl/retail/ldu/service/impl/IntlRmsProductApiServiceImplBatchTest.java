package com.mi.info.intl.retail.ldu.service.impl;

import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.ProjectInfoDto;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.SearchProductReq;
import com.mi.info.intl.retail.ldu.infra.entity.IntlRmsProduct;
import com.mi.info.intl.retail.ldu.service.IntlRmsProductService;
import com.mi.info.intl.retail.model.CommonApiResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * IntlRmsProductApiService批量查询单元测试
 * 
 * <AUTHOR>
 * @date 2025/1/27
 */
@ExtendWith(MockitoExtension.class)
class IntlRmsProductApiServiceImplBatchTest {

    @Mock
    private IntlRmsProductService intlRmsProductService;

    @InjectMocks
    private IntlRmsProductApiServiceImpl intlRmsProductApiService;

    private IntlRmsProduct mockProduct1;
    private IntlRmsProduct mockProduct2;
    private IntlRmsProduct mockProduct3;

    @BeforeEach
    void setUp() {
        mockProduct1 = new IntlRmsProduct();
        mockProduct1.setId(1L);
        mockProduct1.setGoodsId("MI001");
        mockProduct1.setName("小米手机13");
        mockProduct1.setProjectCode("MI13");
        mockProduct1.setProductLine("智能手机");
        mockProduct1.setProductLineCode(1L);
        mockProduct1.setProductLineEn("Smartphone");

        mockProduct2 = new IntlRmsProduct();
        mockProduct2.setId(2L);
        mockProduct2.setGoodsId("MI002");
        mockProduct2.setName("小米手机13 Pro");
        mockProduct2.setProjectCode("MI13PRO");
        mockProduct2.setProductLine("智能手机");
        mockProduct2.setProductLineCode(1L);
        mockProduct2.setProductLineEn("Smartphone");

        mockProduct3 = new IntlRmsProduct();
        mockProduct3.setId(3L);
        mockProduct3.setGoodsId("MI003");
        mockProduct3.setName("小米手机14");
        mockProduct3.setProjectCode("MI14");
        mockProduct3.setProductLine("智能手机");
        mockProduct3.setProductLineCode(1L);
        mockProduct3.setProductLineEn("Smartphone");
    }

    @Test
    void searchProductsByProjectCodeList_ExactSearch_Success() {
        // Given
        List<String> keywords = Arrays.asList("MI13", "MI13PRO", "MI14");
        SearchProductReq req = new SearchProductReq();
        req.setKeywords(keywords);
        req.setSearchType("EXACT");
        List<IntlRmsProduct> mockProducts = Arrays.asList(mockProduct1, mockProduct2, mockProduct3);
        when(intlRmsProductService.searchByProjectCodeListExact(keywords)).thenReturn(mockProducts);

        // When
        CommonApiResponse<List<ProjectInfoDto>> response = intlRmsProductApiService.searchProductsByProjectCodeList(req);

        // Then
        assertNotNull(response);
        assertEquals(200, response.getCode());
        assertEquals("success", response.getMessage());
        
        List<ProjectInfoDto> result = response.getData();
        assertNotNull(result);
        assertEquals(3, result.size());
        
        // 验证第一个产品
        ProjectInfoDto firstProduct = result.get(0);
        assertEquals("MI001", firstProduct.getGoodsId());
        assertEquals("小米手机13", firstProduct.getName());
        assertEquals("MI13", firstProduct.getProjectCode());
        assertEquals("智能手机", firstProduct.getProductLine());
        assertEquals(1L, firstProduct.getProductLineCode());
        assertEquals("Smartphone", firstProduct.getProductLineEn());
        
        // 验证Service方法被正确调用
        verify(intlRmsProductService, times(1)).searchByProjectCodeListExact(keywords);
        verify(intlRmsProductService, never()).searchByProjectCodeFuzzy(anyString());
    }

    @Test
    void searchProductsByProjectCodeList_FuzzySearch_Success() {
        // Given
        List<String> keywords = Arrays.asList("MI13", "MI14");
        SearchProductReq req = new SearchProductReq();
        req.setKeywords(keywords);
        req.setSearchType("FUZZY");
        when(intlRmsProductService.searchByProjectCodeFuzzy("MI13")).thenReturn(Arrays.asList(mockProduct1));
        when(intlRmsProductService.searchByProjectCodeFuzzy("MI14")).thenReturn(Arrays.asList(mockProduct3));

        // When
        CommonApiResponse<List<ProjectInfoDto>> response = intlRmsProductApiService.searchProductsByProjectCodeList(req);

        // Then
        assertNotNull(response);
        assertEquals(200, response.getCode());
        assertEquals("success", response.getMessage());
        
        List<ProjectInfoDto> result = response.getData();
        assertNotNull(result);
        assertEquals(2, result.size());
        
        // 验证Service方法被正确调用
        verify(intlRmsProductService, times(1)).searchByProjectCodeFuzzy("MI13");
        verify(intlRmsProductService, times(1)).searchByProjectCodeFuzzy("MI14");
        verify(intlRmsProductService, never()).searchByProjectCodeListExact(anyList());
    }

    @Test
    void searchProductsByProjectCodeList_EmptyKeywords() {
        // Given
        List<String> keywords = Collections.emptyList();
        SearchProductReq req = new SearchProductReq();
        req.setKeywords(keywords);
        req.setSearchType("EXACT");

        // When
        CommonApiResponse<List<ProjectInfoDto>> response = intlRmsProductApiService.searchProductsByProjectCodeList(req);

        // Then
        assertNotNull(response);
        assertEquals(200, response.getCode());
        assertEquals("success", response.getMessage());
        
        List<ProjectInfoDto> result = response.getData();
        assertNotNull(result);
        assertTrue(result.isEmpty());
        
        // 验证Service方法没有被调用
        verify(intlRmsProductService, never()).searchByProjectCodeListExact(anyList());
        verify(intlRmsProductService, never()).searchByProjectCodeFuzzy(anyString());
    }

    @Test
    void searchProductsByProjectCodeList_NullKeywords() {
        // Given
        SearchProductReq req = new SearchProductReq();
        req.setKeywords(null);
        req.setSearchType("EXACT");

        // When
        CommonApiResponse<List<ProjectInfoDto>> response = intlRmsProductApiService.searchProductsByProjectCodeList(req);

        // Then
        assertNotNull(response);
        assertEquals(200, response.getCode());
        assertEquals("success", response.getMessage());
        
        List<ProjectInfoDto> result = response.getData();
        assertNotNull(result);
        assertTrue(result.isEmpty());
        
        // 验证Service方法没有被调用
        verify(intlRmsProductService, never()).searchByProjectCodeListExact(anyList());
        verify(intlRmsProductService, never()).searchByProjectCodeFuzzy(anyString());
    }

    @Test
    void searchProductsByProjectCodeList_NullRequest() {
        // Given
        SearchProductReq req = null;

        // When
        CommonApiResponse<List<ProjectInfoDto>> response = intlRmsProductApiService.searchProductsByProjectCodeList(req);

        // Then
        assertNotNull(response);
        assertEquals(200, response.getCode());
        assertEquals("success", response.getMessage());
        
        List<ProjectInfoDto> result = response.getData();
        assertNotNull(result);
        assertTrue(result.isEmpty());
        
        // 验证Service方法没有被调用
        verify(intlRmsProductService, never()).searchByProjectCodeListExact(anyList());
        verify(intlRmsProductService, never()).searchByProjectCodeFuzzy(anyString());
    }

    @Test
    void searchProductsByProjectCodeList_WithEmptyAndNullValues() {
        // Given
        List<String> keywords = Arrays.asList("MI13", "", null, "MI14", "  ");
        List<String> expectedFilteredKeywords = Arrays.asList("MI13", "MI14");
        SearchProductReq req = new SearchProductReq();
        req.setKeywords(keywords);
        req.setSearchType("EXACT");
        List<IntlRmsProduct> mockProducts = Arrays.asList(mockProduct1, mockProduct3);
        when(intlRmsProductService.searchByProjectCodeListExact(expectedFilteredKeywords)).thenReturn(mockProducts);

        // When
        CommonApiResponse<List<ProjectInfoDto>> response = intlRmsProductApiService.searchProductsByProjectCodeList(req);

        // Then
        assertNotNull(response);
        assertEquals(200, response.getCode());
        assertEquals("success", response.getMessage());
        
        List<ProjectInfoDto> result = response.getData();
        assertNotNull(result);
        assertEquals(2, result.size());
        
        // 验证Service方法被正确调用
        verify(intlRmsProductService, times(1)).searchByProjectCodeListExact(expectedFilteredKeywords);
    }

    @Test
    void searchProductsByProjectCodeList_ExceptionHandling() {
        // Given
        List<String> keywords = Arrays.asList("MI13", "MI14");
        SearchProductReq req = new SearchProductReq();
        req.setKeywords(keywords);
        req.setSearchType("EXACT");
        when(intlRmsProductService.searchByProjectCodeListExact(keywords)).thenThrow(new RuntimeException("Database error"));

        // When
        CommonApiResponse<List<ProjectInfoDto>> response = intlRmsProductApiService.searchProductsByProjectCodeList(req);

        // Then
        assertNotNull(response);
        assertEquals(500, response.getCode());
        assertTrue(response.getMessage().contains("查询失败"));
        
        // 验证Service方法被调用
        verify(intlRmsProductService, times(1)).searchByProjectCodeListExact(keywords);
    }

    @Test
    void searchProductsByProjectCodeList_CaseInsensitiveSearchType() {
        // Given
        List<String> keywords = Arrays.asList("MI13", "MI14");
        SearchProductReq req = new SearchProductReq();
        req.setKeywords(keywords);
        req.setSearchType("exact"); // 小写，应该被转换为大写
        List<IntlRmsProduct> mockProducts = Arrays.asList(mockProduct1, mockProduct3);
        when(intlRmsProductService.searchByProjectCodeListExact(keywords)).thenReturn(mockProducts);

        // When
        CommonApiResponse<List<ProjectInfoDto>> response = intlRmsProductApiService.searchProductsByProjectCodeList(req);

        // Then
        assertNotNull(response);
        assertEquals(200, response.getCode());
        assertEquals("success", response.getMessage());
        
        List<ProjectInfoDto> result = response.getData();
        assertNotNull(result);
        assertEquals(2, result.size());
        
        // 验证Service方法被正确调用（精确查询）
        verify(intlRmsProductService, times(1)).searchByProjectCodeListExact(keywords);
        verify(intlRmsProductService, never()).searchByProjectCodeFuzzy(anyString());
    }
}
