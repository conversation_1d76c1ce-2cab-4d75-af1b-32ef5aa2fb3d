package com.mi.info.intl.retail.management.service;

import com.mi.info.intl.retail.intlretail.service.api.management.dto.ChannelWorkableChangeParam;
import com.mi.info.intl.retail.management.domain.PositionChangeLogDomain;
import com.mi.info.intl.retail.management.domain.repository.PositionChangeLogRepository;
import com.mi.info.intl.retail.management.domain.repository.StoreChangeLogRepository;
import com.mi.info.intl.retail.management.rpc.StorePositionMainDataRpc;
import com.xiaomi.cnzone.storems.api.model.dto.international.InternationalChannelStoreDetail;
import com.xiaomi.cnzone.storems.api.model.dto.international.StoreInformation;
import com.xiaomi.cnzone.storems.api.model.req.store.GetPositionInfoResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.Date;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * ChannelWorkableServiceImpl 单元测试类
 * 
 * <AUTHOR> Generated
 */
@ExtendWith(MockitoExtension.class)
class ChannelWorkableServiceImplTest {

    @Mock
    private PositionChangeLogRepository positionChangeLogRepository;

    @Mock
    private StoreChangeLogRepository storeChangeLogRepository;

    @Mock
    private StorePositionMainDataRpc storePositionMainDataRpc;

    @InjectMocks
    private ChannelWorkableServiceImpl channelWorkableService;

    private ChannelWorkableChangeParam changeParam;
    private PositionChangeLogDomain existingPositionLog;
    private GetPositionInfoResponse positionInfoResponse;

    private InternationalChannelStoreDetail internationalChannelStoreDetail;

    @BeforeEach
    void setUp() {
        // 初始化测试数据 - 使用提供的模拟数据
        changeParam = new ChannelWorkableChangeParam();
        changeParam.setPositionCode("CID002095");
        changeParam.setChangeType("HAS_PS");
        changeParam.setChangeResult("TRUE");
        changeParam.setEffectiveTime(0L);

        // 创建全新的 PositionChangeLogDomain 对象，确保没有 mock 干扰
        existingPositionLog = new PositionChangeLogDomain();
        existingPositionLog.setPositionCode("CID002095");
        existingPositionLog.setPositionName("测试阵地CID002095");
        existingPositionLog.setStoreCode("STORE_CID002095");
        existingPositionLog.setHasPc(0);
        existingPositionLog.setHasSr(0);
        existingPositionLog.setPositionType(1);
        existingPositionLog.setPositionClass(4);
        existingPositionLog.setEffectiveTime(0L);

        // 创建全新的 GetPositionInfoResponse 对象，确保没有 mock 干扰
        positionInfoResponse = new GetPositionInfoResponse();
        positionInfoResponse.setPositionCode("CID002095");
        positionInfoResponse.setPositionName("测试阵地CID002095");
        positionInfoResponse.setOrgId("STORE_CID002095");

        internationalChannelStoreDetail = InternationalChannelStoreDetail.builder().build();
        StoreInformation storeInformation = StoreInformation.builder().build();
        storeInformation.setOrgId("*********");
        internationalChannelStoreDetail.setStoreInformation(storeInformation);
    }

    @Test
    void testApplyChannelWorkableChanges_WithMockData_CID002095_ShouldProcessSuccessfully() {
        // 使用 setUp 中初始化的 changeParam（已包含模拟数据）
        
        // 模拟没有找到现有的位置日志
        when(positionChangeLogRepository.findLastedByEffectTime(eq("CID002095"), any(Long.class)))
                .thenReturn(existingPositionLog);
        
        // 模拟获取位置信息成功
        GetPositionInfoResponse mockPositionInfo = new GetPositionInfoResponse();
        mockPositionInfo.setPositionCode("CID002095");
        mockPositionInfo.setPositionName("测试阵地CID002095");
        mockPositionInfo.setOrgId("STORE_CID002095");
        
        when(storePositionMainDataRpc.getPosition(any()))
                .thenReturn(mockPositionInfo);
        
        // 模拟保存操作 - save() 方法返回 boolean
        when(positionChangeLogRepository.save(any(PositionChangeLogDomain.class)))
                .thenReturn(true);
        
        // 模拟查找旧数据返回空列表
        when(positionChangeLogRepository.findByPositionCodeAndEffectTime(eq("CID002095"), eq(0L)))
                .thenReturn(Collections.emptyList());
        
        // 模拟查找门店相关数据返回空列表
        when(positionChangeLogRepository.findByStoreCode(anyString()))
                .thenReturn(Arrays.asList(existingPositionLog));
        when(storeChangeLogRepository.findByStoreCodeAndEffectiveTime(anyString(), eq(0L)))
                .thenReturn(Collections.emptyList());
        when(storeChangeLogRepository.findLastedByStoreCodeAndEffectiveTime(anyString(), eq(0L)))
                .thenReturn(null);
        
        // 模拟获取门店信息
        when(storePositionMainDataRpc.getStore(any()))
                .thenReturn(internationalChannelStoreDetail);

        // 执行测试 - 使用 setUp 中的 changeParam
        channelWorkableService.applyChannelWorkableChanges(Arrays.asList(changeParam));

        // 验证调用
        verify(positionChangeLogRepository).findLastedByEffectTime(eq("CID002095"), any(Long.class));
        verify(storePositionMainDataRpc).getPosition(any());
        verify(positionChangeLogRepository).save(any(PositionChangeLogDomain.class));
        
        // 验证保存的位置编码正确
        verify(positionChangeLogRepository).save(argThat(log -> 
            "CID002095".equals(log.getPositionCode())
        ));
    }
}
