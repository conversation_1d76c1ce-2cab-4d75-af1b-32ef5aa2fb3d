package com.mi.info.intl.retail.ldu.service.impl;

import com.mi.info.intl.retail.constant.CommonConstant;
import com.mi.info.intl.retail.intlretail.service.api.fds.FdsService;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.IntlLduSnDto;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.SnImeiGoodsInfoDto;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.SnImeiQueryDto;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.SnImeiValidationDto;
import com.mi.info.intl.retail.ldu.enums.LduTypeEnum;
import com.mi.info.intl.retail.ldu.infra.entity.IntlLduReportLog;
import com.mi.info.intl.retail.ldu.infra.entity.IntlLduSn;
import com.mi.info.intl.retail.ldu.infra.mapper.IntlLduReportLogMapper;
import com.mi.info.intl.retail.ldu.infra.mapper.IntlLduSnMapper;
import com.mi.info.intl.retail.ldu.service.IntlLduSnImeiService;
import com.mi.info.intl.retail.ldu.util.ConstantMessageTemplate;
import com.mi.info.intl.retail.ldu.util.NrJobTaskUtils;
import com.mi.info.intl.retail.org.infra.entity.IntlRmsCountryTimezone;
import com.mi.info.intl.retail.org.infra.mapper.IntlRmsCountryTimezoneMapper;
import com.mi.info.intl.retail.retailer.entity.IntlRmsRetailer;
import com.mi.info.intl.retail.retailer.mapper.IntlRmsRetailerMapper;
import com.mi.info.intl.retail.retailer.mapper.read.IntlRmsRetailerReadMapper;
import com.mi.info.intl.retail.cooperation.downloadcenter.config.LduConfig;
import com.mi.info.intl.retail.cooperation.downloadcenter.example.JobTriggerHelper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.DisplayName;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * IntlLduSnServiceImpl 单元测试
 * 主要测试 conertOtherParams 私有方法的逻辑
 */
@ExtendWith(MockitoExtension.class)
public class IntlLduSnServiceImplTest {

    @InjectMocks
    private IntlLduSnServiceImpl intlLduSnService;

    @Mock
    private FdsService fdsService;

    @Mock
    private IntlLduSnImeiService intlLduSnImeiService;

    @Mock
    private IntlLduSnMapper intlLduSnMapper;

    @Mock
    private IntlRmsCountryTimezoneMapper intlRmsCountryTimezoneMapper;

    @Mock
    private NrJobTaskUtils nrJobTaskUtils;

    @Mock
    private IntlRmsRetailerMapper intlRmsRetailerMapper;

    @Mock
    private LduConfig lduConfig;

    @Mock
    private IntlRmsRetailerReadMapper intlRmsRetailerReadMapper;

    @Mock
    private JobTriggerHelper jobTriggerHelper;

    @Mock
    private ThreadPoolTaskExecutor executor;

    @Mock
    private IntlLduReportLogMapper intlLduReportLogMapper;

    private IntlLduSnDto testSnDto;
    private SnImeiGoodsInfoDto testGoodsInfoDto;
    private SnImeiValidationDto testValidationDto;

    @BeforeEach
    public void setUp() {
        // 初始化测试数据
        testSnDto = new IntlLduSnDto();
        testSnDto.setSn("TEST_SN_001");
        testSnDto.setLduType(LduTypeEnum.MASS_PRODUCTION_VERSION.getType());
        testSnDto.setRetailerCode("RETAILER_001");
        testSnDto.setCountryCode("CN");

        testGoodsInfoDto = new SnImeiGoodsInfoDto();
        testGoodsInfoDto.setSn("TEST_SN_001");
        testGoodsInfoDto.setGoodsId("GOODS_001");
        testGoodsInfoDto.setGoodsName("Test Goods");

        testValidationDto = new SnImeiValidationDto();
        testValidationDto.setSnImei("TEST_SN_001");
        testValidationDto.setIsValid(true);
    }

    @Test
    @DisplayName("conertOtherParams - 已上报状态更新成功")
    public void conertOtherParams_ReportStatusUpdated() {
        // 准备数据 - 模拟已上报的记录
        IntlLduReportLog existingReport = new IntlLduReportLog();
        existingReport.setSn("TEST_SN_001");
        existingReport.setProjectCode("PROJECT_001");

        List<IntlLduReportLog> existingReports = Arrays.asList(existingReport);

        // Mock依赖调用
        when(intlLduReportLogMapper.selectBySns(any(SnImeiQueryDto.class)))
                .thenReturn(existingReports);
        when(intlLduSnImeiService.validateSnImeiInfo(any(SnImeiQueryDto.class)))
                .thenReturn(Arrays.asList(testValidationDto));
        when(intlLduSnImeiService.getGoodsInfoBySnImei("TEST_SN_001"))
                .thenReturn(testGoodsInfoDto);
        when(intlRmsCountryTimezoneMapper.selectAll())
                .thenReturn(Arrays.asList(new IntlRmsCountryTimezone()));
        when(intlRmsRetailerMapper.selectByRetailerCode(anyList()))
                .thenReturn(Arrays.asList(new IntlRmsRetailer()));
        when(intlRmsRetailerReadMapper.queryRetailerByNameOrCode(anyInt(), any()))
                .thenReturn(Arrays.asList());
        when(intlLduSnMapper.selectBySn("TEST_SN_001"))
                .thenReturn(null);

        // 执行测试 - 通过反射调用私有方法
        String result = invokeConertOtherParams(testSnDto);

        // 验证结果
        assertEquals("", result);
        assertEquals(CommonConstant.REPORT_ENABLED, testSnDto.getIsReport());

        // 验证依赖调用
        verify(intlLduReportLogMapper).selectBySns(any(SnImeiQueryDto.class));
        verify(intlLduSnImeiService).validateSnImeiInfo(any(SnImeiQueryDto.class));
        verify(intlLduSnImeiService).getGoodsInfoBySnImei("TEST_SN_001");
    }

    @Test
    @DisplayName("conertOtherParams - 商品信息为空返回错误消息")
    public void conertOtherParams_GoodsInfoNull() {
        // 准备数据
        when(intlLduReportLogMapper.selectBySns(any(SnImeiQueryDto.class)))
                .thenReturn(Arrays.asList());
        when(intlLduSnImeiService.validateSnImeiInfo(any(SnImeiQueryDto.class)))
                .thenReturn(Arrays.asList(testValidationDto));
        when(intlLduSnImeiService.getGoodsInfoBySnImei("TEST_SN_001"))
                .thenReturn(null);

        // 执行测试
        String result = invokeConertOtherParams(testSnDto);

        // 验证结果
        assertEquals(ConstantMessageTemplate.getXiaomiSnValidationFailedMessage(), result);

        // 验证依赖调用
        verify(intlLduSnImeiService).getGoodsInfoBySnImei("TEST_SN_001");
    }

    @Test
    @DisplayName("conertOtherParams - LDU类型错误返回错误消息")
    public void conertOtherParams_InvalidLduType() {
        // 准备数据 - 设置无效的LDU类型
        testSnDto.setLduType("INVALID_TYPE");

        when(intlLduReportLogMapper.selectBySns(any(SnImeiQueryDto.class)))
                .thenReturn(Arrays.asList());
        when(intlLduSnImeiService.validateSnImeiInfo(any(SnImeiQueryDto.class)))
                .thenReturn(Arrays.asList(testValidationDto));
        when(intlLduSnImeiService.getGoodsInfoBySnImei("TEST_SN_001"))
                .thenReturn(testGoodsInfoDto);
        when(intlRmsCountryTimezoneMapper.selectAll())
                .thenReturn(Arrays.asList(new IntlRmsCountryTimezone()));
        when(intlRmsRetailerMapper.selectByRetailerCode(anyList()))
                .thenReturn(Arrays.asList(new IntlRmsRetailer()));
        when(intlRmsRetailerReadMapper.queryRetailerByNameOrCode(anyInt(), any()))
                .thenReturn(Arrays.asList());

        // 执行测试
        String result = invokeConertOtherParams(testSnDto);

        // 验证结果
        assertEquals(ConstantMessageTemplate.getLduTypeErrorMessage(), result);
    }

    @Test
    @DisplayName("conertOtherParams - SN验证失败返回错误消息")
    public void conertOtherParams_SnValidationFailed() {
        // 准备数据 - 设置验证失败
        testValidationDto.setIsValid(false);

        when(intlLduReportLogMapper.selectBySns(any(SnImeiQueryDto.class)))
                .thenReturn(Arrays.asList());
        when(intlLduSnImeiService.validateSnImeiInfo(any(SnImeiQueryDto.class)))
                .thenReturn(Arrays.asList(testValidationDto));
        when(intlLduSnImeiService.getGoodsInfoBySnImei("TEST_SN_001"))
                .thenReturn(testGoodsInfoDto);
        when(intlRmsCountryTimezoneMapper.selectAll())
                .thenReturn(Arrays.asList(new IntlRmsCountryTimezone()));
        when(intlRmsRetailerMapper.selectByRetailerCode(anyList()))
                .thenReturn(Arrays.asList(new IntlRmsRetailer()));
        when(intlRmsRetailerReadMapper.queryRetailerByNameOrCode(anyInt(), any()))
                .thenReturn(Arrays.asList());

        // 执行测试
        String result = invokeConertOtherParams(testSnDto);

        // 验证结果
        assertEquals(ConstantMessageTemplate.getXiaomiSnValidationFailedMessage(), result);
    }

    @Test
    @DisplayName("conertOtherParams - 零售商和国家不匹配返回错误消息")
    public void conertOtherParams_RetailerCountryMismatch() {
        // 准备数据 - 模拟零售商和国家不匹配
        Map<String, String> retailerCodeMap = new HashMap<>();
        retailerCodeMap.put("RETAILER_001", "US"); // 零售商对应US，但SN设置的是CN

        when(intlLduReportLogMapper.selectBySns(any(SnImeiQueryDto.class)))
                .thenReturn(Arrays.asList());
        when(intlLduSnImeiService.validateSnImeiInfo(any(SnImeiQueryDto.class)))
                .thenReturn(Arrays.asList(testValidationDto));
        when(intlLduSnImeiService.getGoodsInfoBySnImei("TEST_SN_001"))
                .thenReturn(testGoodsInfoDto);
        when(intlRmsCountryTimezoneMapper.selectAll())
                .thenReturn(Arrays.asList(new IntlRmsCountryTimezone()));
        when(intlRmsRetailerMapper.selectByRetailerCode(anyList()))
                .thenReturn(Arrays.asList(new IntlRmsRetailer()));
        when(intlRmsRetailerReadMapper.queryRetailerByNameOrCode(anyInt(), any()))
                .thenReturn(Arrays.asList());

        // 执行测试
        String result = invokeConertOtherParams(testSnDto);

        // 验证结果
        assertEquals(ConstantMessageTemplate.matchRetailerCodeAndCountry(), result);
    }

    @Test
    @DisplayName("conertOtherParams - SN重复返回错误消息")
    public void conertOtherParams_SnDuplicate() {
        // 准备数据 - 模拟SN已存在
        IntlLduSn existingSn = new IntlLduSn();
        existingSn.setSn("TEST_SN_001");

        when(intlLduReportLogMapper.selectBySns(any(SnImeiQueryDto.class)))
                .thenReturn(Arrays.asList());
        when(intlLduSnImeiService.validateSnImeiInfo(any(SnImeiQueryDto.class)))
                .thenReturn(Arrays.asList(testValidationDto));
        when(intlLduSnImeiService.getGoodsInfoBySnImei("TEST_SN_001"))
                .thenReturn(testGoodsInfoDto);
        when(intlRmsCountryTimezoneMapper.selectAll())
                .thenReturn(Arrays.asList(new IntlRmsCountryTimezone()));
        when(intlRmsRetailerMapper.selectByRetailerCode(anyList()))
                .thenReturn(Arrays.asList(new IntlRmsRetailer()));
        when(intlRmsRetailerReadMapper.queryRetailerByNameOrCode(anyInt(), any()))
                .thenReturn(Arrays.asList());
        when(intlLduSnMapper.selectBySn("TEST_SN_001"))
                .thenReturn(existingSn);

        // 执行测试
        String result = invokeConertOtherParams(testSnDto);

        // 验证结果
        assertEquals(ConstantMessageTemplate.getSnDuplicateMessage(), result);

        // 验证依赖调用
        verify(intlLduSnMapper).selectBySn("TEST_SN_001");
    }

    @Test
    @DisplayName("conertOtherParams - 所有验证通过返回空字符串")
    public void conertOtherParams_AllValidationsPass() {
        // 准备数据
        when(intlLduReportLogMapper.selectBySns(any(SnImeiQueryDto.class)))
                .thenReturn(Arrays.asList());
        when(intlLduSnImeiService.validateSnImeiInfo(any(SnImeiQueryDto.class)))
                .thenReturn(Arrays.asList(testValidationDto));
        when(intlLduSnImeiService.getGoodsInfoBySnImei("TEST_SN_001"))
                .thenReturn(testGoodsInfoDto);
        when(intlRmsCountryTimezoneMapper.selectAll())
                .thenReturn(Arrays.asList(new IntlRmsCountryTimezone()));
        when(intlRmsRetailerMapper.selectByRetailerCode(anyList()))
                .thenReturn(Arrays.asList(new IntlRmsRetailer()));
        when(intlRmsRetailerReadMapper.queryRetailerByNameOrCode(anyInt(), any()))
                .thenReturn(Arrays.asList());
        when(intlLduSnMapper.selectBySn("TEST_SN_001"))
                .thenReturn(null);

        // 执行测试
        String result = invokeConertOtherParams(testSnDto);

        // 验证结果
        assertEquals("", result);

        // 验证所有依赖都被正确调用
        verify(intlLduReportLogMapper).selectBySns(any(SnImeiQueryDto.class));
        verify(intlLduSnImeiService).validateSnImeiInfo(any(SnImeiQueryDto.class));
        verify(intlLduSnImeiService).getGoodsInfoBySnImei("TEST_SN_001");
        verify(intlRmsCountryTimezoneMapper).selectAll();
        verify(intlRmsRetailerMapper).selectByRetailerCode(anyList());
        verify(intlRmsRetailerReadMapper).queryRetailerByNameOrCode(anyInt(), any());
        verify(intlLduSnMapper).selectBySn("TEST_SN_001");
    }

    /**
     * 通过反射调用私有方法 conertOtherParams
     */
    private String invokeConertOtherParams(IntlLduSnDto confSn) {
        try {
            java.lang.reflect.Method method = IntlLduSnServiceImpl.class
                    .getDeclaredMethod("conertOtherParams", IntlLduSnDto.class);
            method.setAccessible(true);
            return (String) method.invoke(intlLduSnService, confSn);
        } catch (Exception e) {
            throw new RuntimeException("Failed to invoke private method", e);
        }
    }
}