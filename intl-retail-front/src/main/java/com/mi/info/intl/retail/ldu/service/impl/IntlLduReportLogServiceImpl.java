package com.mi.info.intl.retail.ldu.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.ObjectId;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.zxing.NotFoundException;
import com.google.zxing.Result;
import com.mi.info.intl.retail.advice.excel.export.ExportExcelAspect;
import com.mi.info.intl.retail.api.front.dto.RmsPositionInfoRes;
import com.mi.info.intl.retail.api.front.position.IntlPositionApiService;
import com.mi.info.intl.retail.component.ComponentLocator;
import com.mi.info.intl.retail.cooperation.downloadcenter.config.LduConfig;
import com.mi.info.intl.retail.cooperation.downloadcenter.example.JobTriggerHelper;
import com.mi.info.intl.retail.enums.SerialNumberType;
import com.mi.info.intl.retail.exception.RetailRunTimeException;
import com.mi.info.intl.retail.intlretail.service.api.fds.FdsService;
import com.mi.info.intl.retail.intlretail.service.api.fds.dto.FdsUploadResult;
import com.mi.info.intl.retail.intlretail.service.api.ldu.IntlLduReportLogService;
import com.mi.info.intl.retail.intlretail.service.api.ldu.IntlLduSnService;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.*;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialPhotoGroup;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.UploadMaterialData;
import com.mi.info.intl.retail.intlretail.service.api.material.enums.TargetTypeEnum;
import com.mi.info.intl.retail.intlretail.service.api.material.enums.TaskTypeEnum;
import com.mi.info.intl.retail.intlretail.service.api.nrjob.gateway.dto.NrJobGoIn;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.InspectionFlagCompletionEnum;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.InspectionStatusEnum;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.RmsUserBaseDataResponse;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.BusinessDataInputRequest;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.BusinessDataResponse;
import com.mi.info.intl.retail.ldu.dto.IntlLduReportExport;
import com.mi.info.intl.retail.ldu.dto.JobSuccessDto;
import com.mi.info.intl.retail.ldu.dto.StoreGradeInfoDTO;
import com.mi.info.intl.retail.ldu.dto.StoreInfoDTO;
import com.mi.info.intl.retail.ldu.dto.excel.IntlLduEnInspectionExport;
import com.mi.info.intl.retail.ldu.dto.excel.IntlLduEnReportExport;
import com.mi.info.intl.retail.ldu.enums.*;
import com.mi.info.intl.retail.ldu.infra.entity.IntlFileReportRel;
import com.mi.info.intl.retail.ldu.infra.entity.IntlFileUpload;
import com.mi.info.intl.retail.ldu.infra.entity.IntlLduReportLog;
import com.mi.info.intl.retail.ldu.infra.entity.IntlLduTarget;
import com.mi.info.intl.retail.ldu.infra.mapper.IntlFileReportRelMapper;
import com.mi.info.intl.retail.ldu.infra.mapper.IntlFileUploadMapper;
import com.mi.info.intl.retail.ldu.infra.mapper.IntlLduReportLogMapper;
import com.mi.info.intl.retail.ldu.infra.mapper.IntlLduTargetMapper;
import com.mi.info.intl.retail.ldu.infra.repository.IProductQueryService;
import com.mi.info.intl.retail.ldu.service.IntlFileUploadService;
import com.mi.info.intl.retail.ldu.service.IntlLduSnImeiService;
import com.mi.info.intl.retail.ldu.util.NrJobTaskUtils;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.model.PageResponse;
import com.mi.info.intl.retail.org.domain.dto.IntlLduInspectionInfoDTO;
import com.mi.info.intl.retail.org.domain.enums.InspectionType;
import com.mi.info.intl.retail.org.domain.enums.StatisticType;
import com.mi.info.intl.retail.org.infra.entity.IntlRmsCountryTimezone;
import com.mi.info.intl.retail.org.infra.entity.IntlRmsStore;
import com.mi.info.intl.retail.org.infra.mapper.*;
import com.mi.info.intl.retail.org.infra.mapper.read.InspectionRecordReadMapper;
import com.mi.info.intl.retail.user.app.UserService;
import com.mi.info.intl.retail.utils.BarcodeScannerUtil;
import com.mi.info.intl.retail.utils.SnImeiValidateUtil;
import com.mi.info.intl.retail.utils.time.DateTimeUtil;
import com.xiaomi.com.i18n.area.Area;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.dubbo.rpc.RpcContext;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.awt.image.BufferedImage;
import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@DubboService(group = "${dubbo-group.provider.intl-retail:}", interfaceClass = IntlLduReportLogService.class)
public class IntlLduReportLogServiceImpl extends ServiceImpl<IntlLduReportLogMapper, IntlLduReportLog> implements IntlLduReportLogService {

    // 常量定义
    private static final String POSITION_TYPE_LDU = "LDU";
    private static final String EMPTY_STRING = "";
    private static final Integer NOT_DELETED = 1;
    private static final String SELECT_FIELDS = "DISTINCT create_user_id, create_user_name, position_code, position_name, " +
            "store_code, store_name, retailer_code, retailer_name, country_code";
    private static final int BATCH_SIZE = 500;

    @Resource
    private FdsService fdsService;

    @Resource
    private IntlLduReportLogMapper statisticsMapper;

    @Resource
    private NrJobTaskUtils nrJobTaskUtils;

    @Resource
    private IntlLduSnImeiService intlLduSnImeiService;

    @Resource
    private IntlLduSnService intlLduSnService;

    @Resource
    private IntlRmsPositionMapper intlRmsPositionMapper;

    @Resource
    private IntlRmsStoreMapper intlRmsStoreMapper;

    @Resource
    private IntlRmsCountryTimezoneMapper intlRmsCountryTimezoneMapper;

    @Resource
    private IntlFileReportRelMapper intlFileReportRelMapper;

    @Resource
    private IntlFileUploadMapper intlFileUploadMapper;

    @Resource
    private IntlLduTargetMapper intlLduTargetMapper;

    @Resource
    private LduConfig lduConfig;

    @Resource
    private IProductQueryService productQueryService;

    @Resource
    private JobTriggerHelper jobTriggerHelper;

    @Autowired
    private IntlFileUploadService fileUploadService;

    @Autowired
    private InspectionRecordMapper inspectionRecordMapper;

    @Resource
    private UserService userService;

    @Resource
    private IntlLduInspectionInfoMapper intlLduInspectionInfoMapper;

    @Autowired
    private InspectionRecordReadMapper inspectionRecordReadMapper;

    @Resource
    private IntlPositionApiService intlPositionApiService;

    /**
     * 列表
     *
     * @param query
     * @return
     */
    @Override
    public CommonResponse<Page<IntlLduReportLogDto>> pageList(IntlLduReportReq query) {
        log.debug("LDU reporting-pageList: {}", JSONUtil.toJsonStr(query));
        query.setOffset((query.getPageNum() - 1) * query.getPageSize());

        String areaId = RpcContext.getContext().getAttachment("$area_id");

        if (CollUtil.isEmpty(query.getCountryCode())
                && StrUtil.isNotBlank(areaId) && !"GLOBAL".equals(areaId)) {
            query.setCountryCode(Collections.singletonList(areaId));
        }

        if (CharSequenceUtil.isNotBlank(query.getProductLine())) {
            ProductLineDto productLineDto = productQueryService.getProductLineById(query.getProductLine());
            query.setProductLine(productLineDto.getEnName());
        }

        List<IntlLduReportLog> intlLduReportLogs = statisticsMapper.pageList(query);
        int count = statisticsMapper.pageListCount(query);

        return new CommonResponse<>(getIntlLduReportLogDtoPage(query, intlLduReportLogs, count));
    }

    @Override
    public CommonApiResponse<Page<IntlLduReportLogDto>> historyList(IntlLduReportReq query) {
        log.debug("LDU reporting-historyList: {}", JSONUtil.toJsonStr(query));

        query.setOffset((query.getPageNum() - 1) * query.getPageSize());

        if (CharSequenceUtil.isNotBlank(query.getProductLine())) {
            ProductLineDto productLineDto = productQueryService.getProductLineById(query.getProductLine());
            query.setProductLine(productLineDto.getEnName());
        }

        List<IntlLduReportLog> intlLduReportLogs = statisticsMapper.historyList(query);
        int count = statisticsMapper.historyListCount(query);

        return new CommonApiResponse<>(getIntlLduReportLogDtoPage(query, intlLduReportLogs, count));
    }

    @Override
    public CommonResponse<InspectionLduReportDto> inspectionDetail(InspectionReq inspectionReq) {

        InspectionLduReportDto inspectionLduReportDto = statisticsMapper.inspectionDetail(inspectionReq);

        if (null == inspectionLduReportDto) {
            return CommonResponse.failure(ResultCodeEnum.DATA_NOT_FOUND.getCode(), ResultCodeEnum.DATA_NOT_FOUND.getEnMsg());
        }

        boolean needImage = setHasImage(inspectionLduReportDto.getCountryCode());
        inspectionLduReportDto.setHasImage(needImage ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode());

        List<ReportLogDto> reportLogs = inspectionLduReportDto.getReportLogs();


        if (CollectionUtils.isNotEmpty(reportLogs)) {
            reportLogs.forEach(reportLogDto -> {
                List<String> fdsUrlList = intlFileReportRelMapper.selectByReportId(reportLogDto.getLduReportLogId());
                reportLogDto.setFdsUrlList(fdsUrlList);

                IntlLduInspectionInfoDTO inspectionInfo = intlLduInspectionInfoMapper
                        .selectByReportId(reportLogDto.getLduReportLogId(),
                                inspectionLduReportDto.getInspectionRecordId());
                if (null != inspectionInfo) {
                    reportLogDto.setBusinessStatusCode(inspectionInfo.getBusinessStatusCode());
                    reportLogDto.setInspectionUrlList(inspectionInfo.getInspectionUrlList());
                    reportLogDto.setReportDistance(inspectionLduReportDto.getReportDistance());
                    inspectionLduReportDto.setCreatedTime(inspectionInfo.getCreateTime());
                }
            });
        }

        timeZoneConvertInspectionDetail(inspectionLduReportDto, inspectionLduReportDto.getCountryCode());

        return new CommonResponse<>(inspectionLduReportDto);
    }

    private boolean setHasImage(String countryCode) {
        String countryListStr = lduConfig.getList();
        List<String> countryList = new ArrayList<>();

        if (StrUtil.isNotBlank(countryListStr)) {
            countryList = Arrays.stream(countryListStr.split(","))
                    .map(String::trim)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toList());
        }
        countryCode = countryCode != null ? countryCode : null;

        return countryList.isEmpty() || !countryList.contains(countryCode);


    }

    private Map<String, List<String>> getPhotoMap(UploadMaterialData[] uploadData) {
        List<String> guids = new ArrayList<>();
        for (UploadMaterialData db : uploadData) {
            if (db.getMaterialValue() != null && StringUtils.isNotBlank(db.getMaterialValue().getGuid())) {
                guids.add(db.getMaterialValue().getGuid());
            }
        }
        return fileUploadService.getUrlsByModuleAndGuids(FileUploadEnum.POSITION_INSPECTION, guids);
    }

    private MaterialPhotoGroup processUploadDataImages(MaterialPhotoGroup materialValue,
                                                       Map<String, List<String>> phontMap) {
        if (materialValue == null) {
            return null;
        }
        MaterialPhotoGroup result = new MaterialPhotoGroup();
        result.setName(materialValue.getName());
        result.setGuid(materialValue.getGuid());
        result.setImages(materialValue.getImages());
        if (phontMap != null && phontMap.containsKey(materialValue.getGuid())) {
            result.setImages(phontMap.get(materialValue.getGuid()));
        }
        return result;
    }

    private Page<IntlLduReportLogDto> getIntlLduReportLogDtoPage(IntlLduReportReq query, List<IntlLduReportLog> intlLduReportLogs, int count) {
        Page<IntlLduReportLogDto> pageDTO = new Page<>(query.getPageNum(), query.getPageSize());

        if (CollectionUtils.isEmpty(intlLduReportLogs)) {
            pageDTO.setCurrent(query.getPageNum());
            pageDTO.setSize(query.getPageSize());
            return pageDTO;
        }

        Set<String> storeCodeSet = intlLduReportLogs.stream()
                .filter(record -> StrUtil.isNotBlank(record.getStoreCode()))
                .map(IntlLduReportLog::getStoreCode)
                .collect(Collectors.toSet());

        Map<String, String> storeGradeMap = getStoreByStoreCodeBatch(storeCodeSet);

        List<IntlLduReportLogDto> list = ComponentLocator.getConverter().convertList(intlLduReportLogs, IntlLduReportLogDto.class);

        list.forEach(dto -> {
            dto.setDisplayStatusName(DisplayStatusEnum.fromCode(dto.getDisplayStatus()).getZhDesc());
            dto.setPlanStatusName(YesOrNoEnum.getByCode(dto.getPlanStatus()).getDesc());
            dto.setMishowStatusName(null != dto.getMishowStatus() ? YesOrNoEnum.getByCode(dto.getMishowStatus()).getDesc() : "");
            List<IntlFileUploadDto> fileUploadList = dto.getFileUploadList();
            if (CollUtil.isNotEmpty(fileUploadList)) {
                dto.setImageUrlList(fileUploadList.stream()
                        .map(IntlFileUploadDto::getFdsUrl)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList()));
            }
            if (CollUtil.isNotEmpty(dto.getImageUrlList())) {
                dto.setImageUrl(String.join(",", dto.getImageUrlList()));
            }
            if (CharSequenceUtil.isNotBlank(dto.getProductName())) {
                dto.setGoodsName(dto.getProductName());
            }
            if (CharSequenceUtil.isNotBlank(dto.getProductId())) {
                dto.setGoodsId(dto.getProductId());
            }

            timeZoneConvert(dto, dto.getCountryCode());
            if (Objects.equals(dto.getMishowStatus(), YesOrNoEnum.NO.getCode())) {
                dto.setReportLatestTime("");
            }

            if (CollUtil.isNotEmpty(storeGradeMap)) {
                dto.setGradeName(StrUtil.isNotBlank(storeGradeMap.get(dto.getStoreCode()))
                        ? storeGradeMap.get(dto.getStoreCode()) : ConstantsEnum.BLANK.getEnDesc());

                String gradeValue = storeGradeMap.get(dto.getStoreCode());
                dto.setGradeName((StrUtil.isNotBlank(gradeValue) && !"None".equals(gradeValue))
                        ? gradeValue
                        : ConstantsEnum.BLANK.getEnDesc());

            }
        });
        pageDTO.setTotal(count);
        pageDTO.setRecords(list);
        return pageDTO;
    }


    public Map<String, String> getStoreByStoreCodeBatch(Set<String> storeCodeSet) {
        Map<String, String> storeGradeMap = null;
        List<StoreGradeInfoDTO> result = new ArrayList<>();
        List<String> storeCodeList = new ArrayList<>(storeCodeSet);

        // 分批查询
        for (int i = 0; i < storeCodeSet.size(); i += BATCH_SIZE) {
            int end = Math.min(i + BATCH_SIZE, storeCodeList.size());
            List<String> batchCodes = storeCodeList.subList(i, end);

            // 查询当前批次的数据
            List<StoreGradeInfoDTO> batchResult = intlRmsStoreMapper.getStoreByStoreCode(batchCodes);
            result.addAll(batchResult);
        }

        if (CollUtil.isNotEmpty(result)) {
            storeGradeMap = result.stream()
                    .collect(Collectors.toMap(
                            StoreGradeInfoDTO::getCrssCode,
                            StoreGradeInfoDTO::getGradeName,
                            (existing, replacement) -> existing
                    ));
        }
        return storeGradeMap;
    }

    private static void timeZoneConvert(IntlLduReportLogDto item, String areaCode) {
        // 处理创建时间
        item.setReportCreateTime(formatTimeWithArea(item.getCreateTime(), areaCode));

        // 处理更新时间
        item.setReportUpdateTime(formatTimeWithArea(item.getUpdateTime(), areaCode));

        // 处理最新时间
        if (item.getLatestTime() != null) {
            long latestTimeMillis = item.getLatestTime().getTime();
            item.setReportLatestTime(formatTimeWithArea(latestTimeMillis, areaCode));
        } else {
            item.setReportLatestTime("");
        }
    }

    /**
     * 根据区域代码格式化时间戳
     *
     * @param timestamp 时间戳（毫秒）
     * @param areaCode  区域代码
     * @return 格式化后的时间字符串
     */
    private static String formatTimeWithArea(Long timestamp, String areaCode) {
        if (timestamp == null || timestamp == 0) {
            return "";
        }

        if (StringUtils.isNotEmpty(areaCode) && !"GLOBAL".equals(areaCode)) {
            return Area.of(areaCode).timeFormat(timestamp, "yyyy-MM-dd HH:mm:ss");
        } else {
            return DateTimeUtil.formatTimestamp(timestamp);
        }
    }


    private static void timeZoneConvertInspectionDetail(InspectionLduReportDto item, String areaCode) {
        // 处理创建时间
        String formatDate = "yyyy-MM-dd";
        item.setPeriodStartTimeStampText(formatTimeWithArea(item.getPeriodStartTimeStamp(), areaCode, formatDate));

        // 处理更新时间
        item.setPeriodEndTimeStampText(formatTimeWithArea(item.getPeriodEndTimeStamp(), areaCode, formatDate));

        item.setCreatedTimeText(formatTimeWithArea(item.getCreatedTime(), areaCode, "yyyy-MM-dd HH:mm:ss"));
    }

    /**
     * 根据区域代码格式化时间戳
     *
     * @param timestamp  时间戳（毫秒）
     * @param areaCode   区域代码
     * @param formatDate
     * @return 格式化后的时间字符串
     */
    private static String formatTimeWithArea(Long timestamp, String areaCode, String formatDate) {
        if (timestamp == null || timestamp == 0) {
            return "";
        }

        if (StringUtils.isNotEmpty(areaCode) && !"GLOBAL".equals(areaCode)) {
            return Area.of(areaCode).timeFormat(timestamp, formatDate);
        } else {
            return DateTimeUtil.formatTimestamp(timestamp);
        }
    }

    /**
     * 详情
     *
     * @param query
     * @return
     */
    @Override
    public CommonResponse<IntlLduReportLogDto> detail(IntlLduReportReq query) {
        Long id = query.getId();
        if (null == id) {
            return CommonResponse.failure(ResultCodeEnum.PARAM_ERROR.getCode(), "id cannot be empty");
        }

        QueryWrapper<IntlLduReportLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", id);
        IntlLduReportLog intlLduReportLog = this.getOne(queryWrapper);
        if (null == intlLduReportLog) {
            return CommonResponse.failure(ResultCodeEnum.PARAM_ERROR.getCode(), "The corresponding data does not exist.");
        }
        List<IntlFileUpload> intlFileUploads = intlFileUploadMapper
                .selectByBusyIdAndMoudle(intlLduReportLog.getReportId(), FileUploadEnum.LDU_UPLOAD.getCode());
        IntlLduReportLogDto dto = ComponentLocator.getConverter().convert(intlLduReportLog, IntlLduReportLogDto.class);
        if (CollUtil.isNotEmpty(intlFileUploads)) {
            List<IntlFileUploadDto> convertList = ComponentLocator.getConverter()
                    .convertList(intlFileUploads, IntlFileUploadDto.class);
            dto.setFileUploadList(convertList);
        }

        timeZoneConvert(dto, dto.getCountryCode());
        return new CommonResponse<>(dto);
    }

    /**
     * 修改
     *
     * @param query
     * @return
     */
    @Override
    public CommonResponse<String> update(IntlLduReportReq query) {
        log.debug("LDU reporting-update: {}", JSONUtil.toJsonStr(query));
        Long id = query.getId();
        Integer modificationReason = query.getModificationReason();
        if (null == id) {
            return CommonResponse.failure(ResultCodeEnum.PARAM_ERROR.getCode(), "id id cannot be empty");
        }

        if (null == modificationReason) {
            return CommonResponse.failure(ResultCodeEnum.PARAM_ERROR.getCode(), "Modification reason cannot be empty");
        }
        IntlLduReportLog intlLduReportLog = new IntlLduReportLog();
        intlLduReportLog.setId(id);
        intlLduReportLog.setDisplayStatus(modificationReason);
        intlLduReportLog.setUpdateTime(System.currentTimeMillis());
        intlLduReportLog.setUpdateUserId(RpcContext.getContext().getAttachments().get("$upc_miID"));
        intlLduReportLog.setUpdateUserName(RpcContext.getContext().getAttachments().get("$upc_userName"));

        this.updateById(intlLduReportLog);

        return new CommonResponse<>("success");
    }

    @Override
    public CommonResponse<String> excelExport(IntlLduReportReq query) {

        log.debug("LDU reporting-excelExport: {}", JSONUtil.toJsonStr(query));
        String upcAccount = RpcContext.getContext().getAttachment(LduCommonConfigEnum.UPC_ACCOUNT.getCode());

        String language = RpcContext.getContext().getAttachment(LduCommonConfigEnum.LANGUAGE.getCode());

        File tempFile = null;
        ExcelWriter excelWriter = null;
        try {
            tempFile = File.createTempFile(ExportExcelEnum.LDU_REPORT.getExcelName(), ".xlsx");

            if (LanguageEnum.EN_US.getCode().equals(language)) {
                excelWriter = EasyExcel.write(tempFile, IntlLduEnReportExport.class)
                        .registerWriteHandler(new ExportExcelAspect.AdaptiveWidthStyleStrategy()).build();
            } else {
                excelWriter = EasyExcel.write(tempFile, IntlLduReportExport.class)
                        .registerWriteHandler(new ExportExcelAspect.AdaptiveWidthStyleStrategy()).build();
            }


            WriteSheet writeSheet = EasyExcel.writerSheet(ExportExcelEnum.LDU_REPORT.getExcelName()).build();

            long pageSize = 1000L;
            long currentPage = 1L;
            boolean hasNext = true;

            while (hasNext) {
                query.setPageNum(currentPage);
                query.setPageSize(pageSize);

                CommonResponse<Page<IntlLduReportLogDto>> pageCommonResponse = this.pageList(query);

                List<IntlLduReportLogDto> records = pageCommonResponse.getData().getRecords();
                if (CollUtil.isEmpty(records)) {
                    hasNext = false;
                    continue;
                }

                if (LanguageEnum.EN_US.getCode().equals(language)) {
                    List<IntlLduEnReportExport> intlLduEnReportExports = ComponentLocator.getConverter()
                            .convertList(records, IntlLduEnReportExport.class);
                    excelWriter.write(intlLduEnReportExports, writeSheet);
                } else {
                    List<IntlLduReportExport> intlLduReportExports = ComponentLocator.getConverter()
                            .convertList(records, IntlLduReportExport.class);
                    excelWriter.write(intlLduReportExports, writeSheet);
                }

                hasNext = currentPage * pageSize < pageCommonResponse.getData().getTotal();
                currentPage++;
            }

            if (excelWriter != null) {
                excelWriter.finish();
            }

            if (currentPage == 1L) {
                return CommonResponse.failure(ResultCodeEnum.DATA_MISS.getCode(), ResultCodeEnum.DATA_MISS.getEnMsg());
            }

            String timestamp = String.valueOf(System.currentTimeMillis() / 1000L);

            FdsUploadResult upload = fdsService.upload(ExportExcelEnum.LDU_REPORT
                    .getExcelEnName() + timestamp + ".xlsx", tempFile, true);

            return nrJobForExcelExport(upload.getUrl(), upcAccount);
        } catch (Exception e) {
            log.error("LDU Reporting Statistics List Export Exception: {}", e);
            return CommonResponse.failure(ResultCodeEnum.SYSTEM_ERROR.getCode(), ResultCodeEnum.SYSTEM_ERROR.getEnMsg());
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
            if (tempFile != null && tempFile.exists()) {
                tempFile.delete();
            }
        }
    }

    private CommonResponse<String> nrJobForExcelExport(String upload, String upcAccount) {

        JobSuccessDto build = JobSuccessDto.builder().fileUrl(upload).build();

        NrJobGoIn nrJobGoIn = NrJobGoIn.builder()
                .jobKey(ExportExcelEnum.LDU_REPORT.getJobKey())
                .owner(upcAccount)
                .taskDesc(ExportExcelEnum.LDU_REPORT.getExcelEnName())
                .taskParam(JSONUtil.toJsonStr(build))
                // 链路追踪ID
                .traceId(ObjectId.next())
                .taskName(ExportExcelEnum.LDU_REPORT.getExcelName())
                .build();

        return nrJobTaskUtils.triggerNrJobTask(nrJobGoIn);
    }

    @Override
    public CommonResponse<SnImeiGoodsInfoDto> scan(ScanCodeReq scanCodeReq) {
        log.debug("scan-scanCodeReq: {}", JSONUtil.toJsonStr(scanCodeReq));
        String serialNumber = scanCodeReq.getSerialNumber();
        String positionCode = scanCodeReq.getPositionCode();
        if (CharSequenceUtil.isBlank(serialNumber)) {
            return CommonResponse.failure(ResultCodeEnum.PARAM_ERROR.getCode(), "SN/IMEI/69 code cannot be empty");
        }

        if (CharSequenceUtil.isBlank(positionCode)) {
            return CommonResponse.failure(ResultCodeEnum.PARAM_ERROR.getCode(), "Position Code cannot be empty");
        }

        StoreInfoDTO storeInfoDTO = intlRmsStoreMapper.getStoreByPositionCode(positionCode);
        IntlRmsCountryTimezone intlRmsCountryTimezone = null;
        if (null != storeInfoDTO) {
            intlRmsCountryTimezone = intlRmsCountryTimezoneMapper.selectByCountryId(storeInfoDTO.getCountryId());
        }

        SerialNumberType serialNumberType = SnImeiValidateUtil.getSerialNumberType(scanCodeReq.getSerialNumber());

        if (serialNumberType == SerialNumberType.UNKNOWN) {
            return CommonResponse.failure(ResultCodeEnum.ACTIVE.getCode(), "Serial code is not a valid SN/IMEI/69 code");
        }

        List<SnImeiValidationDto> snImeiValidationDtoList = intlLduSnImeiService.validateSnImeiInfo(convertQueryDto(Arrays.asList(serialNumber)));
        if (CollUtil.isNotEmpty(snImeiValidationDtoList) && !snImeiValidationDtoList.get(0).getIsValid().equals(Boolean.TRUE)) {
            return CommonResponse.failure(ResultCodeEnum.ACTIVE.getCode(), "Please make sure to scan any of the SN/IMEI/69 codes of the Xiaomi product");
        }


        SnImeiGoodsInfoDto goodsInfoDto = intlLduSnImeiService.getGoodsInfoBySnImei(scanCodeReq.getSerialNumber());
        if (goodsInfoDto == null) {
            goodsInfoDto = new SnImeiGoodsInfoDto();
            return new CommonResponse<>(ResultCodeEnum.DATA_NOT_FOUND.getCode(), ResultCodeEnum.DATA_NOT_FOUND.getZhMsg(), goodsInfoDto);
        }
        String sn = goodsInfoDto.getSn();
        String imei = goodsInfoDto.getImei();
        String imei2 = goodsInfoDto.getImei2();
        String code69 = goodsInfoDto.getCode69();

        if (serialNumber.equals(sn)) {
            goodsInfoDto.setSerialNumberType(SerialNumberType.SN.toString());
        } else if (serialNumber.equals(imei)) {
            goodsInfoDto.setSerialNumberType(SerialNumberType.IMEI.toString());
        } else if (serialNumber.equals(imei2)) {
            goodsInfoDto.setSerialNumberType(SerialNumberType.IMEI.toString());
        } else if (serialNumber.equals(code69)) {
            goodsInfoDto.setSerialNumberType(SerialNumberType.CODE69.toString());
        } else {
            goodsInfoDto.setSerialNumberType(SerialNumberType.UNKNOWN.toString());
        }

        if (CharSequenceUtil.isNotBlank(sn)) {
            QueryWrapper<IntlLduReportLog> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("sn", sn);
            IntlLduReportLog intlLduReportLog = this.getOne(queryWrapper);

            if (null != intlLduReportLog) {
                return new CommonResponse<>(ResultCodeEnum.REPEAT_OPERATION.getCode(), ResultCodeEnum.REPEAT_OPERATION.getEnMsg(), null);
            }
        }

        String countryListStr = lduConfig.getList();
        List<String> countryList = new ArrayList<>();

        if (StrUtil.isNotBlank(countryListStr)) {
            countryList = Arrays.stream(countryListStr.split(","))
                    .map(String::trim)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toList());
        }
        String countryCode = intlRmsCountryTimezone != null ? intlRmsCountryTimezone.getCountryCode() : null;
        if (StringUtils.isBlank(countryCode)) {
            return CommonResponse.failure(ResultCodeEnum.DATA_NOT_FOUND.getCode(), ResultCodeEnum.DATA_NOT_FOUND.getZhMsg());
        }

        boolean needImage = countryList.isEmpty() || !countryList.contains(intlRmsCountryTimezone.getCountryCode());

        goodsInfoDto.setHasImage(needImage ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode());


        log.debug("scan-goodsInfoDto: {}", JSONUtil.toJsonStr(goodsInfoDto));
        return new CommonResponse<>(ResultCodeEnum.SUCCESS.getCode(), ResultCodeEnum.SUCCESS.getZhMsg(), goodsInfoDto);
    }

    private SnImeiQueryDto convertQueryDto(List<String> snList) {
        SnImeiQueryDto snImeiQueryDto = new SnImeiQueryDto();
        snImeiQueryDto.setSnList(Lists.newArrayList());
        snImeiQueryDto.setImeiList(Lists.newArrayList());
        snImeiQueryDto.setSixNineCodeList(Lists.newArrayList());
        snList.forEach(code -> {
                    SerialNumberType serialNumberType = SnImeiValidateUtil.getSerialNumberType(code);
                    if (Objects.equals(serialNumberType, SerialNumberType.SN)) {
                        snImeiQueryDto.getSnList().add(code);
                    } else if (Objects.equals(serialNumberType, SerialNumberType.IMEI)) {
                        snImeiQueryDto.getImeiList().add(code);
                    } else if (Objects.equals(serialNumberType, SerialNumberType.CODE69)) {
                        snImeiQueryDto.getSixNineCodeList().add(code);
                    }
                }
        );
        return snImeiQueryDto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResponse<Integer> submit(IntlLduReportSubmitReq intlLduReportDataReq, RmsUserBaseDataResponse userBaseInfo) {

        List<IntlFileReportRel> intlFileReportRels = new ArrayList<>();
        List<IntlFileUpload> intlFileUploads = new ArrayList<>();

        Integer submitType = intlLduReportDataReq.getSubmitType();
        if (null == submitType) {
            return CommonResponse.failure(ResultCodeEnum.PARAM_ERROR.getCode(), "Submission type cannot be empty");
        }

        List<SnImeiGoodsInfoReq> goodsList = intlLduReportDataReq.getGoodsList();

        if (goodsList == null || goodsList.isEmpty()) {
            return CommonResponse.failure(ResultCodeEnum.PARAM_ERROR.getCode(), "Product list cannot be empty");
        }

        List<String> snList = goodsList.stream()
                .map(SnImeiGoodsInfoReq::getSn)
                .filter(Objects::nonNull)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());

        boolean hasDuplicates = snList.size() != snList.stream().distinct().count();

        if (hasDuplicates) {
            log.error("LDU reporting-submit: SN duplication:{}", JSONUtil.toJsonStr(intlLduReportDataReq.getGoodsList().stream()
                    .map(SnImeiGoodsInfoReq::getSn)
                    .collect(Collectors.toList())));
        }

        if (CollUtil.isNotEmpty(snList)) {
            QueryWrapper<IntlLduReportLog> queryWrapper = new QueryWrapper<>();
            queryWrapper.in("sn", snList);
            List<IntlLduReportLog> intlLduReportLogList = this.list(queryWrapper);

            if (CollUtil.isNotEmpty(intlLduReportLogList)) {
                return CommonResponse.failure(ResultCodeEnum.REPEAT_OPERATION.getCode(), "SN duplication");
            }
        }

        String positionCode = intlLduReportDataReq.getPositionCode();
        if (StringUtils.isBlank(positionCode)) {
            return CommonResponse.failure(ResultCodeEnum.PARAM_ERROR.getCode(), "Sales point information does not exist");
        }

        StoreInfoDTO intlRmsStore = intlRmsStoreMapper.getStoreByPositionCode(positionCode);

        IntlRmsCountryTimezone intlRmsCountryTimezone = null;
        if (null != intlRmsStore) {
            intlRmsCountryTimezone = intlRmsCountryTimezoneMapper.selectByCountryId(intlRmsStore.getCountryId());
        }

        if (null == intlRmsCountryTimezone) {
            return CommonResponse.failure(ResultCodeEnum.PARAM_ERROR.getCode(), "Country code does not have a corresponding region name");
        }

        // 上报对已经做了计划的SN修改为已上报
        List<IntlLduSnDto> intlLduSns = intlLduSnService.selectBySnAndCountryCode(goodsList, intlRmsCountryTimezone.getCountryCode());
        List<String> finalSnList = updateLduSn(intlRmsCountryTimezone.getCountryCode(), intlLduSns);
        Map<String, IntlLduSnDto> snMap = new HashMap<>(intlLduSns.size());
        if (CollUtil.isNotEmpty(intlLduSns)) {
            snMap = intlLduSns.stream()
                    .collect(Collectors.toMap(
                            IntlLduSnDto::getSn,
                            sn -> sn
                    ));
        }


        List<IntlLduSnDto> intlLduSnList = new ArrayList<>();
        List<IntlLduReportLog> intlLduReportLogs = new ArrayList<>();
        Set<String> snSet = new HashSet<>();

        try {
            for (SnImeiGoodsInfoReq goodsInfoDto : goodsList) {

                // 对sn进行去重   sn不为空并且重复的数据
                if (StrUtil.isNotBlank(goodsInfoDto.getSn()) && !snSet.add(goodsInfoDto.getSn())) {
                    continue;
                }

                IntlLduReportLog statistics = createtIntlLduReportLog(intlLduReportDataReq, userBaseInfo,
                        goodsInfoDto, intlRmsCountryTimezone, intlRmsCountryTimezone.getCountryCode(), intlRmsStore, positionCode);


                createOnlineUpload(goodsInfoDto, statistics, intlFileReportRels, intlFileUploads, userBaseInfo);

                if (finalSnList == null) {
                    finalSnList = Collections.emptyList();
                }

                createLduSnData(userBaseInfo, goodsInfoDto, finalSnList, statistics, intlLduSnList, snMap);
                intlLduReportLogs.add(statistics);

            }
        } catch (Exception e) {
            log.error("Submit LDU report failed: {}", e);
            throw new RetailRunTimeException("Submit failed: file upload failed");
        }

        try {
            if (CollUtil.isNotEmpty(intlLduReportLogs)) {
                statisticsMapper.batchInsertReportLogs(intlLduReportLogs);
                statisticReportLogs(intlLduReportLogs, userBaseInfo);
            }

            if (CollUtil.isNotEmpty(intlFileReportRels)) {
                intlFileReportRelMapper.batchInsertDatas(intlFileReportRels);
            }

            if (CollUtil.isNotEmpty(intlFileUploads)) {
                intlFileUploadMapper.batchInsertDatas(intlFileUploads);
            }
        } catch (Exception e) {
            log.error("Batch insert report logs failed:{}", e);
            throw new RetailRunTimeException("Database operation failed");
        }


        return new CommonResponse<>(ResultCodeEnum.SUCCESS.getCode());
    }


    private void statisticReportLogs(List<IntlLduReportLog> intlLduReportLogs, RmsUserBaseDataResponse userBaseInfo) {
        List<IntlLduTarget> intlLduTargetList = new ArrayList<>();
        List<StoreMetricsDto> storeMetricsDtos = statisticsMapper.statisticReportLogBatch(intlLduReportLogs);
        if (CollUtil.isNotEmpty(storeMetricsDtos)) {
            for (StoreMetricsDto statistics : storeMetricsDtos) {
                IntlLduTarget intlLduTarget = new IntlLduTarget();
                intlLduTarget.setActualCoveredStores(statistics.getActualCoveredStores());
                intlLduTarget.setActualSampleOut(statistics.getActualDisplayCount());
                intlLduTarget.setCountryCode(statistics.getCountryCode());
                intlLduTarget.setProjectCode(statistics.getProjectCode());
                intlLduTarget.setUpdateUserId(userBaseInfo.getUserId());
                intlLduTarget.setUpdateUserName(userBaseInfo.getUserAccount());
                intlLduTarget.setTargetUpdateDate(System.currentTimeMillis());
                intlLduTargetList.add(intlLduTarget);
            }
            intlLduTargetMapper.batchUpdate(intlLduTargetList);
        }
    }

    private static void createLduSnData(RmsUserBaseDataResponse userBaseInfo, SnImeiGoodsInfoReq goodsInfoDto, List<String> finalSnList,
                                        IntlLduReportLog statistics, List<IntlLduSnDto> intlLduSnList, Map<String, IntlLduSnDto> snMap) {
        // SN不在LDU 计划表里面，进行值填充，插入
        if (CollUtil.isNotEmpty(finalSnList)
                && CharSequenceUtil.isNotBlank(goodsInfoDto.getSn())
                && finalSnList.contains(goodsInfoDto.getSn())) {
            statistics.setPlanStatus(YesOrNoEnum.YES.getCode());
            if (CollUtil.isNotEmpty(snMap) && snMap.containsKey(goodsInfoDto.getSn())) {
                statistics.setLduType(snMap.get(goodsInfoDto.getSn()).getLduType());
            } else {
                statistics.setLduType(LduTypeEnum.MASS_PRODUCTION_VERSION.getType());
            }
        } else {
            statistics.setPlanStatus(YesOrNoEnum.NO.getCode());
            statistics.setLduType(LduTypeEnum.MASS_PRODUCTION_VERSION.getType());
        }
    }

    private static void createOnlineUpload(SnImeiGoodsInfoReq goodsInfoDto,
                                           IntlLduReportLog statistics,
                                           List<IntlFileReportRel> intlFileReportRels,
                                           List<IntlFileUpload> intlFileUploads,
                                           RmsUserBaseDataResponse userBaseInfo) {
        FileUploadReq uploadPhotos = goodsInfoDto.getUploadPhotos();
        if (uploadPhotos != null) {
            for (FileUploadReq.UPDetail upDetail : uploadPhotos.getUpDetails()) {
                if (StrUtil.isNotBlank(upDetail.getFdsUrl())) {
                    IntlFileUpload intlFileUpload = new IntlFileUpload();
                    BeanUtils.copyProperties(uploadPhotos, intlFileUpload);

                    intlFileUpload.setFdsUrl(upDetail.getFdsUrl());
                    intlFileUpload.setSuffix(getFileSuffix(upDetail.getFdsUrl()));
                    intlFileUpload.setIsOfflineUpload(0);
                    intlFileUpload.setModuleName(FileUploadEnum.LDU_UPLOAD.getCode());
                    intlFileUpload.setGuid(upDetail.getGuid());
                    intlFileUpload.setIsNoWatermark(0);
                    intlFileUpload.setIsUploadedToBlob(0);
                    intlFileUpload.setCreateTime(System.currentTimeMillis());
                    intlFileUpload.setUpdateTime(System.currentTimeMillis());
                    intlFileUpload.setUploaderName(userBaseInfo.getUserAccount());
                    intlFileUpload.setUploaderTime(System.currentTimeMillis());
                    intlFileUploads.add(intlFileUpload);
                }

                IntlFileReportRel intlFileReportRel = new IntlFileReportRel();

                intlFileReportRel.setReportLogId(statistics.getReportId());
                intlFileReportRel.setGuid(upDetail.getGuid());

                intlFileReportRels.add(intlFileReportRel);

            }
        }
    }

    public static String getFileSuffix(String url) {
        if (StringUtils.isBlank(url)) {
            return "";
        }

        int lastDotIndex = url.lastIndexOf(".");
        int lastSlashIndex = url.lastIndexOf("/");

        if (lastDotIndex > lastSlashIndex && lastDotIndex < url.length() - 1) {
            return url.substring(lastDotIndex + 1); // 返回类似 ".jpg"
        }

        return "";
    }

    private static IntlLduReportLog createtIntlLduReportLog(IntlLduReportSubmitReq intlLduReportDataReq,
                                                            RmsUserBaseDataResponse userBaseInfo, SnImeiGoodsInfoReq goodsInfoDto,
                                                            IntlRmsCountryTimezone intlRmsCountryTimezone, String countryCode,
                                                            StoreInfoDTO intlRmsStore, String positionCode) {
        long id = IdUtil.getSnowflakeNextId();

        IntlLduReportLog statistics = new IntlLduReportLog();

        BeanUtils.copyProperties(goodsInfoDto, statistics);

        statistics.setReportId(id);
        statistics.setRegion(intlRmsCountryTimezone.getArea());
        statistics.setRegionCode(intlRmsCountryTimezone.getAreaCode());
        statistics.setCountry(intlRmsCountryTimezone.getCountryName());
        statistics.setCountryCode(countryCode);


        if (null != intlRmsStore) {
            statistics.setRetailerCode(intlRmsStore.getRetailerIdName());
            statistics.setRetailerName(intlRmsStore.getRetailerName());
            statistics.setChannelType(intlRmsStore.getChannelTypeName());
            statistics.setStoreCode(intlRmsStore.getCode());
            statistics.setStoreName(intlRmsStore.getStoreName());
            statistics.setProvince(intlRmsStore.getProvinceName());
            statistics.setCity(intlRmsStore.getCityIdName());
            statistics.setDistrict(intlRmsStore.getCountyName());
            statistics.setPositionName(intlRmsStore.getPositionName());
        }
        statistics.setPositionCode(positionCode);

        statistics.setProductName(goodsInfoDto.getGoodsNameEn());
        statistics.setColor(goodsInfoDto.getEnglishColor());
        statistics.setProductLine(goodsInfoDto.getProductLineEn());

        statistics.setRamCapacity(goodsInfoDto.getRam());
        statistics.setRomCapacity(goodsInfoDto.getRom());
        statistics.setCode69(goodsInfoDto.getCode69());
        statistics.setImei1(goodsInfoDto.getImei());
        statistics.setProductId(goodsInfoDto.getGoodsId());
        statistics.setDisplayStatus(DisplayStatusEnum.ON_DISPLAY.getCode());
        statistics.setIsDelete(YesOrNoEnum.YES.getCode());
        statistics.setReportRole(userBaseInfo.getJobTitle());
        statistics.setReportDistance(intlLduReportDataReq.getReportDistance());

        statistics.setCreateUserId(userBaseInfo.getMiIdVirtual());
        statistics.setCreateUserName(userBaseInfo.getEnglishName());
        statistics.setUpdateUserId(userBaseInfo.getMiIdVirtual());
        statistics.setUpdateUserName(userBaseInfo.getEnglishName());
        statistics.setCreateTime(System.currentTimeMillis());
        statistics.setUpdateTime(System.currentTimeMillis());
        return statistics;
    }

    private List<String> updateLduSn(String countryCode, List<IntlLduSnDto> intlLduSns) {
        List<String> snList = null;
        if (CollUtil.isNotEmpty(intlLduSns)) {
            snList = intlLduSns.stream()
                    .map(IntlLduSnDto::getSn)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            intlLduSnService.batchUpdateBySn(intlLduSns, countryCode);
        }
        return snList;
    }

    private static void offlineeUpload(List<SnImeiGoodsInfoReq> goodsList, List<IntlFileUpload> intlFileUploads) {
        for (SnImeiGoodsInfoReq goodsInfoDto : goodsList) {

            FileUploadReq uploadPhotos = goodsInfoDto.getUploadPhotos();
            if (uploadPhotos != null) {
                for (FileUploadReq.UPDetail upDetail : uploadPhotos.getUpDetails()) {
                    IntlFileUpload intlFileUpload = new IntlFileUpload();
                    BeanUtils.copyProperties(uploadPhotos, intlFileUpload);
                    if (CharSequenceUtil.isNotBlank(upDetail.getFdsUrl())) {
                        intlFileUpload.setFdsUrl(upDetail.getFdsUrl());
                    }
                    intlFileUpload.setUpdateTime(System.currentTimeMillis());
                    intlFileUpload.setGuid(upDetail.getGuid());
                    intlFileUploads.add(intlFileUpload);
                }
            }
        }
    }

    @Override
    public CommonResponse<SnImeiGoodsInfoDto> scanBarcode(BarcodeScanReq request) {

        log.debug("IntlFileUploadServiceImpl.scanBarcode start:{}", JSONUtil.toJsonStr(request));

        try {
            Result result = BarcodeScannerUtil.decodeBarcode(request.getImageData());

            ScanCodeReq scanCodeReq = new ScanCodeReq();
            scanCodeReq.setSerialNumber(result.getText());
            scanCodeReq.setCountryCode(request.getCountryCode());
            scanCodeReq.setPositionCode(request.getPositionCode());

            log.debug("IntlFileUploadServiceImpl.scanBarcode scanCodeReq:{}", JSONUtil.toJsonStr(scanCodeReq));

            return scan(scanCodeReq);
        } catch (NotFoundException e) {
            return CommonResponse.failure(ResultCodeEnum.PARAM_ERROR.getCode(), "Barcode not found");
        } catch (Exception e) {
            return CommonResponse.failure(ResultCodeEnum.PARAM_ERROR.getCode(), "Failed to parse barcode");
        }
    }

    @Override
    public CommonResponse<StroeInfoDto> getStoreInfo(RmsPositionReq request) {
        log.debug("getStoreInfo request = {}", JSONUtil.toJsonStr(request));
        if (StrUtil.isEmpty(request.getPositionCode())) {
            throw new RetailRunTimeException("Sales point information does not exist");
        }
        IntlRmsStore storeInfo = intlRmsPositionMapper.getStoreInfo(request);

        if (storeInfo == null) {
            throw new RetailRunTimeException("Sales point information does not exist");
        }
        StroeInfoDto storeInfoDto = new StroeInfoDto();
        storeInfoDto.setChannelType(storeInfo.getChannelType());
        storeInfoDto.setChannelTypeDesc(storeInfo.getChannelTypeName());
        storeInfoDto.setStoreName(storeInfo.getName());
        storeInfoDto.setStoreId(storeInfo.getStoreId());
        return new CommonResponse<>(storeInfoDto);
    }

    private BufferedImage preprocessImage(BufferedImage original) {
        // 图像预处理逻辑（如转为灰度、调整对比度等）
        BufferedImage processed = new BufferedImage(original.getWidth(), original.getHeight(), BufferedImage.TYPE_BYTE_GRAY);
        processed.getGraphics().drawImage(original, 0, 0, null);
        return processed;
    }

    @Override
    public CommonResponse<List<String>> getDistinctProjectCodes() {
        try {
            log.info("开始查询所有不重复的 project_code");
            List<String> distinctProjectCodes = statisticsMapper.selectDistinctProjectCodes();
            log.info("查询完成，共找到 {} 个不重复的 project_code", distinctProjectCodes.size());
            return new CommonResponse<>(distinctProjectCodes);
        } catch (Exception e) {
            log.error("查询不重复的 project_code 时发生错误", e);
            return CommonResponse.failure(ResultCodeEnum.SYSTEM_ERROR.getCode(), "查询 project_code 失败: " + e.getMessage());
        }
    }

    @Override
    public List<BusinessDataResponse> getUserPositionsByProjectCountry(List<String> projects, String region) {
        log.info("getUserPositionsByProjectCountry start - projects: {}, region: {}", projects, region);
        //userService.getUserPositionsWithStoreFilter(request);
        return Optional.ofNullable(projects)
                .filter(CollUtil::isNotEmpty)
                .flatMap(p -> Optional.ofNullable(region)
                        .filter(StrUtil::isNotBlank)
                        .map(r -> processUserPositions(p, r)))
                .orElseGet(() -> {
                    log.warn("getUserPositionsByProjectCountry - 参数为空，projects: {}, region: {}", projects, region);
                    return new ArrayList<>();
                });
    }

    @Override
    public CommonResponse<Map<String, CoverageStatisticsResultDto>> getTaskTypeSummary(IntInspectionReq query) {
        log.info("LDU inspection getTaskTypeSummary: {}", JSONUtil.toJsonStr(query));

        if (ObjUtil.isNull(query.getTaskStartTime()) || ObjUtil.isNull(query.getTaskEndTime())) {
            return CommonResponse.failure(ResultCodeEnum.PARAM_ERROR.getCode(), "Start time or end time cannot be empty");
        }

        String areaId = RpcContext.getContext().getAttachment("$area_id");

        if (CollUtil.isEmpty(query.getCountryCode())
                && StrUtil.isNotBlank(areaId) && !"GLOBAL".equals(areaId)) {
            query.setCountryCode(Collections.singletonList(areaId));
        }

        List<CoverageStatisticsDto> taskTypeSummary = inspectionRecordReadMapper.getTaskTypeSummary(query);
        log.info("LDU inspection taskTypeSummary: {}", JSONUtil.toJsonStr(taskTypeSummary));

        Map<String, CoverageStatisticsResultDto> resultMap = Collections.emptyMap();

        if (CollUtil.isNotEmpty(taskTypeSummary)) {
            resultMap = taskTypeSummary.stream()
                    .collect(Collectors.groupingBy(
                            CoverageStatisticsDto::getTaskType,
                            Collectors.collectingAndThen(
                                    Collectors.toList(),
                                    this::aggregateCoverageStatistics
                            )
                    ));
        }


        coverageStatistics(resultMap);

        Map<String, CoverageStatisticsResultDto> statisticsResultDtoMap = replaceKeys(resultMap);
        log.info("任务类型统计结果: {}", JSONUtil.toJsonStr(statisticsResultDtoMap));
        return new CommonResponse<>(statisticsResultDtoMap);
    }

    private void coverageStatistics(Map<String, CoverageStatisticsResultDto> resultMap) {
        if (CollUtil.isNotEmpty(resultMap)) {
            resultMap.forEach((taskType, result) -> {
                List<IntlRmsPositionDto> intlRmsPositionDtos = result.getIntlRmsPositionDtos();
                result.setTaskType(ObjUtil.isNotNull(InspectionType.getByCode(taskType)) ?
                        InspectionType.getByCode(taskType).getEnName() : taskType);
                if (TaskTypeEnum.PRICE_TAG.getCode().toString().equals(taskType)) {
                    processVerificationStatistics(result, intlRmsPositionDtos);
                    coverageStatisticResult(result, intlRmsPositionDtos, StatisticType.BASE);
                } else {
                    coverageStatisticResult(result, intlRmsPositionDtos, StatisticType.BASE);
                }
            });
        }
    }

    private void processVerificationStatistics(CoverageStatisticsResultDto result, List<IntlRmsPositionDto> intlRmsPositionDtos) {
        List<IntlRmsPositionDto> verifiedMatchedPositions = intlRmsPositionDtos.stream()
                .filter(position -> position.getVerifyStatus().equals(InspectionStatusEnum.VERIFICATION_PASSED.getCode()) ||
                        position.getVerifyStatus().equals(InspectionStatusEnum.VERIFICATION_FAILED.getCode()))
                .collect(Collectors.toList());

        List<IntlRmsPositionDto> passMatchedPositions = intlRmsPositionDtos.stream()
                .filter(position -> position.getVerifyStatus().equals(InspectionStatusEnum.VERIFICATION_PASSED.getCode()))
                .collect(Collectors.toList());
        coverageStatisticResult(result, verifiedMatchedPositions, StatisticType.VERIFICATION);
        coverageStatisticResult(result, passMatchedPositions, StatisticType.VERIFICATION_PASS);

        result.setIntlRmsPositionDtos(null);
    }

    /**
     * 统计结果
     *
     * @param result
     * @param intlRmsPositionDtos
     * @param statisticType       null : 代表统计任务类型   verification : 代表统计任务类型下的国家已经审核的  verificationPass: 代表统计任务类型下的国家已经审核通过
     */
    private void coverageStatisticResult(CoverageStatisticsResultDto result,
                                         List<IntlRmsPositionDto> intlRmsPositionDtos,
                                         StatisticType statisticType) {
        // 参数校验
        if (result == null || intlRmsPositionDtos == null) {
            log.warn("参数为空，result: {}, intlRmsPositionDtos: {}", result, intlRmsPositionDtos);
            return;
        }

        List<RmsPositionInfoRes> rmsPositionInfoRes = getRmsPositionInfoRes(intlRmsPositionDtos);

        // 基础统计逻辑
        if (StatisticType.BASE == statisticType) {
            List<RmsPositionInfoRes> bestPositions = intlPositionApiService.getBestPositions(rmsPositionInfoRes);
            if (CollUtil.isNotEmpty(bestPositions)) {
                log.info("阵地最高级返回size:{},taskType:{}", bestPositions.size(), result.getTaskType());
                if (InspectionType.PRICE_TAG.getEnName().equals(result.getTaskType())) {
                    result.setCompletionQty(BigDecimal.valueOf(bestPositions.size()));
                }
                if (ObjUtil.isNotNull(result.getTargetQty()) && result.getTargetQty().compareTo(BigDecimal.ZERO) > 0) {
                    result.setCompletionQty(BigDecimal.valueOf(bestPositions.size()));
                    result.setCompletionRate(
                            result.getCompletionQty().divide(result.getTargetQty(), 4, RoundingMode.HALF_UP)
                                    .multiply(BigDecimal.valueOf(100))
                    );
                }
            }
            result.setIntlRmsPositionDtos(Collections.emptyList());
        }
        // 审核数
        else if (StatisticType.VERIFICATION == statisticType) {
            List<RmsPositionInfoRes> verifiedBestPositions = intlPositionApiService.getBestPositions(rmsPositionInfoRes);
            if (CollUtil.isNotEmpty(verifiedBestPositions)) {
                result.setVerifiedQty(BigDecimal.valueOf(verifiedBestPositions.size()));
            }
            result.setVerifiedIntlRmsPositionDtos(Collections.emptyList());
        }
        // 审核通过数量
        else if (StatisticType.VERIFICATION_PASS == statisticType) {
            List<RmsPositionInfoRes> passBestPositions = intlPositionApiService.getBestPositions(rmsPositionInfoRes);
            if (CollUtil.isNotEmpty(passBestPositions)) {
                result.setPassedQty(BigDecimal.valueOf(passBestPositions.size()));
            }
            result.setPassIntlRmsPositionDtos(Collections.emptyList());
        }

        // 计算通过率
        calculatePassedRate(result);
    }

    /**
     * 计算通过率
     */
    private void calculatePassedRate(CoverageStatisticsResultDto result) {
        if (result.getVerifiedQty() != null && result.getPassedQty() != null &&
                result.getVerifiedQty().compareTo(BigDecimal.ZERO) > 0) {
            result.setPassedRate(
                    result.getPassedQty().divide(result.getVerifiedQty(), 4, RoundingMode.HALF_UP)
                            .multiply(BigDecimal.valueOf(100))
            );
        }
    }

    public static Map<String, CoverageStatisticsResultDto> replaceKeys(Map<String, CoverageStatisticsResultDto> originalMap) {
        Map<String, CoverageStatisticsResultDto> newMap = new HashMap<>();
        originalMap.forEach((taskType, result) -> {
            InspectionType type = InspectionType.getByCode(taskType);
            if (type != null) {
                BigDecimal targetQty = result.getTargetQty();
                if (targetQty.compareTo(BigDecimal.ZERO) <= 0) {
                    result.setCompletionQty(BigDecimal.valueOf(0));
                }
                newMap.put(type.getEnName(), result); // 替换为英文标识
            }
        });
        return newMap;
    }


    @Override
    public CommonResponse<PageResponse<CoverageStatisticsResultDto>> getInspectionStatsByCountry(IntInspectionReq query) {
        log.info("LDU inspection query: {}", JSONUtil.toJsonStr(query));

        if (ObjUtil.isNull(query.getTaskStartTime()) || ObjUtil.isNull(query.getTaskEndTime())) {
            return CommonResponse.failure(ResultCodeEnum.PARAM_ERROR.getCode(), "Start time or end time cannot be empty");
        }

        String areaId = RpcContext.getContext().getAttachment("$area_id");

        if (CollUtil.isEmpty(query.getCountryCode())
                && StrUtil.isNotBlank(areaId) && !"GLOBAL".equals(areaId)) {
            query.setCountryCode(Collections.singletonList(areaId));
        }

        PageResponse<CoverageStatisticsResultDto> pageDTO = new PageResponse<>(0L, query.getPageNum(), query.getPageSize(), null);

        query.setOffset((query.getPageNum() - 1) * query.getPageSize());
        List<CoverageStatisticsDto> coverageStatisticsDtoList = inspectionRecordReadMapper.getInspectionStatsByCountry(query);
        log.info("LDU inspection coverageStatisticsDtoList: {}", JSONUtil.toJsonStr(coverageStatisticsDtoList));

        if (CollectionUtils.isEmpty(coverageStatisticsDtoList)) {
            return new CommonResponse<>(pageDTO);
        }

        Long count = inspectionRecordReadMapper.getInspectionStatsByCountryCount(query);


        List<IntlRmsPositionDto> inspectionByRuleIdList = inspectionRecordReadMapper.getInspectionByRuleIdList(coverageStatisticsDtoList, query);


        List<CoverageStatisticsResultDto> resultList = getCoverageStatisticsResultDtos(coverageStatisticsDtoList, inspectionByRuleIdList);

        if (CollUtil.isNotEmpty(resultList)) {
            resultList.forEach((result) -> {
                List<IntlRmsPositionDto> intlRmsPositionDtos = result.getIntlRmsPositionDtos();
                List<IntlRmsPositionDto> verifiedIntlRmsPositionDtos = result.getVerifiedIntlRmsPositionDtos();
                List<IntlRmsPositionDto> passIntlRmsPositionDtos = result.getPassIntlRmsPositionDtos();

                coverageStatisticResult(result, intlRmsPositionDtos, StatisticType.BASE);
                coverageStatisticResult(result, verifiedIntlRmsPositionDtos, StatisticType.VERIFICATION);
                coverageStatisticResult(result, passIntlRmsPositionDtos, StatisticType.VERIFICATION_PASS);
            });
        }

        pageDTO.setTotalCount(count);
        pageDTO.setList(resultList);
        return new CommonResponse<>(pageDTO);

    }

    @Override
    public CommonResponse<String> exportInspectionStatsByCountry(IntInspectionReq query) {

        log.info("excelExport query:{}", JSONUtil.toJsonStr(query));
        String upcAccount = RpcContext.getContext().getAttachment("$upc_account");

        File tempFile = null;
        ExcelWriter excelWriter = null;
        try {
            tempFile = File.createTempFile(ExportExcelEnum.INSPECTION_TASK.getExcelName(), ".xlsx");

            excelWriter = EasyExcel.write(tempFile, IntlLduEnInspectionExport.class)
                    .registerWriteHandler(new ExportExcelAspect.AdaptiveWidthStyleStrategy()).build();


            WriteSheet writeSheet = EasyExcel.writerSheet(ExportExcelEnum.INSPECTION_TASK.getExcelName()).build();

            long pageSize = 1000L;
            long currentPage = 1L;
            boolean hasNext = true;

            while (hasNext) {
                query.setPageNum(currentPage);
                query.setPageSize(pageSize);

                CommonResponse<PageResponse<CoverageStatisticsResultDto>> pageCommonResponse = this.getInspectionStatsByCountry(query);

                List<CoverageStatisticsResultDto> records = pageCommonResponse.getData().getList();
                if (CollUtil.isEmpty(records)) {
                    hasNext = false;
                    continue;
                }

                List<IntlLduEnInspectionExport> inspectionExports = ComponentLocator.getConverter()
                        .convertList(records, IntlLduEnInspectionExport.class);
                excelWriter.write(inspectionExports, writeSheet);

                hasNext = currentPage * pageSize < pageCommonResponse.getData().getTotalCount();
                currentPage++;
            }

            if (excelWriter != null) {
                excelWriter.finish();
            }

            if (currentPage == 1L) {
                return CommonResponse.failure(ResultCodeEnum.DATA_MISS.getCode(), ResultCodeEnum.DATA_MISS.getEnMsg());
            }

            String timestamp = String.valueOf(System.currentTimeMillis() / 1000L);

            FdsUploadResult upload = fdsService.upload(ExportExcelEnum.LDU_REPORT
                    .getExcelEnName() + timestamp + ".xlsx", tempFile, true);

            return nrJobForInspectionExcelExport(upload.getUrl(), upcAccount);
        } catch (Exception e) {
            log.error("Inspection Statistics List Export Exception: {}", e);
            return CommonResponse.failure(ResultCodeEnum.SYSTEM_ERROR.getCode(), ResultCodeEnum.SYSTEM_ERROR.getEnMsg());
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
            if (tempFile != null && tempFile.exists()) {
                tempFile.delete();
            }
        }
    }

    private CommonResponse<String> nrJobForInspectionExcelExport(String upload, String upcAccount) {

        JobSuccessDto build = JobSuccessDto.builder().fileUrl(upload).build();

        NrJobGoIn nrJobGoIn = NrJobGoIn.builder()
                .jobKey(ExportExcelEnum.INSPECTION_TASK.getJobKey())
                .owner(upcAccount)
                .taskDesc(ExportExcelEnum.INSPECTION_TASK.getExcelEnName())
                .taskParam(JSONUtil.toJsonStr(build))
                // 链路追踪ID
                .traceId(ObjectId.next())
                .taskName(ExportExcelEnum.INSPECTION_TASK.getExcelName())
                .build();

        return nrJobTaskUtils.triggerNrJobTask(nrJobGoIn);
    }

    @NotNull
    private List<CoverageStatisticsResultDto> getCoverageStatisticsResultDtos(List<CoverageStatisticsDto> coverageStatisticsDtoList,
                                                                              List<IntlRmsPositionDto> inspectionByRuleIdList) {
        List<CoverageStatisticsResultDto> resultList = new ArrayList<>();
        // 过滤出阵地List集合
        for (CoverageStatisticsDto coverageDto : coverageStatisticsDtoList) {
            CoverageStatisticsResultDto result = new CoverageStatisticsResultDto();
            BeanUtils.copyProperties(coverageDto, result);
            result.setProductLine(StrUtil.isNotBlank(coverageDto.getProductLine()) ? coverageDto.getProductLine() : "");
            result.setProductLineName(StrUtil.isNotBlank(coverageDto.getProductLineName()) ? coverageDto.getProductLineName() : "");


            BigDecimal coverageValue = getCoverageValueByTaskType(coverageDto.getTaskType(), coverageDto);
            result.setTargetQty(result.getTargetQty().add(coverageValue));
            result.setTaskType(ObjUtil.isNotNull(InspectionType.getByCode(coverageDto.getTaskType()))
                    ? InspectionType.getByCode(coverageDto.getTaskType()).getEnName() : coverageDto.getTaskType());
            result.setTargetType(TargetTypeEnum.getNameByCode(result.getTargetType()));

            if (TaskTypeEnum.PRICE_TAG.getCode().toString().equals(coverageDto.getTaskType())) {
                result.setTargetQty(null);
                result.setCompletionRate(null);
            }

            // 通过 taskType 和 country 进行匹配
            List<IntlRmsPositionDto> matchedPositions = inspectionByRuleIdList.stream()
                    .filter(position -> position.getTaskType().equals(coverageDto.getTaskType()) &&
                            position.getCountry().equals(coverageDto.getCountry()) &&
                            position.getProject().equals(coverageDto.getProjectCode()) &&
                            position.getProductLine().equals(coverageDto.getProductLine()) &&
                            position.getTargetType().equals(coverageDto.getTargetType()) &&
                            position.getRuleId().equals(coverageDto.getRuleId()))
                    .collect(Collectors.toList());

            List<IntlRmsPositionDto> verifiedMatchedPositions = matchedPositions.stream()
                    .filter(position -> position.getVerifyStatus().equals(InspectionStatusEnum.VERIFICATION_PASSED.getCode()) ||
                            position.getVerifyStatus().equals(InspectionStatusEnum.VERIFICATION_FAILED.getCode()))
                    .collect(Collectors.toList());

            List<IntlRmsPositionDto> passMatchedPositions = matchedPositions.stream()
                    .filter(position -> position.getVerifyStatus().equals(InspectionStatusEnum.VERIFICATION_PASSED.getCode()))
                    .collect(Collectors.toList());

            // 将匹配到的列表设置到 coverageDto 中
            result.setIntlRmsPositionDtos(matchedPositions);
            result.setVerifiedIntlRmsPositionDtos(verifiedMatchedPositions);
            result.setPassIntlRmsPositionDtos(passMatchedPositions);
            resultList.add(result);
        }
        return resultList;
    }

    private static List<RmsPositionInfoRes> getRmsPositionInfoRes(List<IntlRmsPositionDto> intlRmsPositionDtos) {
        List<RmsPositionInfoRes> rmsPositionInfoRes = ComponentLocator.getConverter().convertList(intlRmsPositionDtos, RmsPositionInfoRes.class);
        // 去重加过滤
        return new ArrayList<>(rmsPositionInfoRes.stream()
                .filter(record -> (record.getVerifyStatus() >= InspectionStatusEnum.VERIFICATION_PASSED.getCode()
                        && record.getVerifyStatus() <= InspectionStatusEnum.TO_BE_VERIFIED.getCode())
                        || InspectionStatusEnum.COMPLETED.getCode().equals(record.getVerifyStatus())
                        && InspectionFlagCompletionEnum.NORMAL.getCode().equals(record.getFlagCompletion()))
                .collect(Collectors.toMap(
                        RmsPositionInfoRes::getCode,
                        res -> res,
                        (existing, replacement) -> existing
                ))
                .values());
    }

    private CoverageStatisticsResultDto aggregateCoverageStatistics(List<CoverageStatisticsDto> dtoList) {
        if (CollUtil.isEmpty(dtoList)) {
            return new CoverageStatisticsResultDto();
        }

        String taskType = dtoList.get(0).getTaskType();

        CoverageStatisticsResultDto result = new CoverageStatisticsResultDto();
        result.setIntlRmsPositionDtos(new ArrayList<>());
        result.setTaskType(dtoList.get(0).getTaskType());

        for (CoverageStatisticsDto dto : dtoList) {
            BigDecimal coverageValue = getCoverageValueByTaskType(taskType, dto);
            result.setTargetQty(result.getTargetQty().add(coverageValue));
            result.getIntlRmsPositionDtos().addAll(dto.getIntlRmsPositionDtos());
        }
        return result;
    }

    private BigDecimal getCoverageValueByTaskType(String taskType, CoverageStatisticsDto dto) {
        InspectionType type = InspectionType.getByCode(taskType);
        if (type != null) {
            switch (type) {
                case DUMMY: //  "401"
                    return dto.getDummyStoreCoverage();
                case POSM: //  "501"
                    return dto.getPosmStoreCoverage();
                case PRICE_TAG: //  "601"
                    return dto.getPriceTagCoverageTarget();
                case LDU: //  "701"
                    return dto.getLduStoreCoverage();
                default:
                    return BigDecimal.ZERO;
            }
        }
        return BigDecimal.ZERO;
    }

    /**
     * 处理用户阵地信息查询逻辑
     *
     * @param projects 项目代码列表
     * @param region   地区代码
     * @return 用户阵地信息列表
     */
    private List<BusinessDataResponse> processUserPositions(List<String> projects, String region) {
        try {
            List<IntlLduReportLog> reportLogs = queryReportLogs(projects, region);

            if (CollUtil.isEmpty(reportLogs)) {
                log.info("getUserPositionsByProjectCountry - 未找到符合条件的记录，projects: {}, region: {}", projects, region);
                return new ArrayList<>();
            }

            Set<String> uniquePositionCodes = reportLogs.stream()
                    .map(IntlLduReportLog::getPositionCode)
                    .filter(StrUtil::isNotBlank)
                    .collect(Collectors.toSet());

            log.info("从reportLogs中提取到 {} , {} 个不重复的positionCode",
                    region, uniquePositionCodes.size());

            // 调用userService.getUserPositionsWithStoreFilter方法筛选匹配的数据
            return getUserPositionsWithFilter(region, uniquePositionCodes);

        } catch (Exception e) {
            log.error("getUserPositionsByProjectCountry error - projects: {}, region: {}", projects, region, e);
            return new ArrayList<>();
        }
    }

    /**
     * 查询LDU上报记录
     *
     * @param projects 项目代码列表
     * @param region   地区代码
     * @return 上报记录列表
     */
    private List<IntlLduReportLog> queryReportLogs(List<String> projects, String region) {
        QueryWrapper<IntlLduReportLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("project_code", projects)
                .eq("country_code", region)
                .eq("is_delete", NOT_DELETED)
                .eq("display_status", DisplayStatusEnum.ON_DISPLAY.getCode())
                .select(SELECT_FIELDS);

        return this.list(queryWrapper);
    }


    /**
     * 根据countryCode和positionCode筛选用户阵地信息
     *
     * @param region        国家代码集合
     * @param positionCodes 阵地代码集合
     * @return 用户阵地信息列表
     */
    private List<BusinessDataResponse> getUserPositionsWithFilter(String region, Set<String> positionCodes) {
        List<BusinessDataResponse> allResponses = new ArrayList<>();

        // 遍历每个countryCode，调用userService.getUserPositionsWithStoreFilter
        try {
            // 构建请求参数
            BusinessDataInputRequest request = new BusinessDataInputRequest();
            request.setRegion(region);
            request.setPositionCodeList(new ArrayList<>(positionCodes));

            log.info("调用userService.getUserPositionsWithStoreFilter - countryCode: {}, positionCodes: {}",
                    region, positionCodes);

            // 调用userService方法
            List<BusinessDataResponse> responses = userService.getUserPositionsWithStoreFilter(request);

            if (CollUtil.isNotEmpty(responses)) {
                allResponses.addAll(responses);
                log.info("countryCode: {} 获取到 {} 条用户阵地信息", region, responses.size());
            } else {
                log.info("countryCode: {} 未获取到用户阵地信息", region);
            }

        } catch (Exception e) {
            log.error("调用userService.getUserPositionsWithStoreFilter失败 - countryCode: {}, error: {}",
                    region, e.getMessage(), e);
        }

        log.info("总共获取到 {} 条用户阵地信息", allResponses.size());
        return allResponses;
    }

}
