package com.mi.info.intl.retail.org.infra.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.info.intl.retail.api.front.dto.RmsPositionIAndStoreRes;
import com.mi.info.intl.retail.api.front.dto.RmsStoreInfoDto;
import com.mi.info.intl.retail.api.front.position.dto.PositionStoreInfoDTO;
import com.mi.info.intl.retail.api.front.position.dto.StoreValidationInfoDTO;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.RmsPositionReq;
import com.mi.info.intl.retail.org.infra.entity.IntlRmsPosition;
import com.mi.info.intl.retail.org.infra.entity.IntlRmsStore;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
* <AUTHOR>
* @description 针对表【intl_rms_position(RMS阵地表)】的数据库操作Mapper
* @createDate 2025-06-05 10:07:25
* @Entity generator.domain.IntlRmsPosition
*/
public interface IntlRmsPositionMapper extends BaseMapper<IntlRmsPosition> {

    IntlRmsStore getStoreInfo(@Param("rmsPosition") RmsPositionReq rmsPosition);

    List<IntlRmsPosition> selectAll(@Param("retailerCode") List<String> retailerCode);

    /**
     * 根据阵地code查询阵地和门店的联合信息
     *
     * @param positionCode 阵地编码
     * @return 阵地门店联合信息
     */
    PositionStoreInfoDTO selectPositionWithStoreByCode(@Param("positionCode") String positionCode);

    /**
     * 查询门店下所有阵地(Normal Business)
     *
     * @param storeCode 门店编码
     * @return 阵地列表
     */
    List<IntlRmsPosition> getPositionsByStoreCode(@Param("storeCode") String storeCode);

    List<RmsPositionIAndStoreRes> getPositionsByPositionIds(@Param("positionIds") List<String> positionIds);

    /**
     * 一次性查询门店信息、用户门店关系和阵地信息（用于IMEI导入校验）
     *
     * @param storeCode 门店编码
     * @param miId 用户miId
     * @return 门店校验信息，如果门店不存在或用户无权限返回null
     */
    StoreValidationInfoDTO getStoreValidationInfo(@Param("storeCode") String storeCode, @Param("miId") Long miId);

    /**
     * 检查门店是否存在
     *
     * @param storeCode 门店编码
     * @return 门店数量（>0表示存在）
     */
    RmsStoreInfoDto getStoreByCode(@Param("storeCode") String storeCode);

    /**
     * 根据阵地编码获取阵地类型名称
     *
     * @param positionCode 阵地编码
     * @return 阵地类型名称
     */
    String getPositionTypeNameByCode(@Param("positionCode") String positionCode);
}