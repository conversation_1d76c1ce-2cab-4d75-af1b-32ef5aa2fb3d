package com.mi.info.intl.retail.ldu.infra.repository.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.nacos.api.config.annotation.NacosConfigListener;
import com.alibaba.nacos.common.utils.MapUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.mi.info.intl.retail.cooperation.downloadcenter.config.LduConfig;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.ProductLineDto;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.ProductSimpleInfoDto;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.SearchProductInfoDto;
import com.mi.info.intl.retail.ldu.dto.UpcCategoryPagingQueryDto;
import com.mi.info.intl.retail.ldu.dto.UpcCategoryPagingResultDto;
import com.mi.info.intl.retail.ldu.dto.UpcGoodsInfoDto;
import com.mi.info.intl.retail.ldu.dto.UpcGoodsPagingQueryDto;
import com.mi.info.intl.retail.ldu.dto.UpcGoodsPagingResultDto;
import com.mi.info.intl.retail.ldu.dto.UpcGoodsQueryDto;
import com.mi.info.intl.retail.ldu.infra.entity.IntlRmsProduct;
import com.mi.info.intl.retail.ldu.infra.mapper.readmapper.IntlRmsProductReadMapper;
import com.mi.info.intl.retail.ldu.infra.repository.IProductQueryService;
import com.mi.info.intl.retail.utils.X5ProtocolHttpUtil;
import com.xiaomi.core.auth.x5.X5AppInfo;
import com.xiaomi.mit.common.http.Body;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 查询商品信息实现类
 *
 * <AUTHOR>
 * @date 2025/7/7 15:24
 */
@Service
public class ProductQueryServiceImpl implements IProductQueryService, InitializingBean {

    /**
     * 产品线配置map，方便按id查询
     */
    private static final Map<String, ProductLineDto> PRODUCT_LINE_MAP = Maps.newHashMap();

    /**
     * upc服务地址
     */
    @Value("${service-config.upc.url:}")
    private String url;

    /**
     * upc服务appId
     */
    @Value("${service-config.upc.appId:}")
    private String appId;

    /**
     * upc服务apiKey
     */
    @Value("${service-config.upc.appKey:}")
    private String appKey;

    @Resource
    private ObjectMapper objectMapper;

    @Resource
    private IntlRmsProductReadMapper intlRmsProductReadMapper;

    @Resource
    private LduConfig lduConfig;


    @Override
    public void afterPropertiesSet() {
        refreshProductLines();
    }

    @NacosConfigListener(dataId = "intl-retail-ldu-sg-common")
    public void refreshProductLines() {
        List<ProductLineDto> list = Lists.newArrayList();
        if (StringUtils.isBlank(lduConfig.getProductLines())) {
            return;
        }
        JSONArray jsonArray = JSONArray.parseArray(lduConfig.getProductLines());
        List<Map> dataList = jsonArray.toJavaList(Map.class);
        dataList.forEach(it -> {
            String productLine = String.valueOf(it.get("id"));
            String cnName = String.valueOf(it.get("cnName"));
            String enName = String.valueOf(it.get("enName"));
            ProductLineDto productLineDto = new ProductLineDto();
            productLineDto.setProductLine(productLine);
            productLineDto.setCnName(cnName);
            productLineDto.setEnName(enName);
            list.add(productLineDto);
        });
        if (MapUtils.isEmpty(PRODUCT_LINE_MAP)) {
            PRODUCT_LINE_MAP.putAll(list.stream().collect(Collectors.toMap(ProductLineDto::getProductLine, Function.identity())));
        }
    }

    @Override
    public List<ProductLineDto> queryProductLines() {
        return PRODUCT_LINE_MAP.values()
                .stream()
                .sorted(Comparator.comparing(it -> Integer.valueOf(Objects.nonNull(it.getProductLine()) ? it.getProductLine() : "0")))
                .collect(Collectors.toList());
    }

    @Override
    public ProductLineDto getProductLineById(String id) {
        return PRODUCT_LINE_MAP.get(id);
    }


    @Override
    public List<UpcGoodsInfoDto> queryGoodsInfos(List<String> goodsIds) {
        if (CollectionUtils.isEmpty(goodsIds)) {
            return Collections.emptyList();
        }
        UpcGoodsQueryDto queryDto = new UpcGoodsQueryDto();
        // 商品id或商品编码集合 必填
        queryDto.setQueryKey(goodsIds);
        // goods_id或者sku_code  必填
        queryDto.setQueryType("goods_id");
        // 语言（为翻译使用）
        queryDto.setLanguage("zh_CN");
        // 扩展属性id集合  必填
        List<Integer> extIds = JSON.parseArray(lduConfig.getProductExtInfoIds(), Integer.class);
        queryDto.setExtIds(extIds);
        List<?> dataList = X5ProtocolHttpUtil.doX5Post(url + "/skuBaseQuery/listExtInfoByBatchSkuOrGoodsId",
                new X5AppInfo(appId, appKey, "listExtInfoByBatchSkuOrGoodsId"), queryDto, List.class);
        if (CollectionUtils.isEmpty(dataList)) {
            return Collections.emptyList();
        }
        return dataList.stream().map(it -> objectMapper.convertValue(it, UpcGoodsInfoDto.class)).collect(Collectors.toList());
    }


    @Override
    public UpcGoodsPagingResultDto queryUpcGoodsInfosPaging(UpcGoodsPagingQueryDto pagingQueryDto) {
        if (Objects.isNull(pagingQueryDto) || Objects.isNull(pagingQueryDto.getPageSize())) {
            return new UpcGoodsPagingResultDto();
        }
        if (pagingQueryDto.getPageSize() < 1) {
            pagingQueryDto.setPageSize(1);
        }
        return X5ProtocolHttpUtil.post(url + "/skuBaseQuery/timeQuery",
                new X5AppInfo(appId, appKey, "skuBaseTimeQuery"),
                Body.json(JSONUtil.toJsonStr(pagingQueryDto)), UpcGoodsPagingResultDto.class);
    }

    @Override
    public UpcCategoryPagingResultDto queryUpcCategoryInfosPaging(UpcCategoryPagingQueryDto pagingQueryDto) {
        if (Objects.isNull(pagingQueryDto) || Objects.isNull(pagingQueryDto.getPageSize())) {
            return new UpcCategoryPagingResultDto();
        }
        if (pagingQueryDto.getPageSize() < 1) {
            pagingQueryDto.setPageSize(1);
        }
        return X5ProtocolHttpUtil.post(url + "/skuBaseCategory/getBaseCategory",
                new X5AppInfo(appId, appKey, "getBaseCategory"),
                Body.json(JSONUtil.toJsonStr(pagingQueryDto)), UpcCategoryPagingResultDto.class);
    }


    @Override
    public List<ProductSimpleInfoDto> searchProductInfoByIdOrName(SearchProductInfoDto searchProductInfoDto) {
        if (Objects.isNull(searchProductInfoDto) || StringUtils.isBlank(searchProductInfoDto.getQueryKey())) {
            return Collections.emptyList();
        }

        final String query = searchProductInfoDto.getQueryKey();
        List<IntlRmsProduct> resultList = intlRmsProductReadMapper.selectList(
                new LambdaQueryWrapper<IntlRmsProduct>()
                        .select(IntlRmsProduct::getGoodsId, IntlRmsProduct::getName)
                        .like(IntlRmsProduct::getGoodsId, query)
                        .or()
                        .like(IntlRmsProduct::getName, query)
                        .last("LIMIT 10")
        );

        List<ProductSimpleInfoDto> result = new ArrayList<>();
        for (IntlRmsProduct item : resultList) {
            ProductSimpleInfoDto productSimpleInfoDto = new ProductSimpleInfoDto();
            productSimpleInfoDto.setGoodsId(item.getGoodsId());
            productSimpleInfoDto.setGoodsName(item.getName());
            result.add(productSimpleInfoDto);
        }

        return result;
    }
}
