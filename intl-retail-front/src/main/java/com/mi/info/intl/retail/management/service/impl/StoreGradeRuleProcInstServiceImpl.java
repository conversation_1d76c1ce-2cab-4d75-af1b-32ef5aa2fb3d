package com.mi.info.intl.retail.management.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mi.info.intl.retail.constant.CommonConstant;
import com.mi.info.intl.retail.exception.BizException;
import com.mi.info.intl.retail.exception.ErrorCodes;
import com.mi.info.intl.retail.intlretail.service.api.bpm.BusinessBpmCallBack;
import com.mi.info.intl.retail.intlretail.service.api.bpm.dto.BpmCallBackParamDto;
import com.mi.info.intl.retail.intlretail.service.api.bpm.enums.BpmApproveBusinessCodeEnum;
import com.mi.info.intl.retail.intlretail.service.api.management.StoreGradeService;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.StoreGradeRuleReq;
import com.mi.info.intl.retail.management.dto.StoreGradeBatchData;
import com.mi.info.intl.retail.management.entity.CommonApproveLog;
import com.mi.info.intl.retail.management.entity.CommonChangeLog;
import com.mi.info.intl.retail.management.entity.StoreGradeRelation;
import com.mi.info.intl.retail.management.entity.StoreGradeRule;
import com.mi.info.intl.retail.management.mapper.CommonApproveLogMapper;
import com.mi.info.intl.retail.management.mapper.CommonChangeLogMapper;
import com.mi.info.intl.retail.management.mapper.StoreGradeRelationMapper;
import com.mi.info.intl.retail.management.mapper.StoreGradeRuleMapper;
import com.mi.info.intl.retail.management.rpc.MainDataRpc;
import com.mi.info.intl.retail.management.service.enums.ApprovalBusinessKeyEnum;
import com.mi.info.intl.retail.management.service.enums.ApprovalStatus;
import com.mi.info.intl.retail.management.service.enums.BpmCallBackNodeEnum;
import com.mi.info.intl.retail.management.service.enums.CompleteStatusEnum;
import com.mi.info.intl.retail.management.service.enums.ProcessReviewStatus;
import com.mi.info.intl.retail.management.service.enums.RuleStatusEnum;
import com.mi.info.intl.retail.management.service.enums.StoreGradeEnum;
import com.mi.info.intl.retail.management.service.enums.TieringModificationMethodEnum;
import com.mi.info.intl.retail.management.utils.ExcelUtil;
import com.mi.info.intl.retail.utils.JsonUtils;
import com.xiaomi.cnzone.maindataapi.model.req.org.OpInfo;
import com.xiaomi.cnzone.maindataapi.model.req.org.OrgCategory;
import com.xiaomi.cnzone.maindataapi.model.req.org.OrgMerge;
import com.xiaomi.cnzone.maindataapi.model.req.org.OrgParamEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;

@Slf4j
@Service
public class StoreGradeRuleProcInstServiceImpl implements BusinessBpmCallBack {

    @Resource
    private StoreGradeRuleMapper storeGradeRuleMapper;

    @Resource
    private CommonApproveLogMapper commonApproveLogMapper;

    @Resource
    private StoreGradeService storeGradeService;

    @Resource
    private StoreGradeRelationMapper storeGradeRelationMapper;

    @Resource
    private CommonChangeLogMapper commonChangeLogMapper;

    @Resource
    private MainDataRpc mainDataRpc;

    @Override
    public BpmApproveBusinessCodeEnum getBpmApproveTypeEnum() {
        return BpmApproveBusinessCodeEnum.STORE_GRADE_RULE;
    }

    @Override
    public void doCallback(BpmCallBackParamDto response) {
        log.info("StoreGradeRuleProcInstServiceImpl_doCallback_response:{}", response);
        // 如果状态为驳回，则进行驳回处理
        if (response.getStatus().equals(ProcessReviewStatus.REJECTED.getDescEn())) {
            rejected(response);
        } else if (response.getStatus().equals(ProcessReviewStatus.PROCESS_COMPLETED.getCode()) &&
                response.getTaskDefinitionKey().equals(
                        BpmCallBackNodeEnum.HQ_STORE_TEAM_NODE.getNode())) {
            approve(response);
        }

    }

    // 驳回处理
    private void rejected(BpmCallBackParamDto response) {
        // 修改审批记录为驳回
        CommonApproveLog commonApproveLog = getApproveLogByBusinessKey(response.getBusinessKey());
        if (ObjectUtil.isEmpty(commonApproveLog)) {
            log.info("未找到对应的审批请求记录,businessKey:{}", response.getBusinessKey());
            return;
        }
        updateStatus(response, ApprovalStatus.REJECT.getCode());
    }

    /**
     * 审批通过处理
     *
     * @param response
     */
    private void approve(BpmCallBackParamDto response) {
        CommonApproveLog commonApproveLog = getApproveLogByBusinessKey(response.getBusinessKey());
        if (ObjectUtil.isEmpty(commonApproveLog)) {
            log.info("未找到对应的审批请求记录,businessKey:{}", response.getBusinessKey());
            return;
        }
        String id = commonApproveLog.getBusinessId();
        if (StringUtils.isBlank(id)) {
            log.info("门店等级规则不能为空,flowInstId:{}", commonApproveLog.getFlowInstId());
            throw new BizException(ErrorCodes.BIZ_ERROR, "Store Grade rules ID cannot be empty");
        }
        List<Long> ids = Arrays.stream(id.split(","))
                .map(String::trim).filter(StringUtils::isNotBlank)
                .map(Long::valueOf).collect(Collectors.toList());
        List<StoreGradeRule> rulesByIds = storeGradeRuleMapper.getRulesByIds(ids);
        if (CollectionUtils.isEmpty(rulesByIds)) {
            log.info("未找到对应审批请求记录的门店等级规则,id:{}", id);
            throw new BizException(ErrorCodes.BIZ_ERROR,
                    "The store level rule for the approval request record is not found");
        }
        Integer method = rulesByIds.get(0).getMethod();
        Integer channelType = rulesByIds.get(0).getChannelType();
        updateStatus(response, ApprovalStatus.APPROVED.getCode());
        if (method.equals(TieringModificationMethodEnum.SYSTEM_CALCULATION.getKey())) {
            for (StoreGradeRule rule : rulesByIds) {
                //  当规则方式为系统计算时 触发门店等级规则计算
                StoreGradeRuleReq changeTriggerReq = new StoreGradeRuleReq();
                changeTriggerReq.setId(rule.getId());
                log.info("调用门店等级规则计算，{}", response.getBusinessKey());
                storeGradeService.ruleChangeTrigger(changeTriggerReq);
            }
        } else if (method.equals(TieringModificationMethodEnum.MANUAL_UPLOAD.getKey())) {
            // 当规则方式为手动上传时需推送数据至门店主数据
            LambdaQueryWrapper<StoreGradeRelation> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.in(StoreGradeRelation::getStoreGradeRuleId, ids);
            List<StoreGradeRelation> storeGradeRelations = storeGradeRelationMapper.selectList(queryWrapper);
            if (CollectionUtils.isEmpty(storeGradeRelations)) {
                log.info("未找到对应的上传记录,businessKey:{}", response.getBusinessKey());
                return;
            }
            List<CommonChangeLog> commonChangeLogs = new ArrayList<>();
            for (StoreGradeRelation storeGradeRelation : storeGradeRelations) {
                CommonChangeLog commonChangeLog = new CommonChangeLog();
                commonChangeLog.setBusinessKey(ApprovalBusinessKeyEnum.STORE_GRADE_RULE.getValue());
                commonChangeLog.setBusinessId(storeGradeRelation.getStoreCode());
                commonChangeLog.setStoreGrade(storeGradeRelation.getStoreGrade());
                commonChangeLog.setChannelType(channelType);
                commonChangeLog.setCompleteStatus(CompleteStatusEnum.COMPLETED.getCode());
                commonChangeLog.setChangeReason("store_grade_change");
                commonChangeLog.setStoreGradeRuleId(storeGradeRelation.getStoreGradeRuleId());
                updateStoreGrade(commonChangeLog);
                commonChangeLogs.add(commonChangeLog);
            }
            commonChangeLogMapper.batchInsert(commonChangeLogs);
        }
    }

    /**
     * 更新门店等级
     *
     * @param commonChangeLog
     */
    private void updateStoreGrade(CommonChangeLog commonChangeLog) {
        try {
            // TODO: 这里需要根据实际的业务逻辑来实现门店等级的更新
            OrgParamEntity orgParamEntity = new OrgParamEntity();
            orgParamEntity.setOpType(2);
            orgParamEntity.setSource("intl-retail");
            OpInfo opInfo = new OpInfo();
            //Optional.ofNullable(storeModel.getUpdaterMid()).ifPresent(item -> opInfo.setMid(item + ""));
            opInfo.setName(commonChangeLog.getChangeReason());

            opInfo.setDesc(commonChangeLog.getChangeReason());
            orgParamEntity.setOpInfo(opInfo);

            OrgCategory orgCategory = new OrgCategory();
            orgCategory.setOrgId(commonChangeLog.getBusinessId());
            StoreGradeEnum storeGradeEnum = StoreGradeEnum.fromValue(commonChangeLog.getStoreGrade());
            orgCategory.setStoreGrading(storeGradeEnum.getKey().byteValue());
            orgParamEntity.setOrgCategory(orgCategory);
            OrgMerge orgMerge = new OrgMerge();
            orgMerge.setStoreGradingCalcStatus(1);
            orgMerge.setOrgId(commonChangeLog.getBusinessId());
            orgParamEntity.setOrgMerge(orgMerge);
            mainDataRpc.pushEditStoreBeta(orgParamEntity);
        } catch (Exception e) {
            log.error("Failed to update store grade, store code: {}, new grade: {}", commonChangeLog.getBusinessId(),
                    commonChangeLog.getStoreGrade(), e);
            throw new BizException(ErrorCodes.BIZ_ERROR, "Failed to update store grade: " + e.getMessage());
        }
    }

    /**
     * 更新审批状态和门店规则状态
     *
     * @param status
     */
    private void updateStatus(BpmCallBackParamDto response, Integer status) {

        // 修改审批记录状态
        CommonApproveLog commonApproveLog = getApproveLogByBusinessKey(response.getBusinessKey());
        if (ObjectUtil.isEmpty(commonApproveLog)) {
            log.info("未找到对应的审批请求记录,businessKey:{}", response.getBusinessKey());
        }

        // 修改门店等级规则状态
        String id = commonApproveLog.getBusinessId();
        if (StringUtils.isBlank(id)) {
            log.info("门店等级规则不能为空,flowInstId:{}", commonApproveLog.getFlowInstId());
            throw new BizException(ErrorCodes.BIZ_ERROR, "Store Grade rules ID cannot be empty");
        }
        List<Long> ids = Arrays.stream(id.split(","))
                .map(String::trim).filter(StringUtils::isNotBlank)
                .map(Long::valueOf).collect(Collectors.toList());
        List<StoreGradeRule> rulesByIds = storeGradeRuleMapper.getRulesByIds(ids);
        if (CollectionUtils.isEmpty(rulesByIds)) {
            log.info("未找到对应审批请求记录的门店等级规则,id:{}", id);
            throw new BizException(ErrorCodes.BIZ_ERROR,
                    "The store level rule for the approval request record is not found");
        }
        Integer method = rulesByIds.get(0).getMethod();
        if (response.getStatus().equals(ProcessReviewStatus.PROCESS_COMPLETED.getDescEn()) &&
                StringUtils.isNotBlank(commonApproveLog.getTargetBody())) {
            rulesByIds = JsonUtils.parseArray(commonApproveLog.getTargetBody(), StoreGradeRule.class);
        }
        List<StoreGradeRule> upStoreGradeRules = new ArrayList<>();
        for (StoreGradeRule storeGradeRule : rulesByIds) {
            Integer ruleStatus = ApprovalStatus.APPROVED.getCode().equals(status) ? RuleStatusEnum.IN_EFFECT.getCode() :
                    storeGradeRule.getRulesStatus();
            storeGradeRule.setRulesStatus(ruleStatus);
            storeGradeRule.setApproveStatus(status);
            storeGradeRule.setApprovedTime(LocalDateTime.now());
            if (StringUtils.isBlank(storeGradeRule.getFileData())) {
                storeGradeRule.setFileData("");
            }
            upStoreGradeRules.add(storeGradeRule);
        }
        storeGradeRuleMapper.batchUpdate(upStoreGradeRules);
        // 审批时间
        commonApproveLog.setTargetBody(JsonUtils.toStr(upStoreGradeRules));
        commonApproveLog.setApproveUserId(response.getAssignee().getUserName());
        commonApproveLog.setApprovedTime(LocalDateTime.now());
        commonApproveLog.setFlowStatus(status);
        commonApproveLogMapper.updateById(commonApproveLog);

        // 如果是手动上传，驳回处理后逻辑删除数据
        if (TieringModificationMethodEnum.MANUAL_UPLOAD.getKey().equals(method)
                && response.getStatus().equals(ProcessReviewStatus.REJECTED.getDescEn())) {
            delRelation(rulesByIds, ids, commonApproveLog);

        }
    }

    private void delRelation(List<StoreGradeRule> rulesByIds, List<Long> ids, CommonApproveLog commonApproveLog) {
        try {
            String fileDataStr = rulesByIds.get(0).getFileData();
            StoreGradeRuleReq.FileData fileData = JsonUtils.toObject(fileDataStr, StoreGradeRuleReq.FileData.class);
            List<StoreGradeBatchData> dataList = ExcelUtil.parseExcelFile(fileData);
            List<String> storeCodes = dataList.stream().map(item -> item.getStoreCode()).collect(Collectors.toList());
            LambdaUpdateWrapper<StoreGradeRelation> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.in(StoreGradeRelation::getStoreGradeRuleId, ids);
            updateWrapper.in(StoreGradeRelation::getStoreCode, storeCodes);
            List<StoreGradeRelation> storeGradeRelations = storeGradeRelationMapper.selectList(updateWrapper);
            if (CollectionUtils.isNotEmpty(storeGradeRelations)) {
                List<Long> longs =
                        storeGradeRelations.stream()
                                .filter(item -> item.getApproveLogId().equals(CommonConstant.DEFAULT_LONG) ||
                                        item.getApproveLogId().equals(commonApproveLog.getId().longValue()))
                                .map(StoreGradeRelation::getId)
                                .map(Long::valueOf)
                                .collect(Collectors.toList());
                storeGradeRelationMapper.batchDeleteByIds(longs);
            }
        } catch (Exception e) {
            log.error("Failed to delete store grade rule data, businessKey: {}", commonApproveLog.getFlowInstId(), e);
        }
    }

    /**
     * 根据业务key获取审批记录
     *
     * @param businessKey
     * @return
     */
    private CommonApproveLog getApproveLogByBusinessKey(String businessKey) {
        LambdaQueryWrapper<CommonApproveLog> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(businessKey),
                CommonApproveLog::getFlowInstId, businessKey);
        queryWrapper.eq(CommonApproveLog::getFlowStatus, ApprovalStatus.IN_APPROVAL.getCode());
        CommonApproveLog commonApproveLog = commonApproveLogMapper.selectOne(queryWrapper);
        return commonApproveLog;
    }
}
