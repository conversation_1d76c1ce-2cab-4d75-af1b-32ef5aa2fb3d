package com.mi.info.intl.retail.org.infra.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.info.intl.retail.org.infra.entity.IntlInspectionRuleRelation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface IntlInspectionRuleRelationMapper extends BaseMapper<IntlInspectionRuleRelation> {

    void batchInsert(@Param("inspectionRuleRelations") List<IntlInspectionRuleRelation> inspectionRuleRelations);

    void batchUpdate(@Param("inspectionRuleRelations") List<IntlInspectionRuleRelation> inspectionRuleRelations);

    List<IntlInspectionRuleRelation> selectByRuleId(@Param("id") Long id);
}




