package com.mi.info.intl.retail.management.infra.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.github.pagehelper.PageInfo;
import com.mi.info.intl.retail.management.domain.model.req.StoreChangeLogPageReq;
import com.mi.info.intl.retail.management.domain.model.resp.StoreChangeLogPageResp;
import com.mi.info.intl.retail.management.infra.entity.StoreChangeLog;
import org.apache.ibatis.annotations.Mapper;

/**
 * 门店变更日志表Mapper接口
 */
@Mapper
public interface StoreChangeLogMapper extends BaseMapper<StoreChangeLog> {
}
