package com.mi.info.intl.retail.org.infra.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.InspectionLduReportDto;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.InspectionReq;
import com.mi.info.intl.retail.org.infra.entity.InspectionRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 巡检记录表Mapper接口
 */
@Mapper
public interface InspectionRecordMapper extends BaseMapper<InspectionRecord> {

    void batchInsert(@Param("list") List<InspectionRecord> entities);

    int updateVerifyStatus(@Param("e") InspectionRecord entity, @Param("preInspectionStatus") Integer preInspectionStatus);
    
    int updateNotFinished(@Param("e") InspectionRecord inspectionRecord);
}
