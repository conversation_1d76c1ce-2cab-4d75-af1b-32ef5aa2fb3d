package com.mi.info.intl.retail.org.infra.mapper;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.mi.info.intl.retail.exception.RetailRunTimeException;
import com.mi.info.intl.retail.core.utils.CollUtils;
import com.mi.info.intl.retail.core.utils.IntlRetailAssert;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.IntlStoreMaterialStatusConfrimReq;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.IntlStoreMaterialStatusVO;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialInspectionReq;
import com.mi.info.intl.retail.intlretail.service.api.material.enums.IntlStoreMaterialStatusCanModifyEnum;
import com.mi.info.intl.retail.intlretail.service.api.material.enums.StoreMaterialStatusCoveredEnum;
import com.mi.info.intl.retail.intlretail.service.api.material.enums.TaskTypeEnum;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.TaskCenterFinishReq;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.TaskCenterTaskReq;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.InspectionFlagCompletionEnum;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.InspectionStatusEnum;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.TaskStatusEnum;
import com.mi.info.intl.retail.org.app.service.material.convert.NewProductInspectionConvert;
import com.mi.info.intl.retail.org.domain.IntlStoreMaterialStatusDomain;
import com.mi.info.intl.retail.org.domain.MaterialInspectionDomain;
import com.mi.info.intl.retail.org.domain.PositionDomain;
import com.mi.info.intl.retail.org.domain.repository.IntlStoreMaterialStatusRepository;
import com.mi.info.intl.retail.org.domain.repository.NewProductInspectionRepository;
import com.mi.info.intl.retail.org.domain.repository.PositionRepository;
import com.mi.info.intl.retail.org.infra.entity.InspectionRecord;
import com.mi.info.intl.retail.org.infra.entity.IntlStoreMaterialStatus;
import com.mi.info.intl.retail.org.infra.rpc.TaskCenterServiceRpc;
import com.xiaomi.cnzone.proretail.newcommon.util.RetailJsonUtil;
import com.xiaomi.cnzone.storems.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import javax.validation.Valid;
import javax.validation.Validator;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Repository
@Slf4j
public class IntlStoreMaterialStatusRepositoryImpl implements IntlStoreMaterialStatusRepository {
    
    @Resource
    private IntlStoreMaterialStatusMapper intlStoreMaterialStatusMapper;
    
    @Resource
    NewProductInspectionRepository newProductInspectionRepository;
    
    @Resource
    private InspectionRecordMapper inspectionRecordMapper;
    
    @Autowired
    private TaskCenterServiceRpc taskCenterServiceRpc;
    
    @Resource
    private PositionRepository positionRepository;
    
    @Resource
    private Validator validator;
    
    @Override
    public List<IntlStoreMaterialStatusVO> findByBusinessCode(String businessCode) {
        IntlStoreMaterialStatus entity = new IntlStoreMaterialStatus();
        entity.setBusinessCode(businessCode);
        entity.setCovered(0);
        List<IntlStoreMaterialStatusVO> intlStoreMaterialStatusVOS = intlStoreMaterialStatusMapper.selectListIntlStoreMaterialStatusVO(
                entity);
        intlStoreMaterialStatusVOS.forEach(intlStoreMaterialStatusVO -> {
            intlStoreMaterialStatusVO.setName(TaskTypeEnum.getShortNameByCode(intlStoreMaterialStatusVO.getCode()));
        });
        return intlStoreMaterialStatusVOS;
    }
    
    private void validateRequiredFields(Object dto) {
        validatorHandler(validator.validate(dto));
    }
    
    /**
     * 抛出Validator处理的校验
     */
    private <T> void validatorHandler(Set<ConstraintViolation<T>> validate) {
        if (CollUtil.isNotEmpty(validate)) {
            throw new RetailRunTimeException(validate.iterator().next().getMessage());
        }
    }
    
    @Transactional
    @Override
    public int materialConfirm(@Valid List<IntlStoreMaterialStatusConfrimReq> list) {
        for (IntlStoreMaterialStatusConfrimReq intlStoreMaterialStatusConfrimReq : list) {
            validateRequiredFields(intlStoreMaterialStatusConfrimReq);
        }
        List<Long> idList = list.stream().map(IntlStoreMaterialStatusConfrimReq::getId).collect(Collectors.toList());
        
        List<IntlStoreMaterialStatus> intlStoreMaterialStatusList = intlStoreMaterialStatusMapper.selectBatchIds(
                idList);
        Map<Long, IntlStoreMaterialStatus> intlStoreMaterialStatusMap = intlStoreMaterialStatusList.stream()
                .collect(Collectors.toMap(IntlStoreMaterialStatus::getId, Function.identity()));
        int flag = 0, subFlag = 0;
        //如果list为空,抛错
        for (IntlStoreMaterialStatusConfrimReq bean : list) {
            IntlStoreMaterialStatus beforeBean = intlStoreMaterialStatusMap.get(bean.getId());
            if (beforeBean == null) {
                log.warn("IntlStoreMaterialStatusRepositoryImpl#materialConfirm 门店物料状态 未找到id为{}的记录",
                        bean.getId());
                continue;
            }
            if (!IntlStoreMaterialStatusCanModifyEnum.CAN_MODIFY.getCode().equals(beforeBean.getCanModify())) {
                log.warn("IntlStoreMaterialStatusRepositoryImpl#materialConfirm 门店物料状态 不可修改,id为{}的记录",
                        bean.getId());
                continue;
            }
            LambdaQueryWrapper<IntlStoreMaterialStatus> queryWrapper = new LambdaQueryWrapper<IntlStoreMaterialStatus>();
            queryWrapper.eq(IntlStoreMaterialStatus::getBusinessCode, bean.getBusinessCode());
            queryWrapper.eq(IntlStoreMaterialStatus::getTaskType, bean.getTaskType());
            queryWrapper.eq(IntlStoreMaterialStatus::getId, beforeBean.getId());
            queryWrapper.eq(IntlStoreMaterialStatus::getCovered, beforeBean.getCovered());
            queryWrapper.eq(IntlStoreMaterialStatus::getCanModify,
                    IntlStoreMaterialStatusCanModifyEnum.CAN_MODIFY.getCode());
            IntlStoreMaterialStatus setEntry = new IntlStoreMaterialStatus();
            setEntry.setCovered(bean.getCovered());
            log.info("IntlStoreMaterialStatusRepositoryImpl#materialConfirm IntlStoreMaterialStatus 更新门店物流状态:{}",
                    RetailJsonUtil.toJson(setEntry));
            flag = intlStoreMaterialStatusMapper.update(setEntry, queryWrapper);
            //TODO 批量IO优化-分布式事务...
            Long time = System.currentTimeMillis();
            if (flag > 0 && (bean.getCovered().equals(StoreMaterialStatusCoveredEnum.UNCOVERED.getCode())// 0:未覆盖
                    || bean.getCovered().equals(StoreMaterialStatusCoveredEnum.UNRECEIVED.getCode()) // 2:未收到
                    || bean.getCovered().equals(StoreMaterialStatusCoveredEnum.RECEIVED.getCode())) // 3:已收到
            ) {
                MaterialInspectionReq req = new MaterialInspectionReq();
                req.setBusinessCode(bean.getBusinessCode());
                req.setBusinessType(bean.getTaskType());
                // 得到相关的任务实例
                List<MaterialInspectionDomain> inspectionRecordList = null;
                
                if (StoreMaterialStatusCoveredEnum.UNCOVERED.getCode().equals(beforeBean.getCovered()) &&
                        (bean.getCovered().equals(StoreMaterialStatusCoveredEnum.UNRECEIVED.getCode())
                                || bean.getCovered().equals(StoreMaterialStatusCoveredEnum.RECEIVED.getCode()))) {
                    req.setTaskStatusEnum(TaskStatusEnum.COMPLETED);
                    req.setInspectionFlagCompletionEnum(InspectionFlagCompletionEnum.COVER_DONE);
                    inspectionRecordList = newProductInspectionRepository.getListForIntlStoreMaterialStatus(req);
                    //  - 未覆盖 -> 已覆盖(实际上是未收到-2 或者为 已收到-3) 任务重置(调用大脑) 将 已完成 -> 未完成
                    //掉大脑重置任务 对应的 阵地巡检信息表 对应的记录 要设置为 task_status 和 inspection_status 未完成 同时  task_completion_time设置为null;
                    for (MaterialInspectionDomain inspectionRecordDomain : inspectionRecordList) {
                        InspectionRecord inspectionRecord = new InspectionRecord();
                        inspectionRecord.setId(inspectionRecordDomain.getId());
                        inspectionRecord.setTaskStatus(TaskStatusEnum.NOT_COMPLETED.getCode());
                        inspectionRecord.setInspectionStatus(InspectionStatusEnum.NOT_COMPLETED.getCode());
                        inspectionRecord.setFlagCompletion(InspectionFlagCompletionEnum.NORMAL.getCode());
                        log.info("IntlStoreMaterialStatusRepositoryImpl#materialConfirm 未覆盖到已覆盖 updateNotFinished:{}",
                                RetailJsonUtil.toJson(inspectionRecord));
                        subFlag = inspectionRecordMapper.updateNotFinished(inspectionRecord);
                        if (subFlag > 0) {
                            log.info("IntlStoreMaterialStatusRepositoryImpl#materialConfirm 开始调用 大脑侧 重置任务");
                            TaskCenterTaskReq taskCenterFinishReq = new TaskCenterTaskReq();
                            taskCenterFinishReq.setMid(inspectionRecordDomain.getInspectionOwnerMiId());
                            taskCenterFinishReq.setOrgId(inspectionRecordDomain.getBusinessCode());
                            taskCenterFinishReq.setTaskBatchId(inspectionRecordDomain.getTaskBatchId());
                            taskCenterFinishReq.setTaskInstanceId(inspectionRecordDomain.getTaskInstanceId());
                            IntlRetailAssert.isTrue(inspectionRecordDomain.getTaskBatchId() != null
                                            && inspectionRecordDomain.getTaskInstanceId() != null,
                                    "IntlStoreMaterialStatusRepositoryImpl#materialConfirm Inspection record's task"
                                            + " information is incomplete, the task reset failed");
                            taskCenterServiceRpc.reloadTaskStatus(taskCenterFinishReq); // 重置任务状态
                        }
                    }
                } else if (bean.getCovered()
                        .equals(StoreMaterialStatusCoveredEnum.UNCOVERED.getCode())) { // 直接0:未覆盖 先获得 未完成的 状态为3
                    req.setTaskStatusEnum(TaskStatusEnum.NOT_COMPLETED); // 3:未完成
                    req.setInspectionFlagCompletionEnum(null);
                  
                    inspectionRecordList = newProductInspectionRepository.getListForIntlStoreMaterialStatus(req);
                    //如果直接转变为 未覆盖 需要 任务自动完成 - 调用大脑接口完成任务 将未完成 转变为已经完成
                    //调用大脑完成任务 ,对应的 阵地巡检信息表 对应的记录 task_status 设置为 完成 inspection_status 设置为 待核验;
                    //task_completion_time 当前的系统的时间.... 或者 之间调用:
                    for (MaterialInspectionDomain inspectionRecordDomain : inspectionRecordList) {
                        InspectionRecord inspectionRecord = new InspectionRecord();
                        inspectionRecord.setId(inspectionRecordDomain.getId());
                        inspectionRecord.setTaskStatus(TaskStatusEnum.COMPLETED.getCode()); // 1:已完成
                        inspectionRecord.setInspectionStatus(InspectionStatusEnum.COMPLETED.getCode()); // 5:已完成
                        inspectionRecord.setTaskCompletionTime(time);
                        inspectionRecord.setFlagCompletion(InspectionFlagCompletionEnum.COVER_DONE.getCode());
                        log.info("IntlStoreMaterialStatusRepositoryImpl#materialConfirm 直接未覆盖 updateById:{}",
                                RetailJsonUtil.toJson(inspectionRecord));
                        subFlag = inspectionRecordMapper.updateById(inspectionRecord);
                        if (subFlag > 0) {
                            log.info("IntlStoreMaterialStatusRepositoryImpl#materialConfirm 开始调用 大脑侧 完成任务");
                            // 任务中心状态变更为结束
                            TaskCenterFinishReq taskCenterFinishReq = new TaskCenterFinishReq();
                            // 查询TaskBatchId 完成任务
                            taskCenterFinishReq.setTaskBatchId(inspectionRecordDomain.getTaskBatchId());
                            taskCenterFinishReq.setMid(inspectionRecordDomain.getInspectionOwnerMiId());
                            taskCenterFinishReq.setOperatorMid(inspectionRecordDomain.getInspectionOwnerMiId());
                            // 获取阵地信息(数字门店阵地编码转RMS编码)
                            PositionDomain positionDomain = positionRepository.getByCode(
                                    inspectionRecordDomain.getBusinessCode());
                            if (positionDomain == null) {
                                log.error(
                                        "IntlStoreMaterialStatusRepositoryImpl#materialConfirm 阵地信息不存在: positionCode={}",
                                        inspectionRecordDomain.getBusinessCode());
                                throw new BusinessException("PositionDomain is empty");
                                
                            }
                            taskCenterFinishReq.setOrgId(positionDomain.getPositionCode());
                            taskCenterFinishReq.setTaskInstanceId(inspectionRecordDomain.getTaskInstanceId());
                            taskCenterServiceRpc.outerTaskFinish(taskCenterFinishReq);
                        }
                    }
                }
                
            }
        }
        return 1;
    }
    
    @Override
    public void batchSave(List<IntlStoreMaterialStatusDomain> domains) {
        if (CollectionUtils.isEmpty(domains)) {
            return;
        }
        List<IntlStoreMaterialStatus> entities = CollUtils.mapping(domains,
                NewProductInspectionConvert.INSTANCE::domainToEntity);
        Lists.partition(entities, 200).forEach(intlStoreMaterialStatusMapper::save);
    }
    
    @Override
    public List<String> findNotExistBusinessCodes(List<String> businessCodes) {
        if (CollectionUtils.isEmpty(businessCodes)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<IntlStoreMaterialStatus> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(IntlStoreMaterialStatus::getBusinessCode, businessCodes);
        queryWrapper.select(IntlStoreMaterialStatus::getBusinessCode);
        List<IntlStoreMaterialStatus> entities = intlStoreMaterialStatusMapper.selectList(queryWrapper);
        Set<String> set = CollUtils.mappingToSet(entities, IntlStoreMaterialStatus::getBusinessCode);
        return businessCodes.stream().filter(e -> !set.contains(e)).collect(Collectors.toList());
    }
}
