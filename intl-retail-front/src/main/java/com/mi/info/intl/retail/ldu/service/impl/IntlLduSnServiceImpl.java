package com.mi.info.intl.retail.ldu.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.ObjectId;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.mi.info.intl.retail.advice.excel.export.ExportExcelAspect;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.info.intl.retail.constant.CommonConstant;
import com.mi.info.intl.retail.cooperation.downloadcenter.config.LduConfig;
import com.mi.info.intl.retail.component.ComponentLocator;
import com.mi.info.intl.retail.cooperation.downloadcenter.example.JobTriggerHelper;
import com.mi.info.intl.retail.enums.SerialNumberType;
import com.mi.info.intl.retail.intlretail.service.api.fds.FdsService;
import com.mi.info.intl.retail.intlretail.service.api.fds.dto.FdsUploadResult;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.BathConReq;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.CommonResponse;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.IntlLduSnDto;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.IntlLduSnReq;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.ProductLineDto;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.SearchRetailerReqDto;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.SearchRetailerResponseDto;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.SnImeiGoodsInfoDto;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.SnImeiGoodsInfoReq;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.SnImeiQueryDto;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.SnImeiValidationDto;
import com.mi.info.intl.retail.intlretail.service.api.nrjob.gateway.dto.NrJobGoIn;
import com.mi.info.intl.retail.ldu.dto.JobSuccessDto;
import com.mi.info.intl.retail.ldu.dto.excel.ImportLduSnDto;
import com.mi.info.intl.retail.ldu.dto.excel.ImportLduSnExcel;
import com.mi.info.intl.retail.ldu.dto.excel.IntlLduSnEnExcel;
import com.mi.info.intl.retail.ldu.dto.excel.IntlLduSnExcel;
import com.mi.info.intl.retail.ldu.dto.excel.IntlLduSnExcelDto;
import com.mi.info.intl.retail.ldu.enums.ExportExcelEnum;
import com.mi.info.intl.retail.ldu.enums.LanguageEnum;
import com.mi.info.intl.retail.ldu.enums.LduCommonConfigEnum;
import com.mi.info.intl.retail.ldu.enums.LduTypeEnum;
import com.mi.info.intl.retail.ldu.enums.RmsRetailerEnum;
import com.mi.info.intl.retail.ldu.infra.entity.IntlLduReportLog;
import com.mi.info.intl.retail.ldu.infra.entity.IntlLduSn;
import com.mi.info.intl.retail.ldu.infra.mapper.IntlLduReportLogMapper;
import com.mi.info.intl.retail.ldu.infra.mapper.IntlLduSnMapper;
import com.mi.info.intl.retail.ldu.infra.repository.IProductQueryService;
import com.mi.info.intl.retail.ldu.service.IntlLduSnImeiService;
import com.mi.info.intl.retail.intlretail.service.api.ldu.IntlLduSnService;
import com.mi.info.intl.retail.ldu.util.ConstantMessageTemplate;
import com.mi.info.intl.retail.ldu.util.NrJobTaskUtils;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.org.infra.entity.IntlRmsCountryTimezone;
import com.mi.info.intl.retail.org.infra.mapper.IntlRmsCountryTimezoneMapper;
import com.mi.info.intl.retail.retailer.entity.IntlRmsRetailer;
import com.mi.info.intl.retail.retailer.mapper.IntlRmsRetailerMapper;
import com.mi.info.intl.retail.retailer.mapper.read.IntlRmsRetailerReadMapper;
import com.mi.info.intl.retail.utils.SnImeiValidateUtil;
import com.xiaomi.com.i18n.area.Area;
import com.xiaomi.keycenter.okhttp3.OkHttpClient;
import com.xiaomi.keycenter.okhttp3.Request;
import com.xiaomi.keycenter.okhttp3.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.dubbo.rpc.RpcContext;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import com.mi.info.intl.retail.utils.time.DateTimeUtil;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.io.File;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2025-07-03
 */
@Service
@Slf4j
@DubboService(group = "${dubbo-group.provider.intl-retail:}", interfaceClass = IntlLduSnService.class)
public class IntlLduSnServiceImpl
        extends ServiceImpl<IntlLduSnMapper, IntlLduSn>
        implements IntlLduSnService {


    @Resource
    private FdsService fdsService;

    @Resource
    private IntlLduSnImeiService intlLduSnImeiService;

    @Resource
    private IntlLduSnMapper intlLduSnMapper;

    @Resource
    private IntlRmsCountryTimezoneMapper intlRmsCountryTimezoneMapper;

    @Resource
    private NrJobTaskUtils nrJobTaskUtils;

    @Resource
    private IntlRmsRetailerMapper intlRmsRetailerMapper;


    @Resource
    private LduConfig lduConfig;

    @Resource
    private IProductQueryService iProductQueryService;

    @Resource
    private IntlRmsRetailerReadMapper intlRmsRetailerReadMapper;

    @Resource
    private JobTriggerHelper jobTriggerHelper;

    private static final int DEFAULT_LIMIT = 50;

    private static final int MAX_NUM = 5000;

    private static final int MAX_NUMS = 30000;

    private static final long TIME_NUMS = 1000L;

    private static final long START_NUMS = 1L;

    private static final int FAIL_NUMS = 0001;

    private static final int FAIL_CODE = -1;


    @Resource
    @Qualifier("syncDataThreadPool")
    private ThreadPoolTaskExecutor executor;


    @Resource
    private IntlLduReportLogMapper intlLduReportLogMapper;

    @Override
    public CommonApiResponse<IPage<IntlLduSnDto>> pageList(IntlLduSnReq query) {
        String areaId = RpcContext.getContext().getAttachment(LduCommonConfigEnum.AREA_ID.getCode());
        List<String> countrysCode = new ArrayList<>();
        if (!CollectionUtils.isEmpty(query.getCountryCode())) {
            countrysCode = query.getCountryCode();
        }
        if (CollectionUtils.isEmpty(countrysCode) && !LduCommonConfigEnum.GLOBAL.getCode().equals(areaId) && areaId != null) {
            countrysCode.add(areaId);
        }
        Page<IntlLduSn> page = new Page<>(query.getPageNum(), query.getPageSize());
        LambdaQueryWrapper<IntlLduSn> queryWrapper = Wrappers.<IntlLduSn>lambdaQuery()
                .like(StringUtils.isNotEmpty(query.getGoodsName()), IntlLduSn::getGoodsName, query.getGoodsName())
                .eq(StringUtils.isNotEmpty(query.getGoodsId()), IntlLduSn::getGoodsId, query.getGoodsId())
                .eq(StringUtils.isNotEmpty(query.getRetailerCode()), IntlLduSn::getRetailerCode, query.getRetailerCode())
                .eq(StringUtils.isNotEmpty(query.getProductLine()), IntlLduSn::getProductLine, query.getProductLine())
                .eq(StringUtils.isNotEmpty(query.getProjectCode()), IntlLduSn::getProjectCode, query.getProjectCode().trim())
                .eq(StringUtils.isNotEmpty(query.getLduType()), IntlLduSn::getLduType, query.getLduType())
                .in(CollectionUtils.isNotEmpty(countrysCode), IntlLduSn::getCountryCode, countrysCode)
                .eq(StringUtils.isNotEmpty(query.getSnImei()), IntlLduSn::getSn, query.getSnImei())
                .or()
                .eq(StringUtils.isNotEmpty(query.getSnImei()), IntlLduSn::getImei1, query.getSnImei())
                .or()
                .eq(StringUtils.isNotEmpty(query.getSnImei()), IntlLduSn::getImei2, query.getSnImei())
                .eq(Objects.nonNull(query.getIsReport()), IntlLduSn::getIsReport, query.getIsReport())
                .eq(Objects.nonNull(query.getStatus()), IntlLduSn::getStatus, query.getStatus())
                .orderByDesc(IntlLduSn::getPlanCreateDate);

        Page<IntlLduSn> taskConfPage = this.page(page, queryWrapper);
        Page<IntlLduSnDto> pageDTO = new Page<>(query.getPageNum(), query.getPageSize());

        //产品线转换
        if (CollectionUtils.isEmpty(taskConfPage.getRecords())) {
            pageDTO.setCurrent(taskConfPage.getCurrent());
            pageDTO.setSize(taskConfPage.getSize());
            return new CommonApiResponse<>(pageDTO);
        }
        List<IntlLduSnDto> list = ComponentLocator.getConverter()
                .convertList(taskConfPage.getRecords(), IntlLduSnDto.class);


        List<String> skuList = list.stream().map(IntlLduSnDto::getGoodsId).distinct().collect(Collectors.toList());
        List<SnImeiGoodsInfoDto> snImeiGoodsInfoDtoList = intlLduSnImeiService.queryGoodsInfoByGoodsIds(skuList);
        Map<String, SnImeiGoodsInfoDto> snImeiGoodsInfoMap = snImeiGoodsInfoDtoList.stream().collect(
                Collectors.toMap(SnImeiGoodsInfoDto::getGoodsId, it -> it));

        List<ProductLineDto> productLineDtoList = iProductQueryService.queryProductLines();
        Map<String, ProductLineDto> productLineMap = productLineDtoList.stream().collect(
                Collectors.toMap(ProductLineDto::getProductLine, it -> it));

        list.stream().forEach(item -> {
            cntoEn(item, productLineMap, snImeiGoodsInfoMap);
            //时区转换
            timeZoneConvert(item, areaId);
        });

        pageDTO.setPages(taskConfPage.getPages());
        pageDTO.setTotal(taskConfPage.getTotal());
        pageDTO.setRecords(list);
        return new CommonApiResponse<>(pageDTO);
    }


    private void timeZoneConvert(IntlLduSnDto item, String areaCode) {
        if (!StringUtils.isEmpty(areaCode) && !LduCommonConfigEnum.GLOBAL.getCode().equals(areaCode)) {
            item.setPlanCreateTime(Area.of(areaCode).timeFormat(item.getPlanCreateDate(), CommonConstant.DATE_TIME_FORMAT));
            item.setPlanStopTime(item.getPlanStopDate() != CommonConstant.DEFAULT_VALUE ?
                    Area.of(areaCode).timeFormat(item.getPlanStopDate(), CommonConstant.DATE_TIME_FORMAT) : "");
        } else {
            item.setPlanCreateTime(DateTimeUtil.formatTimestamp(item.getPlanCreateDate()));
            item.setPlanStopTime(item.getPlanStopDate() != CommonConstant.DEFAULT_VALUE ? DateTimeUtil.formatTimestamp(item.getPlanStopDate()) : "");
        }
    }

    //中英文翻译：
    private void cntoEn(IntlLduSnDto item, Map<String, ProductLineDto> productLineMap, Map<String, SnImeiGoodsInfoDto> snImeiGoodsInfoMap) {
        if (StringUtils.isNotEmpty(item.getProductLine())) {
            String language = RpcContext.getContext().getAttachment(LduCommonConfigEnum.LANGUAGE.getCode());
            if (LanguageEnum.EN_US.getCode().equals(language)) {
                if (!Objects.isNull(snImeiGoodsInfoMap.get(item.getGoodsId()))) {
                    item.setGoodsName(snImeiGoodsInfoMap.get(item.getGoodsId()).getGoodsNameEn());
                }
                if (!Objects.isNull(productLineMap.get(item.getProductLine()))) {
                    item.setProductLine(productLineMap.get(item.getProductLine()).getEnName());
                }
            } else {
                if (!Objects.isNull(snImeiGoodsInfoMap.get(item.getGoodsId()))) {
                    item.setGoodsName(snImeiGoodsInfoMap.get(item.getGoodsId()).getGoodsName());
                }
                if (!Objects.isNull(productLineMap.get(item.getProductLine()))) {
                    item.setProductLine(productLineMap.get(item.getProductLine()).getCnName());
                }
            }
        }
    }

    @Override
    public IPage<IntlLduSnDto> pageListSn(IntlLduSnReq query) {
        String areaId = RpcContext.getContext().getAttachment(LduCommonConfigEnum.AREA_ID.getCode());
        List<String> countrysCode = new ArrayList<>();
        if (!CollectionUtils.isEmpty(query.getCountryCode())) {
            countrysCode = query.getCountryCode();
        }
        if (CollectionUtils.isEmpty(countrysCode) && !LduCommonConfigEnum.GLOBAL.getCode().equals(areaId) && areaId != null) {
            countrysCode.add(areaId);
        }
        Page<IntlLduSn> page = new Page<>(query.getPageNum(), query.getPageSize());
        LambdaQueryWrapper<IntlLduSn> queryWrapper = Wrappers.<IntlLduSn>lambdaQuery()
                .like(StringUtils.isNotEmpty(query.getGoodsName()), IntlLduSn::getGoodsName, query.getGoodsName())
                .eq(StringUtils.isNotEmpty(query.getGoodsId()), IntlLduSn::getGoodsId, query.getGoodsId())
                .in(CollectionUtils.isNotEmpty(countrysCode), IntlLduSn::getCountryCode, countrysCode)
                .eq(StringUtils.isNotEmpty(query.getRetailerCode()), IntlLduSn::getRetailerCode, query.getRetailerCode())
                .eq(StringUtils.isNotEmpty(query.getProductLine()), IntlLduSn::getProductLine, query.getProductLine())
                .eq(StringUtils.isNotEmpty(query.getProjectCode()), IntlLduSn::getProjectCode, query.getProjectCode().trim())
                .eq(StringUtils.isNotEmpty(query.getLduType()), IntlLduSn::getLduType, query.getLduType())
                .eq(StringUtils.isNotEmpty(query.getSnImei()), IntlLduSn::getSn, query.getSnImei())
                .eq(StringUtils.isNotEmpty(query.getSnImei()), IntlLduSn::getSn, query.getSnImei())
                .or()
                .eq(StringUtils.isNotEmpty(query.getSnImei()), IntlLduSn::getImei1, query.getSnImei())
                .or()
                .eq(StringUtils.isNotEmpty(query.getSnImei()), IntlLduSn::getImei2, query.getSnImei())
                .eq(Objects.nonNull(query.getIsReport()), IntlLduSn::getIsReport, query.getIsReport())
                .eq(Objects.nonNull(query.getStatus()), IntlLduSn::getStatus, query.getStatus())
                .orderByDesc(IntlLduSn::getPlanCreateDate);

        Page<IntlLduSn> taskConfPage = this.page(page, queryWrapper);
        Page<IntlLduSnDto> pageDTO = new Page<>(query.getPageNum(), query.getPageSize());

        if (CollectionUtils.isEmpty(taskConfPage.getRecords())) {
            pageDTO.setCurrent(taskConfPage.getCurrent());
            pageDTO.setSize(taskConfPage.getSize());
            return pageDTO;
        }
        List<IntlLduSnDto> list = ComponentLocator.getConverter()
                .convertList(taskConfPage.getRecords(), IntlLduSnDto.class);

        List<String> skuList = list.stream().map(IntlLduSnDto::getGoodsId).distinct().collect(Collectors.toList());
        List<SnImeiGoodsInfoDto> snImeiGoodsInfoDtoList = intlLduSnImeiService.queryGoodsInfoByGoodsIds(skuList);
        Map<String, SnImeiGoodsInfoDto> snImeiGoodsInfoMap = snImeiGoodsInfoDtoList.stream().collect(
                Collectors.toMap(SnImeiGoodsInfoDto::getGoodsId, it -> it));


        List<ProductLineDto> productLineDtoList = iProductQueryService.queryProductLines();
        Map<String, ProductLineDto> productLineMap = productLineDtoList.stream().collect(
                Collectors.toMap(ProductLineDto::getProductLine, it -> it));

        list.stream().forEach(item -> {
            cntoEn(item, productLineMap, snImeiGoodsInfoMap);
            timeZoneConvert(item, areaId);
        });
        pageDTO.setPages(taskConfPage.getPages());
        pageDTO.setTotal(taskConfPage.getTotal());
        pageDTO.setRecords(list);
        return pageDTO;
    }

    @Override
    public CommonApiResponse<String> create(IntlLduSnDto conf) {
        List<IntlLduSnDto> lduSnList = new ArrayList<>();
        //封装其他参数:三方服务
        String msg = conertOtherParams(conf);
        if (StringUtils.isNotEmpty(msg)) {
            return CommonApiResponse.failure(FAIL_CODE, msg);
        }
        lduSnList.add(conf);
        List<IntlLduSn> intlLduSnList = ComponentLocator.getConverter()
                .convertList(lduSnList, IntlLduSn.class);
        intlLduSnMapper.batchInsert(intlLduSnList);
        return new CommonApiResponse<>(CommonConstant.SUCCESS);
    }

    @Override
    public CommonResponse<String> exportPlanMaintenance(IntlLduSnReq query) {
        File tempFile = null;
        ExcelWriter excelWriter = null;
        try {
            tempFile = File.createTempFile(LduCommonConfigEnum.LDU_PLAN_NAME.getName(), LduCommonConfigEnum.EXCEL_TYPE.getCode());
            String areaId = RpcContext.getContext().getAttachment(LduCommonConfigEnum.AREA_ID.getCode());
            String upcAccount = RpcContext.getContext().getAttachment(LduCommonConfigEnum.UPC_ACCOUNT.getCode());
            String language = RpcContext.getContext().getAttachment(LduCommonConfigEnum.LANGUAGE.getCode());
            if (LanguageEnum.EN_US.getCode().equals(language)) {
                excelWriter = EasyExcel.write(tempFile, IntlLduSnEnExcel.class)
                        .registerWriteHandler(new ExportExcelAspect.AdaptiveWidthStyleStrategy()).build();
            } else {
                excelWriter = EasyExcel.write(tempFile, IntlLduSnExcel.class)
                        .registerWriteHandler(new ExportExcelAspect.AdaptiveWidthStyleStrategy()).build();
            }

            WriteSheet writeSheet = EasyExcel.writerSheet(LduCommonConfigEnum.TASK_LIST.getName()).build();

            long pageSize = TIME_NUMS;
            long currentPage = START_NUMS;
            boolean hasNext = true;

            while (hasNext) {
                query.setPageNum(currentPage);
                query.setPageSize(pageSize);

                IPage<IntlLduSnDto> intlLduReportStatisticsDtoIPage = this.pageListSn(query);

                List<IntlLduSnDto> records = intlLduReportStatisticsDtoIPage.getRecords();
                if (CollUtil.isEmpty(records)) {
                    hasNext = false;
                    continue;
                }
                List<IntlLduSnExcelDto> excelDtoList = new ArrayList<>();
                records.stream().forEach(record -> {
                    IntlLduSnExcelDto excelDto = ComponentLocator.getConverter().convert(record, IntlLduSnExcelDto.class);
                    if (LanguageEnum.EN_US.getCode().equals(language)) {
                        excelDto.setIsReport(Objects.equals(record.getIsReport(), CommonConstant.REPORT_ENABLED)
                                ? LduCommonConfigEnum.REPORTED.getCode() : LduCommonConfigEnum.UNREPORTED.getCode());
                        excelDto.setStatus(Objects.equals(record.getStatus(), CommonConstant.REPORT_ENABLED)
                                ? LduCommonConfigEnum.VALID.getCode() : LduCommonConfigEnum.INVALID.getCode());
                    } else {
                        excelDto.setLduType(LduTypeEnum.getMenuNameByType(record.getLduType()));
                        excelDto.setIsReport(Objects.equals(record.getIsReport(), CommonConstant.REPORT_ENABLED)
                                ? LduCommonConfigEnum.REPORTED.getName() : LduCommonConfigEnum.UNREPORTED.getName());
                        excelDto.setStatus(Objects.equals(record.getStatus(), CommonConstant.REPORT_ENABLED)
                                ? LduCommonConfigEnum.VALID.getName() : LduCommonConfigEnum.INVALID.getName());
                    }
                    if (!StringUtils.isEmpty(areaId) && !LduCommonConfigEnum.GLOBAL.getCode().equals(areaId)) {
                        excelDto.setPlanCreateTime(Area.of(areaId).timeFormat(record.getPlanCreateDate(),
                                CommonConstant.DATE_TIME_FORMAT));
                    } else {
                        excelDto.setPlanCreateTime(DateTimeUtil.formatTimestamp(record.getPlanCreateDate()));
                    }

                    excelDtoList.add(excelDto);
                });

                excelWriter.write(excelDtoList, writeSheet);

                hasNext = currentPage * pageSize < intlLduReportStatisticsDtoIPage.getTotal();
                currentPage++;
            }

            if (excelWriter != null) {
                excelWriter.finish();
            }
            String timestamp = String.valueOf(System.currentTimeMillis() / TIME_NUMS);

            FdsUploadResult upload = fdsService.upload(LduCommonConfigEnum.LDU_PLAN_NAME.getName()
                            + timestamp + LduCommonConfigEnum.EXCEL_TYPE.getCode(),
                    tempFile, true);

            return nrJobForExcelExport(upload.getUrl(), upcAccount);
        } catch (Exception e) {
            log.error("LDU上报统计列表导出异常: {}", e);
            return CommonResponse.failure(FAIL_NUMS, CommonConstant.EXPORT_FAILED_ERROR);
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
            if (tempFile != null && tempFile.exists()) {
                tempFile.delete();
            }
        }
    }

    private CommonResponse<String> nrJobForExcelExport(String upload, String upcAccount) {

        JobSuccessDto build = JobSuccessDto.builder().fileUrl(upload).build();

        NrJobGoIn nrJobGoIn = NrJobGoIn.builder()
                .jobKey(ExportExcelEnum.LDU_PLAN_REPORT.getJobKey())
                .owner(upcAccount)
                .taskDesc(ExportExcelEnum.LDU_PLAN_REPORT.getExcelEnName())
                .taskParam(JSONUtil.toJsonStr(build))
                // 链路追踪ID
                .traceId(ObjectId.next())
                .taskName(ExportExcelEnum.LDU_PLAN_REPORT.getExcelName())
                .build();

        return nrJobTaskUtils.triggerNrJobTask(nrJobGoIn);
    }

    @Override
    public CommonApiResponse<String> stopUse(IntlLduSnReq query) {
        if (Objects.isNull(query) || StringUtils.isBlank(query.getSn())) {
            return CommonApiResponse.failure(FAIL_CODE, ConstantMessageTemplate.getSnEmptyMessage());
        }
        //停用时校验是否已上报，已上报的的数据无法停用
        IntlLduSn intlLduSn = intlLduSnMapper.selectBySn(query.getSn());
        if (Objects.nonNull(intlLduSn) && !Objects.equals(intlLduSn.getIsReport(), CommonConstant.REPORT_DISABLED)) {
            return CommonApiResponse.failure(FAIL_CODE, ConstantMessageTemplate.getSnNotInPlanDeactivateFailedMessage());
        }
        intlLduSnMapper.updateStatusBySn(query.getSn(), System.currentTimeMillis());
        return new CommonApiResponse<>(CommonConstant.SUCCESS);
    }

    @Override
    public CommonApiResponse<List<String>> importStopPlanMaintenance(BathConReq query) {
        List<String> errorList = new ArrayList<>();
        if (Objects.equals(query.getType(), CommonConstant.TEMPLATE_TYPE_STOP)) {
            /**
             * 解析excel,修改状态
             * - 批量停用，通过模板导入时，校验模板中的SN
             *   - 是否存在于计划中，如不存在，则提示
             * Line X,当前SN不在计划中，无法直接停用(Line X,the SN doesn't exist in the LDU plan list,can not deactivate)
             *   - 状态校验，是否是已停用，如已停用，则提示
             *   Line X,当前SN的状态为已停用，请不要重复操作
             */
            try {
                List<String> snList = new ArrayList<>();
                // 创建 OkHttpClient 实例
                OkHttpClient client = new OkHttpClient();
                // 创建 HTTP 请求
                Request request = new Request.Builder().url(query.getUrl()).build();
                Response response = client.newCall(request).execute();

                if (response.isSuccessful()) {
                    // 获取响应的输入流
                    InputStream inputStream = response.body().byteStream();
                    Workbook workbook = new XSSFWorkbook(inputStream);
                    Sheet sheet = workbook.getSheetAt(0);
                    int num = 1;
                    for (Row row : sheet) {
                        if (row.getRowNum() == 0) {
                            if (!LduCommonConfigEnum.SN_IMEI.getCode().equals(getCellValue(row.getCell(0)))) {
                                errorList.add(ConstantMessageTemplate.getTemplateError());
                                return new CommonApiResponse<>(errorList);
                            }
                            continue;
                        }
                        String sn = this.getCellValue(row.getCell(0));
                        // 校验是否可以停用
                        convertDataList(num, sn, errorList, snList);
                        num++;
                    }
                }
                // 批量停用
                if (CollUtil.isNotEmpty(snList)) {
                    intlLduSnMapper.batchUpdateBySn(snList, System.currentTimeMillis());
                }
            } catch (Exception e) {
                log.error("批量停用异常: {}", e);
            }

        }
        return new CommonApiResponse<>(errorList);
    }

    private void convertDataList(int num, String sn, List<String> errorList, List<String> snList) {
        if (StringUtils.isEmpty(sn)) {
            errorList.add(ConstantMessageTemplate.getSnEmptyMessage(num));
        } else {
            IntlLduSn intlLduSn = intlLduSnMapper.selectBySn(sn);
            if (Objects.isNull(intlLduSn)) {
                errorList.add(ConstantMessageTemplate.getSnNotInPlanDeactivateFailedMessage(num));
            } else {
                if (intlLduSn.getStatus() == CommonConstant.SUCCESS_CODE || intlLduSn.getIsReport() == CommonConstant.REPORT_ENABLED) {
                    errorList.add(ConstantMessageTemplate.getSnAlreadyDeactivatedMessage(num));
                } else {
                    snList.add(intlLduSn.getSn());
                }
            }
        }
    }

    private String getCellValue(Cell cell) {
        if (cell == null) {
            return "";
        }
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    return String.valueOf(cell.getNumericCellValue());
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }


    @Override
    public CommonApiResponse<String> importPlanMaintenance(BathConReq query) {

        if (Objects.equals(query.getType(), CommonConstant.TEMPLATE_TYPE_UPLOADED)) {
            /**
             * 1、解析excel，校验数据
             * 2、根据产品ID查询产品信息，产品线，IMEI1，IMEI2，SN
             * 3、根据零售商编码查询零售商信息，零售商名称
             * 4、封装数据，插入数据库
             */
            try {
                List<ImportLduSnDto> confList = new ArrayList<>();
                // 创建 OkHttpClient 实例
                OkHttpClient client = new OkHttpClient();
                // 创建 HTTP 请求
                Request request = new Request.Builder().url(query.getUrl()).build();
                Response response = client.newCall(request).execute();
                List<String> retailerCodeList = null;

                if (response.isSuccessful()) {

                    // 获取响应的输入流
                    InputStream inputStream = response.body().byteStream();
                    Workbook workbook = new XSSFWorkbook(inputStream);
                    Sheet sheet = workbook.getSheetAt(0);
                    //1、获取所有的sn,校验是否小米产品
                    List<IntlLduSnDto> snList = getAllSn(sheet);
                    List<String> codeList = snList.stream().map(IntlLduSnDto::getSn).distinct().collect(Collectors.toList());
                    if (codeList.size() > MAX_NUMS) {
                        return CommonApiResponse.failure(FAIL_CODE, ConstantMessageTemplate.maxNumTioMessage());
                    }
                    //查询是否已经上报，如果已经上报，则更新状态
                    //codeList去掉空字符串
                    codeList = codeList.stream().filter(code -> !StringUtils.isEmpty(code)).collect(Collectors.toList());
                    if (CollUtil.isEmpty(codeList)) {
                        return CommonApiResponse.failure(FAIL_CODE, ConstantMessageTemplate.getSnEmptyMessage());
                    }
                    List<IntlLduReportLog> intlLduReportLogList = intlLduReportLogMapper.selectBySns(convertQueryDto(codeList));
                    Map<String, IntlLduReportLog> lduReportLogMap = intlLduReportLogList.stream().collect(
                            Collectors.toMap(IntlLduReportLog::getSn, v -> v));

                    List<SnImeiValidationDto> snImeiValidationDtoList = intlLduSnImeiService.validateSnImeiInfo(convertQueryDto(codeList));
                    Map<String, SnImeiValidationDto> snImeiValidationDtoMap = snImeiValidationDtoList.stream().collect(
                            Collectors.toMap(SnImeiValidationDto::getSnImei, v -> v));
                    //获取所有的零售商编码
                    retailerCodeList = snList.stream().map(IntlLduSnDto::getRetailerCode)
                            .distinct().collect(Collectors.toList());
                    //retailerCodeList去掉空字符串
                    retailerCodeList = retailerCodeList.stream().filter(code -> !StringUtils.isEmpty(code)).collect(Collectors.toList());
                    //批量查询sn，判断是否重复
                    List<IntlLduSn> existSnList = intlLduSnMapper.batchSelectBySnOrImei(codeList);
                    Map<String, IntlLduSn> existSnMap = existSnList.stream().collect(
                            Collectors.toMap(IntlLduSn::getSn, v -> v));
                    Map<String, String> retailerMap = checkRetailerCodeAndCountry(snList.stream().map(IntlLduSnDto::getRetailerCode)
                            .distinct().collect(Collectors.toList()));
                    for (Row row : sheet) {
                        if (row.getRowNum() == 0) {
                            if (!LduCommonConfigEnum.COUNTRY_CODE.getCode().equals(getCellValue(row.getCell(0)))
                                    && !LduCommonConfigEnum.RESALE_CODE.getCode().equals(getCellValue(row.getCell(1)))) {
                                return CommonApiResponse.failure(FAIL_CODE, ConstantMessageTemplate.getTemplateError());
                            }
                            continue;
                        }
                        String countryCode = this.getCellValue(row.getCell(0)).trim();
                        String retailerCode = this.getCellValue(row.getCell(1)).trim();
                        String sn = this.getCellValue(row.getCell(2)).trim();
                        String lduType = this.getCellValue(row.getCell(3)).trim();
                        // 校验数据
                        covertPlanMaintenance(countryCode, retailerCode, sn,
                                lduType, confList, retailerMap, snImeiValidationDtoMap, existSnMap, lduReportLogMap);
                    }
                }
                // 没有异常，则批量新增
                boolean allErrorInfoEmpty = confList.stream()
                        .map(ImportLduSnDto::getErrorInfo)
                        .allMatch(errorInfo -> errorInfo == null || errorInfo.isEmpty());

                if (allErrorInfoEmpty) {
                    batchInsertAsync(fillOtherParams(confList, retailerCodeList));
                    return CommonApiResponse.success(CommonConstant.SUCCESS);
                } else {
                    //有异常导出错误信息的excel文件
                    String url = exportSnLdu(confList);
                    return CommonApiResponse.failure(FAIL_CODE, ConstantMessageTemplate.getExcelError(), url);
                }
            } catch (Exception e) {
                log.error("批量新增计划维护异常: {}", e);
            }
        }
        return new CommonApiResponse<>(CommonConstant.SUCCESS);
    }

    /**
     * 异步多线程批量插入
     */
    private void batchInsertAsync(List<IntlLduSn> targets) {
        if (targets.isEmpty()) {
            return;
        }
        // 分成多个批次
        List<List<IntlLduSn>> batches = splitIntoBatches(targets, MAX_NUM);
        // 并行处理所有批次
        List<CompletableFuture<Void>> futures = batches.stream()
                .map(batch -> CompletableFuture.runAsync(() -> {
                    try {
                        // 单个批次插入
                        intlLduSnMapper.batchInsert(batch);
                        log.info("完成一批次插入，数量：{}", batch.size());
                    } catch (Exception e) {
                        log.error("批次插入失败，数量：{}", batch.size(), e);
                    }
                }, executor))
                .collect(Collectors.toList());
        // 等待所有批次完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .exceptionally(ex -> {
                    log.error("批量插入整体失败", ex);
                    throw new RuntimeException("批量插入失败", ex);
                })
                .join(); // 等待所有异步任务完成
    }

    /**
     * 将列表分成多个指定大小的子列表
     */
    private List<List<IntlLduSn>> splitIntoBatches(List<IntlLduSn> list, int batchSize) {
        List<List<IntlLduSn>> batches = new ArrayList<>();
        for (int i = 0; i < list.size(); i += batchSize) {
            int end = Math.min(i + batchSize, list.size());
            batches.add(list.subList(i, end));
        }
        return batches;
    }

    private String exportSnLdu(List<ImportLduSnDto> intlLduTargetList) {
        File tempFile = null;
        ExcelWriter excelWriter = null;
        try {
            tempFile = File.createTempFile(LduCommonConfigEnum.LDU_PLAN_RECORD.getName(), LduCommonConfigEnum.EXCEL_TYPE.getCode());
            excelWriter = EasyExcel.write(tempFile, ImportLduSnExcel.class)
                    .registerWriteHandler(new ExportExcelAspect.AdaptiveWidthStyleStrategy()).build();

            WriteSheet writeSheet = EasyExcel.writerSheet(LduCommonConfigEnum.TASK_LIST.getName()).build();
            List<ImportLduSnExcel> excelDtoList = ComponentLocator.getConverter()
                    .convertList(intlLduTargetList, ImportLduSnExcel.class);

            excelWriter.write(excelDtoList, writeSheet);
            if (excelWriter != null) {
                excelWriter.finish();
            }
            String timestamp = String.valueOf(System.currentTimeMillis() / TIME_NUMS);
            FdsUploadResult upload = fdsService.upload(LduCommonConfigEnum.LDU_PLAN_RECORD.getName()
                            + timestamp + LduCommonConfigEnum.EXCEL_TYPE.getCode(),
                    tempFile, true);
            return upload.getUrl();
        } catch (Exception e) {
            log.error("LDU计划维护列表导出异常: {}", e);
            return "";
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
            if (tempFile != null && tempFile.exists()) {
                tempFile.delete();
            }
        }
    }

    private List<IntlLduSnDto> getAllSn(Sheet sheet) {
        List<IntlLduSnDto> snList = new ArrayList<>();
        for (Row row : sheet) {
            if (row.getRowNum() == 0) {
                continue;
            }
            IntlLduSnDto intlLduSnDto = new IntlLduSnDto();
            intlLduSnDto.setSn(this.getCellValue(row.getCell(2)).trim());
            intlLduSnDto.setRetailerCode(this.getCellValue(row.getCell(1)).trim());
            if (!Objects.isNull(intlLduSnDto)) {
                snList.add(intlLduSnDto);
            }
        }
        return snList;
    }

    //封装QueryDto
    private SnImeiQueryDto convertQueryDto(List<String> snList) {
        SnImeiQueryDto snImeiQueryDto = new SnImeiQueryDto();
        snImeiQueryDto.setSnList(Lists.newArrayList());
        snImeiQueryDto.setImeiList(Lists.newArrayList());
        snImeiQueryDto.setSixNineCodeList(Lists.newArrayList());
        snList.forEach(code -> {
                    SerialNumberType serialNumberType = SnImeiValidateUtil.getSerialNumberType(code);
                    if (Objects.equals(serialNumberType, SerialNumberType.SN)) {
                        snImeiQueryDto.getSnList().add(code);
                    } else if (Objects.equals(serialNumberType, SerialNumberType.IMEI)) {
                        snImeiQueryDto.getImeiList().add(code);
                    } else if (Objects.equals(serialNumberType, SerialNumberType.CODE69)) {
                        snImeiQueryDto.getSixNineCodeList().add(code);
                    }
                }
        );
        return snImeiQueryDto;
    }

    private List<IntlLduSn> fillOtherParams(List<ImportLduSnDto> confList, List<String> retailerCodeList) {
        List<IntlLduSnDto> intlLduSnDtoList = new ArrayList<>();
        Map<String, IntlRmsRetailer> retailerMap = new HashMap<>();
        List<IntlLduSn> intlLduSns = new ArrayList<>();
        //1、获取所有的sn:填充商品，产品等数据
        List<String> snList = confList.stream().map(ImportLduSnDto::getSn).collect(Collectors.toList());
        List<SnImeiGoodsInfoDto> snImeiGoodsInfoDtoList = intlLduSnImeiService.queryGoodsInfoBySnImeis(convertQueryDto(snList));
        if (CollectionUtils.isEmpty(snImeiGoodsInfoDtoList)) {
            log.error("根据SN/IMEI查询商品信息异常！");
        }
        Map<String, SnImeiGoodsInfoDto> snImeiGoodsInfoDtoMap = snImeiGoodsInfoDtoList.stream()
                .collect(Collectors.toMap(SnImeiGoodsInfoDto::getSn, v -> v, (existing, replacement) -> replacement, HashMap::new));
        Map<String, SnImeiGoodsInfoDto> snImeiGoodsInfoDtoMap2 = snImeiGoodsInfoDtoList.stream()
                .collect(Collectors.toMap(SnImeiGoodsInfoDto::getImei, v -> v, (existing, replacement) -> replacement, HashMap::new));

        //区域国家，进行填充
        List<IntlRmsCountryTimezone> intlRmsCountryTimezones = intlRmsCountryTimezoneMapper.selectAll();
        //零售商
        if (CollectionUtils.isNotEmpty(retailerCodeList)) {
            List<IntlRmsRetailer> intlRmsRetailers = intlRmsRetailerMapper.selectByRetailerCode(retailerCodeList);
            retailerMap = intlRmsRetailers.stream().distinct()
                    .collect(Collectors.toMap(IntlRmsRetailer::getName, v -> v));
        }

        //填充其他参数
        if (CollUtil.isNotEmpty(snImeiGoodsInfoDtoList)) {
            Map<String, IntlRmsRetailer> finalRetailerMap = retailerMap;
            confList.stream().forEach(conf -> {
                SnImeiGoodsInfoDto snImeiGoodsInfoDto = null;
                SerialNumberType serialNumberType = SnImeiValidateUtil.getSerialNumberType(conf.getSn());
                if (Objects.equals(serialNumberType, SerialNumberType.SN)) {
                    snImeiGoodsInfoDto = snImeiGoodsInfoDtoMap.get(conf.getSn());
                } else if (Objects.equals(serialNumberType, SerialNumberType.IMEI)) {
                    snImeiGoodsInfoDto = snImeiGoodsInfoDtoMap2.get(conf.getSn());
                }
                IntlLduSnDto intlLduSnDto = ComponentLocator.getConverter().convert(conf, IntlLduSnDto.class);
                addOtherParams(intlLduSnDto, snImeiGoodsInfoDto,
                        intlRmsCountryTimezones, finalRetailerMap);
                intlLduSnDtoList.add(intlLduSnDto);
            });
        }
        intlLduSns = ComponentLocator.getConverter()
                .convertList(intlLduSnDtoList, IntlLduSn.class);
        return intlLduSns;
    }

    private Map<String, String> checkRetailerCodeAndCountry(List<String> retailerCode) {
        Map<String, String> retailerMap = new HashMap<>();
        SearchRetailerReqDto searchRetailerReqDto = new SearchRetailerReqDto();
        searchRetailerReqDto.setKeyword(LduCommonConfigEnum.KEYWORDS.getCode());
        searchRetailerReqDto.setId(retailerCode);
        List<SearchRetailerResponseDto> queryRetailerByNameOrCode = intlRmsRetailerReadMapper.queryRetailerByNameOrCode(
                DEFAULT_LIMIT, searchRetailerReqDto);
        if (!CollectionUtils.isEmpty(queryRetailerByNameOrCode)) {
            retailerMap = queryRetailerByNameOrCode.stream().collect(Collectors.toMap(SearchRetailerResponseDto::getCode,
                    SearchRetailerResponseDto::getCountryCode));
        }
        return retailerMap;
    }

    private void covertPlanMaintenance(String countryCode, String retailerCode,
                                       String sn, String lduType, List<ImportLduSnDto> confList,
                                       Map<String, String> retailerMap,
                                       Map<String, SnImeiValidationDto> snImeiValidationDtoMap,
                                       Map<String, IntlLduSn> existSnMap,
                                       Map<String, IntlLduReportLog> lduReportLogMap) {
        // 1. 初始化DTO对象
        ImportLduSnDto importLduSnDto = initImportLduSnDto(countryCode, retailerCode, sn, lduType, lduReportLogMap);

        // 2. 收集所有校验错误信息
        StringBuilder errorMsg = new StringBuilder();
        appendErrorMsg(errorMsg, validateSnNotEmpty(sn));
        appendErrorMsg(errorMsg, validateSnValidity(sn, snImeiValidationDtoMap));
        appendErrorMsg(errorMsg, validateRetailerAndCountry(retailerCode, countryCode, retailerMap));
        appendErrorMsg(errorMsg, validateCountryCode(countryCode));
        appendErrorMsg(errorMsg, validateLduType(lduType, sn, existSnMap));

        // 3. 设置错误信息并添加到列表
        importLduSnDto.setErrorInfo(errorMsg.toString());
        confList.add(importLduSnDto);
    }

    /**
     * 初始化ImportLduSnDto对象
     */
    private ImportLduSnDto initImportLduSnDto(String countryCode, String retailerCode,
                                              String sn, String lduType, Map<String, IntlLduReportLog> lduReportLogMap) {
        ImportLduSnDto dto = new ImportLduSnDto();
        dto.setRetailerCode(retailerCode);
        dto.setSn(sn);
        dto.setLduType(lduType);
        dto.setCountryCode(countryCode);
        dto.setIsReport(Objects.isNull(lduReportLogMap.get(sn)) ? CommonConstant.REPORT_DISABLED : CommonConstant.REPORT_ENABLED);
        return dto;
    }

    /**
     * 校验SN是否为空
     */
    private String validateSnNotEmpty(String sn) {
        return StringUtils.isEmpty(sn) ? ConstantMessageTemplate.getSnEmptyMessage() : "";
    }

    /**
     * 校验SN是否为小米有效商品
     */
    private String validateSnValidity(String sn, Map<String, SnImeiValidationDto> snImeiMap) {
        if (StringUtils.isEmpty(sn)) {
            return ""; // SN为空时不校验（已在validateSnNotEmpty中处理）
        }
        SnImeiValidationDto validationDto = snImeiMap.get(sn);
        return (validationDto == null || !validationDto.getIsValid())
                ? ConstantMessageTemplate.getXiaomiSnValidationFailedMessage()
                : "";
    }

    /**
     * 校验零售商与国家代码是否匹配
     */
    private String validateRetailerAndCountry(String retailerCode, String countryCode,
                                              Map<String, String> retailerMap) {
        if (StringUtils.isEmpty(retailerCode)) {
            return ""; // 零售商代码为空时不校验
        }
        String mappedCountry = retailerMap.get(retailerCode);
        return (mappedCountry == null || !mappedCountry.equals(countryCode))
                ? ConstantMessageTemplate.matchRetailerCodeAndCountry()
                : "";
    }

    /**
     * 校验国家代码是否为空
     */
    private String validateCountryCode(String countryCode) {
        return StringUtils.isEmpty(countryCode)
                ? ConstantMessageTemplate.getXiaomiCountryCodeEmptyMessage()
                : "";
    }

    /**
     * 校验LDU类型及SN重复性
     */
    private String validateLduType(String lduType, String sn, Map<String, IntlLduSn> existSnMap) {
        // 先校验LDU类型是否合法
        boolean isMassProduction = LduTypeEnum.MASS_PRODUCTION_VERSION.getType().equals(lduType);
        boolean isCustomized = LduTypeEnum.CUSTOMIZED_VERSION.getType().equals(lduType);
        if (!isMassProduction && !isCustomized) {
            return ConstantMessageTemplate.getLduTypeErrorMessage();
        }

        // 类型合法时，校验SN是否重复
        if (MapUtils.isNotEmpty(existSnMap) && existSnMap.get(sn) != null) {
            return ConstantMessageTemplate.getSnDuplicateMessage();
        }
        return "";
    }

    /**
     * 拼接错误信息（避免null或空字符串干扰）
     */
    private void appendErrorMsg(StringBuilder builder, String error) {
        if (StringUtils.isNotEmpty(error)) {
            builder.append(error);
        }
    }

    @Override
    public CommonApiResponse<String> downLoadLduTemp(BathConReq query) {
        String urlTemplate = "";
        if (Objects.equals(query.getType(), CommonConstant.TEMPLATE_TYPE_UPLOADED)) {
            //批量上传模版
            urlTemplate = lduConfig.getPlanUploadUrl();
        }
        if (Objects.equals(query.getType(), CommonConstant.TEMPLATE_TYPE_STOP)) {
            //批量停用模版
            urlTemplate = lduConfig.getPlanStopUrl();
        }
        return new CommonApiResponse<>(urlTemplate);
    }

    @Override
    public List<IntlLduSnDto> selectBySnAndCountryCode(List<SnImeiGoodsInfoReq> goodsList, String countryCode) {
        List<IntlLduSn> intlLduSnList = intlLduSnMapper.selectBySnAndCountryCode(goodsList, countryCode);
        List<IntlLduSnDto> intlLduSnDtoList = ComponentLocator.getConverter().convertList(intlLduSnList, IntlLduSnDto.class);
        return intlLduSnDtoList;
    }

    @Override
    public void batchUpdateBySn(List<IntlLduSnDto> intlLduSnDtoList, String countryCode) {
        List<IntlLduSn> intlLduSns = ComponentLocator.getConverter().convertList(intlLduSnDtoList, IntlLduSn.class);
        intlLduSnMapper.batchUpdateBySnCountryCode(intlLduSns, countryCode, 1);
    }

    private String getValueById(String id) {
        if (StringUtils.isEmpty(id)) {
            return "";
        } else {
            return id;
        }
    }

    private void addOtherParams(IntlLduSnDto confSn, SnImeiGoodsInfoDto snImeiGoodsInfoDto,
                                List<IntlRmsCountryTimezone> rmsCountryTimezones,
                                Map<String, IntlRmsRetailer> retailerMap) {
        if (!Objects.isNull(snImeiGoodsInfoDto)) {
            confSn.setImei1(snImeiGoodsInfoDto.getImei());
            confSn.setImei2(snImeiGoodsInfoDto.getImei2());
            confSn.setSn(snImeiGoodsInfoDto.getSn());
            confSn.setProductLine(Objects.isNull(snImeiGoodsInfoDto.getProductLineCode()) ? ""
                    : String.valueOf(snImeiGoodsInfoDto.getProductLineCode()));
            //根据产品线查询项目代码
            confSn.setProductId(getValueById(snImeiGoodsInfoDto.getProductId()));
            confSn.setProductName(getValueById(snImeiGoodsInfoDto.getProductName()));
            confSn.setProjectCode(getValueById(snImeiGoodsInfoDto.getProjectCode()));
            confSn.setGoodsId(getValueById(snImeiGoodsInfoDto.getGoodsId()));
            confSn.setGoodsName(getValueById(snImeiGoodsInfoDto.getGoodsName()));
            confSn.setRamCapacity(getValueById(snImeiGoodsInfoDto.getRam()));
            confSn.setRomCapacity(getValueById(snImeiGoodsInfoDto.getRom()));
        }

        if (!CollectionUtils.isEmpty(rmsCountryTimezones)) {
            //转换为map
            Map<String, IntlRmsCountryTimezone> intlRmsCountryTimezoneMap = rmsCountryTimezones.stream()
                    .collect(Collectors.toMap(IntlRmsCountryTimezone::getCountryCode, v -> v));
            IntlRmsCountryTimezone countryTimezone = intlRmsCountryTimezoneMap.get(confSn.getCountryCode());
            confSn.setCountry(Objects.isNull(countryTimezone) ? "" : countryTimezone.getCountryName());
            confSn.setRegionCode(Objects.isNull(countryTimezone) ? "" : countryTimezone.getAreaCode());
            confSn.setRegion(Objects.isNull(countryTimezone) ? "" : countryTimezone.getArea());
        }
        confSn.setRetailerName(Objects.isNull(retailerMap.get(confSn.getRetailerCode())) ? "" :
                retailerMap.get(confSn.getRetailerCode()).getRetailerName());

        confSn.setChannelType(Objects.isNull(retailerMap.get(confSn.getRetailerCode())) ? "" :
                RmsRetailerEnum.getEnumByCode(retailerMap.get(confSn.getRetailerCode()).getRetailerChannelType()));

        confSn.setCreateUserId(RpcContext.getContext().getAttachments().get(LduCommonConfigEnum.UPC_ACCOUNT.getCode()));
        confSn.setCreateUserName(RpcContext.getContext().getAttachments().get(LduCommonConfigEnum.USER_NAME.getCode()));
        confSn.setPlanCreateDate(System.currentTimeMillis());
        confSn.setIsReport(Objects.equals(confSn.getIsReport(), CommonConstant.REPORT_ENABLED) ?
                CommonConstant.REPORT_ENABLED : CommonConstant.REPORT_DISABLED);
        confSn.setStatus(CommonConstant.REPORT_ENABLED);
    }

    @Override
    public void batchInsert(List<IntlLduSnDto> intlLduSnList) {
        List<IntlLduSn> intlLduSns = ComponentLocator.getConverter().convertList(intlLduSnList, IntlLduSn.class);
        intlLduSnMapper.batchInsert(intlLduSns);
    }

    private String conertOtherParams(IntlLduSnDto confSn) {

        //查询是否已经上报，如果已经上报，则更新状态
        List<IntlLduReportLog> intlLduReportLogList = intlLduReportLogMapper.selectBySns(convertQueryDto(Arrays.asList(confSn.getSn())));
        if (CollectionUtils.isNotEmpty(intlLduReportLogList)) {
            confSn.setIsReport(CommonConstant.REPORT_ENABLED);
        }
        /**
         *  1、根据SN或imei查询IMEI服务接口返回sn和imei、goodsId, IMEI1、IMEI2、SN
         * 2、根据goodsId调商品主数据接口，goodsId、goodsName，
         * 3、根据goodsId查询产品线接口带出：Product Name、产品线
         * 4、项目代码，RAM,ROM
         * 5、校验零售商和国家是否匹配
         */
        String sn = confSn.getSn();
        List<SnImeiValidationDto> snImeiValidationDtoList = intlLduSnImeiService.validateSnImeiInfo(convertQueryDto(Arrays.asList(sn)));

        SnImeiGoodsInfoDto snImeiGoodsInfoDto = intlLduSnImeiService.getGoodsInfoBySnImei(sn);
        if (Objects.isNull(snImeiGoodsInfoDto)) {
            return ConstantMessageTemplate.getXiaomiSnValidationFailedMessage();
        }
        boolean flag = false;
        Map<String, IntlRmsRetailer> retailerMap = new HashMap<>();
        //区域国家，进行填充
        List<IntlRmsCountryTimezone> intlRmsCountryTimezones = intlRmsCountryTimezoneMapper.selectAll();
        if (!StringUtils.isEmpty(confSn.getRetailerCode())) {
            //零售商
            List<IntlRmsRetailer> intlRmsRetailers = intlRmsRetailerMapper.selectByRetailerCode(Arrays.asList(confSn.getRetailerCode()));
            //校验零售商和国家是否匹配
            Map<String, String> retailerCodeMap = checkRetailerCodeAndCountry(Arrays.asList(confSn.getRetailerCode()));
            flag = MapUtils.isNotEmpty(retailerCodeMap)
                    && !Objects.equals(retailerCodeMap.get(confSn.getRetailerCode()), confSn.getCountryCode());
            retailerMap = intlRmsRetailers.stream()
                    .distinct().collect(Collectors.toMap(IntlRmsRetailer::getName, v -> v));
        }
        addOtherParams(confSn, snImeiGoodsInfoDto, intlRmsCountryTimezones, retailerMap);
        String msg = "";
        /**
         * 校验数据：
         *1、 LDU类型：必填，单选，选项值【大货（Mass Production Version）、专样（Customized Version）】
         * 2、通过IMEI服务接口验证IMEI的，产品校验：根据SN/IMEI/69码调imei服务接口，判断是否为小米的商品
         *3、重复性校验：根据SN/IMEI校验后台已上传的LDU数据，判断是否已有记录，如有，则校验不通过
         */
        if (!LduTypeEnum.MASS_PRODUCTION_VERSION.getType().equals(confSn.getLduType()) &&
                !LduTypeEnum.CUSTOMIZED_VERSION.getType().equals(confSn.getLduType())) {
            msg = ConstantMessageTemplate.getLduTypeErrorMessage();
        } else if (CollectionUtils.isEmpty(snImeiValidationDtoList) ||
                !Boolean.TRUE.equals(snImeiValidationDtoList.get(0).getIsValid())) {
            msg = ConstantMessageTemplate.getXiaomiSnValidationFailedMessage();
        } else if (flag) {
            msg = ConstantMessageTemplate.matchRetailerCodeAndCountry();
        } else {
            IntlLduSn snEntity = intlLduSnMapper.selectBySn(sn);
            if (!Objects.isNull(snEntity)) {
                msg = ConstantMessageTemplate.getSnDuplicateMessage();
            }
        }
        return msg;
    }
}
