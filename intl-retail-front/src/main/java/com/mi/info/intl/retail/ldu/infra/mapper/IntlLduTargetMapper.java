package com.mi.info.intl.retail.ldu.infra.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.IntlLduTargetStatisticsReq;
import com.mi.info.intl.retail.ldu.infra.entity.IntlLduTarget;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.LduStatisticsDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025-07-04
 */

public interface IntlLduTargetMapper extends BaseMapper<IntlLduTarget> {

    void batchInsert(@Param("confList") List<IntlLduTarget> confList);


    int updateById(@Param("conf") IntlLduTarget conf);

    void batchUpdate(@Param("intlLduTargetList") List<IntlLduTarget> intlLduTargetList);

    IntlLduTarget queryByProjectCode(@Param("projectCode") String projectCode, @Param("countryCode") String countryCode);

    List<IntlLduTarget> queryByProjectCodeList(@Param("projectCodeList") List<String> projectCodeList);

    /**
     * 根据条件统计LDU目标数据
     * @param countryCode 国家编码
     * @param projectCode 项目编码
     * @return 统计结果列表
     */
    List<LduStatisticsDto> selectStatisticsByCountryProductProject(@Param("countryCode") String countryCode,
                                                                  @Param("projectCode") String projectCode);

    /**
     * 获取所有LDU目标统计数据
     * @return 统计结果列表
     */
    List<LduStatisticsDto> selectAllStatistics(@Param("list") List<IntlLduTargetStatisticsReq> list);
}
