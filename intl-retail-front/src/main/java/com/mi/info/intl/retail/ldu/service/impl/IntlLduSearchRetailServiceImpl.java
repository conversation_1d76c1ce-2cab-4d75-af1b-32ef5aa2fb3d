package com.mi.info.intl.retail.ldu.service.impl;

import com.mi.info.intl.retail.intlretail.service.api.ldu.IntlLduSearchRetailService;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.SearchRetailerReqDto;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.SearchRetailerResponseDto;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.retailer.mapper.read.IntlRmsRetailerReadMapper;
import com.xiaomi.mone.docs.annotations.dubbo.ApiDoc;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2025-07-24
 */
@Service
@DubboService(group = "${dubbo-group.provider.intl-retail:}", interfaceClass = IntlLduSearchRetailService.class)
public class IntlLduSearchRetailServiceImpl implements IntlLduSearchRetailService {

    private static final int DEFAULT_LIMIT = 20;

    @Resource
    private IntlRmsRetailerReadMapper intlRmsRetailerReadMapper;

    @ApiDoc(value = "/api/searchRetailer", description = "零售商下拉框查询")
    @Override
    public CommonApiResponse<List<SearchRetailerResponseDto>> searchRetailer(SearchRetailerReqDto searchRetailerReqDto) {

        if (Objects.isNull(searchRetailerReqDto) || StringUtils.isBlank(searchRetailerReqDto.getKeyword())) {
            return new CommonApiResponse<>(Collections.emptyList());
        }
        List<SearchRetailerResponseDto> queryRetailerByNameOrCode = intlRmsRetailerReadMapper.queryRetailerByNameOrCode(DEFAULT_LIMIT, searchRetailerReqDto);

        return new CommonApiResponse<>(queryRetailerByNameOrCode);

    }
}
