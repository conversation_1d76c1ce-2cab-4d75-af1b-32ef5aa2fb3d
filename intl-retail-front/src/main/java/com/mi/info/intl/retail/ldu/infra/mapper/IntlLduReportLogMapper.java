package com.mi.info.intl.retail.ldu.infra.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.InspectionLduReportDto;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.InspectionReq;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.IntlLduReportReq;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.ReportLogDto;
import com.mi.info.intl.retail.ldu.dto.LduReportSimple;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.SnImeiQueryDto;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.StoreMetricsDto;
import com.mi.info.intl.retail.ldu.infra.entity.IntlLduReportLog;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Mapper
public interface IntlLduReportLogMapper extends BaseMapper<IntlLduReportLog> {


    void batchInsertReportLogs(@Param("intlLduReportLogs") List<IntlLduReportLog> intlLduReportLogs);

    List<IntlLduReportLog> pageList(@Param("query") IntlLduReportReq query);

    int pageListCount(@Param("query") IntlLduReportReq query);

    List<IntlLduReportLog> historyList(@Param("query") IntlLduReportReq query);

    int historyListCount(@Param("query") IntlLduReportReq query);

    /**
     * 查询所有不重复的 project_code
     *
     * @return 不重复的 project_code 列表
     */
    List<String> selectDistinctProjectCodes();

    InspectionLduReportDto inspectionDetail(@Param("inspectionReq") InspectionReq inspectionReq);

    List<LduReportSimple> selectLduReportSimpleByIds(@Param("ids") List<Long> lduReportLogIds);
    List<StoreMetricsDto> statisticReportLogBatch(@Param("list") List<IntlLduReportLog> list);

    List<IntlLduReportLog> selectBySns(@Param("snImeiQueryDto") SnImeiQueryDto snImeiQueryDto);
}
