package com.mi.info.intl.retail.ldu.infra.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.SnImeiGoodsInfoReq;
import com.mi.info.intl.retail.ldu.infra.entity.IntlLduSn;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2025-07-03
 */
public interface IntlLduSnMapper extends BaseMapper<IntlLduSn> {

    void batchInsert(@Param("confList") List<IntlLduSn> confList);

    IntlLduSn selectBySn(@Param("sn") String sn);

    List<IntlLduSn> batchSelectBySnOrImei(@Param("snList") List<String> snList);

    int updateStatusBySn(@Param("sn") String sn, @Param("planStopDate") long planStopDate);

    void batchUpdateBySn(@Param("snList") List<String> snList, @Param("planStopDate") long planStopDate);

    List<IntlLduSn> selectBySnAndCountryCode(@Param("goodsList") List<SnImeiGoodsInfoReq> goodsList, @Param("countryCode") String countryCode);

    void batchUpdateBySnCountryCode(@Param("intlLduSns") List<IntlLduSn> intlLduSns, @Param("countryCode") String countryCode, @Param("isReport") int isReport);
}
