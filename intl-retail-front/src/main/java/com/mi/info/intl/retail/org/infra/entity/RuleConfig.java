package com.mi.info.intl.retail.org.infra.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.info.intl.retail.cooperation.task.config.StringArrayTypeHandler;
import lombok.Data;

import java.io.Serializable;

/**
 * 规则配置表
 *
 * @TableName intl_inspection_rule
 */
@TableName(value = "intl_inspection_rule")
@Data
public class RuleConfig implements Serializable {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 任务模板id (关联汽车中央大脑任务模板id)
     */
    private Long taskDefId;

    /**
     * 任务批次id (关联汽车中央大脑任务批次id)
     */
    private Long taskBatchId;

    /**
     * 规则编码
     */
    private String ruleCode;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 规则状态
     */
    private Integer ruleStatus;

    /**
     * 区域
     */
    private String region;

    /**
     * 国家
     */
    private String country;

    /**
     * 开始时间 比如 阵地升级建设开始时间
     */
    private Long startTime;

    /**
     * 结束时间 比如 阵地升级建设结束时间
     */
    private Long endTime;

    /**
     * 任务结束时间
     */
    private Long taskEndTime;

    /**
     * 拍照是否允许相册选择：1-是，0-否
     */
    private Integer allowPhotoFromGallery;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Long creationTime;

    /**
     * 修改人
     */
    private String modifier;

    /**
     * 修改时间
     */
    private Long modificationTime;

    /**
     * 循环提醒天数
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Integer reminderDays;


    /**
     * 大脑任务业务场景(对应事件类型)：301-front_inspection 401-Dummy，501-POSM，601-Price Tag，701-LDU
     */
    @TableField(value = "task_type")
    private Integer taskType;

    /**
     * 任务名称
     */
    @TableField(value = "task_name")
    private String taskName;

    /**
     * POSM物料名称列表
     */
    @TableField(value = "posm_materials", updateStrategy = FieldStrategy.ALWAYS)
    private String posmMaterials;

    /**
     * 目标类型 1 首销期 2 生命周期
     */
    @TableField(value = "target_type")
    private String targetType;

    /**
     * 任务循环类型：0-无，1-日，2-周，3-月，4-季，5-自定义天数
     */
    @TableField(value = "cycle_type")
    private Integer cycleType;

    /**
     * 自定义循环天数，仅当 cycle_type = 5 时有效
     */
    @TableField(value = "custom_cycle_days", updateStrategy = FieldStrategy.ALWAYS)
    private Integer customCycleDays;

    /**
     * 建议执行时间段开始时间戳（毫秒）
     */
    @TableField(value = "suggested_time_range_start")
    private Long suggestedTimeRangeStart;

    /**
     * 建议执行时间段结束时间戳（毫秒）
     */
    @TableField(value = "suggested_time_range_end")
    private Long suggestedTimeRangeEnd;

    /**
     * 是否需要检查，0-否，1-是
     */
    @TableField(value = "need_inspection")
    private Integer needInspection;

    /**
     * 任务指引
     */
    @TableField(value = "task_guidance", updateStrategy = FieldStrategy.ALWAYS)
    private String taskGuidance;

    /**
     * 项目代码
     */
    @TableField(value = "project", typeHandler = StringArrayTypeHandler.class)
    private String[] project;

    /**
     * 门店类型（来自门店系统）
     */
    @TableField(value = "store_type", updateStrategy = FieldStrategy.ALWAYS, typeHandler = StringArrayTypeHandler.class)
    private String[] storeType;

    /**
     * 自定义上传零售门店编码
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS, typeHandler = StringArrayTypeHandler.class)
    private String[] assignedStore;

    /**
     * 门店文件路径
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String filePath;

    /**
     * 门店文件名称
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String fileName;
}