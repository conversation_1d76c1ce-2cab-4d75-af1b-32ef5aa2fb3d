package com.mi.info.intl.retail.ldu.service.impl;

import com.google.common.collect.Lists;
import com.mi.info.intl.retail.intlretail.service.api.ldu.IntlRmsProductApiService;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.ProjectInfoDto;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.SearchProductReq;
import com.mi.info.intl.retail.ldu.infra.entity.IntlRmsProduct;
import com.mi.info.intl.retail.ldu.service.IntlRmsProductService;
import com.mi.info.intl.retail.model.CommonApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * RMS产品信息API服务实现类
 * 
 * <AUTHOR>
 * @date 2025/1/27
 */
@Slf4j
@Service
@DubboService(group = "${center.dubbo.group:}", interfaceClass = IntlRmsProductApiService.class)
public class IntlRmsProductApiServiceImpl implements IntlRmsProductApiService {

    @Autowired
    private IntlRmsProductService intlRmsProductService;

    @Override
    public CommonApiResponse<List<ProjectInfoDto>> searchProductsByProjectCode(SearchProductReq req) {
        if (req == null || req.getKeyword() == null) {
            log.warn("API层 - 搜索关键字为空，返回空列表");
            return new CommonApiResponse<>(Lists.newArrayList());
        }
        String projectCode = req.getKeyword().trim();
        String searchType = req.getSearchType() != null ? req.getSearchType().toUpperCase() : "FUZZY";
        log.info("API层 - 根据关键字查询产品信息, projectCode: {}, searchType: {}", projectCode, searchType);
        
        if (projectCode.isEmpty()) {
            log.warn("API层 - 搜索关键字为空，返回空列表");
            return new CommonApiResponse<>(Lists.newArrayList());
        }
        
        try {
            // 根据查询类型调用不同的Service层方法
            List<IntlRmsProduct> products;
            if ("EXACT".equals(searchType)) {
                products = intlRmsProductService.searchByProjectCodeExact(projectCode);
            } else {
                products = intlRmsProductService.searchByProjectCodeFuzzy(projectCode);
            }
            
            // 转换为DTO
            List<ProjectInfoDto> result = products.stream()
                    .map(this::convertToDto)
                    .collect(Collectors.toList());
            
            log.info("API层 - 根据关键字查询产品信息成功, 返回{}条记录", result.size());
            return new CommonApiResponse<>(result);
            
        } catch (Exception e) {
            log.error("API层 - 根据关键字查询产品信息失败, projectCode: {}, searchType: {}", projectCode, searchType, e);
            return CommonApiResponse.failure(500, "查询失败: " + e.getMessage());
        }
    }

    @Override
    public CommonApiResponse<List<ProjectInfoDto>> searchProductsByProjectCodeList(SearchProductReq req) {
        if (req == null || req.getKeywords() == null) {
            log.warn("API层 - 搜索关键字列表为空，返回空列表");
            return new CommonApiResponse<>(Lists.newArrayList());
        }
        
        String searchType = req.getSearchType() != null ? req.getSearchType().toUpperCase() : "FUZZY";
        log.info("API层 - 根据关键字列表批量查询产品信息, keywords: {}, searchType: {}", req.getKeywords(), searchType);
        
        if (req.getKeywords().isEmpty()) {
            log.warn("API层 - 搜索关键字列表为空，返回空列表");
            return new CommonApiResponse<>(Lists.newArrayList());
        }
        
        try {
            // 根据查询类型调用不同的Service层方法
            List<IntlRmsProduct> products;
            if ("EXACT".equals(searchType)) {
                products = intlRmsProductService.searchByProjectCodeListExact(req.getKeywords());
            } else {
                // 批量模糊查询暂不支持，可以循环调用单个模糊查询
                products = Lists.newArrayList();
                for (String keyword : req.getKeywords()) {
                    if (keyword != null && !keyword.trim().isEmpty()) {
                        products.addAll(intlRmsProductService.searchByProjectCodeFuzzy(keyword.trim()));
                    }
                }
            }
            
            // 转换为DTO
            List<ProjectInfoDto> result = products.stream()
                    .map(this::convertToDto)
                    .collect(Collectors.toList());
            
            log.info("API层 - 根据关键字列表批量查询产品信息成功, 返回{}条记录", result.size());
            return new CommonApiResponse<>(result);
            
        } catch (Exception e) {
            log.error("API层 - 根据关键字列表批量查询产品信息失败, keywords: {}, searchType: {}", req.getKeywords(), searchType, e);
            return CommonApiResponse.failure(500, "查询失败: " + e.getMessage());
        }
    }


    
    /**
     * 将IntlRmsProduct实体转换为ProjectInfoDto
     * 
     * @param product 产品实体
     * @return 产品信息DTO
     */
    private ProjectInfoDto convertToDto(IntlRmsProduct product) {
        ProjectInfoDto dto = new ProjectInfoDto();
        dto.setGoodsId(product.getGoodsId());
        dto.setProjectCode(product.getProjectCode());
        dto.setName(product.getName());
        dto.setProjectNameEn(product.getProjectNameEn());
        dto.setProjectNameCn(product.getProjectNameCn());
        dto.setProjectId(product.getProjectId());
        dto.setProductLine(product.getProductLine());
        dto.setProductLineCode(product.getProductLineCode());
        dto.setProductLineEn(product.getProductLineEn());

        return dto;
    }
}
