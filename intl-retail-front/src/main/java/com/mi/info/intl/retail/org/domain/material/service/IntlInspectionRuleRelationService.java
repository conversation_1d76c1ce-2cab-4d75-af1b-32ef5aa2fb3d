package com.mi.info.intl.retail.org.domain.material.service;

import com.mi.info.intl.retail.org.infra.entity.IntlInspectionRuleRelation;

import java.util.List;

public interface IntlInspectionRuleRelationService {

    void batchInsert(List<IntlInspectionRuleRelation> inspectionRuleRelations);

    void batchUpdate(List<IntlInspectionRuleRelation> inspectionRuleRelations);

    List<IntlInspectionRuleRelation> selectByRuleId(Long id);
}
