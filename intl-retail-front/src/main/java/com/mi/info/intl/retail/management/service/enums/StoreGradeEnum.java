package com.mi.info.intl.retail.management.service.enums;

import com.google.common.collect.Lists;

import java.util.List;

public enum StoreGradeEnum {
    S(1, "S", "S", "S", Lists.newArrayList("newRetail", "channelRetail")),
    A(20, "A", "A", "A", Lists.newArrayList("newRetail", "channelRetail")),
    B(21, "B", "B", "B", Lists.newArrayList("newRetail", "channelRetail")),
    C(25, "C", "C", "C", Lists.newArrayList("newRetail", "channelRetail")),
    D(26, "D", "D", "D", Lists.newArrayList("channelRetail")),
    BLANK(0, "Blank", "空白", "Blank", Lists.newArrayList("newRetail", "channelRetail"));

    private final Integer key;
    private final String value;
    private final String name;
    private final String description;
    private final List<String> types;

    StoreGradeEnum(Integer key, String value, String name, String description, List<String> types) {
        this.key = key;
        this.value = value;
        this.name = name;
        this.description = description;
        this.types = types;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    public String getDescription() {
        return description;
    }

    public List<String> getTypes() {
        return types;
    }

    // 根据 key 获取对应的枚举值
    public static StoreGradeEnum fromKey(Integer key) {
        if (key == null) {
            return null;
        }
        for (StoreGradeEnum grading : StoreGradeEnum.values()) {
            if (grading.getKey().equals(key)) {
                return grading;
            }
        }
        return null;
    }

    public static StoreGradeEnum fromValue(String value) {
        if (value == null) {
            return null;
        }
        for (StoreGradeEnum grading : StoreGradeEnum.values()) {
            if (grading.getValue().equals(value)) {
                return grading;
            }
        }
        return null;
    }
}
