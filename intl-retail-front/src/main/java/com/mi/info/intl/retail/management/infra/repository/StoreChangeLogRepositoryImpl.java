package com.mi.info.intl.retail.management.infra.repository;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.info.intl.retail.exception.BizException;
import com.mi.info.intl.retail.exception.ErrorCodes;
import com.mi.info.intl.retail.management.constant.EffectiveStatusEnum;
import com.mi.info.intl.retail.management.domain.StoreChangeLogDomain;
import com.mi.info.intl.retail.management.domain.model.req.StoreChangeLogPageReq;
import com.mi.info.intl.retail.management.domain.repository.StoreChangeLogRepository;
import com.mi.info.intl.retail.management.infra.entity.StoreChangeLog;
import com.mi.info.intl.retail.management.infra.mapper.StoreChangeLogMapper;
import com.mi.info.intl.retail.utils.RpcContextUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 门店变更日志仓储实现
 */
@Repository
public class StoreChangeLogRepositoryImpl extends ServiceImpl<StoreChangeLogMapper, StoreChangeLog>
        implements StoreChangeLogRepository {

    @Autowired
    private StoreChangeLogMapper storeChangeLogMapper;

    /**
     * 全球/全球默认区域代码
     */
    private static final String GLOBAL_COUNTRY_CODE = "GLOBAL";

    @Override
    public boolean save(StoreChangeLogDomain domain) {
        if (domain == null) {
            return false;
        }

        // 如果ID为null或为0，则执行插入操作
        if (domain.getId() == null || domain.getId().equals("0")) {
            StoreChangeLog entity = convertToEntity(domain);
            return storeChangeLogMapper.insert(entity) > 0;
        }

        // 如果ID存在，则执行更新操作
        StoreChangeLog entity = convertToEntity(domain);
        return storeChangeLogMapper.updateById(entity) > 0;
    }

    @Override
    public StoreChangeLogDomain getById(Long id) {
        StoreChangeLog entity = storeChangeLogMapper.selectById(id);
        return entity != null ? convertToDomain(entity) : null;
    }

    @Override
    public List<StoreChangeLogDomain> listAll() {
        List<StoreChangeLog> entities = storeChangeLogMapper.selectList(new LambdaQueryWrapper<>());
        return entities.stream().map(this::convertToDomain).collect(Collectors.toList());
    }

    @Override
    public Page<StoreChangeLogDomain> getPageList(StoreChangeLogPageReq query) {
        if (!query.getIsExport()) {
            String countryCode = RpcContextUtil.getCurrentAreaId();
            if (StringUtils.isBlank(countryCode)) {
                throw new BizException(ErrorCodes.BIZ_ERROR, "countryCode is null");
            }
            if (countryCode.equals(GLOBAL_COUNTRY_CODE)) {
                countryCode = null;
            }
            query.setCountryCode(countryCode);
        }
        Page<StoreChangeLog> page = new Page<>(query.getPageNum(), query.getPageSize());
        Page<StoreChangeLog> changeLogPage = this.page(page, Wrappers.<StoreChangeLog>lambdaQuery()
                .like(StringUtils.isNotBlank(query.getStoreName()), StoreChangeLog::getStoreName, query.getStoreName())
                .eq(StringUtils.isNotBlank(query.getStoreCode()), StoreChangeLog::getStoreCode, query.getStoreCode())
                .eq(StringUtils.isNotBlank(query.getRmsStoreCode()), StoreChangeLog::getRmsStoreCode,
                        query.getRmsStoreCode())
                .ge(query.getStartDate() != null, StoreChangeLog::getEffectiveTime, query.getStartDate())
                .le(query.getEndDate() != null, StoreChangeLog::getEffectiveTime, query.getEndDate())
                .in(CollectionUtil.isNotEmpty(query.getStoreClassList()), StoreChangeLog::getStoreClass,
                        query.getStoreClassList())
                .eq(query.getWorkableType() != null, StoreChangeLog::getWorkableType, query.getWorkableType())
                .eq(query.getHasPc() != null, StoreChangeLog::getHasPc, query.getHasPc())
                .eq(query.getHasSr() != null, StoreChangeLog::getHasSr, query.getHasSr())
                .eq(query.getHasFront() != null, StoreChangeLog::getHasFront, query.getHasFront())
                .eq(query.getHasPOS() != null, StoreChangeLog::getHasPos, query.getHasPOS())
                .eq(StoreChangeLog::getEffectiveStatus, EffectiveStatusEnum.IN_EFFECT.getKey())
                .eq(query.getCountryCode() != null, StoreChangeLog::getCountryCode, query.getCountryCode())
                .orderBy(StringUtils.isNotBlank(query.getOrderBy()),
                        !"desc".equalsIgnoreCase(query.getOrderBy()),
                        StoreChangeLog::getEffectiveTime)
                .orderByDesc(StringUtils.isNotBlank(query.getOrderBy()), StoreChangeLog::getEffectiveTime));
        Page<StoreChangeLogDomain> pageDomain =
                new Page<>(changeLogPage.getCurrent(), changeLogPage.getSize(), changeLogPage.getTotal());
        if (CollectionUtil.isEmpty(changeLogPage.getRecords())) {
            return pageDomain;
        }
        List<StoreChangeLogDomain> responses = changeLogPage.getRecords().stream()
                .map(this::convertToDomain)
                .collect(Collectors.toList());
        pageDomain.setRecords(responses);
        return pageDomain;
    }

    @Override
    public List<StoreChangeLogDomain> getListByQuery(StoreChangeLogPageReq query) {
        List<StoreChangeLog> list = this.list(new LambdaQueryWrapper<StoreChangeLog>()
                .like(StringUtils.isNotBlank(query.getStoreName()), StoreChangeLog::getStoreName, query.getStoreName())
                .eq(StringUtils.isNotBlank(query.getStoreCode()), StoreChangeLog::getStoreCode, query.getStoreCode())
                .eq(StringUtils.isNotBlank(query.getRmsStoreCode()), StoreChangeLog::getRmsStoreCode, query.getRmsStoreCode())
                .le(query.getStartDate() != null, StoreChangeLog::getEffectiveTime, query.getStartDate())
                .ge(query.getEndDate() != null, StoreChangeLog::getEffectiveTime, query.getEndDate())
                .in(CollectionUtil.isNotEmpty(query.getStoreClassList()), StoreChangeLog::getStoreClass, query.getStoreClassList())
                .eq(query.getWorkableType() != null, StoreChangeLog::getWorkableType, query.getWorkableType())
                .eq(query.getHasPc() != null, StoreChangeLog::getHasPc, query.getHasPc())
                .eq(query.getHasSr() != null, StoreChangeLog::getHasSr, query.getHasSr())
                .eq(query.getHasFront() != null, StoreChangeLog::getHasFront, query.getHasFront())
                .eq(query.getHasPOS() != null, StoreChangeLog::getHasPos, query.getHasPOS())
                .orderBy(StringUtils.isNotBlank(query.getOrderBy()),
                        !"desc".equalsIgnoreCase(query.getOrderBy()),
                        StoreChangeLog::getEffectiveTime)
                .orderByDesc(StringUtils.isBlank(query.getOrderBy()), StoreChangeLog::getId));
        if (CollectionUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list.stream()
                .map(this::convertToDomain)
                .collect(Collectors.toList());
    }

    @Override
    public Integer batchDelete(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return 0;
        }

        // 创建更新条件
        LambdaQueryWrapper<StoreChangeLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(StoreChangeLog::getId, ids);

        // 创建更新对象
        StoreChangeLog updateEntity = new StoreChangeLog();
        updateEntity.setEffectiveStatus(EffectiveStatusEnum.NOT_EFFECT.getKey());

        // 执行批量更新
        return storeChangeLogMapper.update(updateEntity, queryWrapper);
    }

    @Override
    public List<StoreChangeLogDomain> findByStoreCodeAndEffectiveTime(String storeCode, Long effectiveTime) {
        LambdaQueryWrapper<StoreChangeLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StoreChangeLog::getStoreCode, storeCode)
                .eq(StoreChangeLog::getEffectiveStatus, 1)
                .ge(StoreChangeLog::getEffectiveTime, effectiveTime)
                .orderByDesc(StoreChangeLog::getEffectiveTime);
        List<StoreChangeLog> entities = storeChangeLogMapper.selectList(queryWrapper);
        return entities.stream().map(this::convertToDomain).collect(Collectors.toList());
    }

    /**
     * 根据新系统门店编码或rms门店编码+生效时间查询门店变更记录
     *
     * @param storeCode 新系统门店编码或rms门店编码
     * @param effectiveTime 生效时间
     * @return
     */
    @Override
    public StoreChangeLogDomain findLastedByRmsStoreCodeAndEffectiveTime(String storeCode, Long effectiveTime) {
        LambdaQueryWrapper<StoreChangeLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StoreChangeLog::getEffectiveStatus, 1)
                // 生效时间小于等于指定时间
                .le(StoreChangeLog::getEffectiveTime, effectiveTime)
                // 核心条件：storeCode或rmsStoreCode匹配
                .and(wrapper -> wrapper
                        .eq(StoreChangeLog::getStoreCode, storeCode)
                        .or()
                        .eq(StoreChangeLog::getRmsStoreCode, storeCode)
                )
                // 按生效时间倒序，确保取最新的记录
                .orderByDesc(StoreChangeLog::getEffectiveTime)
                .last("limit 1");
        StoreChangeLog entity = storeChangeLogMapper.selectOne(queryWrapper);
        return convertToDomain(entity);
    }

    @Override
    public StoreChangeLogDomain findLastedByStoreCodeAndEffectiveTime(String storeCode, Long effectiveTime) {
        LambdaQueryWrapper<StoreChangeLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StoreChangeLog::getStoreCode, storeCode)
                .eq(StoreChangeLog::getEffectiveStatus, 1)
                .le(StoreChangeLog::getEffectiveTime, effectiveTime)
                .orderByDesc(StoreChangeLog::getEffectiveTime)
                .last("limit 1");
        StoreChangeLog entity = storeChangeLogMapper.selectOne(queryWrapper);
        return convertToDomain(entity);
    }

    @Override
    public List<StoreChangeLogDomain> findByCode(String storeCode) {
        LambdaQueryWrapper<StoreChangeLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StoreChangeLog::getStoreCode, storeCode)
                .eq(StoreChangeLog::getEffectiveStatus, 1)
                .orderByDesc(StoreChangeLog::getEffectiveTime);
        List<StoreChangeLog> entities = storeChangeLogMapper.selectList(queryWrapper);
        return entities.stream().map(this::convertToDomain).collect(Collectors.toList());
    }

    /**
     * 根据门店code查询所有有效的变更日志
     *
     * @param storeCode
     */
    @Override
    public List<StoreChangeLogDomain> findAllByStoreCode(String storeCode) {
        LambdaQueryWrapper<StoreChangeLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StoreChangeLog::getEffectiveStatus, 1)
                .and(wrapper -> wrapper
                        .eq(StoreChangeLog::getStoreCode, storeCode)
                        .or()
                        .eq(StoreChangeLog::getRmsStoreCode, storeCode)
                )
                .orderByDesc(StoreChangeLog::getEffectiveTime);
        List<StoreChangeLog> entities = storeChangeLogMapper.selectList(queryWrapper);
        return entities.stream().map(this::convertToDomain).collect(Collectors.toList());
    }

    private StoreChangeLog convertToEntity(StoreChangeLogDomain domain) {
        if (domain == null) return null;
        StoreChangeLog entity = new StoreChangeLog();
        BeanUtils.copyProperties(domain, entity);
        return entity;
    }

    private StoreChangeLogDomain convertToDomain(StoreChangeLog entity) {
        if (entity == null) return null;
        StoreChangeLogDomain domain = new StoreChangeLogDomain();
        BeanUtils.copyProperties(entity, domain);
        return domain;
    }
}
