package com.mi.info.intl.retail.org.domain.material.service.impl;

import com.mi.info.intl.retail.org.domain.material.service.IntlInspectionRuleRelationService;
import com.mi.info.intl.retail.org.infra.entity.IntlInspectionRuleRelation;
import com.mi.info.intl.retail.org.infra.mapper.IntlInspectionRuleRelationMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@Slf4j
@Service
public class IntlInspectionRuleRelationServiceImpl implements IntlInspectionRuleRelationService {


    @Resource
    private IntlInspectionRuleRelationMapper inspectionRuleRelationMapper;

    @Override
    public void batchInsert(List<IntlInspectionRuleRelation> inspectionRuleRelations) {

        inspectionRuleRelationMapper.batchInsert(inspectionRuleRelations);

    }

    @Override
    public void batchUpdate(List<IntlInspectionRuleRelation> inspectionRuleRelations) {
        inspectionRuleRelationMapper.batchUpdate(inspectionRuleRelations);
    }

    @Override
    public List<IntlInspectionRuleRelation> selectByRuleId(Long id) {
        return inspectionRuleRelationMapper.selectByRuleId(id);
    }
}
