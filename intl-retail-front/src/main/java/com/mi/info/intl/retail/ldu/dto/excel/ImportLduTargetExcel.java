package com.mi.info.intl.retail.ldu.dto.excel;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025-08-07
 */
@Data
@ExcelIgnoreUnannotated
public class ImportLduTargetExcel {

    /**
     * 国家编码
     */
    @ExcelProperty(value = "国家编码", index = 0)
    private String countryCode;

    /**
     * 项目ID
     */
    @ExcelProperty(value = "项目代码", index = 1)
    private String projectCode;


    /**
     * 目标覆盖门店数
     */
    @ExcelProperty(value = "目标覆盖门店数", index = 2)
    private int targetCoveredStores;


    /**
     * 目标销出样品数
     */
    @ExcelProperty(value = "目标销出样品数", index = 3)
    private int targetSampleOut;

    /**
     * 错误信息
     */
    @ExcelProperty(value = "错误信息", index = 4)
    private String errorInfo;

}
