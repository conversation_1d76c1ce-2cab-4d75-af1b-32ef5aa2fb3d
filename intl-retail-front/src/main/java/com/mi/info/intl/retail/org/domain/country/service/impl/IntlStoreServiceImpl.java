package com.mi.info.intl.retail.org.domain.country.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.info.intl.retail.api.front.position.IntlStoreApiService;
import com.mi.info.intl.retail.api.front.position.dto.IntlPositionDTO;
import com.mi.info.intl.retail.org.domain.country.service.IntlStoreService;
import com.mi.info.intl.retail.org.infra.entity.IntlRmsStore;
import com.mi.info.intl.retail.org.infra.mapper.IntlRmsStoreMapper;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * 国际零售组织-职位服务实现类
 *
 * <AUTHOR>
 * @date 2025/07/28
 */
@Service
public class IntlStoreServiceImpl extends ServiceImpl<IntlRmsStoreMapper, IntlRmsStore>
        implements IntlStoreService, IntlStoreApiService {

    /**
     * 通过商店代码获取零售商
     *
     * @param dto DTO
     * @return {@link Optional }<{@link IntlPositionDTO }>
     */
    @Override
    public Optional<IntlPositionDTO> getRetailerByStoreCode(IntlPositionDTO dto) {
        LambdaQueryWrapper<IntlRmsStore> wrapper = new LambdaQueryWrapper<>();
        wrapper = wrapper.select(IntlRmsStore::getStoreId, IntlRmsStore::getCode, IntlRmsStore::getRetailerId,
                IntlRmsStore::getRetailerIdName);
        wrapper.eq(IntlRmsStore::getCode, dto.getStoreCode()).isNotNull(IntlRmsStore::getRetailerId)
                .ne(IntlRmsStore::getRetailerId, "");
        IntlRmsStore intlRmsStore = this.getOne(wrapper);
        if (intlRmsStore == null) {
            return Optional.empty();
        }
        IntlPositionDTO result = new IntlPositionDTO();
        dto.setRetailerId(intlRmsStore.getRetailerId());
        dto.setRetailerCode(intlRmsStore.getRetailerName());
        dto.setStoreCode(intlRmsStore.getCode());
        return Optional.of(result);
    }


    @Override
    public Optional<IntlRmsStore> getRetailerByStoreId(String storeId) {
        LambdaQueryWrapper<IntlRmsStore> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(IntlRmsStore::getStoreId, storeId);
        IntlRmsStore intlRmsStore = this.getOne(wrapper);
        if (intlRmsStore == null) {
            return Optional.empty();
        }
        return Optional.of(intlRmsStore);
    }

}
