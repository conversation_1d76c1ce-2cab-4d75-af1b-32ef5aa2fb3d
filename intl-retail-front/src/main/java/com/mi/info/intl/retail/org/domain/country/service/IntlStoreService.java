package com.mi.info.intl.retail.org.domain.country.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.info.intl.retail.org.infra.entity.IntlRmsStore;

import java.util.Optional;

/**
 * INTL商店服务
 *
 * <AUTHOR>
 * @date 2025/07/29
 */
public interface IntlStoreService extends IService<IntlRmsStore> {
    /**
     * 通过商店ID获取零售商
     *
     * @param storeId 存储ID
     * @return {@link Optional }<{@link IntlRmsStore }>
     */
    Optional<IntlRmsStore> getRetailerByStoreId(String storeId);
}
