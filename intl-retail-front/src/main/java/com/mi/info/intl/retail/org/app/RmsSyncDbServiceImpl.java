package com.mi.info.intl.retail.org.app;

import com.mi.info.intl.retail.intlretail.service.api.mq.RmsSyncDbService;
import com.mi.info.intl.retail.intlretail.service.api.mq.dto.RmsDbRequest;
import com.mi.info.intl.retail.intlretail.service.api.retailer.dto.CommonResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@DubboService(group = "${center.dubbo.group:}", interfaceClass = RmsSyncDbService.class)
public class RmsSyncDbServiceImpl implements RmsSyncDbService {

    @Autowired
    private RocketMQTemplate rocketMQTemplate;

    @Value("${intl-retail.rocketmq.syncdb.topic}")
    private String topic;

    @Override
    public CommonResponse<String> syncRmsDbMsg(RmsDbRequest request) {
        rocketMQTemplate.convertAndSend(topic, request);
        return new CommonResponse<>("success");
    }
}

