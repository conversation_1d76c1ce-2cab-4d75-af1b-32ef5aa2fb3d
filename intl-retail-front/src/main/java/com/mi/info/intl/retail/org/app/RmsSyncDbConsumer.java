package com.mi.info.intl.retail.org.app;

import com.mi.info.intl.retail.exception.BizException;
import com.mi.info.intl.retail.intlretail.service.api.mq.dto.RmsDbRequest;
import com.mi.info.intl.retail.org.domain.RmsSyncDbManager;
import com.mi.info.intl.retail.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.util.Optional;
import javax.annotation.Resource;

@Slf4j
@Service
@ConditionalOnProperty(name = "intl-retail.rocketmq.syncdb.enabled", havingValue = "true", matchIfMissing = true)
@RocketMQMessageListener(topic = "${intl-retail.rocketmq.syncdb.topic}", consumerGroup = "${intl-retail.rocketmq" +
        ".syncdb.group}")
public class RmsSyncDbConsumer implements RocketMQListener<String> {

    @Resource
    private RmsSyncDbManager rmsSyncDbManager;

    @SuppressWarnings("checkstyle:SeparatorWrap")
    @Override
    public void onMessage(String message) {
        log.info("PositionConsumer message:{}", message);

        try {
            RmsDbRequest rmsDBRequest =
                    Optional.ofNullable(JsonUtil.json2bean(message, RmsDbRequest.class)).
                            orElseThrow(() -> new RuntimeException("RmsDBRequest is null"));
            rmsSyncDbManager.editDb(rmsDBRequest);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException("RmsSyncDbConsumer error", e);
        }

    }
}