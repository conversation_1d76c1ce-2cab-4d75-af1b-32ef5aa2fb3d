package com.mi.info.intl.retail.ldu.service.impl;

import java.io.File;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.mi.info.intl.retail.constant.CommonConstant;
import com.mi.info.intl.retail.ldu.enums.LduCommonConfigEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.dubbo.rpc.RpcContext;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.info.intl.retail.advice.excel.export.ExportExcelAspect;
import com.mi.info.intl.retail.component.ComponentLocator;
import com.mi.info.intl.retail.cooperation.downloadcenter.config.LduConfig;
import com.mi.info.intl.retail.cooperation.downloadcenter.example.JobTriggerHelper;
import com.mi.info.intl.retail.intlretail.service.api.fds.FdsService;
import com.mi.info.intl.retail.intlretail.service.api.fds.dto.FdsUploadResult;
import com.mi.info.intl.retail.intlretail.service.api.ldu.IntlLduTargetService;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.*;
import com.mi.info.intl.retail.intlretail.service.api.nrjob.gateway.dto.NrJobGoIn;
import com.mi.info.intl.retail.ldu.dto.JobSuccessDto;
import com.mi.info.intl.retail.ldu.dto.excel.*;
import com.mi.info.intl.retail.ldu.enums.ExportExcelEnum;
import com.mi.info.intl.retail.ldu.enums.LanguageEnum;
import com.mi.info.intl.retail.ldu.infra.entity.IntlLduReportLog;
import com.mi.info.intl.retail.ldu.infra.entity.IntlLduTarget;
import com.mi.info.intl.retail.ldu.infra.entity.IntlRmsProduct;
import com.mi.info.intl.retail.ldu.infra.mapper.IntlLduReportLogMapper;
import com.mi.info.intl.retail.ldu.infra.mapper.IntlLduTargetMapper;
import com.mi.info.intl.retail.ldu.service.IntlRmsProductService;
import com.mi.info.intl.retail.ldu.util.ConstantMessageTemplate;
import com.mi.info.intl.retail.ldu.util.NrJobTaskUtils;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.org.infra.entity.IntlRmsCountryTimezone;
import com.mi.info.intl.retail.org.infra.mapper.IntlRmsCountryTimezoneMapper;
import com.mi.info.intl.retail.utils.time.DateTimeUtil;
import com.xiaomi.com.i18n.area.Area;
import com.xiaomi.keycenter.okhttp3.OkHttpClient;
import com.xiaomi.keycenter.okhttp3.Request;
import com.xiaomi.keycenter.okhttp3.Response;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.ObjectId;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;

/**
 * <AUTHOR>
 * @date 2025-07-04
 */
@Slf4j
@Service
@DubboService(group = "${dubbo-group.provider.intl-retail:}", interfaceClass = IntlLduTargetService.class)
public class IntlLduTargetServiceImpl extends ServiceImpl<IntlLduTargetMapper, IntlLduTarget> implements IntlLduTargetService {

    @Resource
    private FdsService fdsService;

    @Resource
    private IntlLduTargetMapper intlLduTargetMapper;

    @Resource
    private IntlRmsCountryTimezoneMapper intlRmsCountryTimezoneMapper;

    @Resource
    private NrJobTaskUtils nrJobTaskUtils;


    @Resource
    private LduConfig lduConfig;

    @Resource
    private IntlRmsProductService intlRmsProductService;


    @Resource
    private IntlLduReportLogMapper intlLduReportLogMapper;


    @Resource
    private JobTriggerHelper jobTriggerHelper;


    private static final int MAX_NUM = 5000;

    private static final int MAX_NUMS = 30000;

    private static final long TIME_NUMS = 1000L;

    private static final int FAIL_NUMS = 0001;

    private static final long START_NUMS = 1L;

    private static final int FAIL_CODE = -1;

    @Resource
    @Qualifier("syncDataThreadPool")
    private ThreadPoolTaskExecutor executor;

    @Override
    public IPage<IntlLduTargetDto> pageListTarget(IntlLduTargetReq query) {
        List<String> countrysCode = new ArrayList<>();
        String areaId = RpcContext.getContext().getAttachment(LduCommonConfigEnum.AREA_ID.getCode());
        if (!CollectionUtils.isEmpty(query.getCountryCode())) {
            countrysCode = query.getCountryCode();
        }
        if (CollectionUtils.isEmpty(countrysCode) && !LduCommonConfigEnum.GLOBAL.getCode().equals(areaId) && areaId != null) {
            countrysCode.add(areaId);
        }
        Page<IntlLduTarget> page = new Page<>(query.getPageNum(), query.getPageSize());
        LambdaQueryWrapper<IntlLduTarget> queryWrapper = Wrappers.<IntlLduTarget>lambdaQuery()
                .eq(StringUtils.isNotEmpty(query.getProductLine()), IntlLduTarget::getProductLine, query.getProductLine())
                .like(StringUtils.isNotEmpty(query.getProjectName()), IntlLduTarget::getProjectName, query.getProjectName())
                .eq(StringUtils.isNotEmpty(query.getProjectCode()), IntlLduTarget::getProjectCode, query.getProjectCode().trim())
                .in(CollectionUtils.isNotEmpty(countrysCode), IntlLduTarget::getCountryCode, countrysCode)
                .orderByDesc(IntlLduTarget::getTargetCreateDate);

        Page<IntlLduTarget> taskConfPage = this.page(page, queryWrapper);
        Page<IntlLduTargetDto> pageDTO = new Page<>(query.getPageNum(), query.getPageSize());

        if (CollectionUtils.isEmpty(taskConfPage.getRecords())) {
            pageDTO.setCurrent(taskConfPage.getCurrent());
            pageDTO.setSize(taskConfPage.getSize());
            return pageDTO;
        }
        List<IntlLduTargetDto> list = ComponentLocator.getConverter()
                .convertList(taskConfPage.getRecords(), IntlLduTargetDto.class);

        list.stream().forEach(item -> {
            cntoEn(item);
            timeZoneConvert(item, areaId);
        });
        pageDTO.setPages(taskConfPage.getPages());
        pageDTO.setTotal(taskConfPage.getTotal());
        pageDTO.setRecords(list);
        return pageDTO;
    }

    private void cntoEn(IntlLduTargetDto item) {
        String language = RpcContext.getContext().getAttachment(LduCommonConfigEnum.LANGUAGE.getCode());
        if (LanguageEnum.EN_US.getCode().equals(language)) {
            item.setProductLine(item.getProductLineNameEn());
            item.setProjectName(item.getProjectNameEn());
        } else {
            item.setProductLine(item.getProductLineName());
            item.setProjectName(item.getProjectName());
        }
    }

    @Override
    public CommonApiResponse<IPage<IntlLduTargetDto>> pageList(IntlLduTargetReq query) {
        String areaId = RpcContext.getContext().getAttachment(LduCommonConfigEnum.AREA_ID.getCode());
        List<String> countrysCode = new ArrayList<>();
        if (!CollectionUtils.isEmpty(query.getCountryCode())) {
            countrysCode = query.getCountryCode();
        }
        if (CollectionUtils.isEmpty(countrysCode) && !LduCommonConfigEnum.GLOBAL.getCode().equals(areaId) && areaId != null) {
            countrysCode.add(areaId);
        }
        Page<IntlLduTarget> page = new Page<>(query.getPageNum(), query.getPageSize());
        LambdaQueryWrapper<IntlLduTarget> queryWrapper = Wrappers.<IntlLduTarget>lambdaQuery()
                .eq(StringUtils.isNotEmpty(query.getProductLine()), IntlLduTarget::getProductLine, query.getProductLine())
                .like(StringUtils.isNotEmpty(query.getProjectName()), IntlLduTarget::getProjectName, query.getProjectName())
                .eq(StringUtils.isNotEmpty(query.getProjectCode()), IntlLduTarget::getProjectCode, query.getProjectCode().trim())
                .in(CollectionUtils.isNotEmpty(countrysCode), IntlLduTarget::getCountryCode, countrysCode)
                .orderByDesc(IntlLduTarget::getTargetCreateDate);

        Page<IntlLduTarget> taskConfPage = this.page(page, queryWrapper);
        Page<IntlLduTargetDto> pageDTO = new Page<>(query.getPageNum(), query.getPageSize());

        if (CollectionUtils.isEmpty(taskConfPage.getRecords())) {
            pageDTO.setCurrent(taskConfPage.getCurrent());
            pageDTO.setSize(taskConfPage.getSize());
            return new CommonApiResponse<>(pageDTO);
        }
        List<IntlLduTargetDto> list = ComponentLocator.getConverter()
                .convertList(taskConfPage.getRecords(), IntlLduTargetDto.class);

        list.stream().forEach(item -> {
            cntoEn(item);
            timeZoneConvert(item, areaId);
        });
        pageDTO.setPages(taskConfPage.getPages());
        pageDTO.setTotal(taskConfPage.getTotal());
        pageDTO.setRecords(list);
        return new CommonApiResponse<>(pageDTO);
    }

    @Override
    public CommonApiResponse<String> create(IntlLduTargetDto confList) {
        List<IntlLduTargetDto> lduSnList = new ArrayList<>();
        IntlLduTarget conf = intlLduTargetMapper.queryByProjectCode(confList.getProjectCode(), confList.getCountryCode());
        if (!Objects.isNull(conf)) {
            return CommonApiResponse.failure(FAIL_CODE, ConstantMessageTemplate.getTargetDataExistsMessageNoNum());
        }
        lduSnList.add(confList);
        List<IntlLduTarget> intlLduTargets = ComponentLocator.getConverter()
                .convertList(lduSnList, IntlLduTarget.class);
        List<IntlRmsProduct> intlRmsProductList = intlRmsProductService.searchByProjectCodeList(Arrays.asList(confList.getProjectCode()));

        if (!CollectionUtils.isEmpty(intlLduTargets) && !CollectionUtils.isEmpty(intlRmsProductList)) {
            List<ProjectInfoDto> projectInfoDtoList = intlRmsProductList.stream()
                    .map(this::convertToDto).distinct().collect(Collectors.toList());
            Map<String, ProjectInfoDto> projectInfoDtoMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(projectInfoDtoList)) {
                List<IntlRmsCountryTimezone> intlRmsCountryTimezones = intlRmsCountryTimezoneMapper.selectAll();
                Map<String, IntlRmsCountryTimezone> intlRmsCountryTimezoneMap = intlRmsCountryTimezones.stream()
                        .collect(Collectors.toMap(IntlRmsCountryTimezone::getCountryCode, v -> v));
                projectInfoDtoMap.put(confList.getProjectCode(), projectInfoDtoList.get(0));
                addOtherBatchData(intlLduTargets, projectInfoDtoMap, intlRmsCountryTimezoneMap, null);
                intlLduTargetMapper.batchInsert(intlLduTargets);
            } else {
                return CommonApiResponse.failure(FAIL_CODE, ConstantMessageTemplate.getProductIdNotExistsMessage());
            }
        }
        return new CommonApiResponse<>(CommonConstant.SUCCESS);
    }


    private ProjectInfoDto convertToDto(IntlRmsProduct product) {
        ProjectInfoDto dto = new ProjectInfoDto();
        dto.setProjectCode(product.getProjectCode());
        dto.setProjectNameEn(product.getShortname());
        dto.setProjectNameCn(product.getShortname());
        dto.setProductLine(product.getProductLine());
        dto.setProductLineCode(product.getProductLineCode());
        dto.setProductLineEn(product.getProductLineEn());
        return dto;
    }

    @Override
    public CommonApiResponse<String> modify(IntlLduTargetDto confList) {
        IntlLduTarget conf = ComponentLocator.getConverter()
                .convert(confList, IntlLduTarget.class);
        addOtherData(conf);
        intlLduTargetMapper.updateById(conf);
        return new CommonApiResponse<>(CommonConstant.SUCCESS);
    }


    private void addOtherData(IntlLduTarget intlLduTarget) {
        intlLduTarget.setTargetUpdateDate(System.currentTimeMillis());
        intlLduTarget.setUpdateUserId(RpcContext.getContext().getAttachments().get(LduCommonConfigEnum.UPC_MIID.getCode()));
        intlLduTarget.setUpdateUserName(RpcContext.getContext().getAttachments().get(LduCommonConfigEnum.USER_NAME.getCode()));
    }

    private CommonResponse<String> nrJobForExcelExport(String upload, String upcAccount) {

        JobSuccessDto build = JobSuccessDto.builder().fileUrl(upload).build();

        NrJobGoIn nrJobGoIn = NrJobGoIn.builder()
                .jobKey(ExportExcelEnum.LDU_TARGET_REPORT.getJobKey())
                .owner(upcAccount)
                .taskDesc(ExportExcelEnum.LDU_TARGET_REPORT.getExcelEnName())
                .taskParam(JSONUtil.toJsonStr(build))
                // 链路追踪ID
                .traceId(ObjectId.next())
                .taskName(ExportExcelEnum.LDU_TARGET_REPORT.getExcelName())
                .build();

        return nrJobTaskUtils.triggerNrJobTask(nrJobGoIn);
    }

    private void addOtherBatchData(List<IntlLduTarget> intlLduTargets, Map<String, ProjectInfoDto> projectInfoDtoMap,
                                   Map<String, IntlRmsCountryTimezone> intlRmsCountryTimezoneMap, Map<String, StoreMetricsDto> reportMap) {
        //根据填写的SKU信息查询产品主数据对以下字段赋值数,如果项目代码、RMA、ROM无值，则不赋值
        if (CollectionUtils.isNotEmpty(intlLduTargets)) {
            intlLduTargets.forEach(intlLduTarget -> {
                IntlRmsCountryTimezone countryTimezone = intlRmsCountryTimezoneMap.get(intlLduTarget.getCountryCode());
                if (!Objects.isNull(countryTimezone)) {
                    intlLduTarget.setCountry(Objects.isNull(countryTimezone) ? "" : countryTimezone.getCountryName());
                    intlLduTarget.setRegionCode(Objects.isNull(countryTimezone) ? "" : countryTimezone.getAreaCode());
                    intlLduTarget.setRegion(Objects.isNull(countryTimezone) ? "" : countryTimezone.getArea());
                }
                ProjectInfoDto projectInfoDto = projectInfoDtoMap.get(intlLduTarget.getProjectCode());
                if (Objects.nonNull(projectInfoDto)) {
                    intlLduTarget.setProductLine(Objects.isNull(projectInfoDto.getProductLineCode()) ? ""
                            : String.valueOf(projectInfoDto.getProductLineCode()));
                    intlLduTarget.setProjectName(projectInfoDto.getProjectNameCn());
                    intlLduTarget.setProjectNameEn(projectInfoDto.getProjectNameEn());
                    intlLduTarget.setProductLineName(projectInfoDto.getProductLine());
                    intlLduTarget.setProductLineNameEn(projectInfoDto.getProductLineEn());
                }
                // 赋值实际覆盖率和实际门店覆盖率
                if (MapUtils.isEmpty(reportMap)) {
                    calculateActualCoverage(intlLduTarget);
                } else {
                    StoreMetricsDto storeMetricsDto = reportMap.get(intlLduTarget.getProjectCode() + intlLduTarget.getCountryCode());
                    if (Objects.nonNull(storeMetricsDto)) {
                        intlLduTarget.setActualCoveredStores(storeMetricsDto.getActualCoveredStores());
                        intlLduTarget.setActualSampleOut(storeMetricsDto.getActualDisplayCount());
                    }
                }
                intlLduTarget.setTargetCreateDate(System.currentTimeMillis());
                intlLduTarget.setCreateUserId(RpcContext.getContext().getAttachments().get(LduCommonConfigEnum.UPC_MIID.getCode()));
                intlLduTarget.setCreateUserName(RpcContext.getContext().getAttachments().get(LduCommonConfigEnum.USER_NAME.getCode()));
            });
        }
    }

    private void calculateActualCoverage(IntlLduTarget intlLduTarget) {
        List<IntlLduReportLog> reportLogList = new ArrayList<>();
        IntlLduReportLog reportLog = new IntlLduReportLog();
        reportLog.setProjectCode(intlLduTarget.getProjectCode());
        reportLog.setCountryCode(intlLduTarget.getCountryCode());
        reportLogList.add(reportLog);
        List<StoreMetricsDto> storeMetricsDtoList = intlLduReportLogMapper.statisticReportLogBatch(reportLogList);
        if (CollUtil.isNotEmpty(storeMetricsDtoList)) {
            intlLduTarget.setActualCoveredStores(storeMetricsDtoList.get(0).getActualCoveredStores());
            intlLduTarget.setActualSampleOut(storeMetricsDtoList.get(0).getActualDisplayCount());
        }
    }

    private void timeZoneConvert(IntlLduTargetDto item, String areaCode) {
        if (!StringUtils.isEmpty(areaCode) && !LduCommonConfigEnum.GLOBAL.getCode().equals(areaCode)) {
            item.setTargetCreateTime(Area.of(areaCode).timeFormat(item.getTargetCreateDate(), CommonConstant.DATE_TIME_FORMAT));
            item.setTargetUpdateTime(item.getTargetUpdateDate() != CommonConstant.DEFAULT_VALUE ?
                    Area.of(areaCode).timeFormat(item.getTargetUpdateDate(), CommonConstant.DATE_TIME_FORMAT) : "");
        } else {
            item.setTargetCreateTime(DateTimeUtil.formatTimestamp(item.getTargetCreateDate()));
            item.setTargetUpdateTime(item.getTargetUpdateDate() != CommonConstant.DEFAULT_VALUE ?
                    DateTimeUtil.formatTimestamp(item.getTargetUpdateDate()) : "");
        }
    }


    @Override
    public CommonResponse<String> exportTargetMaintenance(IntlLduTargetReq query) {
        File tempFile = null;
        ExcelWriter excelWriter = null;
        try {
            tempFile = File.createTempFile(LduCommonConfigEnum.LDU_TARGET_NAME.getName(), LduCommonConfigEnum.EXCEL_TYPE.getCode());
            String areaId = RpcContext.getContext().getAttachment(LduCommonConfigEnum.AREA_ID.getCode());
            String upcAccount = RpcContext.getContext().getAttachment(LduCommonConfigEnum.UPC_ACCOUNT.getCode());
            String language = RpcContext.getContext().getAttachment(LduCommonConfigEnum.LANGUAGE.getCode());
            if (LanguageEnum.EN_US.getCode().equals(language)) {
                excelWriter = EasyExcel.write(tempFile, IntlLduTargetEnExcel.class)
                        .registerWriteHandler(new ExportExcelAspect.AdaptiveWidthStyleStrategy()).build();
            } else {
                excelWriter = EasyExcel.write(tempFile, IntlLduTargetExcel.class)
                        .registerWriteHandler(new ExportExcelAspect.AdaptiveWidthStyleStrategy()).build();
            }

            WriteSheet writeSheet = EasyExcel.writerSheet(LduCommonConfigEnum.TASK_LIST.getName()).build();

            long pageSize = TIME_NUMS;
            long currentPage = START_NUMS;
            boolean hasNext = true;

            while (hasNext) {
                query.setPageNum(currentPage);
                query.setPageSize(pageSize);

                IPage<IntlLduTargetDto> searchResult = this.pageListTarget(query);

                List<IntlLduTargetDto> records = searchResult.getRecords();
                List<IntlLduTargetExcelDto> excelDtoList = new ArrayList<>();

                records.stream().forEach(record -> {
                    IntlLduTargetExcelDto excelDto = ComponentLocator.getConverter()
                            .convert(record, IntlLduTargetExcelDto.class);
                    if (LanguageEnum.EN_US.getCode().equals(language)) {
                        excelDto.setProductLine(record.getProductLineNameEn());
                    } else {
                        excelDto.setProductLine(record.getProductLineName());
                    }
                    excelDtoList.add(excelDto);
                });
                if (CollUtil.isEmpty(records)) {
                    hasNext = false;
                    continue;
                }
                excelWriter.write(excelDtoList, writeSheet);

                hasNext = currentPage * pageSize < searchResult.getTotal();
                currentPage++;
            }
            if (excelWriter != null) {
                excelWriter.finish();
            }
            String timestamp = String.valueOf(System.currentTimeMillis() / TIME_NUMS);

            FdsUploadResult upload = fdsService.upload(LduCommonConfigEnum.LDU_TARGET_NAME.getName()
                            + timestamp + LduCommonConfigEnum.EXCEL_TYPE.getCode(),
                    tempFile, true);

            return nrJobForExcelExport(upload.getUrl(), upcAccount);
        } catch (Exception e) {
            log.error("LDU目标维护列表导出异常: {}", e);
            return CommonResponse.failure(FAIL_NUMS, CommonConstant.EXPORT_FAILED_ERROR);
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
            if (tempFile != null && tempFile.exists()) {
                tempFile.delete();
            }
        }
    }

    private String exportTargetLdu(List<ImportLduTargetDto> intlLduTargetList) {
        File tempFile = null;
        ExcelWriter excelWriter = null;
        try {
            tempFile = File.createTempFile(LduCommonConfigEnum.LDU_TARGET_NAME.getName(), LduCommonConfigEnum.EXCEL_TYPE.getCode());
            excelWriter = EasyExcel.write(tempFile, ImportLduTargetExcel.class)
                    .registerWriteHandler(new ExportExcelAspect.AdaptiveWidthStyleStrategy()).build();

            WriteSheet writeSheet = EasyExcel.writerSheet(LduCommonConfigEnum.TASK_LIST.getName()).build();
            List<ImportLduTargetExcel> excelDtoList = ComponentLocator.getConverter()
                    .convertList(intlLduTargetList, ImportLduTargetExcel.class);

            excelWriter.write(excelDtoList, writeSheet);
            if (excelWriter != null) {
                excelWriter.finish();
            }
            String timestamp = String.valueOf(System.currentTimeMillis() / TIME_NUMS);
            FdsUploadResult upload = fdsService.upload(LduCommonConfigEnum.LDU_ERROR_RECORD.getName()
                    + timestamp + LduCommonConfigEnum.EXCEL_TYPE.getCode(), tempFile, true);
            return upload.getUrl();
        } catch (Exception e) {
            log.error("LDU目标维护列表导出异常: {}", e);
            return "";
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
            if (tempFile != null && tempFile.exists()) {
                tempFile.delete();
            }
        }
    }

    private List<String> getProjectCodeList(Sheet sheet) {
        List<String> skuList = new ArrayList<>();
        for (Row row : sheet) {
            if (row.getRowNum() == 0) {
                continue;
            }
            skuList.add(this.getCellValue(row.getCell(1)).trim());
        }
        return skuList;
    }

    private List<String> getProjectCountryList(Sheet sheet) {
        List<String> skuList = new ArrayList<>();
        for (Row row : sheet) {
            if (row.getRowNum() == 0) {
                continue;
            }
            skuList.add(this.getCellValue(row.getCell(1)).trim()
                    + this.getCellValue(row.getCell(0)).trim());
        }
        return skuList;
    }

    private List<IntlLduReportLog> getReportLogList(Sheet sheet) {
        List<IntlLduReportLog> dtoList = new ArrayList<>();
        for (Row row : sheet) {
            if (row.getRowNum() == 0) {
                continue;
            }
            IntlLduReportLog dto = new IntlLduReportLog();
            dto.setProjectCode(this.getCellValue(row.getCell(1)).trim());
            dto.setCountryCode(this.getCellValue(row.getCell(0)).trim());
            dtoList.add(dto);
        }
        return dtoList;
    }


    @Override
    public CommonApiResponse<String> importTargetMaintenance(ImportIntlLduTargetReq query) {
        try {
            // 创建 OkHttpClient 实例
            OkHttpClient client = new OkHttpClient();
            // 创建 HTTP 请求
            Request request = new Request.Builder().url(query.getUrl()).build();
            Response response = client.newCall(request).execute();
            List<ImportLduTargetDto> intlLduTargetList = new ArrayList<>();
            Map<String, ProjectInfoDto> projectInfoDtoMap = new HashMap<>();
            Map<String, StoreMetricsDto> reportMap = new HashMap<>();
            //区域国家查询
            List<IntlRmsCountryTimezone> intlRmsCountryTimezones = intlRmsCountryTimezoneMapper.selectAll();
            Map<String, IntlRmsCountryTimezone> intlRmsCountryTimezoneMap = intlRmsCountryTimezones.stream()
                    .collect(Collectors.toMap(IntlRmsCountryTimezone::getCountryCode, v -> v));
            if (response.isSuccessful()) {
                // 获取响应的输入流
                InputStream inputStream = Objects.requireNonNull(response.body()).byteStream();
                Workbook workbook = new XSSFWorkbook(inputStream);
                Sheet sheet = workbook.getSheetAt(0);
                //获取sku列表
                List<String> codeList = getProjectCodeList(sheet);
                if (codeList.size() > MAX_NUMS) {
                    return CommonApiResponse.failure(FAIL_CODE, ConstantMessageTemplate.maxNumTioMessage());
                }
                List<IntlRmsProduct> intlRmsProductList = intlRmsProductService.searchByProjectCodeList(codeList);
                if (CollectionUtils.isEmpty(intlRmsProductList)) {
                    return CommonApiResponse.failure(FAIL_CODE, ConstantMessageTemplate.xiaoMiProductIdNotExistsMessage());
                }

                List<ProjectInfoDto> projectInfoDtoList = intlRmsProductList.stream()
                        .map(this::convertToDto).distinct()
                        .collect(Collectors.toList());
                projectInfoDtoMap = projectInfoDtoList.stream()
                        .collect(Collectors.toMap(ProjectInfoDto::getProjectCode, v -> v));
                //重复校验:
                Map<String, IntlLduTarget> targetMap = new HashMap<>();
                checkTargetList(sheet, targetMap);

                //查询是否关联已经上报的数据，填充实际覆盖率和实际门店覆盖率
                List<IntlLduReportLog> reportLogList = getReportLogList(sheet);
                checkReportLog(reportLogList, reportMap);

                for (Row row : sheet) {
                    if (row.getRowNum() == 0) {
                        if (!LduCommonConfigEnum.COUNTRY_CODE.getCode().equals(getCellValue(row.getCell(0)))
                                && !LduCommonConfigEnum.PROJECT_CODE.getCode().equals(getCellValue(row.getCell(1)))) {
                            return CommonApiResponse.failure(FAIL_CODE, ConstantMessageTemplate.getTemplateError());
                        }
                        continue;
                    }
                    String countryCode = this.getCellValue(row.getCell(0)).trim();
                    String projectCode = this.getCellValue(row.getCell(1)).trim();
                    String targetCoverages = this.getCellValue(row.getCell(2)).trim();
                    String storeTargetCoverages = this.getCellValue(row.getCell(3)).trim();
                    // 校验数据
                    covertTargetMaintenance(intlLduTargetList, countryCode, projectInfoDtoMap,
                            targetMap, targetCoverages, storeTargetCoverages, projectCode, intlRmsCountryTimezoneMap);
                }

            }
            // 没有异常，则批量新增
            boolean allErrorInfoEmpty = intlLduTargetList.stream()
                    .map(ImportLduTargetDto::getErrorInfo)
                    .allMatch(errorInfo -> errorInfo == null || errorInfo.isEmpty());

            if (allErrorInfoEmpty) {
                List<IntlLduTarget> intlLduTargets = ComponentLocator.getConverter()
                        .convertList(intlLduTargetList, IntlLduTarget.class);
                addOtherBatchData(intlLduTargets, projectInfoDtoMap, intlRmsCountryTimezoneMap, reportMap);
                // 异步多线程批量插入（分批次并行处理）
                batchInsertAsync(intlLduTargets);
                return CommonApiResponse.success(CommonConstant.SUCCESS);
            } else {
                //有异常导出错误信息的excel文件
                String url = exportTargetLdu(intlLduTargetList);
                return CommonApiResponse.failure(FAIL_CODE, ConstantMessageTemplate.getExcelError(), url);
            }

        } catch (Exception e) {
            log.error("批量新增目标维护异常: {}", e);
        }
        return new CommonApiResponse<>(CommonConstant.SUCCESS);
    }

    private void checkTargetList(Sheet sheet, Map<String, IntlLduTarget> targetMap) {
        List<IntlLduTarget> targetList = intlLduTargetMapper.queryByProjectCodeList(getProjectCountryList(sheet));
        if (CollectionUtils.isNotEmpty(targetList)) {
            targetList.stream().forEach(target -> {
                targetMap.put(target.getProjectCode() + target.getCountryCode(), target);
            });
        }
    }

    private void checkReportLog(List<IntlLduReportLog> reportLogList, Map<String, StoreMetricsDto> reportMap) {
        if (CollectionUtils.isNotEmpty(reportLogList)) {
            List<StoreMetricsDto> storeMetricsDtoList = intlLduReportLogMapper.statisticReportLogBatch(reportLogList);
            if (CollectionUtils.isNotEmpty(storeMetricsDtoList)) {
                storeMetricsDtoList.stream().forEach(target -> {
                    reportMap.put(target.getProjectCode() + target.getCountryCode(), target);
                });
            }
        }
    }


    /**
     * 异步多线程批量插入
     */
    private void batchInsertAsync(List<IntlLduTarget> targets) {
        if (targets.isEmpty()) {
            return;
        }
        // 分成多个批次
        List<List<IntlLduTarget>> batches = splitIntoBatches(targets, MAX_NUM);
        // 并行处理所有批次
        List<CompletableFuture<Void>> futures = batches.stream()
                .map(batch -> CompletableFuture.runAsync(() -> {
                    try {
                        // 单个批次插入
                        intlLduTargetMapper.batchInsert(batch);
                        log.info("完成一批次插入，数量：{}", batch.size());
                    } catch (Exception e) {
                        log.error("批次插入失败，数量：{}", batch.size(), e);
                    }
                }, executor))
                .collect(Collectors.toList());
        // 等待所有批次完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .exceptionally(ex -> {
                    log.error("批量插入整体失败", ex);
                    throw new RuntimeException("批量插入失败", ex);
                })
                .join(); // 等待所有异步任务完成
    }

    /**
     * 将列表分成多个指定大小的子列表
     */
    private List<List<IntlLduTarget>> splitIntoBatches(List<IntlLduTarget> list, int batchSize) {
        List<List<IntlLduTarget>> batches = new ArrayList<>();
        for (int i = 0; i < list.size(); i += batchSize) {
            int end = Math.min(i + batchSize, list.size());
            batches.add(list.subList(i, end));
        }
        return batches;
    }


    private void covertTargetMaintenance(List<ImportLduTargetDto> intlLduTargetList, String countryCode, Map<String, ProjectInfoDto> projectInfoDtoMap,
                                         Map<String, IntlLduTarget> targetMap, String targetCoverages, String storeTargetCoverages, String projectCode,
                                         Map<String, IntlRmsCountryTimezone> countryTimezoneMap) {
        StringBuilder errorMsg = new StringBuilder();
        ImportLduTargetDto intlLduTarget = new ImportLduTargetDto();
        //根据这些信息查询零售商信息，零售商名称等其他数据
        intlLduTarget.setTargetCoveredStores(StringUtils.isEmpty(targetCoverages) ? 0
                : (int) Double.parseDouble(targetCoverages));
        intlLduTarget.setTargetSampleOut(StringUtils.isEmpty(storeTargetCoverages) ? 0
                : (int) Double.parseDouble(storeTargetCoverages));
        intlLduTarget.setCountryCode(countryCode);
        intlLduTarget.setProjectCode(projectCode);

        //为空校验
        if (checkIsEmpty(projectCode, countryCode, targetCoverages, storeTargetCoverages, errorMsg)) {
            //校验数据：
            checkError(intlLduTarget, errorMsg, projectInfoDtoMap, targetMap, countryTimezoneMap);
        }
        intlLduTarget.setErrorInfo(errorMsg.toString());
        intlLduTargetList.add(intlLduTarget);
    }

    private boolean checkIsEmpty(String projectCode, String countryCode,
                                 String targetCoverages, String storeTargetCoverages, StringBuilder errorMsg) {
        boolean flag = true;
        if (StringUtils.isEmpty(projectCode)) {
            flag = false;
            errorMsg.append(ConstantMessageTemplate.productIdNotExistsMessage());

        } else if (!isValidPositiveInteger(targetCoverages)) {
            flag = false;
            errorMsg.append(ConstantMessageTemplate.targetNumsNotExistsMessage());
        } else if (!isValidPositiveInteger(storeTargetCoverages)) {
            flag = false;
            errorMsg.append(ConstantMessageTemplate.targetProductNumsNotExistsMessage());
        } else if (StringUtils.isEmpty(countryCode)) {
            flag = false;
            errorMsg.append(ConstantMessageTemplate.getXiaomiCountryCodeEmptyMessage());
        }
        return flag;
    }

    public boolean isValidPositiveInteger(String str) {
        Pattern pattern = Pattern.compile("^[1-9]\\d*$");
        // 先判断字符串是否为null或空
        if (str == null || str.trim().isEmpty()) {
            return false;
        } else {
            str = str.split("\\.")[0];
        }
        // 使用正则表达式匹配
        return pattern.matcher(str).matches();
    }


    private void checkError(ImportLduTargetDto intlLduTarget, StringBuilder errorMsg, Map<String, ProjectInfoDto> projectInfoDtoMap,
                            Map<String, IntlLduTarget> targetMap, Map<String, IntlRmsCountryTimezone> countryTimezoneMap) {
        if (Objects.isNull(projectInfoDtoMap.get(intlLduTarget.getProjectCode()))) {
            errorMsg.append(ConstantMessageTemplate.xiaoMiProductIdNotExistsMessage());
        } else if (StringUtils.isEmpty(intlLduTarget.getCountryCode()) ||
                Objects.isNull(countryTimezoneMap.get(intlLduTarget.getCountryCode()))) {
            errorMsg.append(ConstantMessageTemplate.getXiaomiCountryCodeEmptyMessage());
        } else if (!Objects.isNull(targetMap.get(intlLduTarget.getProjectCode() + intlLduTarget.getCountryCode()))) {
            errorMsg.append(ConstantMessageTemplate.getTargetDataExistsMessage());
        }
    }


    private String getCellValue(Cell cell) {
        if (cell == null) {
            return "";
        }
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    return String.valueOf(cell.getNumericCellValue());
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }

    @Override
    public CommonApiResponse<String> downLoadLduTemp(BathConReq query) {
        return new CommonApiResponse<>(lduConfig.getTargetUploadUrl());
    }

    @Override
    public CommonApiResponse<List<IntlLduTargetStatisticsDto>> getStatistics(IntlLduTargetStatisticsReq query) {
        try {
            List<LduStatisticsDto> statisticsList = intlLduTargetMapper.selectStatisticsByCountryProductProject(
                    query.getCountryCode(), query.getProjectCode());

            // 直接使用ComponentLocator进行对象转换，复用现有逻辑
            List<IntlLduTargetStatisticsDto> resultList = ComponentLocator.getConverter()
                    .convertList(statisticsList, IntlLduTargetStatisticsDto.class);

            return new CommonApiResponse<List<IntlLduTargetStatisticsDto>>(resultList);
        } catch (Exception e) {
            return CommonApiResponse.failure(CommonConstant.ERROR_STATUS, CommonConstant.QUERY_STATISTICS_FAILED_ERROR
                    + e.getMessage());
        }
    }

    @Override
    public CommonApiResponse<List<IntlLduTargetStatisticsDto>> getAllStatistics() {
        return getAllStatistics(null);
    }

    @Override
    public CommonApiResponse<List<IntlLduTargetStatisticsDto>> getAllStatistics(List<IntlLduTargetStatisticsReq> list) {
        try {
            List<LduStatisticsDto> statisticsList = intlLduTargetMapper.selectAllStatistics(list);

            // 直接使用ComponentLocator进行对象转换，复用现有逻辑
            List<IntlLduTargetStatisticsDto> resultList = ComponentLocator.getConverter()
                    .convertList(statisticsList, IntlLduTargetStatisticsDto.class);

            return new CommonApiResponse<List<IntlLduTargetStatisticsDto>>(resultList);
        } catch (Exception e) {
            return CommonApiResponse.failure(CommonConstant.ERROR_STATUS, CommonConstant.QUERY_STATISTICS_FAILED_ERROR
                    + e.getMessage());
        }
    }
}
