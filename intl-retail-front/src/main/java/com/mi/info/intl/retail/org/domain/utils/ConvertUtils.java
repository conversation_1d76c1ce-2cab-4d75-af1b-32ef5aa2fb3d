package com.mi.info.intl.retail.org.domain.utils;

import com.mi.info.intl.retail.api.country.dto.CountryDTO;
import com.mi.info.intl.retail.org.infra.entity.IntlRmsCountryTimezone;

/**
 * 转换utils
 *
 * <AUTHOR>
 * @date 2025/07/30
 */
public class ConvertUtils {

    /**
     * 获取国家DTO
     *
     * @param countryTimezone 乡村时区
     * @return {@link CountryDTO }
     */
    public static CountryDTO getCountryDTO(IntlRmsCountryTimezone countryTimezone) {
        CountryDTO countryDTO = new CountryDTO();
        countryDTO.setId(countryTimezone.getId());
        countryDTO.setCountryName(countryTimezone.getCountryName());
        countryDTO.setCountryCode(countryTimezone.getCountryCode());
        countryDTO.setTimezoneCode(countryTimezone.getTimezoneCode());
        countryDTO.setArea(countryTimezone.getArea());
        countryDTO.setAreaCode(countryTimezone.getAreaCode());
        countryDTO.setCreatedAt(countryTimezone.getCreatedAt());
        countryDTO.setUpdatedAt(countryTimezone.getUpdatedAt());
        countryDTO.setStateCode(countryTimezone.getStateCode());
        return countryDTO;
    }
}
