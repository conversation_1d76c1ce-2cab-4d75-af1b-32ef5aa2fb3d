package com.mi.info.intl.retail.org.domain.material.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mi.info.intl.retail.cooperation.task.domain.RmsCountryTimezoneService;
import com.mi.info.intl.retail.cooperation.task.dto.dto.IntlRmsUserDto;
import com.mi.info.intl.retail.cooperation.task.inspection.IntlRmsUserService;
import com.mi.info.intl.retail.core.utils.CollUtils;
import com.mi.info.intl.retail.core.utils.IntlRetailAssert;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.InspectionRecordPageItem;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.InspectionRecordPageRequest;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialInspectionDetailRequest;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialInspectionDetailResponse;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialInspectionItem;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialInspectionOperationHistoryResponse;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialInspectionRecordPageDTO;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialInspectionRecordSelectorItem;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialInspectionRecordSelectorMap;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialInspectionReq;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialInspectionVerifyRequest;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialPhotoGroup;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialSampleRequest;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialSampleResponse;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.PosmMaterialDTO;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.SubmitMaterialInspectionRequest;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.TaskRemindDTO;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.UploadMaterialData;
import com.mi.info.intl.retail.intlretail.service.api.material.enums.BusinessTypeEnum;
import com.mi.info.intl.retail.intlretail.service.api.material.enums.CanModifyEnum;
import com.mi.info.intl.retail.intlretail.service.api.material.enums.PosmMaterialEnum;
import com.mi.info.intl.retail.intlretail.service.api.material.enums.StoreMaterialStatusCoveredEnum;
import com.mi.info.intl.retail.intlretail.service.api.material.enums.TargetTypeEnum;
import com.mi.info.intl.retail.intlretail.service.api.material.enums.TaskTypeEnum;
import com.mi.info.intl.retail.intlretail.service.api.material.enums.YESNOEnum;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.OptionalItem;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.TaskCenterFinishReq;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.TaskCenterTaskReq;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.BoolEnum;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.BusinessStatusEnum;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.DisapproveReasonEnum;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.I18nDesc;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.InspectionStatusEnum;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.MaterialDisapproveReasonEnum;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.MaterialInspectionStatus;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.StoreLimitedRangeEnum;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.TaskStatusEnum;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.taskcenter.TaskCenterTaskNumReq;
import com.mi.info.intl.retail.intlretail.service.api.upload.FileUploadInfo;
import com.mi.info.intl.retail.ldu.dto.LduReportSimple;
import com.mi.info.intl.retail.ldu.enums.FileUploadEnum;
import com.mi.info.intl.retail.ldu.infra.repository.LduReportRepository;
import com.mi.info.intl.retail.ldu.service.IntlFileUploadService;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.model.PageResponse;
import com.mi.info.intl.retail.model.UserInfo;
import com.mi.info.intl.retail.org.app.service.material.convert.NewProductInspectionConvert;
import com.mi.info.intl.retail.org.domain.CalculateSphericalDistance;
import com.mi.info.intl.retail.org.domain.InspectionHistoryDomain;
import com.mi.info.intl.retail.org.domain.InspectionRecordDomain;
import com.mi.info.intl.retail.org.domain.IntlStoreMaterialStatusDomain;
import com.mi.info.intl.retail.org.domain.MaterialInspectionDomain;
import com.mi.info.intl.retail.org.domain.PositionDomain;
import com.mi.info.intl.retail.org.domain.RuleConfigDomain;
import com.mi.info.intl.retail.org.domain.dto.DistanceRange;
import com.mi.info.intl.retail.org.domain.enums.OperationType;
import com.mi.info.intl.retail.org.domain.material.config.NewProductSampleExampleConfig;
import com.mi.info.intl.retail.org.domain.material.service.NewProductInspectionDomainService;
import com.mi.info.intl.retail.org.domain.repository.InspectionHistoryRepository;
import com.mi.info.intl.retail.org.domain.repository.InspectionRecordRepository;
import com.mi.info.intl.retail.org.domain.repository.IntlStoreMaterialStatusRepository;
import com.mi.info.intl.retail.org.domain.repository.NewProductInspectionRepository;
import com.mi.info.intl.retail.org.domain.repository.PositionRepository;
import com.mi.info.intl.retail.org.domain.repository.RuleConfigRepository;
import com.mi.info.intl.retail.org.domain.service.RuleConfigService;
import com.mi.info.intl.retail.org.domain.util.RpcUtil;
import com.mi.info.intl.retail.org.infra.entity.IntlLduInspectionInfo;
import com.mi.info.intl.retail.org.infra.entity.IntlStoreMaterialStatus;
import com.mi.info.intl.retail.org.infra.mapper.IntlLduInspectionInfoMapper;
import com.mi.info.intl.retail.org.infra.mapper.IntlStoreMaterialStatusMapper;
import com.mi.info.intl.retail.org.infra.rpc.TaskCenterServiceRpc;
import com.mi.info.intl.retail.utils.JsonUtil;
import com.mi.info.intl.retail.utils.UserInfoUtil;
import com.mi.info.intl.retail.utils.intl.IntlTimeUtil;
import com.xiaomi.cnzone.maindataapi.model.dto.common.ConfigKV2;
import com.xiaomi.cnzone.maindatacommon.enums.OrganTypeEnums;
import com.xiaomi.cnzone.storems.api.api.StoreRelateProvider;
import com.xiaomi.cnzone.storems.api.model.dto.store.CommonConfigDTO2;
import com.xiaomi.cnzone.storems.api.model.req.store.CommonConfigReq;
import com.xiaomi.nr.eiam.api.dto.userinfo.GetUserInfoResp;
import com.xiaomi.nr.eiam.api.dto.userinfo.ListUserInfoReq;
import com.xiaomi.nr.eiam.api.dto.userinfo.Position;
import com.xiaomi.nr.eiam.api.provider.UserProvider;
import com.xiaomi.nr.global.dev.base.RequestContextInfo;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.validation.Validator;

@Slf4j
@Service
public class NewProductInspectionDomainServiceImpl implements NewProductInspectionDomainService {

    @Resource
    private NewProductInspectionRepository newProductInspectionRepository;

    @DubboReference(group = "${maindata.dubbo.group:}", version = "1.0", check = false, interfaceClass = StoreRelateProvider.class)
    private StoreRelateProvider storeRelateProvider;

    @Autowired
    private InspectionRecordRepository inspectionRecordRepository;

    @Resource
    private PositionRepository positionRepository;

    @Autowired
    private IntlFileUploadService fileUploadService;

    @Resource
    private IntlRmsUserService intlRmsUserService;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private RuleConfigRepository ruleConfigRepository;

    @Autowired
    private TaskCenterServiceRpc taskCenterServiceRpc;

    @Autowired
    private InspectionHistoryRepository inspectionHistoryRepository;

    @Autowired
    private LduReportRepository lduReportRepository;

    @Autowired
    private Validator validator;

    @Autowired
    private RmsCountryTimezoneService rmsCountryTimezoneService;

    @Autowired
    private IntlStoreMaterialStatusRepository intlStoreMaterialStatusRepository;

    @Resource
    private IntlLduInspectionInfoMapper intlLduInspectionInfoMapper;
    
    @Resource
    private IntlStoreMaterialStatusMapper intlStoreMaterialStatusMapper;

    @Resource
    private ThreadPoolTaskExecutor batchQueryExecutor;

    @Resource
    private RuleConfigService ruleConfigService;
    //NewProductSampleExampleConfig
    @Resource
    private NewProductSampleExampleConfig newProductSampleExampleConfig;

    @DubboReference(group = "${organization.dubbo-group}", interfaceClass = UserProvider.class, check = false, timeout = 5000)
    private UserProvider userProvider;
    
    private static final List<Integer> RETAIL_EVENT_BUSINESS_TYPES = Arrays.asList(
            BusinessTypeEnum.NEW_PRODUCT_INSPECTION_TOUR_DUMMY.getCode(),
            BusinessTypeEnum.NEW_PRODUCT_INSPECTION_TOUR_POSM.getCode(),
            BusinessTypeEnum.NEW_PRODUCT_INSPECTION_TOUR_PRICE_TAG.getCode(),
            BusinessTypeEnum.NEW_PRODUCT_INSPECTION_TOUR_LDU.getCode());
    
    @Override
    public Page<MaterialInspectionItem> pageMaterialInspection(MaterialInspectionReq request) {
        request.setBusinessTypes(RETAIL_EVENT_BUSINESS_TYPES);
        Page<MaterialInspectionItem> page = new Page<>(request.getPageNum(), request.getPageSize());
        return newProductInspectionRepository.pageMaterialInspection(page, request);
    }

    @Override
    public boolean hasUnCompletedTask(String account) {
        return newProductInspectionRepository.existsUnCompletedTaskByOwner(account);
    }

    @Override
    public CommonApiResponse<MaterialInspectionDetailResponse> getMaterialInspectionDetail(
            MaterialInspectionDetailRequest request) {
        InspectionRecordDomain inspectionRecordDomain = getInspectionRecordDomain(request.getMaterialInspectionId(),
                request.getTaskInstanceId());
        if (inspectionRecordDomain == null) {
            log.error("新品物料巡检记录不存在: positionInspectionId={}", request.getMaterialInspectionId());
            return new CommonApiResponse<>(500, "新品物料巡检记录不存在", null);
        }
        Integer businessType = inspectionRecordDomain.getBusinessType();
        if (!RETAIL_EVENT_BUSINESS_TYPES.contains(businessType)) {
            log.error("业务类型错误: businessType={}", businessType);
            return new CommonApiResponse<>(500, "业务类型错误", null);
        }
        // 获取阵地信息
        PositionDomain positionDomain = positionRepository.getByCode(inspectionRecordDomain.getPositionCode());
        if (positionDomain == null) {
            log.error("阵地信息不存在: positionCode={}", inspectionRecordDomain.getPositionCode());
            return new CommonApiResponse<>(500, "阵地信息不存在", null);
        }
        // 构建响应对象
        MaterialInspectionDetailResponse response = new MaterialInspectionDetailResponse();
        // 设置门店信息
        MaterialInspectionDetailResponse.StoreInfo storeInfo = new MaterialInspectionDetailResponse.StoreInfo();
        storeInfo.setStoreName(positionDomain.getStoreName());
        storeInfo.setFrontName(positionDomain.getPositionName());
        storeInfo.setFlagCompletion(inspectionRecordDomain.getFlagCompletion());
        RuleConfigDomain ruleConfigByRuleCode = ruleConfigService.getRuleConfigByRuleCode(
                inspectionRecordDomain.getRuleCode());
        String posmMaterials = ruleConfigByRuleCode.getPosmMaterials();
        boolean checkPosmMaterials = checkPosmMaterials(posmMaterials);
        if (checkPosmMaterials) {
            storeInfo.setSingleColumnShow(false);
        }
        if (ruleConfigByRuleCode != null && ruleConfigByRuleCode.getTaskType() != null) {
            storeInfo.setTaskTypeDesc(ruleConfigByRuleCode.getTaskType().getShortname());
        }
        BusinessTypeEnum businessTypeEnum = BusinessTypeEnum.getByCode(inspectionRecordDomain.getBusinessType());
        if (businessTypeEnum != null) {
            storeInfo.setTaskName(businessTypeEnum.getI18nDesc().get());
        }
        storeInfo.setInspectionStatus(inspectionRecordDomain.getInspectionStatus());
        if (inspectionRecordDomain.getPositionInspectionBusinessTypeEnum() != null) {
            storeInfo.setPositionConstructionType(inspectionRecordDomain.getPositionInspectionBusinessTypeEnum().getDesc());
        }
        storeInfo.setCreationTime(inspectionRecordDomain.getPositionCreationTime());
        // 安全设置状态，避免空指针
        storeInfo.setStatus(
                inspectionRecordDomain.getInspectionStatus() != null ? inspectionRecordDomain.getInspectionStatus()
                        .getDesc() : null);
        // 安全设置remark，避免空指针
        storeInfo.setRemark(inspectionRecordDomain.getMaterialDisapproveReason() != null
                ? inspectionRecordDomain.getMaterialDisapproveReason().getI18nDesc().get() : null);
        response.setStoreInfo(storeInfo);
        // 解析上传的数据
        List<UploadMaterialData> sections = new ArrayList<>();
        String uploadDataStr = inspectionRecordDomain.getUploadData();
        if (StringUtils.isNotEmpty(uploadDataStr)) {
            UploadMaterialData[] uploadData = JsonUtil.json2bean(uploadDataStr, UploadMaterialData[].class);
            if (uploadData != null) {
                Map<String, List<String>> phontMap = getPhotoMap(uploadData);
                for (UploadMaterialData db : uploadData) {
                    UploadMaterialData item = new UploadMaterialData();
                    item.setMaterialKey(db.getMaterialKey());
                    item.setMaterialValue(processUploadDataImages(db.getMaterialValue(), phontMap));
                    PosmMaterialEnum posmMaterial = PosmMaterialEnum.valueOfCode(db.getMaterialKey());
                    if (posmMaterial != null) {
                        item.setPosmMaterial(posmMaterial);
                        item.setMaterialShowName(posmMaterial.getShowName(posmMaterial));
                    }
                    sections.add(item);
                }
            }
        }
        response.setSections(sections);
        return new CommonApiResponse<>(response);
    }

    @Override
    public CommonApiResponse<String> submitMaterialInspection(SubmitMaterialInspectionRequest request) {
        InspectionRecordDomain inspectionRecordDomain = getInspectionRecordDomain(request.getMaterialInspectionId(),
                request.getTaskInstanceId());
        if (inspectionRecordDomain == null) {
            log.error("新品物料巡检记录不存在: positionInspectionId={}", request.getMaterialInspectionId());
            return new CommonApiResponse<>(500, "新品物料巡检记录不存在", null);
        }
        // 当前登陆人信息从Token获取
        IntlRmsUserDto intlRmsUserDto = intlRmsUserService.getIntlRmsUserByDomainName(request.getOwner());
        IntlRetailAssert.nonNull(intlRmsUserDto, "未获取到当前登录人信息: owner=" + request.getOwner());
        IntlRetailAssert.nonNull(intlRmsUserDto.getMiId(), "未获取到当前登录人MiId: owner=" + request.getOwner());
        if (!intlRmsUserDto.getMiId().equals(inspectionRecordDomain.getInspectionOwnerMiId())) {
            log.error("当前登录人与巡检负责人不一致:  inspectionOwnerMiId={}",
                    inspectionRecordDomain.getInspectionOwnerMiId());
            return new CommonApiResponse<>(500, "当前登录人与巡检负责人不一致", "");
        }
        // 验证位置信息
        if (request.getLatitude() == null || request.getLongitude() == null || request.getPositionLatitude() == null
                || request.getPositionLongitude() == null) {
            log.error("阵地巡检位置信息不完整");
            return new CommonApiResponse<>(500, "位置信息不完整", "");
        }
        // 获取阵地信息(数字门店阵地编码转RMS编码)
        PositionDomain positionDomain = positionRepository.getByCode(inspectionRecordDomain.getPositionCode());
        if (positionDomain == null) {
            log.error("阵地信息不存在: positionCode={}", inspectionRecordDomain.getPositionCode());
            return new CommonApiResponse<>(500, "阵地信息不存在", null);
        }
        String ruleCode = inspectionRecordDomain.getRuleCode();
        // 获取规则配置
        RuleConfigDomain ruleConfig = ruleConfigRepository.getByRuleCode(ruleCode);
        if (ruleConfig == null) {
            log.warn("Rule config not found. ruleCode: {}", ruleCode);
            return new CommonApiResponse<>(500, "获取规则配置", null);
        }
        // 设置是否在打卡范围内
        DistanceRange distanceRange = isWithinCheckInRange(request.getLatitude(), request.getLongitude(),
                request.getPositionLatitude(), request.getPositionLongitude(), request.getCheckInDistance());
        inspectionRecordDomain.setStoreLimitedRange(distanceRange.getLimitedRange());
        inspectionRecordDomain.setReportDistance(distanceRange.getReportDistance());
        // 获取当前时间,到毫秒级
        Instant utcInstant = Instant.now().truncatedTo(ChronoUnit.MILLIS);
        inspectionRecordDomain.setModifiedOn(utcInstant.toEpochMilli());
        // 记录修改人为巡检负责人
        inspectionRecordDomain.setModifiedBy(inspectionRecordDomain.getInspectionOwner());
        // 提交后更新巡检记录状态
        if (Objects.equals(ruleConfig.getNeedInspection(), YESNOEnum.NO.getCode())) {
            inspectionRecordDomain.setInspectionStatus(InspectionStatusEnum.COMPLETED);
        } else {
            inspectionRecordDomain.setInspectionStatus(InspectionStatusEnum.TO_BE_VERIFIED);
        }
        //清空被驳回二次提交时校验驳回的时间
        if (inspectionRecordDomain.getVerificationTime() != null) {
            //设置成0L，前端会自动识别为空
            inspectionRecordDomain.setVerificationTime(0L);
        }
        boolean result;
        // 更新fileUpload表，建立guid和fds_url映射
        List<FileUploadInfo.MetaData> metaDataList = extractMetaDataFromRequest(request.getSections());
        // 调用文件上传服务保存guid和url映射关系
        if (!CollectionUtils.isEmpty(metaDataList)) {
            try {
                fileUploadService.saveSimple(metaDataList, FileUploadEnum.MATERIAL_INSPECTION, request.getOwner());
                log.info("成功保存文件上传映射关系，共{}条记录", metaDataList.size());
            } catch (Exception e) {
                log.error("保存文件上传映射关系失败", e);
                // 这里不抛出异常，避免影响主流程
            }
        }
        if (Objects.equals(inspectionRecordDomain.getBusinessType(),
                BusinessTypeEnum.NEW_PRODUCT_INSPECTION_TOUR_LDU.getCode())) {

            // 批量生成并保存LDU巡检明细数据
            saveLduInspectionInfoBatch(request.getSections(), inspectionRecordDomain.getId(),
                    utcInstant.toEpochMilli());
        }
        // 将数据转换为JSON字符串
        String uploadDataJson;
        try {
            uploadDataJson = objectMapper.writeValueAsString(request.getSections());
            log.info("生成的上传数据JSON: {}", uploadDataJson);
        } catch (Exception e) {
            log.error("JSON序列化失败", e);
            return new CommonApiResponse<>(500, "数据处理失败", "");
        }
        // 保存到阵地巡检记录
        inspectionRecordDomain.setUploadData(uploadDataJson);
        // 任务中心状态变更为结束
        TaskCenterFinishReq taskCenterFinishReq = new TaskCenterFinishReq();
        // 查询TaskBatchId 完成任务
        taskCenterFinishReq.setTaskBatchId(inspectionRecordDomain.getTaskBatchId());
        taskCenterFinishReq.setMid(inspectionRecordDomain.getInspectionOwnerMiId());
        taskCenterFinishReq.setOperatorMid(inspectionRecordDomain.getInspectionOwnerMiId());
        taskCenterFinishReq.setOrgId(positionDomain.getPositionCode());
        taskCenterFinishReq.setTaskInstanceId(inspectionRecordDomain.getTaskInstanceId());
        taskCenterServiceRpc.outerTaskFinish(taskCenterFinishReq);
        // 提交后更新任务状态-已完成
        inspectionRecordDomain.setTaskStatus(TaskStatusEnum.COMPLETED);

        inspectionRecordDomain.setTaskCompletionTime(utcInstant.toEpochMilli());
        // 补充事务用来回滚状态
        result = inspectionRecordRepository.update(inspectionRecordDomain);
        log.info("更新阵地巡检记录: id={}, result={}", inspectionRecordDomain.getId(), result);
        
        //提交时更新对应阵地物料状态can_modify为不可修改
        IntlStoreMaterialStatus intlStoreMaterialStatus = intlStoreMaterialStatusMapper.selectBusinessCodeAndTaskType(
                inspectionRecordDomain.getPositionCode(), inspectionRecordDomain.getBusinessType());
        if (intlStoreMaterialStatus != null) {
            intlStoreMaterialStatus.setCanModify(CanModifyEnum.CAN_NOT_MODIFY.getCode());
            intlStoreMaterialStatus.setUpdatedBy(inspectionRecordDomain.getInspectionOwner());
            intlStoreMaterialStatus.setUpdatedTime(utcInstant.toEpochMilli());
            intlStoreMaterialStatusMapper.updateById(intlStoreMaterialStatus);
        }
        if (!result) {
            log.error("阵地巡检数据更新失败");
            return new CommonApiResponse<>(500, "数据保存失败", "");
        } else {
            // 新建历史操作记录单
            InspectionHistoryDomain history = new InspectionHistoryDomain();
            history.setInspectionRecordId(inspectionRecordDomain.getId());
            history.setOperationType(OperationType.SUBMIT);
            history.setOperator(inspectionRecordDomain.getInspectionOwner());
            history.setOperationTime(utcInstant.toEpochMilli());
            history.setCreateTime(utcInstant.toEpochMilli());
            history.setUploadData(uploadDataJson);
            boolean historyResult = inspectionHistoryRepository.save(history);
            if (!historyResult) {
                log.warn("保存新品物料巡检历史记录失败，但主流程已完成");
            }
            // 记录history对象的详细信息到日志
            log.info("保存的新品物料巡检历史记录信息: {}", history);
            return new CommonApiResponse<>("");
        }

    }
    
    /**
     * 检查打卡距离是否在限定范围内
     *
     * @param currentLatitude   当前纬度
     * @param currentLongitude  当前经度
     * @param positionLatitude  阵地纬度
     * @param positionLongitude 阵地经度
     * @param checkInDistance   允许打卡的最大距离
     * @return 是否在打卡范围内
     */
    private DistanceRange isWithinCheckInRange(Double currentLatitude, Double currentLongitude, Double positionLatitude,
            Double positionLongitude, Double checkInDistance) {
        // 计算距离
        double distance = CalculateSphericalDistance.getDistance(currentLatitude, currentLongitude, positionLatitude,
                positionLongitude);
        
        // 设置默认打卡范围为200米
        double maxDistance = 200.0;
        if (checkInDistance != null && checkInDistance > 0) {
            maxDistance = checkInDistance;
        }
        
        // 判断是否在打卡范围内
        boolean isWithinRange = distance <= maxDistance;
        
        if (!isWithinRange) {
            log.info("超出打卡范围: 当前距离={}, 配置距离={}", distance, maxDistance);
        }
        
        return new DistanceRange(distance, isWithinRange ? StoreLimitedRangeEnum.YES : StoreLimitedRangeEnum.NO);
    }
    
    @Nullable
    private InspectionRecordDomain getInspectionRecordDomain(Long materialInspectionId, Long taskInstanceId) {
        InspectionRecordDomain inspectionRecordDomain = null;
        if (null != materialInspectionId && materialInspectionId > 0) {
            inspectionRecordDomain = inspectionRecordRepository.getById(materialInspectionId);
        }
        if (null != taskInstanceId && taskInstanceId > 0) {
            inspectionRecordDomain = inspectionRecordRepository.getTaskInstanceId(taskInstanceId);
        }
        return inspectionRecordDomain;
    }
    
    @Override
    public MaterialSampleResponse getMaterialSample(MaterialSampleRequest request) {
        MaterialSampleResponse response = new MaterialSampleResponse();
        // 获取检查记录
        InspectionRecordDomain inspectionRecordDomain = getInspectionRecordDomain(request.getMaterialInspectionId(),
                request.getTaskInstanceId());
        if (inspectionRecordDomain == null) {
            log.warn("Position inspection record not found. materialInspectionId: {}, taskInstanceId: {}",
                    request.getMaterialInspectionId(), request.getTaskInstanceId());
            return response;
        }
        String ruleCode = inspectionRecordDomain.getRuleCode();
        // 获取规则配置
        RuleConfigDomain ruleConfig = ruleConfigRepository.getByRuleCode(ruleCode);
        if (ruleConfig == null) {
            log.warn("Rule config not found. ruleCode: {}", ruleCode);
            return response;
        }
        response.setTaskName(ruleConfig.getTaskName());
        // 设置允许从相册拍照
        response.setAllowPhotoFromGallery(ruleConfig.getAllowPhotoFromGallery() ? 1 : 0);
        response.setTaskType(ruleConfig.getTaskType());
        // 解析 POSM 材料
        String posmMaterials = ruleConfig.getPosmMaterials();
        boolean isPosmMaterials = checkPosmMaterials(posmMaterials);
        if (isPosmMaterials) {
            parsePosmMaterials(posmMaterials, response);
        } else {
            response.setSingleColumnShow(true);
            PosmMaterialDTO posmMaterialDTO = new PosmMaterialDTO();
            posmMaterialDTO.setMaterialKey("defaultKey");
            response.setPosmMaterials(Arrays.asList(posmMaterialDTO));
        }
        return response;
    }
    
    private boolean checkPosmMaterials(String posmMaterials) {
        if (StringUtils.isEmpty(posmMaterials)) {
            return false;
        }
        try {
            JSONObject jsonObject = JSONObject.parseObject(posmMaterials);
            JSONArray jsonArray = jsonObject != null ? jsonObject.getJSONArray("posmMaterials") : null;
            return jsonArray != null && !jsonArray.isEmpty();
        } catch (Exception e) {
            log.warn("Failed to parse posmMaterials JSON: {}", posmMaterials, e);
            return false;
        }
    }
    
    /**
     * 解析 POSM 材料并设置到响应中
     * @param posmMaterials POSM 材料的 JSON 字符串
     * @param response MaterialSampleResponse
     */
    private void parsePosmMaterials(String posmMaterials, MaterialSampleResponse response) {
        List<PosmMaterialDTO> posmMaterialDTOList = new ArrayList<>();
        try {
            JSONObject jsonObject = JSONObject.parseObject(posmMaterials);
            JSONArray jsonArray = jsonObject.getJSONArray("posmMaterials");
            // 使用流式操作简化遍历和过滤
            jsonArray.stream().map(Object::toString).map(PosmMaterialEnum::valueOfCode).filter(Objects::nonNull)
                    .map(posmMaterialEnum -> {
                        PosmMaterialDTO dto = new PosmMaterialDTO();
                        dto.setMaterialKey(posmMaterialEnum.getCode());
                        String imageUrl = newProductSampleExampleConfig.getImageUrl(posmMaterialEnum.getCode());
                        dto.setExampleImage(imageUrl);
                        dto.setMaterialShowName(posmMaterialEnum.getShowName(posmMaterialEnum));
                        return dto;
                    }).forEach(posmMaterialDTOList::add);
            response.setPosmMaterials(posmMaterialDTOList);
        } catch (Exception e) {
            log.error("Failed to parse POSM materials list. posmMaterials: {}", posmMaterials, e);
        }
    }

    @Override
    public List<MaterialInspectionOperationHistoryResponse> getMaterialInspectionOperationHistory(
            Long materialInspectionId) {
        InspectionRecordDomain record = inspectionRecordRepository.getById(materialInspectionId);
        if (record == null) {
            log.warn("position inspection record not found. id: {}", materialInspectionId);
            return Collections.emptyList();
        }
        Boolean singleColumnShow = true;
        String ruleCode = record.getRuleCode();
        // 获取规则配置
        RuleConfigDomain ruleConfig = ruleConfigRepository.getByRuleCode(ruleCode);
        if (ruleConfig == null) {
            log.warn("getMaterialInspectionOperationHistory. ruleCode: {}", ruleCode);
            return Collections.emptyList();
        }
        boolean isPosmMaterials = checkPosmMaterials(ruleConfig.getPosmMaterials());
        if (isPosmMaterials) {
            singleColumnShow = false;
        }
        List<InspectionHistoryDomain> historyList = inspectionHistoryRepository.getByInspectionRecordId(
                materialInspectionId);
        List<MaterialInspectionOperationHistoryResponse> historyItems = new ArrayList<>();
        for (InspectionHistoryDomain historyDomain : historyList) {
            MaterialInspectionOperationHistoryResponse historyItem = new MaterialInspectionOperationHistoryResponse();
            historyItem.setOperationTypeDesc(I18nDesc.safeGetDesc(historyDomain.getOperationType()));
            historyItem.setOperator(historyDomain.getOperator());
            historyItem.setSingleColumnShow(singleColumnShow);
            historyItem.setTaskTypeDesc(TaskTypeEnum.getShortNameByCode(record.getBusinessType()));
            historyItem.setDisapproveReasonDesc(
                    I18nDesc.getDescByCode(DisapproveReasonEnum.class, historyDomain.getDisapproveReason()));
            historyItem.setOperationTime(historyDomain.getOperationTime());
            historyItem.setDisapproveReasonDesc(
                    I18nDesc.getDescByCode(DisapproveReasonEnum.class, historyDomain.getDisapproveReason()));
            if (historyDomain.getUploadData() != null) {
                UploadMaterialData[] uploadData = JsonUtil.json2bean(historyDomain.getUploadData(),
                        UploadMaterialData[].class);
                Map<String, List<String>> photoMap = getPhotoMap(uploadData);
                List<UploadMaterialData> sections = new ArrayList<>();
                for (UploadMaterialData uploadDatum : uploadData) {
                    UploadMaterialData data = new UploadMaterialData();
                    data.setMaterialValue(processUploadDataImages(uploadDatum.getMaterialValue(), photoMap));
                    data.setMaterialKey(uploadDatum.getMaterialKey());
                    PosmMaterialEnum posmMaterialEnum = PosmMaterialEnum.valueOfCode(uploadDatum.getMaterialKey());
                    if (posmMaterialEnum != null) {
                        data.setMaterialShowName(posmMaterialEnum.getShowName(posmMaterialEnum));
                    }
                    sections.add(data);
                }
                historyItem.setSections(sections);
            }
            historyItems.add(historyItem);
        }
        return historyItems;
    }
    
    private List<FileUploadInfo.MetaData> extractMetaDataFromRequest(List<UploadMaterialData> sections) {
        List<FileUploadInfo.MetaData> metaDataList = new ArrayList<>();
        
        // 增加非空判断，避免NullPointerException
        if (CollectionUtils.isEmpty(sections)) {
            log.debug("上传的物料数据为空，跳过元数据提取");
            return metaDataList;
        }
        
        for (UploadMaterialData db : sections) {
            MaterialPhotoGroup materialValue = db.getMaterialValue();
            if (materialValue == null) {
                continue;
            }
            FileUploadInfo.MetaData metaData = new FileUploadInfo.MetaData(materialValue.getGuid(),
                    materialValue.getImages());
            List<String> images = materialValue.getImages();
            if (CollectionUtils.isEmpty(images)) {
                continue;
            }
            metaDataList.add(metaData);
        }
        return metaDataList;
    }

    /**
     * 批量生成并保存LDU巡检明细数据
     *
     * @param sections 上传的物料数据列表
     * @param inspectionRecordId 巡检记录ID
     * @param createTime 创建时间戳
     */
    private void saveLduInspectionInfoBatch(List<UploadMaterialData> sections, Long inspectionRecordId,
            Long createTime) {
        if (CollectionUtils.isEmpty(sections)) {
            log.info("上传的物料数据为空，跳过LDU巡检明细数据保存");
            return;
        }

        List<IntlLduInspectionInfo> lduInspectionInfoList = new ArrayList<>();

        for (UploadMaterialData section : sections) {
            // 跳过无效数据
            if (section.getMaterialValue() == null || StringUtils.isBlank(section.getMaterialValue().getGuid())) {
                log.warn("跳过无效的物料数据: materialKey={}", section.getMaterialKey());
                continue;
            }

            // 创建LDU巡检明细数据对象
            IntlLduInspectionInfo lduInspectionInfo = new IntlLduInspectionInfo();
            lduInspectionInfo.setInspectionRecordId(inspectionRecordId);
            lduInspectionInfo.setLduReportLogId(section.getLduReportLogId());
            lduInspectionInfo.setBusinessStatusCode(section.getBusinessStatusCode());
            lduInspectionInfo.setGuid(section.getMaterialValue().getGuid());
            lduInspectionInfo.setCreateTime(createTime);

            lduInspectionInfoList.add(lduInspectionInfo);
        }

        // 批量保存LDU巡检明细数据
        if (!CollectionUtils.isEmpty(lduInspectionInfoList)) {
            try {
                int insertCount = intlLduInspectionInfoMapper.batchInsert(lduInspectionInfoList);
                log.info("成功保存LDU巡检明细数据，共{}条记录", insertCount);
            } catch (Exception e) {
                log.error("保存LDU巡检明细数据失败", e);
                // 这里不抛出异常，避免影响主流程
            }
        }
    }

    private Map<String, List<String>> getPhotoMap(UploadMaterialData[] uploadData) {
        List<String> guids = new ArrayList<>();
        for (UploadMaterialData db : uploadData) {
            if (db.getMaterialValue() != null && StringUtils.isNotBlank(db.getMaterialValue().getGuid())) {
                guids.add(db.getMaterialValue().getGuid());
            }
        }
        return fileUploadService.getUrlsByModuleAndGuids(FileUploadEnum.MATERIAL_INSPECTION, guids);
    }

    private MaterialPhotoGroup processUploadDataImages(MaterialPhotoGroup materialValue,
            Map<String, List<String>> phontMap) {
        if (materialValue == null) {
            return null;
        }
        MaterialPhotoGroup result = new MaterialPhotoGroup();
        result.setName(materialValue.getName());
        result.setGuid(materialValue.getGuid());
        result.setImages(materialValue.getImages());
        if (phontMap != null && phontMap.containsKey(materialValue.getGuid())) {
            result.setImages(phontMap.get(materialValue.getGuid()));
        }
        return result;
    }

    @Override
    public MaterialInspectionRecordSelectorItem getSelectorItemList() {
        MaterialInspectionRecordSelectorItem item = new MaterialInspectionRecordSelectorItem();
        String language = RequestContextInfo.getLanguage();
        CommonConfigReq req = new CommonConfigReq();
        req.setBusinessScene("channelRetail");
        req.setLanguage(language);
        CommonConfigDTO2 commonConfig = RpcUtil.getRpcResult(storeRelateProvider.get3CCommonConfig(req));
        // 从3CCommonConfig获取数据
        item.setChannel(toOptionalItems(commonConfig.getChannelType(), ConfigKV2::getValue, ConfigKV2::getValue));
        item.setPositionType(toOptionalItems(commonConfig.getPositionType()));
        item.setStoreGrade(toOptionalItems(commonConfig.getStoreGradings(), ConfigKV2::getValue, ConfigKV2::getValue));
        // 从枚举列表获取数据
        List<OptionalItem<Integer>> taskStatusItemList = I18nDesc.toOptionalItems(TaskStatusEnum.class);
        taskStatusItemList.removeIf(e -> StringUtils.isEmpty(e.getValue()));
        item.setTaskStatus(taskStatusItemList);
        item.setTaskType(I18nDesc.toOptionalItems(TaskTypeEnum.class));
        item.setDisapproveReason(I18nDesc.toOptionalItems(MaterialDisapproveReasonEnum.class));
        item.setStoreStatus(I18nDesc.toOptionalItems(MaterialInspectionStatus.class));
        item.setInspectionStatus(I18nDesc.toOptionalItems(MaterialInspectionStatus.class));
        item.setStoreLimitedRange(I18nDesc.toOptionalItems(BoolEnum.class));
        item.setWhetherTakePhoto(I18nDesc.toOptionalItems(BoolEnum.class));
        item.setMaterialCovered(I18nDesc.toOptionalItems(StoreMaterialStatusCoveredEnum.class));

        return item;
    }

    @Override
    @Transactional
    public void verifyMaterialInspection(MaterialInspectionVerifyRequest request) {
        IntlRetailAssert.check(validator.validate(request));

        Long id = request.getId();
        MaterialInspectionDomain domain = newProductInspectionRepository.getById(id);
        IntlRetailAssert.nonNull(domain, "Inspection record not found. id:" + id);

        RuleConfigDomain rule = ruleConfigRepository.getById(domain.getRuleConfigId());
        IntlRetailAssert.nonNull(domain, "Inspection rule not found. id:" + id);

        IntlRetailAssert.isTrue(BoolEnum.YES.eq(rule.getNeedInspection()), "Inspection record is no need to verify");

        domain.checkAndSetInspectionStatus(request.getVerifyStatus());
        domain.checkDisapproveReason(request.getDisapproveReason());

        UserInfo userContext = UserInfoUtil.getUserContext();
        String userName = userContext.getUserName();
        domain.setVerifierMiId(userContext.getMiID());
        domain.setVerifier(userName);
        domain.setUpdatedBy(userName);
        long currentTimeStamp = System.currentTimeMillis();
        domain.setVerificationTime(currentTimeStamp);
        domain.setUpdatedTime(currentTimeStamp);
        domain.setDisapproveReason(request.getDisapproveReason());
        // 拒绝时将任务设置为未完成
        if (MaterialInspectionStatus.REJECTED.equals(domain.getInspectionStatus())) {
            domain.setTaskStatus(TaskStatusEnum.NOT_COMPLETED);
            domain.setTaskCompletionTime(0L);
        }
        int updated = newProductInspectionRepository.updateVerifyStatus(domain);
        IntlRetailAssert.isTrue(updated > 0, "Too many request");

        if (MaterialInspectionStatus.REJECTED.equals(domain.getInspectionStatus())) {
            // 判断是否是首销期
            TargetTypeEnum taskType = rule.getTargetType();
            if (TargetTypeEnum.FIRST_SALES_PERIOD.equals(taskType) && domain.isWithinPeriod()) {
                TaskCenterTaskReq req = new TaskCenterTaskReq();
                req.setMid(domain.getInspectionOwnerMiId());
                req.setOrgId(domain.getBusinessCode());
                req.setTaskBatchId(domain.getTaskBatchId());
                req.setTaskInstanceId(domain.getTaskInstanceId());
                IntlRetailAssert.isTrue(req.getTaskBatchId() != null && req.getTaskInstanceId() != null,
                        "Inspection record's task information is incomplete, the task reset failed");
                taskCenterServiceRpc.reloadTaskStatus(req); // 重置任务状态
            }
        }
    }

    @Override
    public List<TaskRemindDTO> getNoCompletedList(Long miId) {
        return newProductInspectionRepository.getNoCompletedList(miId);
    }

    @Override
    @Transactional
    public void batchSaveMaterialInspections(List<MaterialInspectionDomain> inspectionRecords,
            List<IntlStoreMaterialStatusDomain> storeMaterials) {
        newProductInspectionRepository.batchSave(inspectionRecords);
        intlStoreMaterialStatusRepository.batchSave(storeMaterials);
    }

    @Override
    public List<String> getStoreModelList(String positionCode) {
        return newProductInspectionRepository.getStoreModelList(positionCode);
    }
    
    @Override
    public boolean checkNewProductInspection(TaskCenterTaskNumReq req) {
        return newProductInspectionRepository.checkNewProductInspection(req,
                RETAIL_EVENT_BUSINESS_TYPES);
    }
    
    @SneakyThrows
    @Override
    public PageResponse<MaterialInspectionRecordPageDTO> getMaterialInspectionRecordPageItem(
            InspectionRecordPageRequest request) {
        request.setBusinessType(I18nDesc.codeSet(BusinessTypeEnum.class));
        request.freshTakePhoto();
        Page<InspectionRecordPageItem> page = newProductInspectionRepository.getMaterialInspectionRecordPageItem(
                request);

        if (CollectionUtils.isEmpty(page.getRecords())) {
            return new PageResponse<>(page.getTotal(), request.getPageNum(), request.getPageSize(),
                    Collections.emptyList());
        }

        List<InspectionRecordPageItem> records = page.getRecords();
        ThreadPoolExecutor pool = batchQueryExecutor.getThreadPoolExecutor();

        CompletableFuture<MaterialInspectionRecordSelectorMap> f1 = CompletableFuture.supplyAsync(this::getSelectorMap,
                pool);

        CompletableFuture<Map<Long, IntlRmsUserDto>> f2 = CompletableFuture.supplyAsync(() -> getRmsUserDtoMap(records),
                pool);

        CompletableFuture<Map<String, String>> f3 = CompletableFuture.supplyAsync(() -> getCountryAreaMap(records),
                pool);

        CompletableFuture<Map<Long, GetUserInfoResp>> f4 = CompletableFuture.supplyAsync(
                () -> getCreateUserInfoRespMap(records), pool);

        CompletableFuture<Map<String, List<String>>> f5 = CompletableFuture.supplyAsync(() -> getGuidUrlMap(records),
                pool);

        CompletableFuture<Void> f6 = CompletableFuture.runAsync(() -> loadLduInspectionRecordBusinessCodes(records),
                pool);

        CompletableFuture.allOf(f1, f2, f3, f4, f5, f6).join();

        MaterialInspectionRecordSelectorMap selectorMap = f1.get();
        Map<Long, IntlRmsUserDto> userDtoMap = f2.get();
        Map<String, String> countryAreaMap = f3.get();
        Map<Long, GetUserInfoResp> userPositionMap = f4.get();
        Map<String, List<String>> guidMap = f5.get();

        List<MaterialInspectionRecordPageDTO> list = new ArrayList<>();

        for (InspectionRecordPageItem record : records) {
            MaterialInspectionRecordPageDTO dto = new MaterialInspectionRecordPageDTO();
            list.add(dto);
            dto.setId(record.getId());
            dto.setBusinessCreationTime(record.getBusinessCreationTime());
            dto.setTaskName(record.getTaskName());
            dto.setPositionName(record.getPositionName());
            dto.setPositionCode(record.getPositionCode());
            dto.setStoreName(record.getStoreName());
            dto.setStoreCode(record.getStoreCode());
            dto.setStoreGrade(record.getStoreGrade());
            dto.setReportDistance(record.getReportDistance());
            dto.setBusinessStatusCode(record.getBusinessStatusCode());
            dto.setStoreStatusCode(record.getStoreStatus());
            dto.setTaskStatusCode(record.getTaskStatus());
            dto.setChannel(record.getChannel());
            dto.setInspectionStatusCode(record.getStoreStatus());
            dto.setMaterialCoveredCode(record.getCovered());
            dto.setStoreLimitedRangeCode(record.getStoreLimitedRange());
            dto.setTaskTypeCode(record.getTaskType());

            // 项目编码
            String projectCodeStr =
                    TaskTypeEnum.LDU.ne(record.getTaskType()) && StringUtils.isNotEmpty(record.getOnSaleProjectCode())
                            ? record.getOnSaleProjectCode() : record.getProjectCode();
            dto.setProjectCode(JsonUtil.jsonArr2beanList(projectCodeStr, String.class));
            // 国家区域
            dto.setCountry(countryAreaMap.getOrDefault(record.getCountryCode(), record.getCountryCode()));
            dto.setRegion(countryAreaMap.getOrDefault(record.getRegionCode(), record.getRegionCode()));
            // 筛选项描述
            dto.setStoreStatus(selectorMap.getStoreStatus().get(String.valueOf(record.getStoreStatus())));
            dto.setInspectionStatus(dto.getStoreStatus());
            dto.setTaskStatus(selectorMap.getTaskStatus().get(String.valueOf(record.getTaskStatus())));
            dto.setTaskType(selectorMap.getTaskType().get(String.valueOf(record.getTaskType())));
            dto.setMaterialCovered(selectorMap.getMaterialCovered().get(String.valueOf(record.getCovered())));
            dto.setStoreLimitedRange(I18nDesc.getDescByCode(BoolEnum.class, record.getStoreLimitedRange()));

            // 核验人名称
            IntlRmsUserDto u = userDtoMap.get(record.getVerifierMiId());
            dto.setVerifier(u == null ? record.getVerifier() : u.getEnglishName() + "(" + u.getDomainName() + ")");
            // 创建时间
            dto.setCreateTime(IntlTimeUtil.parseTimestampToAreaTime(record.getCountryCode(), record.getCreatedTime()));
            // 图片
            List<String> pictures = Optional.ofNullable(record.getSections()).orElse(Collections.emptyList()).stream()
                    .map(UploadMaterialData::getMaterialValue).map(MaterialPhotoGroup::getGuid).map(guidMap::get)
                    .filter(Objects::nonNull).flatMap(List::stream).collect(Collectors.toList());
            dto.setPictures(pictures);
            dto.setPicture1(CollUtils.get(pictures, 0));
            dto.setPicture2(CollUtils.get(pictures, 1));
            dto.setPicture3(CollUtils.get(pictures, 2));
            dto.setPicture4(CollUtils.get(pictures, 3));
            dto.setPicture5(String.join(";", CollUtils.subList(pictures, 4)));

            if (TaskTypeEnum.LDU.eq(record.getTaskType())) {
                dto.setReportRole(record.getReportRole());
                GetUserInfoResp userInfoResp = userPositionMap.get(record.getInspectionOwnerMiId());
                boolean isHeadquarters = Optional.ofNullable(userInfoResp).map(GetUserInfoResp::getPositionList)
                        .orElse(Collections.emptyList()).stream().filter(Objects::nonNull).map(Position::getOrganType)
                        .anyMatch(OrganTypeEnums.ORGAN_HEADQUARTERS.getCode()::equals);
                if (isHeadquarters) {
                    dto.setCreateUserId("***");
                    dto.setCreateUserName("***");
                } else {
                    dto.setCreateUserId(Objects.toString(record.getInspectionOwnerMiId(), null));
                    dto.setCreateUserName(record.getInspectionOwner());
                }
            }
        }

        return new PageResponse<>(page.getTotal(), request.getPageNum(), request.getPageSize(), list);
    }

    private void loadLduInspectionRecordBusinessCodes(List<InspectionRecordPageItem> records) {
        List<InspectionRecordPageItem> recordList = CollUtils.filter(records,
                r -> TaskTypeEnum.LDU.eq(r.getTaskType()) && !r.getSections().isEmpty());
        List<Long> reportIds = recordList.stream().map(InspectionRecordPageItem::getSections).flatMap(List::stream)
                .map(UploadMaterialData::getLduReportLogId).collect(Collectors.toList());
        List<LduReportSimple> lduList = lduReportRepository.findSnByIds(reportIds);
        Map<Long, LduReportSimple> lduMap = CollUtils.toMap(lduList, LduReportSimple::getId);
        for (InspectionRecordPageItem record : recordList) {
            List<String> subDesc = new ArrayList<>();
            for (UploadMaterialData section : record.getSections()) {
                LduReportSimple ldu = lduMap.get(section.getLduReportLogId());
                if (ldu != null) {
                    record.setReportRole(ldu.getReportRole());
                    String codeDesc =
                            StringUtils.isEmpty(ldu.getSn()) ? ("69Code:" + ldu.getCode69()) : ("SN:" + ldu.getSn());
                    subDesc.add(codeDesc + " " + I18nDesc.getDescByCode(BusinessStatusEnum.class,
                            section.getBusinessStatusCode()));
                }
            }
            record.setBusinessStatusCode(String.join("; ", subDesc));
        }
    }

    private MaterialInspectionRecordSelectorMap getSelectorMap() {
        MaterialInspectionRecordSelectorItem selectorItemList = getSelectorItemList();
        return NewProductInspectionConvert.INSTANCE.mapToSelectorMap(selectorItemList);
    }

    private Map<String, List<String>> getGuidUrlMap(List<InspectionRecordPageItem> records) {
        List<String> guids = new ArrayList<>();
        for (InspectionRecordPageItem record : records) {
            List<String> subGuids = record.getSections().stream().map(UploadMaterialData::getMaterialValue)
                    .map(MaterialPhotoGroup::getGuid).collect(Collectors.toList());
            guids.addAll(subGuids);
        }
        return fileUploadService.getUrlsByModuleAndGuids(null, guids);
    }

    private Map<Long, GetUserInfoResp> getCreateUserInfoRespMap(List<InspectionRecordPageItem> records) {
        List<Long> createUserMiIds = records.stream().filter(r -> TaskTypeEnum.LDU.eq(r.getTaskType()))
                .map(InspectionRecordPageItem::getInspectionOwnerMiId).distinct().collect(Collectors.toList());
        ListUserInfoReq userInfoReq = new ListUserInfoReq();
        userInfoReq.setMiIdList(createUserMiIds);
        userInfoReq.setFilterList(Collections.singletonList("effectivePosition"));
        Result<List<GetUserInfoResp>> listResult = userProvider.listUserInfo(userInfoReq);
        List<GetUserInfoResp> userInfoResps = Optional.ofNullable(listResult).map(Result::getData)
                .orElse(Collections.emptyList());
        return CollUtils.toMap(userInfoResps, GetUserInfoResp::getMiId);
    }

    private Map<String, String> getCountryAreaMap(List<InspectionRecordPageItem> records) {
        List<String> countryCodes = CollUtils.mapping(records, InspectionRecordPageItem::getCountryCode);
        return rmsCountryTimezoneService.getCountryAreaMapByCountryCodes(countryCodes);
    }

    private Map<Long, IntlRmsUserDto> getRmsUserDtoMap(List<InspectionRecordPageItem> records) {
        List<Long> verifierMiIds = CollUtils.mapping(records, InspectionRecordPageItem::getVerifierMiId);
        List<IntlRmsUserDto> rmsUsers = intlRmsUserService.getIntlRmsUserByMiIds(verifierMiIds);
        return CollUtils.toMap(rmsUsers, IntlRmsUserDto::getMiId);
    }

    private List<OptionalItem<String>> toOptionalItems(List<ConfigKV2> configKVs, Function<ConfigKV2, Object> kf,
            Function<ConfigKV2, Object> vf) {
        if (CollectionUtils.isEmpty(configKVs)) {
            return Collections.emptyList();
        }
        return configKVs.stream().map(kv -> new OptionalItem<>(Objects.toString(kf.apply(kv), null),
                Objects.toString(vf.apply(kv), null))).collect(Collectors.toList());
    }

    private List<OptionalItem<String>> toOptionalItems(List<ConfigKV2> configKVs) {
        return toOptionalItems(configKVs, ConfigKV2::getKey, ConfigKV2::getValue);
    }
}
