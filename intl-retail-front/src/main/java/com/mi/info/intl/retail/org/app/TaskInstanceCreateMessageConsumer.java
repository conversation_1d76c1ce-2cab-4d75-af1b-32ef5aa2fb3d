package com.mi.info.intl.retail.org.app;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.mi.info.intl.retail.cooperation.task.dto.dto.IntlRmsUserDto;
import com.mi.info.intl.retail.cooperation.task.inspection.IntlRmsUserService;
import com.mi.info.intl.retail.core.utils.CollUtils;
import com.mi.info.intl.retail.exception.ReconsumeRuntimeException;
import com.mi.info.intl.retail.intlretail.service.api.material.enums.StoreMaterialStatusCoveredEnum;
import com.mi.info.intl.retail.intlretail.service.api.material.enums.TaskTypeEnum;
import com.mi.info.intl.retail.intlretail.service.api.mq.dto.TaskInstanceMessageBody;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.BoolEnum;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.I18nDesc;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.MaterialInspectionStatus;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.TaskStatusEnum;
import com.mi.info.intl.retail.org.domain.IntlStoreMaterialStatusDomain;
import com.mi.info.intl.retail.org.domain.MaterialInspectionDomain;
import com.mi.info.intl.retail.org.domain.PositionDomain;
import com.mi.info.intl.retail.org.domain.RuleConfigDomain;
import com.mi.info.intl.retail.org.domain.material.service.NewProductInspectionDomainService;
import com.mi.info.intl.retail.org.domain.repository.IntlStoreMaterialStatusRepository;
import com.mi.info.intl.retail.org.domain.repository.NewProductInspectionRepository;
import com.mi.info.intl.retail.org.domain.repository.PositionRepository;
import com.mi.info.intl.retail.org.domain.repository.RuleConfigRepository;
import com.mi.info.intl.retail.org.infra.config.TaskInstanceMqBusinessTypeConfig;
import com.mi.info.intl.retail.utils.intl.IntlTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.annotation.Resource;

/**
 * Task Instance 创建消息消费者
 * 用于处理任务实例创建的MQ消息
 *
 * <AUTHOR> Generated
 * @date 2024/12/19
 */
@Slf4j
@Service
@ConditionalOnProperty(name = "intl-retail.rocketmq.task.create.enabled", havingValue = "true", matchIfMissing = true)
@RocketMQMessageListener(topic = "${intl-retail.rocketmq.task.create.topic}",
        consumerGroup = "${intl-retail.rocketmq.task.create.group}", enableMsgTrace = true)
public class TaskInstanceCreateMessageConsumer implements RocketMQListener<MessageExt> {

    @Resource
    private TaskInstanceMqBusinessTypeConfig taskInstanceMqBusinessTypeConfig;

    @Resource
    private PositionRepository positionRepository;

    @Resource
    private RuleConfigRepository ruleConfigRepository;

    @Resource
    private IntlRmsUserService rmsUserService;

    @Resource
    private NewProductInspectionRepository newProductInspectionRepository;

    @Resource
    private IntlStoreMaterialStatusRepository intlStoreMaterialStatusRepository;

    @Resource
    private NewProductInspectionDomainService newProductInspectionDomainService;

    @Override
    public void onMessage(MessageExt message) {
        String tag = message.getTags();
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        log.info("收到任务实例创建消息: tag={}, body={}", tag, body);

        // 验证tag是否在配置的业务类型列表中
        if (!isValidBusinessTypeTag(tag)) {
            log.warn("不支持的business_type_id tag: {}, 跳过处理", tag);
            return;
        }

        try {
            if (StrUtil.isBlank(body)) {
                log.warn("收到空消息，跳过处理");
                return;
            }

            TaskInstanceMessageBody messageBody = JSONUtil.toBean(body, TaskInstanceMessageBody.class);
            if (messageBody == null || messageBody.getData() == null) {
                log.warn("消息体解析失败或数据为空，跳过处理");
                return;
            }

            if (message.getReconsumeTimes() > 0) {
                log.info("Message reconsume, times: {}", message.getReconsumeTimes());
            }

            Set<Long> unProceedTasks = handleTaskInstanceCreated(messageBody, tag);

            if (!unProceedTasks.isEmpty() && message.getReconsumeTimes() <= 2) {
                log.warn("UnProceed task exists : {} , Reconsume this message. message id: {}", unProceedTasks,
                        message.getMsgId());
                throw new ReconsumeRuntimeException(message.getMsgId());
            }

        } catch (ReconsumeRuntimeException ex) {
            throw ex;
        } catch (Exception e) {
            log.error("处理任务实例创建消息失败: tag={}, body={}", tag, body, e);
        }
    }

    /**
     * 验证tag是否为有效的业务类型
     */
    private boolean isValidBusinessTypeTag(String tag) {
        if (StrUtil.isBlank(tag)) {
            return false;
        }

        try {
            Long businessTypeId = Long.parseLong(tag);
            return taskInstanceMqBusinessTypeConfig.shouldProcessMqMessage(businessTypeId);
        } catch (NumberFormatException e) {
            log.warn("无效的business_type_id tag格式: {}", tag);
            return false;
        }
    }

    public Set<Long> handleTaskInstanceCreated(TaskInstanceMessageBody messageBody, String tag) {
        log.info("处理任务实例创建消息: tag={}, 数量={}", tag,
                messageBody.getData() != null ? messageBody.getData().size() : 0);
        List<TaskInstanceMessageBody.TaskInstanceData> dataList = messageBody.getData();
        List<String> orgIds = CollUtils.mapping(dataList,
                TaskInstanceMessageBody.TaskInstanceData::getOrgId);
        List<Long> taskDefinitionIds = CollUtils.mapping(dataList,
                TaskInstanceMessageBody.TaskInstanceData::getTaskDefinitionId);
        List<Long> miIds = CollUtils.mapping(dataList,
                TaskInstanceMessageBody.TaskInstanceData::getMid);
        List<Long> taskInstanceIds = CollUtils.mapping(dataList, TaskInstanceMessageBody.TaskInstanceData::getId);

        List<PositionDomain> positions = positionRepository.getByCodes(orgIds);
        Map<String, PositionDomain> positionMap = CollUtils.toMap(positions, PositionDomain::getPositionCode);

        List<RuleConfigDomain> rules = ruleConfigRepository.getByTaskDefineIds(taskDefinitionIds);
        Map<Long, RuleConfigDomain> ruleMap = CollUtils.toMap(rules, RuleConfigDomain::getTaskDefId);

        List<IntlRmsUserDto> users = rmsUserService.getIntlRmsUserByMiIds(miIds);
        Map<Long, IntlRmsUserDto> userMap = CollUtils.toMap(users, IntlRmsUserDto::getMiId);

        List<Long> unexistsInstanceIds = newProductInspectionRepository.getUnexistsTaskInstanceIds(taskInstanceIds);

        Set<Long> unProceedTasks = new HashSet<>();

        List<MaterialInspectionDomain> inspectionRecords = new ArrayList<>();
        for (TaskInstanceMessageBody.TaskInstanceData data : dataList) {
            if (!unexistsInstanceIds.contains(data.getId())) {
                log.warn("task instance id exists. id: {}", data.getId());
                continue;
            }

            String orgId = data.getOrgId(); // rmsPositionCode
            PositionDomain positionDomain = positionMap.get(orgId);
            if (positionDomain == null) {
                log.warn("position not found: {}", orgId);
                continue;
            }
            Long taskDefinitionId = data.getTaskDefinitionId();
            RuleConfigDomain ruleDomain = ruleMap.get(taskDefinitionId);
            if (ruleDomain == null) {
                unProceedTasks.add(data.getId());
                continue;
            }

            IntlRmsUserDto rmsUser = userMap.get(data.getMid());
            if (rmsUser == null) {
                log.warn("user not found: {}", data.getMid());
                continue;
            }

            MaterialInspectionDomain record =
                    generateMaterialInspectionDomain(data, ruleDomain, orgId, rmsUser, positionDomain);
            inspectionRecords.add(record);
        }

        List<String> businessCodes = intlStoreMaterialStatusRepository.findNotExistBusinessCodes(orgIds);
        Set<String> businessCodeSet = new HashSet<>(businessCodes);

        List<IntlStoreMaterialStatusDomain> storeMaterials = new ArrayList<>();
        for (MaterialInspectionDomain record : inspectionRecords) {
            if (businessCodeSet.contains(record.getBusinessCode())) {
                businessCodeSet.remove(record.getBusinessCode());
                Arrays.stream(TaskTypeEnum.values()).filter(e -> e != TaskTypeEnum.LDU)
                        .map(e -> generateStoreMaterialStatusDomain(record, e.getCode())).forEach(storeMaterials::add);
            }
        }

        newProductInspectionDomainService.batchSaveMaterialInspections(inspectionRecords, storeMaterials);
        return unProceedTasks;
    }

    private static IntlStoreMaterialStatusDomain generateStoreMaterialStatusDomain(MaterialInspectionDomain record,
            Integer taskType) {
        IntlStoreMaterialStatusDomain intlStoreMaterialStatusDomain = new IntlStoreMaterialStatusDomain();
        intlStoreMaterialStatusDomain.setBusinessCode(record.getBusinessCode());
        intlStoreMaterialStatusDomain.setTaskType(taskType.longValue());
        intlStoreMaterialStatusDomain.setCovered(StoreMaterialStatusCoveredEnum.COVERED);
        intlStoreMaterialStatusDomain.setCanModify(Boolean.TRUE);
        intlStoreMaterialStatusDomain.setCreatedBy(record.getCreatedBy());
        intlStoreMaterialStatusDomain.setCreatedTime(record.getCreatedTime());
        intlStoreMaterialStatusDomain.setUpdatedBy(record.getUpdatedBy());
        intlStoreMaterialStatusDomain.setUpdatedTime(record.getUpdatedTime());
        return intlStoreMaterialStatusDomain;
    }

    private MaterialInspectionDomain generateMaterialInspectionDomain(TaskInstanceMessageBody.TaskInstanceData data,
            RuleConfigDomain ruleDomain, String orgId, IntlRmsUserDto rmsUser, PositionDomain positionDomain) {
        MaterialInspectionDomain record = new MaterialInspectionDomain();

        record.setRuleCode(ruleDomain.getRuleCode());
        record.setBusinessCode(orgId);
        record.setBusinessType(data.getBusinessTypeId());
        TaskStatusEnum taskStatus = I18nDesc.getByCode(TaskStatusEnum.class, data.getStatus());
        record.setTaskStatus(taskStatus == null ? TaskStatusEnum.NOT_COMPLETED : taskStatus);
        record.setBusinessId(positionDomain.getId());
        record.setBusinessCreationTime(data.getId());

        record.setInspectionOwnerMiId(data.getMid());
        String userName = rmsUser.getDomainName();
        record.setInspectionOwner(userName);
        record.setCreatedBy(userName);
        long currentTimeStamp = System.currentTimeMillis();
        record.setCreatedTime(currentTimeStamp);
        record.setUpdatedBy(userName);
        record.setUpdatedTime(currentTimeStamp);
        record.setPositionType(positionDomain.getPositionTypeName());
        record.setTaskCreateInstanceTime(data.getCreateTimeStamp());
        record.setRuleConfigId(ruleDomain.getId());
        record.setTaskInstanceId(data.getId());
        record.setTaskBatchId(data.getTaskBatchId());
        record.setPeriodStartTimeStamp(data.getPeriodStartTimeStamp());
        record.setPeriodEndTimeStamp(data.getPeriodEndTimeStamp());
        record.setDeadlineStamp(data.getDeadlineStamp());
        // 提醒时间为 早上9点+提醒间隔天数
        long reminderTime = 0;
        if (ObjectUtils.defaultIfNull(ruleDomain.getReminderDays(), 0) > 0) {
            reminderTime = IntlTimeUtil.getStartOfDayTimestamp(ruleDomain.getCountry()) + 9 * 60 * 60 * 1000L +
                    ruleDomain.getReminderDays() * 24 * 60 * 60 * 1000L;
        }
        record.setReminderTime(reminderTime);
        record.setExpire(data.getExpire() == null ? 0 : data.getExpire());
        record.setEnable(data.getEnable() == null ? 1 : data.getEnable());
        MaterialInspectionStatus completeStatus = BoolEnum.YES.eq(ruleDomain.getNeedInspection()) ?
                MaterialInspectionStatus.VERIFYING : MaterialInspectionStatus.COMPLETED;
        record.setInspectionStatus(TaskStatusEnum.NOT_COMPLETED.equals(record.getTaskStatus()) ?
                MaterialInspectionStatus.INCOMPLETED : completeStatus);
        return record;
    }
}