package com.mi.info.intl.retail.ldu.infra.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.io.Serializable;

/**
 * 从 RMS同步的国家信息
 *
 * <AUTHOR>
 * @date 2025/7/24 11:28
 */
@Data
@TableName("intl_rms_country_timezone")
public class IntlRmsCountryTimezone implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 唯一标识
     */
    @TableField("country_timezone_id")
    private String countryTimezoneId;

    /**
     * 国家id
     */
    @TableField("country_id")
    private String countryId;

    /**
     * 国家名称
     */
    @TableField("country_name")
    private String countryName;

    /**
     * 国家编码
     */
    @TableField("country_code")
    private String countryCode;

}
