package com.mi.info.intl.retail.org.infra.mapper;

import com.mi.info.intl.retail.api.front.position.dto.IntlPositionDTO;
import com.mi.info.intl.retail.ldu.dto.StoreGradeInfoDTO;
import com.mi.info.intl.retail.ldu.dto.StoreInfoDTO;
import com.mi.info.intl.retail.org.infra.entity.IntlRmsStore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【intl_rms_store(RMS门店表)】的数据库操作Mapper
* @createDate 2025-06-05 10:22:53
* @Entity generator.domain.IntlRmsStore
*/
public interface IntlRmsStoreMapper extends BaseMapper<IntlRmsStore> {

    StoreInfoDTO getStoreInfo(@Param("storeId") String storeId);

    StoreInfoDTO getStoreByPositionCode(@Param("positionCode") String positionCode);

    List<IntlPositionDTO> getStoreInfoByStoreIdList(List<String> list);

    /**
     * 根据门店编号更新门店等级
     * @param storeId 门店编号
     * @param grade 门店等级
     * @return 更新影响的行数
     */
    int updateGradeByStoreId(@Param("storeId") String storeId, @Param("grade") Integer grade,
                             @Param("gradeValue") String gradeValue, @Param("flag") Integer flag);

    List<IntlRmsStore> pageSelectByCountryCodeAndChannelType(@Param("countryCode") String countryCode, @Param("channelType") Integer channelType,
                                                             @Param("offset") Integer offset, @Param("limit") Integer pageSize);

    List<StoreGradeInfoDTO> getStoreByStoreCode(@Param("list") List<String> list);
}




