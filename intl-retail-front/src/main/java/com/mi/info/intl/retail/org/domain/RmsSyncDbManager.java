package com.mi.info.intl.retail.org.domain;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.nacos.common.utils.Objects;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mi.info.intl.retail.api.so.rule.blacklist.SoBlacklistApiService;
import com.mi.info.intl.retail.api.so.rule.model.SoRuleRetailerModel;
import com.mi.info.intl.retail.api.so.rule.service.IntlSoRuleRetailerApiService;
import com.mi.info.intl.retail.config.AlarmEmailConfig;
import com.mi.info.intl.retail.cooperation.task.infra.entity.IntlRmsSignRule;
import com.mi.info.intl.retail.cooperation.task.inspection.impl.IntlRmsSignRuleServiceImpl;
import com.mi.info.intl.retail.core.feishu.service.SendMessageService;
import com.mi.info.intl.retail.domain.retailer.service.impl.IntlRetailerServiceImpl;
import com.mi.info.intl.retail.enums.AlarmKey;
import com.mi.info.intl.retail.exception.BizException;
import com.mi.info.intl.retail.intlretail.service.api.mq.dto.RmsDbContentRequest;
import com.mi.info.intl.retail.intlretail.service.api.mq.dto.RmsDbRequest;
import com.mi.info.intl.retail.intlretail.service.api.so.sales.request.SalesImeiReqDto;
import com.mi.info.intl.retail.intlretail.service.api.so.sales.request.SalesQtyReqDto;
import com.mi.info.intl.retail.ldu.infra.entity.IntlRmsProduct;
import com.mi.info.intl.retail.ldu.service.impl.IntlRmsProductServiceImpl;
import com.mi.info.intl.retail.ldu.service.impl.RmsStoreServiceImpl;
import com.mi.info.intl.retail.org.domain.country.service.impl.IntlCountryTimeZoneServiceImpl;
import com.mi.info.intl.retail.org.domain.country.service.impl.IntlPositionServiceImpl;
import com.mi.info.intl.retail.org.infra.entity.IntlRmsCountryTimezone;
import com.mi.info.intl.retail.org.infra.entity.IntlRmsPosition;
import com.mi.info.intl.retail.org.infra.entity.IntlRmsStore;
import com.mi.info.intl.retail.retailer.entity.IntlRmsRetailer;
import com.mi.info.intl.retail.so.app.mq.SyncSoToEsProducer;
import com.mi.info.intl.retail.so.domain.datasync.enums.DataSyncDataTypeEnum;
import com.mi.info.intl.retail.so.domain.sales.model.SoImeiIndex;
import com.mi.info.intl.retail.so.domain.sales.model.SoQtyIndex;
import com.mi.info.intl.retail.so.domain.sales.service.IntlSoImeiEsService;
import com.mi.info.intl.retail.so.domain.sales.service.IntlSoQtyEsService;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlRmsCity;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlRmsProvince;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlRmsRrp;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlRmsSecondarychannel;
import com.mi.info.intl.retail.so.domain.upload.service.impl.IntlRmsCityServiceImpl;
import com.mi.info.intl.retail.so.domain.upload.service.impl.IntlRmsProvinceServiceImpl;
import com.mi.info.intl.retail.so.domain.upload.service.impl.IntlRmsRrpServiceImpl;
import com.mi.info.intl.retail.so.domain.upload.service.impl.IntlRmsSecondarychannelServiceImpl;
import com.mi.info.intl.retail.so.util.PaginationUtil;
import com.mi.info.intl.retail.user.app.impl.IntlRmsPersonnelPositionServiceImpl;
import com.mi.info.intl.retail.user.app.impl.UserServiceImpl;
import com.mi.info.intl.retail.user.infra.entity.IntlRmsPersonnelPosition;
import com.mi.info.intl.retail.user.infra.entity.IntlRmsUser;
import com.mi.info.intl.retail.utils.JsonUtil;
import com.xiaomi.cnzone.commons.utils.JacksonUtil;
import com.xiaomi.hera.trace.context.TraceIdUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;

@Slf4j
@Service
public class RmsSyncDbManager {

    @Resource
    private RmsStoreServiceImpl rmsStoreServiceImpl;
    @Resource
    private IntlPositionServiceImpl intlPositionServiceImpl;
    @Resource
    private IntlCountryTimeZoneServiceImpl intlCountryTimeZoneServiceImpl;
    @Resource
    private UserServiceImpl userServiceImpl;
    @Resource
    private IntlRmsPersonnelPositionServiceImpl intlRmsPersonnelPositionServiceImpl;
    @Resource
    private IntlRmsSignRuleServiceImpl intlRmsSignRuleServiceImpl;
    @Resource
    private IntlRetailerServiceImpl intlRetailerServiceImpl;
    @Resource
    private SoBlacklistApiService soBlacklistApiService;
    @Resource
    private IntlRmsRrpServiceImpl intlRmsRrpServiceImpl;
    @Resource
    private IntlRmsProvinceServiceImpl intlRmsProvinceServiceImpl;
    @Resource
    private IntlRmsCityServiceImpl intlRmsCityServiceImpl;
    @Resource
    private IntlRmsSecondarychannelServiceImpl intlRmsSecondarychannelServiceImpl;
    @Resource
    private IntlRmsProductServiceImpl intlRmsProductServiceImpl;
    @Resource
    private IntlSoRuleRetailerApiService intlSoRuleRetailerApiService;
    @Resource
    private SyncSoToEsProducer syncSoToEsProducer;
    @Resource
    private SendMessageService sendMessageService;
    @Resource
    private IntlSoImeiEsService intlSoImeiEsService;
    @Resource
    private IntlSoQtyEsService intlSoQtyEsService;

    @Value("${miwork.alarm.groupId.p1:oc_1e6d1e54ac5eae84730fe55e86a013e7}")
    private String p1;

    @Resource
    private AlarmEmailConfig alarmEmailConfig;
    public void editDb(RmsDbRequest rmsDBRequest) {
        if (rmsDBRequest.getRmsDBContentList().isEmpty()) {
            return;
        }
        //按表名分组处理
        Map<String, List<RmsDbContentRequest>> tableGroup = rmsDBRequest.getRmsDBContentList().stream()
                .collect(Collectors.groupingBy(RmsDbContentRequest::getTable));
        //分别处理每组数据
        for (Map.Entry<String, List<RmsDbContentRequest>> entry : tableGroup.entrySet()) {
            String tableName = entry.getKey();
            List<RmsDbContentRequest> requests = entry.getValue();
            // 分批处理，每批最多100条
            int batchSize = 100;
            for (int i = 0; i < requests.size(); i += batchSize) {
                try {
                    int endIndex = Math.min(i + batchSize, requests.size());
                    List<RmsDbContentRequest> batchRequests = requests.subList(i, endIndex);
                    switch (tableName) {
                        case "intl_rms_store":
                            batchProcessStores(batchRequests);
                            break;
                        case "intl_rms_user":
                            batchProcessUsers(batchRequests);
                            break;
                        case "intl_rms_position":
                            batchProcessPositions(batchRequests);
                            break;
                        case "intl_rms_country_timezone":
                            batchProcessCountryTimezones(batchRequests);
                            break;
                        case "intl_rms_personnel_position":
                            batchProcessPersonnelPositions(batchRequests);
                            break;
                        case "intl_rms_sign_rule":
                            batchProcessSignRules(batchRequests);
                            break;
                        case "intl_rms_retailer":
                            batchProcessRetailers(batchRequests);
                            break;
                        case "intl_so_sn_blacklist":
                            batchProcessSoSnBlacklists(batchRequests);
                            break;
                        case "intl_rms_rrp":
                            batchProcessRrps(batchRequests);
                            break;
                        case "intl_rms_province":
                            batchProcessProvinces(batchRequests);
                            break;
                        case "intl_rms_city":
                            batchProcessCitys(batchRequests);
                            break;
                        case "intl_rms_secondarychannel":
                            batchProcessSecondaryChannels(batchRequests);
                            break;
                        case "intl_rms_product":
                            batchProcessProducts(batchRequests);
                            break;
                        default:
                            throw new BizException("SyncRmsDb:table not found : tableName" + tableName);
                    }
                } catch (Exception e) {
                    sendMessageService.sendGroupTextMessage(p1,
                            alarmEmailConfig.getRmsSyncDbEmail(),
                            "tableName:" + tableName + " error:" + e.getMessage() + ", " +
                                    "traceId=" + TraceIdUtil.traceId());
                    log.error("SyncRmsDbError:{}, tableName:{}", e, tableName);
                    throw new BizException("SyncRmsDbError", e);
                }
            }
        }
    }

    /**
     * 批量处理门店数据
     *
     * @param requests 请求列表
     */
    private void batchProcessStores(List<RmsDbContentRequest> requests) {
        // 批量处理门店：先解析请求，按业务主键 storeId 批量预查询已存在数据，然后分组执行插入/更新，并按动作上报指标
        List<IntlRmsStore> insertList = new ArrayList<>();
        List<IntlRmsStore> updateList = new ArrayList<>();
        List<IntlRmsStore> syncToEsList = new ArrayList<>();
        try {
            // 解析请求数据
            List<IntlRmsStore> parsed =
                    requests.stream().map(request -> JsonUtil.json2bean(JsonUtil.bean2json(request.getContent()),
                            IntlRmsStore.class)).collect(Collectors.toList());
            if (parsed.isEmpty()) {
                return;
            }

            // 按 storeId 批量预查询，避免单条循环查库
            Map<String, IntlRmsStore> existingByStoreMap = new HashMap<>();
            List<String> storeIds = parsed.stream().map(IntlRmsStore::getStoreId).collect(Collectors.toList());
            LambdaQueryWrapper<IntlRmsStore> wrapper = Wrappers.lambdaQuery();
            wrapper.in(IntlRmsStore::getStoreId, storeIds);
            List<IntlRmsStore> exist = rmsStoreServiceImpl.list(wrapper);
            if (exist != null && !exist.isEmpty()) {
                existingByStoreMap.putAll(
                        exist.stream().collect(Collectors.toMap(IntlRmsStore::getStoreId, store -> store)));
            }

            long now = System.currentTimeMillis();

            // 将数据分为更新与新增两类分别处理
            for (IntlRmsStore store : parsed) {
                IntlRmsStore old = existingByStoreMap.get(store.getStoreId());
                boolean isUpdate = old != null;
                if (isUpdate) {
                    //判断传入数据和已存在数据是否一致，不一致则更新
                    if (!compareStore(old, store)) {
                        store.setId(old.getId());
                        store.setUpdatedAt(now);
                        updateList.add(store);
                        if (!store.getName().equalsIgnoreCase(old.getName())
                                || !store.getCityCode().equalsIgnoreCase(old.getCityCode())
                                || !store.getCityIdName().equalsIgnoreCase(old.getCityIdName())
                                || !store.getProvinceCode().equalsIgnoreCase(old.getProvinceCode())
                                || !store.getProvinceLabel().equalsIgnoreCase(old.getProvinceLabel())) {
                            syncToEsList.add(store);
                        }
                    }
                } else {
                    store.setCreatedAt(now);
                    store.setUpdatedAt(now);
                    insertList.add(store);
                }
            }

            // 分别处理插入和更新操作
            if (CollectionUtils.isNotEmpty(insertList)) {
                rmsStoreServiceImpl.saveBatch(insertList);
            }
            if (CollectionUtils.isNotEmpty(updateList)) {
                rmsStoreServiceImpl.updateBatchById(updateList);
            }
            // 推送ES
            if (CollectionUtils.isNotEmpty(syncToEsList)) {
                List<String> storeCodeList =
                        syncToEsList.stream().map(IntlRmsStore::getCode).collect(Collectors.toList());
                syncToEsStore(storeCodeList);
            }
        } catch (Exception e) {
            sendMessageService.sendGroupTextMessage(p1, AlarmKey.RMS_SYNC_DB.getEmail(),
                    " 插入/更新失败：" + e.getMessage() + ", traceId=" + TraceIdUtil.traceId());
            log.error("batchProcessStores failed, insetListSize:{}, updateListSize:{}, error:{}", insertList.size(),
                    updateList.size(), e.getMessage(), e);
            throw new BizException("batchProcessStores SyncRmsDbError", e);
        }
    }

    /**
     * 根据门店code列表分页同步数据到ES
     *
     * @param storeCodeList 门店code列表
     */
    private void syncToEsStore(List<String> storeCodeList) {
        if (CollectionUtils.isEmpty(storeCodeList)) {
            return;
        }
        for (String storeCode : storeCodeList) {
            try {
                // 处理 IMEI 数据分页同步
                SalesImeiReqDto salesImeiReqDto = new SalesImeiReqDto();
                salesImeiReqDto.setStoreRmsCode(storeCode);
                imeiQuerySync(salesImeiReqDto);

                // 处理 QTY 数据分页同步
                SalesQtyReqDto salesQtyReqDto = new SalesQtyReqDto();
                salesQtyReqDto.setStoreRmsCode(storeCode);
                qtyQuerySync(salesQtyReqDto);
            } catch (Exception e) {
                log.error("处理 storeCode={} 时发生异常", storeCode, e);
                throw new BizException("处理 storeCode=" + storeCode + " 时发生异常>>>" + e.getMessage());
            }
        }
    }

    public void imeiQuerySync(SalesImeiReqDto query) {
        long totalCountLong = intlSoImeiEsService.count(query);
        if (totalCountLong < 1) {
            return;
        }
        int totalCount = (int) totalCountLong;
        int pageSize = 500;
        // 分页查询ES中QTY数据
        int totalPages = PaginationUtil.getTotalPages(totalCount, pageSize);
        for (int i = 1; i <= totalPages; i++) {
            query.setPageSize(pageSize);
            PageResponse<SoImeiIndex> soImeiIndexPage = intlSoImeiEsService.queryByPage(query);
            List<SoImeiIndex> soImeiIndexList = soImeiIndexPage.getData();
            if (CollUtil.isEmpty(soImeiIndexList)) {
                log.error("Sales IMEI Data search exception ===> query: {}", JacksonUtil.toStr(query));
                break;
            }
            // 深度分页处理
            SoImeiIndex lastSoImeiIndex = soImeiIndexList.get(soImeiIndexList.size() - 1);
            List<Object> searchAfter = new ArrayList<>();
            searchAfter.add(lastSoImeiIndex.getId());
            query.setSearchAfter(searchAfter);
            List<Long> imeiIds = soImeiIndexList.stream().map(SoImeiIndex::getId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(imeiIds)) {
                syncSoToEsProducer.sendSyncEsMsg(DataSyncDataTypeEnum.QTY, imeiIds, true);
            }
        }
    }

    public void qtyQuerySync(SalesQtyReqDto query) {
        long totalCountLong = intlSoQtyEsService.count(query);
        if (totalCountLong < 1) {
            return;
        }
        int totalCount = (int) totalCountLong;
        int pageSize = 500;
        // 分页查询ES中QTY数据
        int totalPages = PaginationUtil.getTotalPages(totalCount, pageSize);
        for (int i = 1; i <= totalPages; i++) {
            query.setPageSize(pageSize);
            PageResponse<SoQtyIndex> soQtyIndexPage = intlSoQtyEsService.search(query);
            List<SoQtyIndex> soQtyIndexList = soQtyIndexPage.getData();
            if (CollUtil.isEmpty(soQtyIndexList)) {
                log.error("Sales QTY Data search exception ===> query: {}", JacksonUtil.toStr(query));
                break;
            }
            // 深度分页处理
            SoQtyIndex lastSoQtyIndex = soQtyIndexList.get(soQtyIndexList.size() - 1);
            List<Object> searchAfter = new ArrayList<>();
            searchAfter.add(lastSoQtyIndex.getId());
            query.setSearchAfter(searchAfter);
            List<Long> qtyIds = soQtyIndexList.stream().map(SoQtyIndex::getId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(qtyIds)) {
                syncSoToEsProducer.sendSyncEsMsg(DataSyncDataTypeEnum.QTY, qtyIds, true);
            }
        }
    }

    private boolean compareStore(IntlRmsStore old, IntlRmsStore store) {
        if (ObjectUtils.isEmpty(old) || ObjectUtils.isEmpty(store)) {
            return true;
        }
        return Objects.equals(old.getName(), store.getName()) &&
                Objects.equals(old.getType(), store.getType()) &&
                Objects.equals(old.getTypeName(), store.getTypeName()) &&
                Objects.equals(old.getCode(), store.getCode()) &&
                Objects.equals(old.getCrssCode(), store.getCrssCode()) &&
                Objects.equals(old.getRetailerName(), store.getRetailerName()) &&
                Objects.equals(old.getRetailerId(), store.getRetailerId()) &&
                Objects.equals(old.getRetailerIdName(), store.getRetailerIdName()) &&
                Objects.equals(old.getCountryId(), store.getCountryId()) &&
                Objects.equals(old.getCountryIdName(), store.getCountryIdName()) &&
                Objects.equals(old.getAccountId(), store.getAccountId()) &&
                Objects.equals(old.getAccountIdName(), store.getAccountIdName()) &&
                Objects.equals(old.getDistributorId(), store.getDistributorId()) &&
                Objects.equals(old.getDistributorIdName(), store.getDistributorIdName()) &&
                Objects.equals(old.getCityId(), store.getCityId()) &&
                Objects.equals(old.getCityCode(), store.getCityCode()) &&
                Objects.equals(old.getCityIdName(), store.getCityIdName()) &&
                Objects.equals(old.getAddress(), store.getAddress()) &&
                Objects.equals(old.getChannelType(), store.getChannelType()) &&
                Objects.equals(old.getChannelTypeName(), store.getChannelTypeName()) &&
                Objects.equals(old.getGrade(), store.getGrade()) &&
                Objects.equals(old.getGradeName(), store.getGradeName()) &&
                Objects.equals(old.getOperationStatus(), store.getOperationStatus()) &&
                Objects.equals(old.getOperationStatusName(), store.getOperationStatusName()) &&
                Objects.equals(old.getHasPc(), store.getHasPc()) &&
                Objects.equals(old.getOwnerId(), store.getOwnerId()) &&
                Objects.equals(old.getOwnerIdName(), store.getOwnerIdName()) &&
                Objects.equals(old.getStateCode(), store.getStateCode()) &&
                Objects.equals(old.getProvinceCode(), store.getProvinceCode()) &&
                Objects.equals(old.getProvinceLabel(), store.getProvinceLabel()) &&
                Objects.equals(old.getCountyCode(), store.getCountyCode()) &&
                Objects.equals(old.getCountyLabel(), store.getCountyLabel()) &&
                Objects.equals(old.getCountryShortcode(), store.getCountryShortcode()) &&
                Objects.equals(old.getGradeCalFlag(), store.getGradeCalFlag()) &&
                Objects.equals(old.getDistrictOrgCode(), store.getDistrictOrgCode()) &&
                Objects.equals(old.getDivisionOrgCode(), store.getDivisionOrgCode()) &&
                Objects.equals(old.getAreaOrgCode(), store.getAreaOrgCode()) &&
                Objects.equals(old.getRegionOrgCode(), store.getRegionOrgCode()) &&
                Objects.equals(old.getCountryOrgCode(), store.getCountryOrgCode()) &&
                Objects.equals(old.getStoreClass(), store.getStoreClass()) &&
                Objects.equals(old.getHasSr(), store.getHasSr());
    }

    /**
     * 批量处理用户数据
     *
     * @param requests 请求列表
     */
    private void batchProcessUsers(List<RmsDbContentRequest> requests) {
        // 批量处理用户：先解析请求，按业务主键 rmsUserid 批量预查询已存在数据，然后分组执行插入/更新，并按动作上报指标
        List<IntlRmsUser> insertList = new ArrayList<>();
        List<IntlRmsUser> updateList = new ArrayList<>();

        try {
            // 解析请求数据
            List<IntlRmsUser> parsed =
                    requests.stream().map(request -> JsonUtil.json2bean(JsonUtil.bean2json(request.getContent()),
                            IntlRmsUser.class)).collect(Collectors.toList());
            if (ObjectUtils.isEmpty(parsed)) {
                return;
            }

            // 按 rmsUserid 批量预查询
            Map<String, IntlRmsUser> existingByUserId = new HashMap<>();
            List<String> userIds = parsed.stream().map(IntlRmsUser::getRmsUserid).collect(Collectors.toList());
            LambdaQueryWrapper<IntlRmsUser> wrapper = Wrappers.lambdaQuery();
            wrapper.in(IntlRmsUser::getRmsUserid, userIds);
            List<IntlRmsUser> exist = userServiceImpl.list(wrapper);
            if (exist != null && !exist.isEmpty()) {
                existingByUserId.putAll(
                        exist.stream().collect(Collectors.toMap(IntlRmsUser::getRmsUserid, user -> user)));
            }

            long now = System.currentTimeMillis();

            // 将数据分为更新与新增两类分别处理
            for (IntlRmsUser user : parsed) {
                IntlRmsUser old = existingByUserId.get(user.getRmsUserid());
                boolean isUpdate = old != null;
                if (isUpdate) {
                    //判断传入数据和已存在数据是否一致，不一致则更新
                    if (!compareUser(old, user)) {
                        user.setId(old.getId());
                        user.setUpdatedAt(now);
                        updateList.add(user);
                    }
                } else {
                    user.setCreatedAt(now);
                    user.setUpdatedAt(now);
                    insertList.add(user);
                }
            }

            // 分别处理插入和更新操作
            if (CollectionUtils.isNotEmpty(insertList)) {
                userServiceImpl.saveBatch(insertList);
            }
            if (CollectionUtils.isNotEmpty(updateList)) {
                userServiceImpl.updateBatchById(updateList);
            }
        } catch (Exception e) {
            sendMessageService.sendGroupTextMessage(p1, AlarmKey.RMS_SYNC_DB.getEmail(),
                    " 插入/更新失败：" + e.getMessage() + ", traceId=" + TraceIdUtil.traceId());
            log.error("batchProcessUsers failed, insetListSize:{}, updateListSize:{}, error:{}", insertList.size(),
                    updateList.size(), e.getMessage(), e);
            throw new BizException("batchProcessUsers SyncRmsDbError", e);
        }
    }

    /**
     * 比较两个用户对象的字段是否相等
     *
     * @param old 原始用户对象
     * @param user 新用户对象
     * @return 如果所有字段都相等返回true，否则返回false
     */
    private boolean compareUser(IntlRmsUser old, IntlRmsUser user) {
        if (ObjectUtils.isEmpty(old) || ObjectUtils.isEmpty(user)) {
            return true;
        }
        return Objects.equals(old.getCode(), user.getCode()) &&
                Objects.equals(old.getDomainName(), user.getDomainName()) &&
                Objects.equals(old.getEnglishName(), user.getEnglishName()) &&
                Objects.equals(old.getCountryId(), user.getCountryId()) &&
                Objects.equals(old.getCountryName(), user.getCountryName()) &&
                Objects.equals(old.getJobId(), user.getJobId()) &&
                Objects.equals(old.getJobName(), user.getJobName()) &&
                Objects.equals(old.getEmail(), user.getEmail()) &&
                Objects.equals(old.getMobile(), user.getMobile()) &&
                Objects.equals(old.getMiId(), user.getMiId()) &&
                Objects.equals(old.getManagerId(), user.getManagerId()) &&
                Objects.equals(old.getManagerName(), user.getManagerName()) &&
                Objects.equals(old.getVirtualMiId(), user.getVirtualMiId()) &&
                Objects.equals(old.getLanguageId(), user.getLanguageId()) &&
                Objects.equals(old.getLanguageName(), user.getLanguageName()) &&
                Objects.equals(old.getLanguageCode(), user.getLanguageCode()) &&
                Objects.equals(old.getIsDisabled(), user.getIsDisabled()) &&
                Objects.equals(old.getTimezoneCode(), user.getTimezoneCode()) &&
                Objects.equals(old.getTimezoneName(), user.getTimezoneName()) &&
                Objects.equals(old.getCountryShortcode(), user.getCountryShortcode()) &&
                Objects.equals(old.getHasBindMiId(), user.getHasBindMiId()) &&
                Objects.equals(old.getIsOffline(), user.getIsOffline()) &&
                Objects.equals(old.getBrands(), user.getBrands()) &&
                Objects.equals(old.getKeySellingProducts(), user.getKeySellingProducts()) &&
                Objects.equals(old.getTestAccount(), user.getTestAccount());
    }

    /**
     * 批量处理阵地数据
     *
     * @param requests 请求列表
     */
    private void batchProcessPositions(List<RmsDbContentRequest> requests) {
        // 批量处理岗位：先解析请求，按业务主键 positionId 批量预查询已存在数据，然后分组执行插入/更新，并按动作上报指标
        List<IntlRmsPosition> insertList = new ArrayList<>();
        List<IntlRmsPosition> updateList = new ArrayList<>();
        List<IntlRmsPosition> syncToEsList = new ArrayList<>();
        try {
            // 解析所有请求数据
            List<IntlRmsPosition> parsedPositions = requests.stream().map(request -> {
                IntlRmsPosition position = JsonUtil.json2bean(JsonUtil.bean2json(request.getContent()),
                        IntlRmsPosition.class);
                if (position != null && StringUtils.isBlank(position.getPositionCategory())) {
                    position.setPositionCategory("[]");
                }
                return position;
            }).filter(position -> position != null && position.getPositionId() != null).collect(Collectors.toList());

            if (parsedPositions.isEmpty()) {
                return;
            }

            // 预查询已有记录
            Map<String, IntlRmsPosition> existingPositionsMap = new HashMap<>();
            List<String> positionIds =
                    parsedPositions.stream().map(IntlRmsPosition::getPositionId).collect(Collectors.toList());
            LambdaQueryWrapper<IntlRmsPosition> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.in(IntlRmsPosition::getPositionId, positionIds);
            List<IntlRmsPosition> existingPositions = intlPositionServiceImpl.list(queryWrapper);
            if (existingPositions != null && !existingPositions.isEmpty()) {
                existingPositionsMap.putAll(existingPositions.stream()
                        .collect(Collectors.toMap(IntlRmsPosition::getPositionId, position -> position)));
            }

            long now = System.currentTimeMillis();

            // 将数据分为更新与新增两类分别处理
            for (IntlRmsPosition position : parsedPositions) {
                IntlRmsPosition old = existingPositionsMap.get(position.getPositionId());
                boolean isUpdate = old != null;

                if (isUpdate) {
                    //判断传入数据和已存在数据是否一致，不一致则更新
                    if (!comparePosition(old, position)) {
                        position.setId(old.getId());
                        position.setUpdatedAt(now);
                        updateList.add(position);
                        if (!position.getName().equalsIgnoreCase(old.getName())) {
                            syncToEsList.add(position);
                        }
                    }

                } else {
                    position.setCreatedAt(now);
                    position.setUpdatedAt(now);
                    insertList.add(position);
                }
            }

            // 分别处理插入和更新操作
            if (CollectionUtils.isNotEmpty(insertList)) {
                intlPositionServiceImpl.saveBatch(insertList);
            }
            if (CollectionUtils.isNotEmpty(updateList)) {
                intlPositionServiceImpl.updateBatchById(updateList);
            }
            // 推送ES
            if (CollectionUtils.isNotEmpty(syncToEsList)) {
                List<String> positionCodeList =
                        syncToEsList.stream().map(IntlRmsPosition::getCode).collect(Collectors.toList());
                syncToEsPosition(positionCodeList);
            }
        } catch (Exception e) {
            sendMessageService.sendGroupTextMessage(p1, AlarmKey.RMS_SYNC_DB.getEmail(),
                    " 插入/更新失败：" + e.getMessage() + ", traceId=" + TraceIdUtil.traceId());
            log.error("batchProcessPositions failed, insetListSize:{}, updateListSize:{}, error:{}", insertList.size(),
                    updateList.size(), e.getMessage(), e);
            throw new BizException("batchProcessPositions SyncRmsDbError", e);
        }
    }

    /**
     * 根据阵地code列表分页同步数据到ES
     *
     * @param positionCodeList 阵地code列表
     */
    private void syncToEsPosition(List<String> positionCodeList) {
        if (CollectionUtils.isEmpty(positionCodeList)) {
            return;
        }

        try {
            // 处理 IMEI 数据分页同步
            SalesImeiReqDto salesImeiReqDto = new SalesImeiReqDto();
            salesImeiReqDto.setPositionRmsCodeList(positionCodeList);
            imeiQuerySync(salesImeiReqDto);

            // 处理 QTY 数据分页同步
            SalesQtyReqDto salesQtyReqDto = new SalesQtyReqDto();
            salesQtyReqDto.setPositionRmsCodeList(positionCodeList);
            qtyQuerySync(salesQtyReqDto);

        } catch (Exception e) {
            log.error("根据 positionCodeList 分页同步数据到ES时发生异常, positionCodeList={}", positionCodeList, e);
            throw new BizException("syncToEsPosition SyncRmsDbError", e);
        }
    }

    /**
     * 比较两个阵地对象的字段是否相等
     *
     * @param old 原始阵地对象
     * @param position 新阵地对象
     * @return 如果所有字段都相等返回true，否则返回false
     */
    private boolean comparePosition(IntlRmsPosition old, IntlRmsPosition position) {
        if (ObjectUtils.isEmpty(old) || ObjectUtils.isEmpty(position)) {
            return true;
        }
        return Objects.equals(old.getCode(), position.getCode()) &&
                Objects.equals(old.getName(), position.getName()) &&
                Objects.equals(old.getStoreId(), position.getStoreId()) &&
                Objects.equals(old.getStoreName(), position.getStoreName()) &&
                Objects.equals(old.getAbbreviation(), position.getAbbreviation()) &&
                Objects.equals(old.getState(), position.getState()) &&
                Objects.equals(old.getStateName(), position.getStateName()) &&
                Objects.equals(old.getDistributorId(), position.getDistributorId()) &&
                Objects.equals(old.getDistributorName(), position.getDistributorName()) &&
                Objects.equals(old.getAccountId(), position.getAccountId()) &&
                Objects.equals(old.getAccountName(), position.getAccountName()) &&
                Objects.equals(old.getRetailerId(), position.getRetailerId()) &&
                Objects.equals(old.getRetailerName(), position.getRetailerName()) &&
                Objects.equals(old.getChannelType(), position.getChannelType()) &&
                Objects.equals(old.getChannelTypeName(), position.getChannelTypeName()) &&
                Objects.equals(old.getType(), position.getType()) &&
                Objects.equals(old.getTypeName(), position.getTypeName()) &&
                Objects.equals(old.getLevel(), position.getLevel()) &&
                Objects.equals(old.getLevelName(), position.getLevelName()) &&
                Objects.equals(old.getIsPromotionStore(), position.getIsPromotionStore()) &&
                Objects.equals(old.getCountryId(), position.getCountryId()) &&
                Objects.equals(old.getCountryName(), position.getCountryName()) &&
                Objects.equals(old.getCityId(), position.getCityId()) &&
                Objects.equals(old.getCityName(), position.getCityName()) &&
                Objects.equals(old.getAddress(), position.getAddress()) &&
                Objects.equals(old.getOwnerId(), position.getOwnerId()) &&
                Objects.equals(old.getOwnerName(), position.getOwnerName()) &&
                Objects.equals(old.getStateCode(), position.getStateCode()) &&
                Objects.equals(old.getArea(), position.getArea()) &&
                Objects.equals(old.getAreaCode(), position.getAreaCode()) &&
                Objects.equals(old.getCrpsCode(), position.getCrpsCode()) &&
                Objects.equals(old.getPositionCategory(), position.getPositionCategory()) &&
                Objects.equals(old.getFurnitureTtl(), position.getFurnitureTtl()) &&
                Objects.equals(old.getDisplayCapacityExpansionStatus(), position.getDisplayCapacityExpansionStatus()) &&
                Objects.equals(old.getPositionLocation(), position.getPositionLocation()) &&
                Objects.equals(old.getPositionLongitude(), position.getPositionLongitude()) &&
                Objects.equals(old.getPositionLatitude(), position.getPositionLatitude()) &&
                Objects.equals(old.getCityCode(), position.getCityCode()) &&
                Objects.equals(old.getOrganizationCode(), position.getOrganizationCode()) &&
                Objects.equals(old.getProvinceCode(), position.getProvinceCode()) &&
                Objects.equals(old.getCountryShortcode(), position.getCountryShortcode()) &&
                Objects.equals(old.getSupplierCode(), position.getSupplierCode()) &&
                Objects.equals(old.getHasPc(), position.getHasPc()) &&
                Objects.equals(old.getHasSr(), position.getHasSr()) &&
                Objects.equals(old.getPositionClass(), position.getPositionClass()) &&
                Objects.equals(old.getConstructionType(), position.getConstructionType());
    }

    /**
     * 批量处理国家时区数据
     *
     * @param requests 请求列表
     */
    private void batchProcessCountryTimezones(List<RmsDbContentRequest> requests) {
        // 批量处理国家时区：先解析请求，按业务主键 countryCode 批量预查询已存在数据，然后分组执行插入/更新，并按动作上报指标
        List<IntlRmsCountryTimezone> insertList = new ArrayList<>();
        List<IntlRmsCountryTimezone> updateList = new ArrayList<>();

        try {
            List<IntlRmsCountryTimezone> parsed = requests.stream()
                    .map(request -> JsonUtil.json2bean(JsonUtil.bean2json(request.getContent()),
                            IntlRmsCountryTimezone.class)).collect(Collectors.toList());
            if (parsed.isEmpty()) return;

            // 批量预查询
            Map<String, IntlRmsCountryTimezone> existing = new HashMap<>();
            List<String> codes =
                    parsed.stream().map(IntlRmsCountryTimezone::getCountryCode).collect(Collectors.toList());
            LambdaQueryWrapper<IntlRmsCountryTimezone> w = Wrappers.lambdaQuery();
            w.in(IntlRmsCountryTimezone::getCountryCode, codes);
            List<IntlRmsCountryTimezone> list = intlCountryTimeZoneServiceImpl.list(w);
            if (list != null && !list.isEmpty()) {
                existing.putAll(list.stream().collect(
                        Collectors.toMap(IntlRmsCountryTimezone::getCountryCode,
                                countryTimezone -> countryTimezone)));
            }

            long now = System.currentTimeMillis();

            // 将数据分为更新与新增两类分别处理
            for (IntlRmsCountryTimezone tz : parsed) {
                IntlRmsCountryTimezone old = existing.get(tz.getCountryCode());
                boolean isUpdate = old != null;

                if (isUpdate) {
                    //判断传入数据和已存在数据是否一致，不一致则更新
                    if (!compareCountryTimezone(old, tz)) {
                        tz.setId(old.getId());
                        tz.setUpdatedAt(now);
                        updateList.add(tz);
                    }
                } else {
                    tz.setCreatedAt(now);
                    tz.setUpdatedAt(now);
                    insertList.add(tz);
                }
            }

            // 分别处理插入和更新操作
            if (CollectionUtils.isNotEmpty(insertList)) {
                intlCountryTimeZoneServiceImpl.saveBatch(insertList);
            }
            if (CollectionUtils.isNotEmpty(updateList)) {
                intlCountryTimeZoneServiceImpl.updateBatchById(updateList);
            }
        } catch (Exception e) {
            sendMessageService.sendGroupTextMessage(p1, AlarmKey.RMS_SYNC_DB.getEmail(),
                    " 插入/更新失败：" + e.getMessage() + ", traceId=" + TraceIdUtil.traceId());
            log.error("batchProcessCountryTimezones failed, insetListSize:{}, updateListSize:{}, error:{}",
                    insertList.size(),
                    updateList.size(), e.getMessage(), e);
            throw new BizException("batchProcessCountryTimezones SyncRmsDbError", e);
        }
    }

    /**
     * 比较两个国家时区对象的字段是否相等
     *
     * @param old 原始国家时区对象
     * @param tz 新国家时区对象
     * @return 如果所有字段都相等返回true，否则返回false
     */
    private boolean compareCountryTimezone(IntlRmsCountryTimezone old, IntlRmsCountryTimezone tz) {
        if (ObjectUtils.isEmpty(old) || ObjectUtils.isEmpty(tz)) {
            return true;
        }
        return Objects.equals(old.getCountryTimezoneId(), tz.getCountryTimezoneId()) &&
                Objects.equals(old.getName(), tz.getName()) &&
                Objects.equals(old.getCountryId(), tz.getCountryId()) &&
                Objects.equals(old.getCountryName(), tz.getCountryName()) &&
                Objects.equals(old.getCountryCode(), tz.getCountryCode()) &&
                Objects.equals(old.getTimezoneName(), tz.getTimezoneName()) &&
                Objects.equals(old.getTimezoneCode(), tz.getTimezoneCode()) &&
                Objects.equals(old.getBias(), tz.getBias()) &&
                Objects.equals(old.getStateCode(), tz.getStateCode()) &&
                Objects.equals(old.getArea(), tz.getArea()) &&
                Objects.equals(old.getAreaCode(), tz.getAreaCode()) &&
                Objects.equals(old.getCode(), tz.getCode()) &&
                Objects.equals(old.getCurrencyCode(), tz.getCurrencyCode()) &&
                Objects.equals(old.getRmsCurrencyId(), tz.getRmsCurrencyId());
    }

    /**
     * 批量处理员工阵地关联数据
     *
     * @param requests 请求列表
     */
    private void batchProcessPersonnelPositions(List<RmsDbContentRequest> requests) {
        // 批量处理员工阵地关联：先解析请求，按业务主键 associationId 批量预查询已存在数据，然后分组执行插入/更新，并按动作上报指标
        List<IntlRmsPersonnelPosition> insertList = new ArrayList<>();
        List<IntlRmsPersonnelPosition> updateList = new ArrayList<>();

        try {
            List<IntlRmsPersonnelPosition> parsed = requests.stream()
                    .map(request -> JsonUtil.json2bean(JsonUtil.bean2json(request.getContent()),
                            IntlRmsPersonnelPosition.class))
                    .collect(Collectors.toList());
            if (parsed.isEmpty()) return;

            // 批量预查询
            Map<String, IntlRmsPersonnelPosition> existing = new HashMap<>();
            List<String> associationIds =
                    parsed.stream().map(IntlRmsPersonnelPosition::getAssociationId).collect(Collectors.toList());
            LambdaQueryWrapper<IntlRmsPersonnelPosition> w = Wrappers.lambdaQuery();
            w.in(IntlRmsPersonnelPosition::getAssociationId, associationIds);
            List<IntlRmsPersonnelPosition> list = intlRmsPersonnelPositionServiceImpl.list(w);
            if (list != null && !list.isEmpty()) {
                existing.putAll(list.stream().collect(
                        Collectors.toMap(IntlRmsPersonnelPosition::getAssociationId,
                                personnelPosition -> personnelPosition)));
            }

            long now = System.currentTimeMillis();

            // 将数据分为更新与新增两类分别处理
            for (IntlRmsPersonnelPosition p : parsed) {
                IntlRmsPersonnelPosition old = existing.get(p.getAssociationId());
                boolean isUpdate = old != null;

                if (isUpdate) {
                    //判断传入数据和已存在数据是否一致，不一致则更新
                    if (!comparePersonnelPosition(old, p)) {
                        p.setId(old.getId());
                        p.setUpdatedAt(now);
                        updateList.add(p);
                    }
                } else {
                    p.setCreatedAt(now);
                    p.setUpdatedAt(now);
                    insertList.add(p);
                }
            }

            // 分别处理插入和更新操作
            if (CollectionUtils.isNotEmpty(insertList)) {
                intlRmsPersonnelPositionServiceImpl.saveBatch(insertList);
            }
            if (CollectionUtils.isNotEmpty(updateList)) {
                intlRmsPersonnelPositionServiceImpl.updateBatchById(updateList);
            }
        } catch (Exception e) {
            sendMessageService.sendGroupTextMessage("admin_group", AlarmKey.RMS_SYNC_DB.getEmail(),
                    " 插入/更新失败：" + e.getMessage() +
                            ", traceId=" + TraceIdUtil.traceId());
            log.error("batchProcessPersonnelPositions failed, insetList:{}, updateList:{}, error:{}", insertList,
                    updateList,
                    e.getMessage(), e);
            throw new BizException("batchProcessPersonnelPositions SyncRmsDbError", e);
        }
    }

    /**
     * 比较两个员工阵地关联对象的字段是否相等
     *
     * @param old 原始员工阵地关联对象
     * @param p 新员工阵地关联对象
     * @return 如果所有字段都相等返回true，否则返回false
     */
    private boolean comparePersonnelPosition(IntlRmsPersonnelPosition old, IntlRmsPersonnelPosition p) {
        if (ObjectUtils.isEmpty(old) || ObjectUtils.isEmpty(p)) {
            return true;
        }
        return Objects.equals(old.getUserId(), p.getUserId()) &&
                Objects.equals(old.getPositionId(), p.getPositionId()) &&
                Objects.equals(old.getUserName(), p.getUserName()) &&
                Objects.equals(old.getStoreName(), p.getStoreName()) &&
                Objects.equals(old.getStateCode(), p.getStateCode());
    }

    /**
     * 批量处理签到规则数据
     *
     * @param requests 请求列表
     */
    private void batchProcessSignRules(List<RmsDbContentRequest> requests) {
        // 批量处理签到规则：先解析请求，按业务主键 signRuleId 批量预查询已存在数据，然后分组执行插入/更新，并按动作上报指标
        List<IntlRmsSignRule> insertList = new ArrayList<>();
        List<IntlRmsSignRule> updateList = new ArrayList<>();

        try {
            List<IntlRmsSignRule> parsed = requests.stream()
                    .map(request -> JsonUtil.json2bean(JsonUtil.bean2json(request.getContent()), IntlRmsSignRule.class))
                    .collect(Collectors.toList());
            if (parsed.isEmpty()) return;

            // 批量预查询
            Map<String, IntlRmsSignRule> existing = new HashMap<>();
            List<String> ids = parsed.stream().map(IntlRmsSignRule::getSignRuleId).collect(Collectors.toList());
            LambdaQueryWrapper<IntlRmsSignRule> w = Wrappers.lambdaQuery();
            w.in(IntlRmsSignRule::getSignRuleId, ids);
            List<IntlRmsSignRule> list = intlRmsSignRuleServiceImpl.list(w);
            if (list != null && !list.isEmpty()) {
                existing.putAll(list.stream().collect(
                        Collectors.toMap(IntlRmsSignRule::getSignRuleId, signRule -> signRule)));
            }

            long now = System.currentTimeMillis();

            // 将数据分为更新与新增两类分别处理
            for (IntlRmsSignRule r : parsed) {
                IntlRmsSignRule old = existing.get(r.getSignRuleId());
                boolean isUpdate = old != null;
                if (isUpdate) {
                    //判断传入数据和已存在数据是否一致，不一致则更新
                    if (!compareSignRule(old, r)) {
                        r.setId(old.getId());
                        r.setUpdatedAt(now);
                        updateList.add(r);
                    }
                } else {
                    r.setCreatedAt(now);
                    r.setUpdatedAt(now);
                    insertList.add(r);
                }
            }

            // 分别处理插入和更新操作
            if (CollectionUtils.isNotEmpty(insertList)) {
                intlRmsSignRuleServiceImpl.saveBatch(insertList);
            }
            if (CollectionUtils.isNotEmpty(updateList)) {
                intlRmsSignRuleServiceImpl.updateBatchById(updateList);
            }
        } catch (Exception e) {
            sendMessageService.sendGroupTextMessage(p1, AlarmKey.RMS_SYNC_DB.getEmail(),
                    " 插入/更新失败：" + e.getMessage() + ", traceId=" + TraceIdUtil.traceId());
            log.error("batchProcessSignRules failed, insetListSize:{}, updateListSize:{}, error:{}", insertList.size(),
                    updateList.size(), e.getMessage(), e);
            throw new BizException("batchProcessSignRules SyncRmsDbError", e);
        }
    }

    /**
     * 比较两个签到规则对象的字段是否相等
     *
     * @param old 原始签到规则对象
     * @param r 新签到规则对象
     * @return 如果所有字段都相等返回true，否则返回false
     */
    private boolean compareSignRule(IntlRmsSignRule old, IntlRmsSignRule r) {
        if (ObjectUtils.isEmpty(old) || ObjectUtils.isEmpty(r)) {
            return true;
        }
        return Objects.equals(old.getCode(), r.getCode()) &&
                Objects.equals(old.getName(), r.getName()) &&
                Objects.equals(old.getCountryId(), r.getCountryId()) &&
                Objects.equals(old.getCountryName(), r.getCountryName()) &&
                Objects.equals(old.getStartTime(), r.getStartTime()) &&
                Objects.equals(old.getEndTime(), r.getEndTime()) &&
                Objects.equals(old.getJob(), r.getJob()) &&
                Objects.equals(old.getJobName(), r.getJobName()) &&
                Objects.equals(old.getStoreType(), r.getStoreType()) &&
                Objects.equals(old.getStoreTypeName(), r.getStoreTypeName()) &&
                Objects.equals(old.getStoreLevel(), r.getStoreLevel()) &&
                Objects.equals(old.getStoreLevelName(), r.getStoreLevelName()) &&
                Objects.equals(old.getEffectiveRange(), r.getEffectiveRange()) &&
                Objects.equals(old.getValidTime(), r.getValidTime()) &&
                Objects.equals(old.getStateCode(), r.getStateCode());
    }

    /**
     * 批量处理零售商数据
     *
     * @param requests 请求列表
     */
    private void batchProcessRetailers(List<RmsDbContentRequest> requests) {
        // 批量处理零售商：先解析请求，按业务主键 retailerId 批量预查询已存在数据，然后分组执行插入/更新（含 isNew 维护）-> 同步 SO 规则零售商 -> 上报指标
        List<IntlRmsRetailer> insertList = new ArrayList<>();
        List<IntlRmsRetailer> updateList = new ArrayList<>();

        try {
            List<IntlRmsRetailer> parsed = requests.stream()
                    .map(request -> JsonUtil.json2bean(JsonUtil.bean2json(request.getContent()), IntlRmsRetailer.class))
                    .collect(Collectors.toList());
            if (parsed.isEmpty()) return;

            // 批量预查询
            Map<String, IntlRmsRetailer> existing = new HashMap<>();
            List<String> ids = parsed.stream().map(IntlRmsRetailer::getRetailerId).collect(Collectors.toList());
            LambdaQueryWrapper<IntlRmsRetailer> w = Wrappers.lambdaQuery();
            w.in(IntlRmsRetailer::getRetailerId, ids);
            List<IntlRmsRetailer> list = intlRetailerServiceImpl.list(w);
            if (list != null && !list.isEmpty()) {
                existing.putAll(list.stream().collect(
                        Collectors.toMap(IntlRmsRetailer::getRetailerId, retailer -> retailer)));
            }

            long now = System.currentTimeMillis();

            // 将数据分为更新与新增两类分别处理，并维护 isNew 字段
            for (IntlRmsRetailer t : parsed) {
                IntlRmsRetailer old = existing.get(t.getRetailerId());
                boolean isUpdate = old != null;
                if (isUpdate) {
                    //判断传入数据和已存在数据是否一致，不一致则更新
                    if (!compareRetailer(old, t)) {
                        t.setId(old.getId());
                        t.setCreatedAt(old.getCreatedAt());
                        t.setUpdatedAt(now);
                        t.setIsNew(0);
                        updateList.add(t);
                    }
                } else {
                    t.setCreatedAt(now);
                    t.setUpdatedAt(now);
                    t.setIsNew(1);
                    insertList.add(t);
                }
            }

            // 分别处理插入和更新操作
            if (CollectionUtils.isNotEmpty(insertList)) {
                intlRetailerServiceImpl.saveBatch(insertList);
            }
            if (CollectionUtils.isNotEmpty(updateList)) {
                intlRetailerServiceImpl.updateBatchById(updateList);
            }
            // 同步 SO 规则零售商
            saveOrUpdateSoRuleRetailer(parsed);
        } catch (Exception e) {
            sendMessageService.sendGroupTextMessage(p1, AlarmKey.RMS_SYNC_DB.getEmail(),
                    " 插入/更新失败：" + e.getMessage() + ", traceId=" + TraceIdUtil.traceId());
            log.error("batchProcessRetailers failed, insetListSize:{}, updateListSize:{}, error:{}", insertList.size(),
                    updateList.size(), e.getMessage(), e);
            throw new BizException("batchProcessRetailers SyncRmsDbError", e);
        }
    }

    /**
     * 比较两个零售商对象的字段是否相等
     *
     * @param old 原始零售商对象
     * @param t 新零售商对象
     * @return 如果所有字段都相等返回true，否则返回false
     */
    private boolean compareRetailer(IntlRmsRetailer old, IntlRmsRetailer t) {
        if (ObjectUtils.isEmpty(old) || ObjectUtils.isEmpty(t)) {
            return true;
        }
        return Objects.equals(old.getRetailerId(), t.getRetailerId()) &&
                Objects.equals(old.getName(), t.getName()) &&
                Objects.equals(old.getCrmCode(), t.getCrmCode()) &&
                Objects.equals(old.getRetailerName(), t.getRetailerName()) &&
                Objects.equals(old.getFromCrm(), t.getFromCrm()) &&
                Objects.equals(old.getRetailerForShort(), t.getRetailerForShort()) &&
                Objects.equals(old.getKeyRetailer(), t.getKeyRetailer()) &&
                Objects.equals(old.getRetailerGrade(), t.getRetailerGrade()) &&
                Objects.equals(old.getCountryId(), t.getCountryId()) &&
                Objects.equals(old.getCountryName(), t.getCountryName()) &&
                Objects.equals(old.getCountryCode(), t.getCountryCode()) &&
                Objects.equals(old.getProvinceCode(), t.getProvinceCode()) &&
                Objects.equals(old.getProvinceName(), t.getProvinceName()) &&
                Objects.equals(old.getCityCode(), t.getCityCode()) &&
                Objects.equals(old.getCityName(), t.getCityName()) &&
                Objects.equals(old.getCountyCode(), t.getCountyCode()) &&
                Objects.equals(old.getCountyName(), t.getCountyName()) &&
                Objects.equals(old.getAddress(), t.getAddress()) &&
                Objects.equals(old.getRetailerChannelType(), t.getRetailerChannelType()) &&
                Objects.equals(old.getRetailerChannelTypeName(), t.getRetailerChannelTypeName()) &&
                Objects.equals(old.getStateCode(), t.getStateCode()) &&
                Objects.equals(old.getIsNew(), t.getIsNew()) &&
                Objects.equals(old.getCountryShortcode(), t.getCountryShortcode());
    }

    /**
     * 保存或更新，so规则零售商
     *
     * @param retailerList INTL RMS零售商
     */
    private void saveOrUpdateSoRuleRetailer(List<IntlRmsRetailer> retailerList) {
        try {
            // 批量构建零售商模型列表并调用外部服务
            List<SoRuleRetailerModel> retailerModelList = retailerList.stream()
                    .map(intlRmsRetailer -> {
                        SoRuleRetailerModel retailerModel = new SoRuleRetailerModel();
                        retailerModel.setRetailerCode(intlRmsRetailer.getName());
                        retailerModel.setRetailerName(intlRmsRetailer.getRetailerName());
                        retailerModel.setChannelType(intlRmsRetailer.getRetailerChannelTypeName());
                        retailerModel.setCountryName(intlRmsRetailer.getCountryName());
                        retailerModel.setCountryCode(intlRmsRetailer.getCountryShortcode());
                        retailerModel.setCreateRetailerTime(intlRmsRetailer.getCreatedAt());
                        return retailerModel;
                    })
                    .collect(Collectors.toList());

            // 批量调用外部服务
            intlSoRuleRetailerApiService.saveOrUpdateByRetailerCode(retailerModelList);

        } catch (Exception e) {
            log.error("saveOrUpdateSoRuleRetailer error", e);
        }
    }

    /**
     * 批量处理SN黑名单数据
     *
     * @param requests 请求列表
     */
    private void batchProcessSoSnBlacklists(List<RmsDbContentRequest> requests) {
        requests.forEach(this::addSoSnBlacklist);
    }

    /**
     * 添加SN黑名单数据
     *
     * @param rmsDBContentRequest 请求内容
     */
    private void addSoSnBlacklist(RmsDbContentRequest rmsDBContentRequest) {
        try {
            log.info("SoSnBlacklist start :{}", rmsDBContentRequest);
            soBlacklistApiService.syncSoSnBlacklist(rmsDBContentRequest.getContent());
        } catch (Exception e) {
            log.error("addSoSnBlacklist error", e);
            throw new BizException("addSoSnBlacklist RmsSyncDbConsumer error", e);
        }
    }

    /**
     * 批量处理RRP数据
     *
     * @param requests 请求列表
     */
    private void batchProcessRrps(List<RmsDbContentRequest> requests) {
        // 批量处理 RRP：先解析请求，按业务主键 rrpId 批量预查询已存在数据，然后分组执行插入/更新，并按动作上报指标
        List<IntlRmsRrp> insertList = new ArrayList<>();
        List<IntlRmsRrp> updateList = new ArrayList<>();

        try {
            List<IntlRmsRrp> parsed = requests.stream()
                    .map(request -> JsonUtil.json2bean(JsonUtil.bean2json(request.getContent()), IntlRmsRrp.class))
                    .collect(Collectors.toList());
            if (parsed.isEmpty()) return;

            // 批量预查询
            Map<String, IntlRmsRrp> existing = new HashMap<>();
            List<String> ids = parsed.stream().map(IntlRmsRrp::getRrpId).collect(Collectors.toList());
            LambdaQueryWrapper<IntlRmsRrp> w = Wrappers.lambdaQuery();
            w.in(IntlRmsRrp::getRrpId, ids);
            List<IntlRmsRrp> list = intlRmsRrpServiceImpl.list(w);
            if (list != null && !list.isEmpty()) {
                existing.putAll(list.stream()
                        .collect(Collectors.toMap(IntlRmsRrp::getRrpId, rrp -> rrp)));
            }

            long now = System.currentTimeMillis();

            // 将数据分为更新与新增两类分别处理
            for (IntlRmsRrp r : parsed) {
                IntlRmsRrp old = existing.get(r.getRrpId());
                boolean isUpdate = old != null;
                if (isUpdate) {
                    //判断传入数据和已存在数据是否一致，不一致则更新
                    if (!compareRrp(old, r)) {
                        r.setId(old.getId());
                        r.setUpdatedAt(now);
                        updateList.add(r);
                    }
                } else {
                    r.setCreatedAt(now);
                    r.setUpdatedAt(now);
                    insertList.add(r);
                }
            }

            // 分别处理插入和更新操作
            if (CollectionUtils.isNotEmpty(insertList)) {
                intlRmsRrpServiceImpl.saveBatch(insertList);
            }
            if (CollectionUtils.isNotEmpty(updateList)) {
                intlRmsRrpServiceImpl.updateBatchById(updateList);
            }
        } catch (Exception e) {
            sendMessageService.sendGroupTextMessage(p1, AlarmKey.RMS_SYNC_DB.getEmail(),
                    " 插入/更新失败：" + e.getMessage() + ", traceId=" + TraceIdUtil.traceId());
            log.error("batchProcessRrps failed, insetListSize:{}, updateListSize:{}, error:{}", insertList.size(),
                    updateList.size(), e.getMessage(), e);
            throw new BizException("batchProcessRrps RmsSyncDbConsumer error", e);
        }
    }

    /**
     * 比较两个RRP对象的字段是否相等
     *
     * @param old 原始RRP对象
     * @param r 新RRP对象
     * @return 如果所有字段都相等返回true，否则返回false
     */
    private boolean compareRrp(IntlRmsRrp old, IntlRmsRrp r) {
        if (ObjectUtils.isEmpty(old) || ObjectUtils.isEmpty(r)) {
            return true;
        }
        return Objects.equals(old.getRrpId(), r.getRrpId()) &&
                Objects.equals(old.getRrpName(), r.getRrpName()) &&
                Objects.equals(old.getRrpCode(), r.getRrpCode()) &&
                Objects.equals(old.getCountryId(), r.getCountryId()) &&
                Objects.equals(old.getCountryName(), r.getCountryName()) &&
                Objects.equals(old.getCountryCode(), r.getCountryCode()) &&
                Objects.equals(old.getProductId(), r.getProductId()) &&
                Objects.equals(old.getProductName(), r.getProductName()) &&
                Objects.equals(old.getProductCode(), r.getProductCode()) &&
                Objects.equals(old.getRrp(), r.getRrp()) &&
                Objects.equals(old.getRdp(), r.getRdp()) &&
                Objects.equals(old.getCurrency(), r.getCurrency()) &&
                Objects.equals(old.getSpu(), r.getSpu()) &&
                Objects.equals(old.getSpuEn(), r.getSpuEn()) &&
                Objects.equals(old.getValidTime(), r.getValidTime()) &&
                Objects.equals(old.getInvalidTime(), r.getInvalidTime()) &&
                Objects.equals(old.getStatus(), r.getStatus());
    }

    /**
     * 批量处理省份数据
     *
     * @param requests 请求列表
     */
    private void batchProcessProvinces(List<RmsDbContentRequest> requests) {
        // 批量处理省份：先解析请求，按业务主键 provinceCode 批量预查询已存在数据，然后分组执行插入/更新，并按动作上报指标
        List<IntlRmsProvince> insertList = new ArrayList<>();
        List<IntlRmsProvince> updateList = new ArrayList<>();

        try {
            List<IntlRmsProvince> parsed = requests.stream()
                    .map(request -> JsonUtil.json2bean(JsonUtil.bean2json(request.getContent()), IntlRmsProvince.class))
                    .collect(Collectors.toList());
            if (parsed.isEmpty()) return;

            // 批量预查询
            Map<String, IntlRmsProvince> existing = new HashMap<>();
            List<String> codes = parsed.stream().map(IntlRmsProvince::getProvinceCode).collect(Collectors.toList());
            LambdaQueryWrapper<IntlRmsProvince> w = Wrappers.lambdaQuery();
            w.in(IntlRmsProvince::getProvinceCode, codes);
            List<IntlRmsProvince> list = intlRmsProvinceServiceImpl.list(w);
            if (list != null && !list.isEmpty()) {
                existing.putAll(list.stream().collect(
                        Collectors.toMap(IntlRmsProvince::getProvinceCode, province -> province)));
            }

            long now = System.currentTimeMillis();

            // 将数据分为更新与新增两类分别处理
            for (IntlRmsProvince r : parsed) {
                IntlRmsProvince old = existing.get(r.getProvinceCode());
                boolean isUpdate = old != null;
                if (isUpdate) {
                    //判断传入数据和已存在数据是否一致，不一致则更新
                    if (!compareProvince(old, r)) {
                        r.setId(old.getId());
                        r.setUpdatedAt(now);
                        updateList.add(r);
                    }
                } else {
                    r.setCreatedAt(now);
                    r.setUpdatedAt(now);
                    insertList.add(r);
                }
            }

            // 分别处理插入和更新操作
            if (CollectionUtils.isNotEmpty(insertList)) {
                intlRmsProvinceServiceImpl.saveBatch(insertList);
            }
            if (CollectionUtils.isNotEmpty(updateList)) {
                intlRmsProvinceServiceImpl.updateBatchById(updateList);
            }
        } catch (Exception e) {
            sendMessageService.sendGroupTextMessage(p1, AlarmKey.RMS_SYNC_DB.getEmail(),
                    " 插入/更新失败：" + e.getMessage() + ", traceId=" + TraceIdUtil.traceId());
            log.error("batchProcessProvinces failed, insetListSize:{}, updateListSize:{}, error:{}", insertList.size(),
                    updateList.size(), e.getMessage(), e);
            throw new BizException("batchProcessProvinces RmsSyncDbConsumer error", e);
        }
    }

    /**
     * 比较两个省份对象的字段是否相等
     *
     * @param old 原始省份对象
     * @param r 新省份对象
     * @return 如果所有字段都相等返回true，否则返回false
     */
    private boolean compareProvince(IntlRmsProvince old, IntlRmsProvince r) {
        if (ObjectUtils.isEmpty(old) || ObjectUtils.isEmpty(r)) {
            return true;
        }
        return Objects.equals(old.getCountryId(), r.getCountryId()) &&
                Objects.equals(old.getCountryName(), r.getCountryName()) &&
                Objects.equals(old.getCountryCode(), r.getCountryCode()) &&
                Objects.equals(old.getCountryShortcode(), r.getCountryShortcode()) &&
                Objects.equals(old.getProvinceId(), r.getProvinceId()) &&
                Objects.equals(old.getProvinceName(), r.getProvinceName()) &&
                Objects.equals(old.getProvinceCode(), r.getProvinceCode()) &&
                Objects.equals(old.getStatus(), r.getStatus());
    }

    /**
     * 批量处理城市数据
     *
     * @param requests 请求列表
     */
    private void batchProcessCitys(List<RmsDbContentRequest> requests) {
        // 批量处理城市：先解析请求，按业务主键 cityCode 批量预查询已存在数据，然后分组执行插入/更新，并按动作上报指标
        List<IntlRmsCity> insertList = new ArrayList<>();
        List<IntlRmsCity> updateList = new ArrayList<>();

        try {
            List<IntlRmsCity> parsed = requests.stream()
                    .map(request -> JsonUtil.json2bean(JsonUtil.bean2json(request.getContent()), IntlRmsCity.class))
                    .collect(Collectors.toList());
            if (parsed.isEmpty()) return;

            // 批量预查询
            Map<String, IntlRmsCity> existing = new HashMap<>();
            List<String> codes = parsed.stream().map(IntlRmsCity::getCityCode).collect(Collectors.toList());
            LambdaQueryWrapper<IntlRmsCity> w = Wrappers.lambdaQuery();
            w.in(IntlRmsCity::getCityCode, codes);
            List<IntlRmsCity> list = intlRmsCityServiceImpl.list(w);
            if (list != null && !list.isEmpty()) {
                existing.putAll(list.stream()
                        .collect(Collectors.toMap(IntlRmsCity::getCityCode, city -> city)));
            }

            long now = System.currentTimeMillis();

            // 将数据分为更新与新增两类分别处理
            for (IntlRmsCity r : parsed) {
                IntlRmsCity old = existing.get(r.getCityCode());
                boolean isUpdate = old != null;
                if (isUpdate) {
                    //判断传入数据和已存在数据是否一致，不一致则更新
                    if (!compareCity(old, r)) {
                        r.setId(old.getId());
                        r.setUpdatedAt(now);
                        updateList.add(r);
                    }
                } else {
                    r.setCreatedAt(now);
                    r.setUpdatedAt(now);
                    insertList.add(r);
                }
            }

            // 分别处理插入和更新操作
            if (CollectionUtils.isNotEmpty(insertList)) {
                intlRmsCityServiceImpl.saveBatch(insertList);
            }
            if (CollectionUtils.isNotEmpty(updateList)) {
                intlRmsCityServiceImpl.updateBatchById(updateList);
            }
        } catch (Exception e) {
            sendMessageService.sendGroupTextMessage(p1, AlarmKey.RMS_SYNC_DB.getEmail(),
                    " 插入/更新失败：" + e.getMessage() + ", traceId=" + TraceIdUtil.traceId());
            log.error("batchProcessCities failed, insetListSize:{}, updateListSize:{}, error:{}", insertList.size(),
                    updateList.size(), e.getMessage(), e);
            throw new BizException("batchProcessCities RmsSyncDbConsumer error", e);
        }
    }

    /**
     * 比较两个城市对象的字段是否相等
     *
     * @param old 原始城市对象
     * @param r 新城市对象
     * @return 如果所有字段都相等返回true，否则返回false
     */
    private boolean compareCity(IntlRmsCity old, IntlRmsCity r) {
        if (ObjectUtils.isEmpty(old) || ObjectUtils.isEmpty(r)) {
            return true;
        }
        return Objects.equals(old.getCountryId(), r.getCountryId()) &&
                Objects.equals(old.getCountryName(), r.getCountryName()) &&
                Objects.equals(old.getCountryCode(), r.getCountryCode()) &&
                Objects.equals(old.getCountryShortcode(), r.getCountryShortcode()) &&
                Objects.equals(old.getProvinceId(), r.getProvinceId()) &&
                Objects.equals(old.getProvinceName(), r.getProvinceName()) &&
                Objects.equals(old.getProvinceCode(), r.getProvinceCode()) &&
                Objects.equals(old.getCityId(), r.getCityId()) &&
                Objects.equals(old.getCityName(), r.getCityName()) &&
                Objects.equals(old.getCityCode(), r.getCityCode()) &&
                Objects.equals(old.getStatus(), r.getStatus());
    }

    /**
     * 批量处理二级渠道数据
     *
     * @param requests 请求列表
     */
    private void batchProcessSecondaryChannels(List<RmsDbContentRequest> requests) {
        // 批量处理二级渠道：先解析请求，按业务主键 channelId 批量预查询已存在数据，然后分组执行插入/更新，并按动作上报指标
        List<IntlRmsSecondarychannel> insertList = new ArrayList<>();
        List<IntlRmsSecondarychannel> updateList = new ArrayList<>();

        try {
            List<IntlRmsSecondarychannel> parsed = requests.stream()
                    .map(request -> JsonUtil.json2bean(JsonUtil.bean2json(request.getContent()),
                            IntlRmsSecondarychannel.class))
                    .collect(Collectors.toList());
            if (parsed.isEmpty()) return;

            // 批量预查询
            Map<String, IntlRmsSecondarychannel> existing = new HashMap<>();
            List<String> ids = parsed.stream().map(IntlRmsSecondarychannel::getChannelId).collect(Collectors.toList());
            LambdaQueryWrapper<IntlRmsSecondarychannel> w = Wrappers.lambdaQuery();
            w.in(IntlRmsSecondarychannel::getChannelId, ids);
            List<IntlRmsSecondarychannel> list = intlRmsSecondarychannelServiceImpl.list(w);
            if (list != null && !list.isEmpty()) {
                existing.putAll(list.stream().collect(
                        Collectors.toMap(IntlRmsSecondarychannel::getChannelId, channel -> channel)));
            }

            long now = System.currentTimeMillis();

            // 将数据分为更新与新增两类分别处理
            for (IntlRmsSecondarychannel r : parsed) {
                IntlRmsSecondarychannel old = existing.get(r.getChannelId());
                boolean isUpdate = old != null;
                if (isUpdate) {
                    //判断传入数据和已存在数据是否一致，不一致则更新
                    if (!compareSecondaryChannel(old, r)) {
                        r.setId(old.getId());
                        r.setUpdatedAt(now);
                        updateList.add(r);
                    }
                } else {
                    r.setCreatedAt(now);
                    r.setUpdatedAt(now);
                    insertList.add(r);
                }
            }

            // 分别处理插入和更新操作
            if (CollectionUtils.isNotEmpty(insertList)) {
                intlRmsSecondarychannelServiceImpl.saveBatch(insertList);
            }
            if (CollectionUtils.isNotEmpty(updateList)) {
                intlRmsSecondarychannelServiceImpl.updateBatchById(updateList);
            }
        } catch (Exception e) {
            sendMessageService.sendGroupTextMessage(p1, AlarmKey.RMS_SYNC_DB.getEmail(),
                    " 插入/更新失败：" + e.getMessage() + ", traceId=" + TraceIdUtil.traceId());
            log.error("batchProcessSecondaryChannels failed, insetListSize:{}, updateListSize:{}, error:{}",
                    insertList.size(), updateList.size(), e.getMessage(), e);
            throw new BizException("batchProcessSecondaryChannels RmsSyncDbManager error", e);
        }
    }

    /**
     * 比较两个二级渠道对象的字段是否相等
     *
     * @param old 原始二级渠道对象
     * @param r 新二级渠道对象
     * @return 如果所有字段都相等返回true，否则返回false
     */
    private boolean compareSecondaryChannel(IntlRmsSecondarychannel old, IntlRmsSecondarychannel r) {
        if (ObjectUtils.isEmpty(old) || ObjectUtils.isEmpty(r)) {
            return true;
        }
        return Objects.equals(old.getChannelId(), r.getChannelId()) &&
                Objects.equals(old.getChannelName(), r.getChannelName()) &&
                Objects.equals(old.getChannelBuyChannelId(), r.getChannelBuyChannelId()) &&
                Objects.equals(old.getChannelEnglishName(), r.getChannelEnglishName()) &&
                Objects.equals(old.getChannelSuperiorChannel(), r.getChannelSuperiorChannel()) &&
                Objects.equals(old.getChannelCountryIds(), r.getChannelCountryIds()) &&
                Objects.equals(old.getChannelSapChannelId(), r.getChannelSapChannelId()) &&
                Objects.equals(old.getChannelBusinessArea(), r.getChannelBusinessArea()) &&
                Objects.equals(old.getChannelBusinessAreaValue(), r.getChannelBusinessAreaValue()) &&
                Objects.equals(old.getChannelOnlineAndOffline(), r.getChannelOnlineAndOffline()) &&
                Objects.equals(old.getStatus(), r.getStatus());
    }

    /**
     * 批量处理产品数据
     *
     * @param requests 请求列表
     */
    private void batchProcessProducts(List<RmsDbContentRequest> requests) {
        // 批量处理产品：先解析请求，按业务主键 goodsId 批量预查询已存在数据，然后分组执行更新（仅支持更新）-> 上报指标
        List<IntlRmsProduct> updateList = new ArrayList<>();

        try {

            List<IntlRmsProduct> parsed = requests.stream()
                    .map(request -> JsonUtil.json2bean(JsonUtil.bean2json(request.getContent()), IntlRmsProduct.class))
                    .collect(Collectors.toList());
            if (parsed.isEmpty()) return;

            // 批量预查询
            Map<String, IntlRmsProduct> existing = new HashMap<>();
            List<String> ids = parsed.stream().map(IntlRmsProduct::getGoodsId).collect(Collectors.toList());
            LambdaQueryWrapper<IntlRmsProduct> w = Wrappers.lambdaQuery();
            w.in(IntlRmsProduct::getGoodsId, ids);
            List<IntlRmsProduct> list = intlRmsProductServiceImpl.list(w);
            if (list != null && !list.isEmpty()) {
                existing.putAll(list.stream()
                        .collect(Collectors.toMap(IntlRmsProduct::getGoodsId, product -> product)));
            }

            // 仅当存在记录时执行字段更新
            for (IntlRmsProduct p : parsed) {
                IntlRmsProduct have = existing.get(p.getGoodsId());
                if (have != null) {
                    have.setModelLevel(p.getModelLevel());
                    have.setModelLevelName(p.getModelLevelName());
                    updateList.add(have);
                }
            }

            // 批量更新产品数据
            if (CollectionUtils.isNotEmpty(updateList)) {
                intlRmsProductServiceImpl.updateBatchById(updateList);
            }
        } catch (Exception e) {
            sendMessageService.sendGroupTextMessage(p1, AlarmKey.RMS_SYNC_DB.getEmail(),
                    " 更新失败：" + e.getMessage() + ", traceId=" + TraceIdUtil.traceId());
            log.error("batchProcessProducts failed, updateListSize:{}, error:{}", updateList.size(), e.getMessage(), e);
            throw new BizException("RmsSyncDbManager.batchProcessProducts error", e);
        }
    }

    public IntlRmsCountryTimezone getCountryTimezoneByCountryCode(String countryCode) {
        LambdaQueryWrapper<IntlRmsCountryTimezone> query = new LambdaQueryWrapper<>();
        query.eq(IntlRmsCountryTimezone::getCountryCode, countryCode);
        return intlCountryTimeZoneServiceImpl.getOne(query);
    }

    public IntlRmsCountryTimezone getCountryTimezoneByCountryName(String countryName) {
        LambdaQueryWrapper<IntlRmsCountryTimezone> query = new LambdaQueryWrapper<>();
        query.eq(IntlRmsCountryTimezone::getCountryName, countryName);
        return intlCountryTimeZoneServiceImpl.getOne(query);
    }
}
