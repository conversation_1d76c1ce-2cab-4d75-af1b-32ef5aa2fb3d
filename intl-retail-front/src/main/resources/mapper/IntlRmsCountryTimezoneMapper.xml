<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.info.intl.retail.org.infra.mapper.IntlRmsCountryTimezoneMapper">

    <resultMap id="BaseResultMap" type="com.mi.info.intl.retail.org.infra.entity.IntlRmsCountryTimezone">
            <id property="id" column="id" />
            <result property="countryTimezoneId" column="country_timezone_id"/>
            <result property="name" column="name"/>
            <result property="countryId" column="country_id"/>
            <result property="countryName" column="country_name"/>
            <result property="countryCode" column="country_code"/>
            <result property="timezoneName" column="timezone_name"/>
            <result property="timezoneCode" column="timezone_code"/>
            <result property="bias" column="bias"/>
            <result property="stateCode" column="state_code"/>
            <result property="area" column="area"/>
            <result property="areaCode" column="area_code"/>
            <result property="createdAt" column="created_at"/>
            <result property="updatedAt" column="updated_at"/>
            <result property="code" column="code"/>

    </resultMap>

    <sql id="Base_Column_List">
        id,country_timezone_id,name,country_id,country_name,country_code,
        timezone_name,timezone_code,bias,state_code,area,
        area_code, created_at, updated_at
    </sql>
    <!-- 查询所有记录 -->
    <select id="selectAll" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM intl_rms_country_timezone
        ORDER BY created_at DESC
    </select>

    <select id="selectByCountryCode" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM intl_rms_country_timezone
        WHERE country_code = #{countryCode}
    </select>

    <select id="selectByCountryId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM intl_rms_country_timezone
        WHERE country_id = #{countryId}
    </select>

</mapper>
