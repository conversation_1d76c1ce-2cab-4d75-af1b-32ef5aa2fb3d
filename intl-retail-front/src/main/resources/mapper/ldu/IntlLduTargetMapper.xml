<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.info.intl.retail.ldu.infra.mapper.IntlLduTargetMapper">

    <!-- 基础查询列 -->
    <sql id="Base_Column_List">
        id
        , region, region_code, country, country_code,project_code,project_name,project_name_en,
        product_line,product_line_name,product_line_name_en, target_covered_stores, actual_covered_stores,
        target_sample_out, actual_sample_out, create_user_id,
        create_user_name, target_create_date, update_user_id,
        update_user_name, target_update_date
    </sql>

    <!-- 基础ResultMap -->
    <resultMap id="BaseResultMap" type="com.mi.info.intl.retail.ldu.infra.entity.IntlLduTarget">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="region" property="region" jdbcType="VARCHAR"/>
        <result column="region_code" property="regionCode" jdbcType="VARCHAR"/>
        <result column="country" property="country" jdbcType="VARCHAR"/>
        <result column="country_code" property="countryCode" jdbcType="VARCHAR"/>
        <result column="product_line" property="productLine" jdbcType="VARCHAR"/>
        <result column="product_line_name" property="productLineName" jdbcType="VARCHAR"/>
        <result column="product_line_name_en" property="productLineNameEn" jdbcType="VARCHAR"/>
        <result column="project_code" property="projectCode" jdbcType="VARCHAR"/>
        <result column="project_name" property="projectName" jdbcType="VARCHAR"/>
        <result column="project_name_en" property="projectNameEn" jdbcType="VARCHAR"/>
        <result column="target_covered_stores" property="targetCoveredStores" jdbcType="INTEGER"/>
        <result column="actual_covered_stores" property="actualCoveredStores" jdbcType="INTEGER"/>
        <result column="target_sample_out" property="targetSampleOut" jdbcType="INTEGER"/>
        <result column="actual_sample_out" property="actualSampleOut" jdbcType="INTEGER"/>
        <result column="create_user_id" property="createUserId" jdbcType="VARCHAR"/>
        <result column="create_user_name" property="createUserName" jdbcType="VARCHAR"/>
        <result column="target_create_date" property="targetCreateDate" jdbcType="BIGINT"/>
        <result column="update_user_id" property="updateUserId" jdbcType="VARCHAR"/>
        <result column="update_user_name" property="updateUserName" jdbcType="VARCHAR"/>
        <result column="target_update_date" property="targetUpdateDate" jdbcType="BIGINT"/>
    </resultMap>


    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO intl_ldu_target (
        region, region_code, country, country_code,
        product_line,product_line_name,product_line_name_en,
        project_code,project_name,project_name_en,
        target_covered_stores, actual_covered_stores,
        target_sample_out, actual_sample_out, create_user_id,
        create_user_name, target_create_date, update_user_id,
        update_user_name, target_update_date
        )
        VALUES
        <foreach collection="confList" item="conf" separator=",">
            (
            #{conf.region,jdbcType=VARCHAR},
            #{conf.regionCode,jdbcType=VARCHAR},
            #{conf.country,jdbcType=VARCHAR},
            #{conf.countryCode,jdbcType=VARCHAR},
            #{conf.productLine,jdbcType=VARCHAR},
            #{conf.productLineName,jdbcType=VARCHAR},
            #{conf.productLineNameEn,jdbcType=VARCHAR},
            #{conf.projectCode,jdbcType=VARCHAR},
            #{conf.projectName,jdbcType=VARCHAR},
            #{conf.projectNameEn,jdbcType=VARCHAR},
            #{conf.targetCoveredStores,jdbcType=INTEGER},
            #{conf.actualCoveredStores,jdbcType=INTEGER},
            #{conf.targetSampleOut,jdbcType=INTEGER},
            #{conf.actualSampleOut,jdbcType=INTEGER},
            #{conf.createUserId,jdbcType=VARCHAR},
            #{conf.createUserName,jdbcType=VARCHAR},
            #{conf.targetCreateDate,jdbcType=BIGINT},
            #{conf.updateUserId,jdbcType=VARCHAR},
            #{conf.updateUserName,jdbcType=VARCHAR},
            #{conf.targetUpdateDate,jdbcType=BIGINT}
            )
        </foreach>
    </insert>


    <!--    &lt;!&ndash; 根据ID更新记录 &ndash;&gt;-->
    <update id="updateById">
        UPDATE intl_ldu_target
        <set>
            <if test="conf.region != null">region = #{conf.region,jdbcType=VARCHAR},</if>
            <if test="conf.regionCode != null">region_code = #{conf.regionCode,jdbcType=VARCHAR},</if>
            <if test="conf.country != null">country = #{conf.country,jdbcType=VARCHAR},</if>
            <if test="conf.countryCode != null">country_code = #{conf.countryCode,jdbcType=VARCHAR},</if>
            <if test="conf.productLine != null">product_line = #{conf.productLine,jdbcType=VARCHAR},</if>
            <if test="conf.projectCode != null">project_code = #{conf.projectCode,jdbcType=VARCHAR},</if>
            <if test="conf.targetCoveredStores != null">target_covered_stores =
                #{conf.targetCoveredStores,jdbcType=INTEGER},
            </if>
            <if test="conf.actualCoveredStores != null">actual_covered_stores =
                #{conf.actualCoveredStores,jdbcType=INTEGER},
            </if>
            <if test="conf.targetSampleOut != null">target_sample_out = #{conf.targetSampleOut,jdbcType=INTEGER},</if>
            <if test="conf.actualSampleOut != null">actual_sample_out = #{conf.actualSampleOut,jdbcType=INTEGER},</if>
            <if test="conf.updateUserId != null">update_user_id = #{conf.updateUserId,jdbcType=VARCHAR},</if>
            <if test="conf.updateUserName != null">update_user_name = #{conf.updateUserName,jdbcType=VARCHAR},</if>
            <if test="conf.targetUpdateDate != null">target_update_date = #{conf.targetUpdateDate,jdbcType=BIGINT},</if>
        </set>
        WHERE id = #{conf.id,jdbcType=BIGINT}

    </update>

    <!-- 批量更新多个字段 -->
    <update id="batchUpdate">
        <foreach collection="intlLduTargetList" item="item" separator=";">
            UPDATE intl_ldu_target
            SET
            actual_covered_stores = #{item.actualCoveredStores},
            actual_sample_out = #{item.actualSampleOut},
            update_user_id = #{item.updateUserId},
            update_user_name = #{item.updateUserName},
            target_update_date = #{item.targetUpdateDate}
            WHERE  project_code = #{item.projectCode} and country_code = #{item.countryCode}
        </foreach>
    </update>

    <select id="queryByProjectCode" resultMap="BaseResultMap" parameterType="java.lang.String">
        SELECT
        <include refid="Base_Column_List"/>
        FROM intl_ldu_target
        WHERE project_code = #{projectCode,jdbcType=VARCHAR} and country_code = #{countryCode,jdbcType=VARCHAR}
    </select>
    <!-- 根据country_code、project_code聚合统计target_covered_stores和target_sample_out -->
    <select id="selectStatisticsByCountryProductProject" resultType="com.mi.info.intl.retail.intlretail.service.api.ldu.dto.LduStatisticsDto">
        SELECT
            country_code as countryCode,
            product_line as productLine,
            project_code as projectCode,
            SUM(target_covered_stores) as totalTargetCoveredStores,
            SUM(target_sample_out) as totalTargetSampleOut,
            COUNT(*) as recordCount
        FROM intl_ldu_target
        WHERE 1=1
        <if test="countryCode != null and countryCode != ''">
            AND country_code = #{countryCode,jdbcType=VARCHAR}
        </if>
        <if test="projectCode != null and projectCode != ''">
            AND project_code = #{projectCode,jdbcType=VARCHAR}
        </if>
        GROUP BY country_code, project_code
        ORDER BY country_code, project_code
    </select>

    <!-- 根据country_code、product_line、project_code聚合统计，返回所有组合的统计结果 -->
    <select id="selectAllStatistics" parameterType="com.mi.info.intl.retail.intlretail.service.api.ldu.dto.IntlLduTargetStatisticsReq" resultType="com.mi.info.intl.retail.intlretail.service.api.ldu.dto.LduStatisticsDto">
        SELECT
        country_code as countryCode,
        product_line as productLine,
        project_code as projectCode,
        SUM(target_covered_stores) as totalTargetCoveredStores,
        SUM(target_sample_out) as totalTargetSampleOut,
        COUNT(*) as recordCount
        FROM intl_ldu_target
        <where>
            <if test="list != null and list.size() > 0">
                ( product_line,country_code,project_code)
                IN
                <foreach collection="list" item="i" open="(" close=")" separator=",">
                    (
                    #{i.productLine},#{i.countryCode},#{i.projectCode}
                    )
                </foreach>
            </if>
        </where>
        GROUP BY country_code, product_line, project_code
        ORDER BY country_code, product_line, project_code
    </select>

    <select id="queryByProjectCodeList" resultMap="BaseResultMap" parameterType="java.lang.String">
        SELECT
        <include refid="Base_Column_List"/>
        FROM intl_ldu_target
        WHERE concat(project_code,country_code) in
        <foreach collection="projectCodeList" item="projectCode" open="(" separator="," close=")">
            #{projectCode,jdbcType=VARCHAR}
        </foreach>
    </select>
</mapper>