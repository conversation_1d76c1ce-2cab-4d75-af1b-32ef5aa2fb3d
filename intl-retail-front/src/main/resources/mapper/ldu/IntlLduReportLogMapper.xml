<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.info.intl.retail.ldu.infra.mapper.IntlLduReportLogMapper">

    <resultMap id="BaseResultMap" type="com.mi.info.intl.retail.ldu.infra.entity.IntlLduReportLog">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="region" property="region" jdbcType="VARCHAR"/>
        <result column="region_code" property="regionCode" jdbcType="VARCHAR"/>
        <result column="country" property="country" jdbcType="VARCHAR"/>
        <result column="country_code" property="countryCode" jdbcType="VARCHAR"/>
        <result column="retailer_code" property="retailerCode" jdbcType="VARCHAR"/>
        <result column="retailer_name" property="retailerName" jdbcType="VARCHAR"/>
        <result column="store_code" property="storeCode" jdbcType="VARCHAR"/>
        <result column="store_name" property="storeName" jdbcType="VARCHAR"/>
        <result column="province" property="province" jdbcType="VARCHAR"/>
        <result column="city" property="city" jdbcType="VARCHAR"/>
        <result column="district" property="district" jdbcType="VARCHAR"/>
        <result column="product_line" property="productLine" jdbcType="VARCHAR"/>
        <result column="product_id" property="productId" jdbcType="VARCHAR"/>
        <result column="product_name" property="productName" jdbcType="VARCHAR"/>
        <result column="project_code" property="projectCode" jdbcType="VARCHAR"/>
        <result column="ram_capacity" property="ramCapacity" jdbcType="VARCHAR"/>
        <result column="rom_capacity" property="romCapacity" jdbcType="VARCHAR"/>
        <result column="sn" property="sn" jdbcType="VARCHAR"/>
        <result column="69code" property="code69" jdbcType="VARCHAR"/>
        <result column="imei_1" property="imei1" jdbcType="VARCHAR"/>
        <result column="imei_2" property="imei2" jdbcType="VARCHAR"/>
        <result column="plan_status" property="planStatus" jdbcType="VARCHAR"/>
        <result column="ldu_type" property="lduType" jdbcType="VARCHAR"/>
        <result column="last_mishow_fetch_time" property="lastMishowFetchTime" jdbcType="TIMESTAMP"/>
        <result column="display_status" property="displayStatus" jdbcType="TINYINT"/>
        <result column="report_distance" property="reportDistance" jdbcType="DECIMAL"/>
        <result column="create_user_id" property="createUserId" jdbcType="VARCHAR"/>
        <result column="create_user_name" property="createUserName" jdbcType="VARCHAR"/>
        <result column="is_delete" property="isDelete" jdbcType="TINYINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_user_id" property="updateUserId" jdbcType="VARCHAR"/>
        <result column="update_user_name" property="updateUserName" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>


    <resultMap id="reportWithFilesMap" type="com.mi.info.intl.retail.ldu.infra.entity.IntlLduReportLog">
        <id column="report_id" property="id" jdbcType="BIGINT"/>
        <result column="region" property="region" jdbcType="VARCHAR"/>
        <result column="region_code" property="regionCode" jdbcType="VARCHAR"/>
        <result column="country" property="country" jdbcType="VARCHAR"/>
        <result column="country_code" property="countryCode" jdbcType="VARCHAR"/>
        <result column="biz_region" property="bizRegion" jdbcType="VARCHAR"/>
        <result column="ops_city" property="opsCity" jdbcType="VARCHAR"/>
        <result column="grid" property="grid" jdbcType="VARCHAR"/>
        <result column="channel_type" property="channelType" jdbcType="VARCHAR"/>
        <result column="retailer_code" property="retailerCode" jdbcType="VARCHAR"/>
        <result column="retailer_name" property="retailerName" jdbcType="VARCHAR"/>
        <result column="store_code" property="storeCode" jdbcType="VARCHAR"/>
        <result column="store_name" property="storeName" jdbcType="VARCHAR"/>
        <result column="province" property="province" jdbcType="VARCHAR"/>
        <result column="city" property="city" jdbcType="VARCHAR"/>
        <result column="district" property="district" jdbcType="VARCHAR"/>
        <result column="product_line" property="productLine" jdbcType="VARCHAR"/>
        <result column="product_id" property="productId" jdbcType="VARCHAR"/>
        <result column="product_name" property="productName" jdbcType="VARCHAR"/>
        <result column="project_code" property="projectCode" jdbcType="VARCHAR"/>
        <result column="ram_capacity" property="ramCapacity" jdbcType="VARCHAR"/>
        <result column="rom_capacity" property="romCapacity" jdbcType="VARCHAR"/>
        <result column="sn" property="sn" jdbcType="VARCHAR"/>
        <result column="69code" property="code69" jdbcType="VARCHAR"/>
        <result column="imei_1" property="imei1" jdbcType="VARCHAR"/>
        <result column="imei_2" property="imei2" jdbcType="VARCHAR"/>
        <result column="plan_status" property="planStatus" jdbcType="VARCHAR"/>
        <result column="mishow_status" property="mishowStatus" jdbcType="TINYINT"/>
        <result column="ldu_type" property="lduType" jdbcType="VARCHAR"/>
        <result column="quantity" property="quantity" jdbcType="INTEGER"/>
        <result column="last_mishow_fetch_time" property="lastMishowFetchTime" jdbcType="BIGINT"/>
        <result column="latest_time" property="latestTime" />
        <result column="display_status" property="displayStatus" jdbcType="TINYINT"/>
        <result column="report_distance" property="reportDistance" jdbcType="DECIMAL"/>
        <result column="create_user_id" property="createUserId" jdbcType="VARCHAR"/>
        <result column="create_user_name" property="createUserName" jdbcType="VARCHAR"/>
        <result column="report_role" property="reportRole" jdbcType="VARCHAR"/>
        <result column="is_delete" property="isDelete" jdbcType="TINYINT"/>
        <result column="create_time" property="createTime" jdbcType="BIGINT"/>
        <result column="update_user_id" property="updateUserId" jdbcType="VARCHAR"/>
        <result column="update_user_name" property="updateUserName" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="BIGINT"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="color" property="color" jdbcType="VARCHAR"/>
        <result column="position_name" property="positionName" jdbcType="VARCHAR"/>
        <result column="position_code" property="positionCode" jdbcType="VARCHAR"/>
        <result column="grade_name" property="gradeName" jdbcType="VARCHAR"/>

        <collection property="fileUploadList" ofType="com.mi.info.intl.retail.ldu.infra.entity.IntlFileUpload">
            <id column="id" property="id" jdbcType="BIGINT"/>
            <result column="guid" property="guid" jdbcType="VARCHAR"/>
            <result column="url" property="fdsUrl" jdbcType="VARCHAR"/>
            <result column="suffix" property="suffix" jdbcType="VARCHAR"/>
            <result column="uploader" property="uploaderName" jdbcType="VARCHAR"/>
            <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        </collection>
    </resultMap>


        <resultMap id="inspectionLduReportMap" type="com.mi.info.intl.retail.intlretail.service.api.ldu.dto.InspectionLduReportDto">
            <id property="inspectionRecordId" column="id"/>
            <result property="positionCode" column="position_code"/>
            <result property="positionName" column="position_name"/>
            <result property="periodStartTimeStamp" column="period_start_timestamp"/>
            <result property="periodEndTimeStamp" column="period_end_timestamp"/>
            <result property="taskName" column="task_name"/>
            <result property="projectCode" column="project_code"/>
            <result property="countryCode" column="country_code"/>
            <result property="reportDistance" column="report_distance"/>
            <result property="createdTime" column="created_time"/>
            <result property="inspectionOwnerMiId" column="inspection_owner_miId"/>
            <result property="inspectionOwner" column="inspection_owner"/>
            <result property="allowPhotoFromGallery" column="allow_photo_from_gallery"/>
            <result property="taskStatus" column="task_status"/>
            <result property="inspectionStatus" column="inspection_status"/>


            <collection property="reportLogs" ofType="com.mi.info.intl.retail.intlretail.service.api.ldu.dto.ReportLogDto">
                <id property="lduReportLogId" column="ldu_report_log_id" jdbcType="BIGINT"/>
                <result property="projectCode" column="project_code"/>
                <result property="positionName" column="position_name"/>
                <result property="productName" column="product_name"/>
                <result property="imei1" column="imei1"/>
                <result property="imei2" column="imei2"/>
                <result property="sn" column="sn"/>
                <result property="code69" column="code69"/>
                <result property="romCapacity" column="rom_capacity"/>
                <result property="ramCapacity" column="ram_capacity"/>
                <result property="color" column="color"/>
                <result property="businessStatusCode" column="business_status_code"/>
                <result property="reportDistance" column="report_distance"/>
                <result property="positionCode" column="position_code"/>
            </collection>
        </resultMap>


    <sql id="Base_Column_List">
        id
        , region, region_code, country, country_code, retailer_code, retailer_name,
        store_code, store_name, province, city, district,  product_line,
        product_id, product_name, project_code, ram_capacity, rom_capacity, sn,
        69code, imei_1, imei_2, plan_status, ldu_type,
        last_mishow_fetch_time, display_status, reporte_distance, create_user_id,
        create_user_name, is_delete, create_time, update_user_id, update_user_name,
        update_time
    </sql>

    <insert id="batchInsertReportLogs" parameterType="java.util.List">
        INSERT INTO intl_ldu_report_log (
        report_id,
        region, region_code, country, country_code,
        retailer_code, retailer_name, store_code, store_name,
        province, city, district,
        product_line, product_id, product_name, project_code,
        ram_capacity, rom_capacity, sn, 69code,
        imei_1, imei_2, plan_status, ldu_type,
        last_mishow_fetch_time, display_status,
        report_distance, create_user_id, create_user_name,
        is_delete, create_time, update_user_id, update_user_name,
        update_time,report_role,quantity,channel_type,remark,color,position_code,position_name
        ) VALUES
        <foreach collection="intlLduReportLogs" item="log" separator=",">
            (
            <if test="log.reportId != null">#{log.reportId}</if>,
            <if test="log.region != null">#{log.region,jdbcType=VARCHAR}</if>
            <if test="log.region == null">''</if>,

            <if test="log.regionCode != null">#{log.regionCode,jdbcType=VARCHAR}</if>
            <if test="log.regionCode == null">''</if>,

            <if test="log.country != null">#{log.country,jdbcType=VARCHAR}</if>
            <if test="log.country == null">''</if>,

            <if test="log.countryCode != null">#{log.countryCode,jdbcType=VARCHAR}</if>
            <if test="log.countryCode == null">''</if>,

            <if test="log.retailerCode != null">#{log.retailerCode,jdbcType=VARCHAR}</if>
            <if test="log.retailerCode == null">''</if>,

            <if test="log.retailerName != null">#{log.retailerName,jdbcType=VARCHAR}</if>
            <if test="log.retailerName == null">''</if>,

            <if test="log.storeCode != null">#{log.storeCode,jdbcType=VARCHAR}</if>
            <if test="log.storeCode == null">''</if>,

            <if test="log.storeName != null">#{log.storeName,jdbcType=VARCHAR}</if>
            <if test="log.storeName == null">''</if>,

            <if test="log.province != null">#{log.province,jdbcType=VARCHAR}</if>
            <if test="log.province == null">''</if>,

            <if test="log.city != null">#{log.city,jdbcType=VARCHAR}</if>
            <if test="log.city == null">''</if>,

            <if test="log.district != null">#{log.district,jdbcType=VARCHAR}</if>
            <if test="log.district == null">''</if>,

            <if test="log.productLine != null">#{log.productLine,jdbcType=VARCHAR}</if>
            <if test="log.productLine == null">''</if>,

            <if test="log.productId != null">#{log.productId,jdbcType=VARCHAR}</if>
            <if test="log.productId == null">''</if>,

            <if test="log.productName != null">#{log.productName,jdbcType=VARCHAR}</if>
            <if test="log.productName == null">''</if>,

            <if test="log.projectCode != null">#{log.projectCode,jdbcType=VARCHAR}</if>
            <if test="log.projectCode == null">''</if>,

            <if test="log.ramCapacity != null">#{log.ramCapacity,jdbcType=VARCHAR}</if>
            <if test="log.ramCapacity == null">''</if>,

            <if test="log.romCapacity != null">#{log.romCapacity,jdbcType=VARCHAR}</if>
            <if test="log.romCapacity == null">''</if>,

            <if test="log.sn != null">#{log.sn,jdbcType=VARCHAR}</if>
            <if test="log.sn == null">''</if>,

            <if test="log.code69 != null">#{log.code69,jdbcType=VARCHAR}</if>
            <if test="log.code69 == null">''</if>,

            <if test="log.imei1 != null">#{log.imei1,jdbcType=VARCHAR}</if>
            <if test="log.imei1 == null">''</if>,

            <if test="log.imei2 != null">#{log.imei2,jdbcType=VARCHAR}</if>
            <if test="log.imei2 == null">''</if>,

            <if test="log.planStatus != null">#{log.planStatus,jdbcType=TINYINT}</if>
            <if test="log.planStatus == null">0</if>,

            <if test="log.lduType != null">#{log.lduType,jdbcType=VARCHAR}</if>
            <if test="log.lduType == null">''</if>,

            <if test="log.lastMishowFetchTime != null">#{log.lastMishowFetchTime,jdbcType=TIMESTAMP}</if>
            <if test="log.lastMishowFetchTime == null">0</if>,

            <if test="log.displayStatus != null">#{log.displayStatus,jdbcType=TINYINT}</if>
            <if test="log.displayStatus == null">0</if>,

            <if test="log.reportDistance != null">#{log.reportDistance,jdbcType=DECIMAL}</if>
            <if test="log.reportDistance == null">0</if>,

            <if test="log.createUserId != null">#{log.createUserId,jdbcType=VARCHAR}</if>
            <if test="log.createUserId == null">''</if>,

            <if test="log.createUserName != null">#{log.createUserName,jdbcType=VARCHAR}</if>
            <if test="log.createUserName == null">''</if>,

            <if test="log.isDelete != null">#{log.isDelete,jdbcType=TINYINT}</if>
            <if test="log.isDelete == null">0</if>,

            <if test="log.createTime != null">#{log.createTime,jdbcType=TIMESTAMP}</if>
            <if test="log.createTime == null">0</if>,

            <if test="log.updateUserId != null">#{log.updateUserId,jdbcType=VARCHAR}</if>
            <if test="log.updateUserId == null">''</if>,

            <if test="log.updateUserName != null">#{log.updateUserName,jdbcType=VARCHAR}</if>
            <if test="log.updateUserName == null">''</if>,

            <if test="log.updateTime != null">#{log.updateTime,jdbcType=TIMESTAMP}</if>
            <if test="log.updateTime == null">0</if>,

            <if test="log.reportRole != null">#{log.reportRole,jdbcType=VARCHAR}</if>
            <if test="log.reportRole == null">''</if>,

            <if test="log.quantity != null">#{log.quantity}</if>
            <if test="log.quantity == null">1</if>,

            <if test="log.channelType != null">#{log.channelType}</if>
            <if test="log.channelType == null">''</if>,

            <if test="log.remark != null">#{log.remark}</if>
            <if test="log.remark == null">''</if>,

            <if test="log.color != null">#{log.color}</if>
            <if test="log.color == null">''</if>,

            <if test="log.positionCode != null">#{log.positionCode}</if>
            <if test="log.positionCode == null">''</if>,

            <if test="log.positionName != null">#{log.positionName}</if>
            <if test="log.positionName == null">''</if>
            )
        </foreach>
    </insert>


    <select id="pageList" resultMap="reportWithFilesMap">
        SELECT
        f.id AS id,
        f.guid AS guid,
        f.fds_url AS url,
        f.suffix AS suffix,
        f.uploader_name AS uploader,
        filtered.id AS report_id,
        filtered.region,
        filtered.region_code,
        filtered.country,
        filtered.country_code,
        filtered.biz_region,
        filtered.ops_city,
        filtered.grid,
        filtered.quantity,
        filtered.channel_type,
        filtered.retailer_code,
        filtered.retailer_name,
        filtered.store_code,
        filtered.store_name,
        filtered.province,
        filtered.city,
        filtered.district,
        filtered.product_line,
        filtered.product_id,
        filtered.product_name,
        filtered.project_code,
        filtered.ram_capacity,
        filtered.rom_capacity,
        filtered.sn,
        filtered.69code,
        filtered.imei_1,
        filtered.imei_2,
        filtered.plan_status,
        filtered.ldu_type,
        filtered.quantity,
        filtered.last_mishow_fetch_time,
        filtered.display_status,
        filtered.report_distance,
        filtered.create_user_id,
        filtered.create_user_name,
        filtered.report_role,
        filtered.is_delete,
        filtered.create_time,
        filtered.update_user_id,
        filtered.update_user_name,
        filtered.update_time,
        filtered.color,
        filtered.remark,
        filtered.mishow_status,
        filtered.position_code,
        filtered.position_name,
        filtered.latest_time
        FROM
        (
            SELECT
            l.*,
            m.mishow_status,
            m.latest_time
            FROM
            intl_ldu_report_log l
            LEFT JOIN (
            SELECT
            sn,
            MAX(create_time) AS latest_time,
            mishow_status
            FROM
            intl_ldu_mishow_status_sync
            GROUP BY
            sn
            ) m ON
            l.sn = m.sn
            <where>
                <if test="query.countryCode != null and !query.countryCode.isEmpty()">
                    AND l.country_code IN
                    <foreach item="item" collection="query.countryCode" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="query.retailerCode != null and query.retailerCode != ''">
                    AND l.retailer_code = #{query.retailerCode}
                </if>
                <if test="query.storeCode != null and query.storeCode != ''">
                    AND l.store_code = #{query.storeCode}
                </if>
                <if test="query.productLine != null and query.productLine != ''">
                    AND l.product_line = #{query.productLine}
                </if>
                <if test="query.projectCode != null and query.projectCode != ''">
                    AND l.project_code = #{query.projectCode}
                </if>
                <if test="query.goodsId != null and query.goodsId != ''">
                    AND l.product_id = #{query.goodsId}
                </if>
                <if test="query.goodsName != null and query.goodsName != ''">
                    AND l.product_name LIKE CONCAT('%', #{query.goodsName}, '%')
                </if>
                <if test="query.planStatus != null and (query.planStatus != '' or query.planStatus == 0)">
                    AND l.plan_status = #{query.planStatus}
                </if>
                <if test="query.lduType != null and query.lduType != ''">
                    AND l.ldu_type = #{query.lduType}
                </if>
                <if test="query.mishowStatus != null and (query.mishowStatus != '' or query.mishowStatus == 0)">
                    AND m.mishow_status = #{query.mishowStatus}
                </if>
                <if test="query.displayStatus != null and (query.displayStatus != '' or query.displayStatus == 0)">
                    AND l.display_status = #{query.displayStatus}
                </if>
                <if test="query.reportStartTime != null and query.reportStartTime != ''">
                    AND l.create_time &gt;= #{query.reportStartTime}
                </if>
                <if test="query.reportEndTime != null and query.reportEndTime != ''">
                    AND l.create_time &lt;= #{query.reportEndTime}
                </if>
            </where>
            ORDER BY l.id DESC
            LIMIT #{query.pageSize} OFFSET #{query.offset}
        ) filtered
        LEFT JOIN intl_file_report_rel fr ON filtered.report_id = fr.report_log_id
        LEFT JOIN intl_file_upload f ON fr.guid = f.guid AND f.module_name = 'ldu_upload'
        ORDER BY filtered.id DESC
    </select>

    <select id="pageListCount" resultType="java.lang.Integer">
        SELECT
        count(1)
        FROM
        (
        SELECT
        l.*,
        m.mishow_status
        FROM
        intl_ldu_report_log l
        LEFT JOIN (
        SELECT
        sn,
        MAX(create_time) AS latest_time,
        mishow_status
        FROM
        intl_ldu_mishow_status_sync
        GROUP BY
        sn
        ) m ON
        l.imei_1 = m.sn
        <where>
            <if test="query.countryCode != null and !query.countryCode.isEmpty()">
                AND l.country_code IN
                <foreach item="item" collection="query.countryCode" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.retailerCode != null and query.retailerCode != ''">
                AND l.retailer_code = #{query.retailerCode}
            </if>
            <if test="query.storeCode != null and query.storeCode != ''">
                AND l.store_code = #{query.storeCode}
            </if>
            <if test="query.productLine != null and query.productLine != ''">
                AND l.product_line = #{query.productLine}
            </if>
            <if test="query.projectCode != null and query.projectCode != ''">
                AND l.project_code = #{query.projectCode}
            </if>
            <if test="query.goodsId != null and query.goodsId != ''">
                AND l.product_id = #{query.goodsId}
            </if>
            <if test="query.goodsName != null and query.goodsName != ''">
                AND l.product_name LIKE CONCAT('%', #{query.goodsName}, '%')
            </if>
            <if test="query.planStatus != null and (query.planStatus != '' or query.planStatus == 0)">
                AND l.plan_status = #{query.planStatus}
            </if>
            <if test="query.lduType != null and query.lduType != ''">
                AND l.ldu_type = #{query.lduType}
            </if>
            <if test="query.mishowStatus != null and (query.mishowStatus != '' or query.mishowStatus == 0)">
                AND m.mishow_status = #{query.mishowStatus}
            </if>
            <if test="query.displayStatus != null and (query.displayStatus != '' or query.displayStatus == 0)">
                AND l.display_status = #{query.displayStatus}
            </if>
            <if test="query.reportStartTime != null and query.reportStartTime != ''">
                AND l.create_time &gt;= #{query.reportStartTime}
            </if>
            <if test="query.reportEndTime != null and query.reportEndTime != ''">
                AND l.create_time &lt;= #{query.reportEndTime}
            </if>
        </where>
        ) filtered
    </select>


    <select id="historyList" resultMap="reportWithFilesMap">
        SELECT
        f.id AS id,
        f.guid AS guid,
        f.fds_url AS url,
        f.suffix AS suffix,
        f.uploader_name AS uploader,
        f.create_time AS create_time,
        filtered.id AS report_id,
        filtered.region,
        filtered.region_code,
        filtered.country,
        filtered.country_code,
        filtered.biz_region,
        filtered.ops_city,
        filtered.grid,
        filtered.channel_type,
        filtered.retailer_code,
        filtered.retailer_name,
        filtered.store_code,
        filtered.store_name,
        filtered.quantity,
        filtered.province,
        filtered.city,
        filtered.district,
        filtered.product_line,
        filtered.product_id,
        filtered.product_name,
        filtered.project_code,
        filtered.ram_capacity,
        filtered.rom_capacity,
        filtered.sn,
        filtered.69code,
        filtered.imei_1,
        filtered.imei_2,
        filtered.plan_status,
        filtered.ldu_type,
        filtered.quantity,
        filtered.last_mishow_fetch_time,
        filtered.display_status,
        filtered.report_distance,
        filtered.create_user_id,
        filtered.create_user_name,
        filtered.report_role,
        filtered.is_delete,
        filtered.create_time,
        filtered.update_user_id,
        filtered.update_user_name,
        filtered.update_time,
        filtered.remark,
        filtered.color,
        filtered.position_code,
        filtered.position_name
        FROM
        (
            SELECT * FROM intl_ldu_report_log l
            <where>
                <if test="query.positionCode != null and query.positionCode != ''">
                    AND l.position_code = #{query.positionCode}
                </if>
                <if test="query.productLine != null and query.productLine != ''">
                    AND l.product_line = #{query.productLine}
                </if>
                <if test="query.search != null and query.search != ''">
                    AND (
                    l.product_id = #{query.search}
                    OR l.product_name LIKE CONCAT('%', #{query.search}, '%')
                    OR l.sn = #{query.search}
                    OR l.imei_1 = #{query.search}
                    OR l.imei_2 = #{query.search}
                    )
                </if>
                <if test="query.reportStartTime != null and query.reportStartTime != ''">
                    AND l.create_time &gt;= #{query.reportStartTime}
                </if>
                <if test="query.reportEndTime != null  and query.reportEndTime != ''">
                    AND l.create_time &lt;= #{query.reportEndTime}
                </if>
            </where>
            ORDER BY l.id DESC
            LIMIT #{query.pageSize} OFFSET #{query.offset}
        ) filtered
        LEFT JOIN intl_file_report_rel fr ON filtered.report_id = fr.report_log_id
        LEFT JOIN intl_file_upload f ON fr.guid = f.guid AND f.module_name = 'ldu_upload'
        ORDER BY filtered.id DESC
    </select>

    <select id="historyListCount" resultType="java.lang.Integer">
        SELECT
        count(*)
        FROM
        intl_ldu_report_log l
        <where>
            <if test="query.positionCode != null and query.positionCode != ''">
                AND l.position_code = #{query.positionCode}
            </if>
            <if test="query.productLine != null and query.productLine != ''">
                AND product_line = #{query.productLine}
            </if>
            <if test="query.search != null and query.search != ''">
                AND (
                l.product_id = #{query.search}
                OR l.product_name LIKE CONCAT('%', #{query.search}, '%')
                OR l.sn = #{query.search}
                OR l.imei_1 = #{query.search}
                OR l.imei_2 = #{query.search}
                )
            </if>
            <if test="query.reportStartTime != null and query.reportStartTime != ''">
                AND l.create_time &gt;= #{query.reportStartTime}
            </if>
            <if test="query.reportEndTime != null  and query.reportEndTime != ''">
                AND l.create_time &lt;= #{query.reportEndTime}
            </if>
        </where>
    </select>

    <!-- 查询所有不重复的 project_code -->
    <select id="selectDistinctProjectCodes" resultType="java.lang.String">
        SELECT DISTINCT project_code
        FROM intl_ldu_report_log
        WHERE project_code IS NOT NULL
        AND project_code != ''
        ORDER BY project_code
    </select>

    <select id="statisticReportLogBatch" resultType="com.mi.info.intl.retail.intlretail.service.api.ldu.dto.StoreMetricsDto">
        <foreach collection="list" item="item" separator="UNION ALL">
            SELECT
            #{item.projectCode} AS projectCode,
            #{item.countryCode} AS countryCode,
            COUNT(DISTINCT store_code) AS actualCoveredStores,
            COUNT(*) AS actualDisplayCount
            FROM intl_ldu_report_log
            WHERE project_code = #{item.projectCode}  AND country_code = #{item.countryCode}
        </foreach>
    </select>

    <select id="selectBySns" resultType="com.mi.info.intl.retail.ldu.infra.entity.IntlLduReportLog">
        SELECT DISTINCT sn, imei_1, imei_2, country_code, project_code
        FROM (
        <if test="snImeiQueryDto.snList != null and snImeiQueryDto.snList.size() > 0">
            SELECT sn, imei_1, imei_2, country_code, project_code
            FROM intl_ldu_report_log
            where sn IN
            <foreach collection="snImeiQueryDto.snList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="snImeiQueryDto.snList != null and snImeiQueryDto.snList.size() > 0
              and snImeiQueryDto.imeiList != null and snImeiQueryDto.imeiList.size() > 0">
            UNION ALL
        </if>

        <if test="snImeiQueryDto.imeiList != null and snImeiQueryDto.imeiList.size() > 0">
            SELECT sn, imei_1, imei_2, country_code, project_code
            FROM intl_ldu_report_log
            where imei_1 IN
            <foreach collection="snImeiQueryDto.imeiList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            UNION ALL
            SELECT sn, imei_1, imei_2, country_code, project_code
            FROM intl_ldu_report_log
            where imei_2 IN
            <foreach collection="snImeiQueryDto.imeiList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ) AS combined_results
    </select>

    <select id="inspectionDetail" resultMap="inspectionLduReportMap">
        SELECT ir.id ,
        ir.business_code AS position_code,
        ir.period_start_time_stamp AS period_start_timestamp,
        ir.period_end_time_stamp AS period_end_timestamp,
        ir.report_distance AS report_distance,
        ir.inspection_owner_miId AS inspection_owner_miId,
        ir.inspection_owner AS inspection_owner,
        ir.task_status AS task_status,
        ir.inspection_status AS inspection_status,
        rule.task_name AS task_name,
        rule.allow_photo_from_gallery AS allow_photo_from_gallery,
        ldu.project_code AS project_code,
        rule.country AS country_code,
        ldu.position_name AS position_name,
        ldu.product_name AS product_name,
        ldu.imei_1 AS imei1,
        ldu.imei_2 AS imei2,
        ldu.sn AS sn,
        ldu.69code AS code69,
        ldu.rom_capacity AS rom_capacity,
        ldu.ram_capacity AS ram_capacity,
        ldu.color AS color,
        ldu.id AS ldu_report_log_id
        FROM intl_inspection_record ir
        JOIN
        intl_inspection_rule rule ON ir.rule_config_id = rule.id
        JOIN intl_inspection_rule_relation rel ON rule.id = rel.rule_id
        JOIN
        intl_ldu_report_log ldu ON (
        ir.business_code = ldu.position_code
        AND rule.country = ldu.country_code
        AND rel.project = ldu.project_code
        )
        <where>
            <if test="inspectionReq != null">
                <if test="inspectionReq.taskId != null and inspectionReq.taskId != ''">
                    AND ir.id = #{inspectionReq.taskId}
                </if>
                <if test="inspectionReq.taskInstanceId != null and inspectionReq.taskInstanceId != ''">
                    AND ir.task_instance_id = #{inspectionReq.taskInstanceId}
                </if>
            </if>
        </where>
    </select>

    <select id="selectLduReportSimpleByIds" resultType="com.mi.info.intl.retail.ldu.dto.LduReportSimple">
        select id, sn, report_role, 69code AS code69
        from intl_ldu_report_log
        where id in
        <foreach collection="ids" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>


</mapper>