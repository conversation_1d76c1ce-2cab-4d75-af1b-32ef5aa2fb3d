<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.info.intl.retail.ldu.infra.mapper.IntlLduSnMapper">

    <!-- 基础查询列 -->
    <sql id="Base_Column_List">
        id
        , region, region_code, country, country_code,
        channel_type, retailer_code, retailer_name, ldu_type,
        product_line, goods_id, goods_name,product_id,product_name, project_code,
        ram_capacity, imei_1, rom_capacity, sn, imei_2,
        create_user_id, create_user_name, plan_create_date,
        stop_user_id, stop_user_name, plan_stop_date,
        is_report, ldu_sn_status
    </sql>


    <!-- 基础ResultMap -->
    <resultMap id="BaseResultMap" type="com.mi.info.intl.retail.ldu.infra.entity.IntlLduSn">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="region" property="region" jdbcType="VARCHAR"/>
        <result column="region_code" property="regionCode" jdbcType="VARCHAR"/>
        <result column="country" property="country" jdbcType="VARCHAR"/>
        <result column="country_code" property="countryCode" jdbcType="VARCHAR"/>
        <result column="channel_type" property="channelType" jdbcType="VARCHAR"/>
        <result column="retailer_code" property="retailerCode" jdbcType="VARCHAR"/>
        <result column="retailer_name" property="retailerName" jdbcType="VARCHAR"/>
        <result column="ldu_type" property="lduType" jdbcType="VARCHAR"/>
        <result column="product_line" property="productLine" jdbcType="VARCHAR"/>
        <result column="goods_id" property="goodsId" jdbcType="VARCHAR"/>
        <result column="goods_name" property="goodsName" jdbcType="VARCHAR"/>
        <result column="product_id" property="productId" jdbcType="VARCHAR"/>
        <result column="product_name" property="productName" jdbcType="VARCHAR"/>
        <result column="project_code" property="projectCode" jdbcType="VARCHAR"/>
        <result column="ram_capacity" property="ramCapacity" jdbcType="VARCHAR"/>
        <result column="imei_1" property="imei1" jdbcType="VARCHAR"/>
        <result column="rom_capacity" property="romCapacity" jdbcType="VARCHAR"/>
        <result column="sn" property="sn" jdbcType="VARCHAR"/>
        <result column="imei_2" property="imei2" jdbcType="VARCHAR"/>
        <result column="create_user_id" property="createUserId" jdbcType="VARCHAR"/>
        <result column="create_user_name" property="createUserName" jdbcType="VARCHAR"/>
        <result column="plan_create_date" property="planCreateDate" jdbcType="BIGINT"/>
        <result column="stop_user_id" property="stopUserId" jdbcType="VARCHAR"/>
        <result column="stop_user_name" property="stopUserName" jdbcType="VARCHAR"/>
        <result column="plan_stop_date" property="planStopDate" jdbcType="BIGINT"/>
        <result column="is_report" property="isReport" jdbcType="TINYINT"/>
        <result column="ldu_sn_status" property="status" jdbcType="TINYINT"/>
    </resultMap>


    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO intl_ldu_sn (
        region, region_code, country, country_code,
        channel_type, retailer_code, retailer_name, ldu_type,
        product_line, goods_id, goods_name, product_id, product_name, project_code,
        ram_capacity, imei_1, rom_capacity, sn, imei_2,
        create_user_id, create_user_name, plan_create_date,
        stop_user_id, stop_user_name, plan_stop_date,
        is_report, ldu_sn_status
        )
        VALUES
        <foreach collection="confList" item="conf" separator=",">
            (
            <if test="conf.region != null">#{conf.region,jdbcType=VARCHAR}</if>
            <if test="conf.region == null">''</if>,

            <if test="conf.regionCode != null">#{conf.regionCode,jdbcType=VARCHAR}</if>
            <if test="conf.regionCode == null">''</if>,

            <if test="conf.country != null">#{conf.country,jdbcType=VARCHAR}</if>
            <if test="conf.country == null">''</if>,

            <if test="conf.countryCode != null">#{conf.countryCode,jdbcType=VARCHAR}</if>
            <if test="conf.countryCode == null">''</if>,

            <if test="conf.channelType != null">#{conf.channelType,jdbcType=VARCHAR}</if>
            <if test="conf.channelType == null">''</if>,

            <if test="conf.retailerCode != null">#{conf.retailerCode,jdbcType=VARCHAR}</if>
            <if test="conf.retailerCode == null">''</if>,

            <if test="conf.retailerName != null">#{conf.retailerName,jdbcType=VARCHAR}</if>
            <if test="conf.retailerName == null">''</if>,

            <if test="conf.lduType != null">#{conf.lduType,jdbcType=VARCHAR}</if>
            <if test="conf.lduType == null">''</if>,

            <if test="conf.productLine != null">#{conf.productLine,jdbcType=VARCHAR}</if>
            <if test="conf.productLine == null">''</if>,

            <if test="conf.goodsId != null">#{conf.goodsId,jdbcType=VARCHAR}</if>
            <if test="conf.goodsId == null">''</if>,

            <if test="conf.goodsName != null">#{conf.goodsName,jdbcType=VARCHAR}</if>
            <if test="conf.goodsName == null">''</if>,

            <if test="conf.productId != null">#{conf.productId,jdbcType=VARCHAR}</if>
            <if test="conf.productId == null">''</if>,

            <if test="conf.productName != null">#{conf.productName,jdbcType=VARCHAR}</if>
            <if test="conf.productName == null">''</if>,

            <if test="conf.projectCode != null">#{conf.projectCode,jdbcType=VARCHAR}</if>
            <if test="conf.projectCode == null">''</if>,

            <if test="conf.ramCapacity != null">#{conf.ramCapacity,jdbcType=VARCHAR}</if>
            <if test="conf.ramCapacity == null">0</if>,

            <if test="conf.imei1 != null">#{conf.imei1,jdbcType=VARCHAR}</if>
            <if test="conf.imei1 == null">''</if>,

            <if test="conf.romCapacity != null">#{conf.romCapacity,jdbcType=VARCHAR}</if>
            <if test="conf.romCapacity == null">0</if>,

            <if test="conf.sn != null">#{conf.sn,jdbcType=VARCHAR}</if>
            <if test="conf.sn == null">''</if>,

            <if test="conf.imei2 != null">#{conf.imei2,jdbcType=VARCHAR}</if>
            <if test="conf.imei2 == null">''</if>,

            <if test="conf.createUserId != null">#{conf.createUserId,jdbcType=VARCHAR}</if>
            <if test="conf.createUserId == null">''</if>,

            <if test="conf.createUserName != null">#{conf.createUserName,jdbcType=VARCHAR}</if>
            <if test="conf.createUserName == null">''</if>,

            <if test="conf.planCreateDate != null">#{conf.planCreateDate,jdbcType=BIGINT}</if>
            <if test="conf.planCreateDate == null">0</if>,

            <if test="conf.stopUserId != null">#{conf.stopUserId,jdbcType=VARCHAR}</if>
            <if test="conf.stopUserId == null">''</if>,

            <if test="conf.stopUserName != null">#{conf.stopUserName,jdbcType=VARCHAR}</if>
            <if test="conf.stopUserName == null">''</if>,

            <if test="conf.planStopDate != null">#{conf.planStopDate,jdbcType=BIGINT}</if>
            <if test="conf.planStopDate == null">0</if>,

            <if test="conf.isReport != null">#{conf.isReport,jdbcType=TINYINT}</if>
            <if test="conf.isReport == null">0</if>,

            <if test="conf.status != null">#{conf.status,jdbcType=TINYINT}</if>
            <if test="conf.status == null">0</if>
            )
        </foreach>
    </insert>

    <!--    &lt;!&ndash; 根据ID查询 &ndash;&gt;-->
    <select id="selectBySn" resultMap="BaseResultMap" parameterType="java.lang.String">
        SELECT
        <include refid="Base_Column_List"/>
        FROM intl_ldu_sn
        WHERE sn = #{sn,jdbcType=VARCHAR} or imei_1 = #{sn,jdbcType=VARCHAR} or imei_2 = #{sn,jdbcType=VARCHAR}
    </select>

    <select id="batchSelectBySnOrImei" resultMap="BaseResultMap" parameterType="java.lang.String">
        SELECT
        <include refid="Base_Column_List"/>
        FROM intl_ldu_sn
        WHERE sn IN
        <foreach collection="snList" item="sn" open="(" separator="," close=")">
            #{sn}
        </foreach>
        or imei_1 IN
        <foreach collection="snList" item="sn" open="(" separator="," close=")">
            #{sn}
        </foreach>
        or imei_2 IN
        <foreach collection="snList" item="sn" open="(" separator="," close=")">
            #{sn}
        </foreach>
    </select>



    <!--    &lt;!&ndash; 标记记录为无效 &ndash;&gt;-->
    <update id="updateStatusBySn">
        UPDATE intl_ldu_sn
        SET ldu_sn_status         = 0,
            plan_stop_date = #{planStopDate}
        WHERE sn = #{sn}
    </update>

    <update id="batchUpdateBySn">
        UPDATE intl_ldu_sn
        SET ldu_sn_status = 0,
        plan_stop_date = #{planStopDate}
        WHERE sn IN
        <foreach collection="snList" item="sn" open="(" separator="," close=")">
            #{sn}
        </foreach>

    </update>



    <select id="selectBySnAndCountryCode" resultType="com.mi.info.intl.retail.ldu.infra.entity.IntlLduSn">
        SELECT *
        FROM intl_ldu_sn
        WHERE ldu_sn_status = 1
        AND country_code = #{countryCode}
        AND sn IN
        <foreach item="good" collection="goodsList" open="(" close=")" separator=",">
            #{good.sn}
        </foreach>
    </select>

    <update id="batchUpdateBySnCountryCode">
        UPDATE intl_ldu_sn
        SET is_report = #{isReport}
        WHERE country_code = #{countryCode}
        AND sn IN
        <foreach collection="intlLduSns" item="intlLduSn" open="(" separator="," close=")">
            #{intlLduSn.sn}
        </foreach>
    </update>


</mapper>
