<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.info.intl.retail.org.infra.mapper.read.InspectionRecordReadMapper">

    <resultMap id="BaseResultMap" type="com.mi.info.intl.retail.org.infra.entity.InspectionRecord">
        <id property="id" column="id"/>
        <result property="ruleCode" column="rule_code"/>
        <result property="businessCode" column="business_code"/>
        <result property="businessType" column="business_type"/>
        <result property="businessCreationTime" column="business_creation_time"/>
        <result property="constructionActionCode" column="construction_action_code"/>
        <result property="taskStatus" column="task_status"/>
        <result property="taskCompletionTime" column="task_completion_time"/>
        <result property="inspectionStatus" column="inspection_status"/>
        <result property="verificationTime" column="verification_time"/>
        <result property="uploadData" column="upload_data"/>
        <result property="inspectionOwner" column="inspection_owner"/>
        <result property="inspectionOwnerMiId" column="inspection_owner_miId"/>
        <result property="verifyStatus" column="verify_status"/>
        <result property="remark" column="remark"/>
        <result property="createdBy" column="created_by"/>
        <result property="createdTime" column="created_on"/>
        <result property="updatedBy" column="modified_by"/>
        <result property="updatedTime" column="modified_on"/>
        <result property="taskCreateInstanceTime" column="task_create_instance_time"/>
        <result property="reminderTime" column="reminder_time"/>
        <result property="verifier" column="verifier"/>
        <result property="verifierMiid" column="verifier_miid"/>
    </resultMap>

    <resultMap id="InspectionWithRuleResultMap" type="com.mi.info.intl.retail.org.domain.dto.InspectionRecordDTO">
        <id property="id" column="id"/>
        <result property="ruleCode" column="rule_code"/>
        <result property="businessCode" column="business_code"/>
        <result property="businessType" column="business_type"/>
        <result property="country" column="rc_country"/>
        <result property="businessCreationTime" column="business_creation_time"/>
        <result property="constructionActionCode" column="construction_action_code"/>
        <result property="taskStatus" column="task_status"/>
        <result property="taskCompletionTime" column="task_completion_time"/>
        <result property="inspectionStatus" column="inspection_status"/>
        <result property="verificationTime" column="verification_time"/>
        <result property="inspectionOwner" column="inspection_owner"/>
        <result property="inspectionOwnerMiId" column="inspection_owner_miId"/>
        <result property="verifyStatus" column="verify_status"/>
        <result property="taskCreateInstanceTime" column="task_create_instance_time"/>
        <result property="reminderTime" column="reminder_time"/>
        <result property="taskDefId" column="rc_task_def_id"/>
        <result property="taskBatchId" column="rc_task_batch_id"/>
        <result property="ruleCode" column="rc_rule_code"/>
        <result property="ruleName" column="rc_rule_name"/>
        <result property="ruleStatus" column="rc_rule_status"/>
        <result property="startTime" column="rc_start_time"/>
        <result property="endTime" column="rc_end_time"/>
        <result property="allowPhotoFromGallery" column="rc_allow_photo_from_gallery"/>
        <result property="creator" column="rc_creator"/>
        <result property="creationTime" column="rc_creation_time"/>
        <result property="modifier" column="rc_modifier"/>
        <result property="modificationTime" column="rc_modification_time"/>
        <result property="reminderDays" column="rc_reminder_days"/>
        <result property="ruleId" column="rc_id"/>


    </resultMap>

    <resultMap id="PositionInspectionItemResultMap" type="com.mi.info.intl.retail.intlretail.service.api.position.dto.PositionInspectionItem">
        <result property="id" column="id"/>
        <result property="positionName" column="position_name"/>
        <result property="storeName" column="store_name"/>
        <result property="inspectionStatus" column="inspectionStatus"/>
        <result property="region" column="region"/>
        <result property="country" column="country"/>
        <result property="positionCode" column="positionCode"/>
        <result property="positionType" column="positionType"/>
        <result property="positionInspectionBusinessTypeEnum" column="positionConstructionType"/>
        <result property="positionCategory" column="position_category"/>
        <result property="storeLimitedRange" column="store_limited_range"/>
        <result property="positonLocation" column="position_location"/>
        <result property="displayStandardization" column="display_standardization"/>
        <result property="creationTime" column="creationTime"/>
        <result property="owner" column="owner"/>
        <result property="taskStatus" column="taskStatus"/>
        <result property="verificationStatus" column="verificationStatus"/>
        <result property="verifier" column="verifier"/>
        <result property="taskCompletionTime" column="taskCompletionTime"/>
        <result property="verificationTime" column="verificationTime"/>
        <result property="canVerfiy" column="canVerfiy"/>
        <result property="allowPhotoFromGallery" column="allow_photo_from_gallery"/>
        <result property="positionLongitude" column="position_longitude"/>
        <result property="positionLatitude" column="position_latitude"/>
    </resultMap>

    <resultMap id="CoverageStatisticsMap" type="com.mi.info.intl.retail.intlretail.service.api.ldu.dto.CoverageStatisticsDto">
        <!-- 任务基本信息 -->
        <id property="targetId" column="target_id"/>  <!-- 与SQL别名匹配 -->
        <id property="taskType" column="task_type"/>
        <result property="country" column="country"/>
        <result property="region" column="region"/>
        <result property="productLine" column="product_line"/>
        <result property="projectCode" column="project"/>

        <!-- 覆盖统计信息 -->
        <result property="posmStoreCoverage" column="posm_store_coverage"/>
        <result property="dummyStoreCoverage" column="dummy_store_coverage"/>
        <result property="priceTagCoverageTarget" column="price_tag_coverage_target"/>
        <result property="lduStoreCoverage" column="ldu_store_coverage"/>

        <!-- 关联的RMS阵地列表（column与SQL别名对应） -->
        <collection property="intlRmsPositionDtos" ofType="com.mi.info.intl.retail.intlretail.service.api.ldu.dto.IntlRmsPositionDto">
            <id property="irpId" column="irp_id"/>
            <id property="recordId" column="recordId"/>
            <result property="positionId" column="rms_position_id"/>
            <result property="code" column="rms_code"/>
            <result property="name" column="rms_name"/>
            <result property="storeId" column="rms_store_id"/>
            <result property="storeName" column="rms_store_name"/>
            <result property="abbreviation" column="rms_abbreviation"/>
            <result property="state" column="rms_state"/>
            <result property="stateName" column="rms_state_name"/>
            <result property="distributorId" column="rms_distributor_id"/>
            <result property="distributorName" column="rms_distributor_name"/>
            <result property="accountId" column="rms_account_id"/>
            <result property="accountName" column="rms_account_name"/>
            <result property="retailerId" column="rms_retailer_id"/>
            <result property="retailerName" column="rms_retailer_name"/>
            <result property="channelType" column="rms_channel_type"/>
            <result property="channelTypeName" column="rms_channel_type_name"/>
            <result property="type" column="rms_type"/>
            <result property="typeName" column="rms_type_name"/>
            <result property="level" column="rms_level"/>
            <result property="levelName" column="rms_level_name"/>
            <result property="isPromotionStore" column="rms_is_promotion_store"/>
            <result property="countryId" column="rms_country_id"/>
            <result property="countryName" column="rms_country_name"/>
            <result property="cityId" column="rms_city_id"/>
            <result property="cityName" column="rms_city_name"/>
            <result property="address" column="rms_address"/>
            <result property="modifiedOn" column="rms_modified_on"/>
            <result property="createdOn" column="rms_created_on"/>
            <result property="ownerId" column="rms_owner_id"/>
            <result property="ownerName" column="rms_owner_name"/>
            <result property="stateCode" column="rms_state_code"/>
            <result property="area" column="rms_area"/>
            <result property="areaCode" column="rms_area_code"/>
            <result property="crpsCode" column="rms_crps_code"/>
            <result property="positionCategory" column="rms_position_category"/>
            <result property="furnitureTtl" column="rms_furniture_ttl"/>
            <result property="displayCapacityExpansionStatus" column="rms_display_capacity_expansion_status"/>
            <result property="positionLocation" column="rms_position_location"/>
            <result property="positionLongitude" column="rms_position_longitude"/>
            <result property="positionLatitude" column="rms_position_latitude"/>
            <result property="createdAt" column="rms_created_at"/>
            <result property="updatedAt" column="rms_updated_at"/>
            <result property="verifyStatus" column="verifyStatus"/>
            <result property="flagCompletion" column="flagCompletion"/>
        </collection>
    </resultMap>



    <sql id="Base_Column_List">
        id,rule_code,businesss_code,businesss_type,business_creation_time,
        construction_action_code,task_status,task_completion_time,inspection_status,
        verification_time,upload_data,inspection_owner,inspection_owner_miId,
        verify_status,remark,created_by,created_time,updated_by,updated_time,task_create_instance_time,  reminder_time
    </sql>

    <!-- 分页查询阵地巡检信息 -->
    <select id="pagePositionInspection" resultMap="PositionInspectionItemResultMap">
        SELECT
            ir.id AS id,
            p.name AS position_name,
            p.store_name AS store_name,
            ct.area AS region,
            ct.country_name AS country,
            ir.business_code AS positionCode,
            p.type AS positionType,
            p.type_name AS positionTypeDesc,
            ir.business_type AS positionConstructionType,
            p.position_category AS position_category,
            ir.store_limited_range AS store_limited_range,
            p.position_location AS position_location,
            p.display_capacity_expansion_status AS display_standardization,
            ir.business_creation_time AS creationTime,
            ir.inspection_owner AS owner,
            ir.task_status AS taskStatus,
            ir.inspection_status AS inspectionStatus,
            ir.inspection_status AS verificationStatus,
            ir.verifier AS verifier,
            ir.task_completion_time AS taskCompletionTime,
            ir.verification_time AS verificationTime,
            rc.allow_photo_from_gallery AS allow_photo_from_gallery,
            p.position_longitude AS position_longitude,
            p.position_latitude AS position_latitude
        FROM
            intl_inspection_record ir
        INNER JOIN
            intl_rms_position p ON p.crpscode = ir.business_code
        LEFT JOIN
            intl_rms_country_timezone ct ON p.country_id = ct.country_id
        LEFT JOIN
            intl_inspection_rule rc ON ir.rule_code = rc.rule_code
        <where>
            <if test="request.query != null and request.query != ''">
                AND (p.name LIKE CONCAT(#{request.query}, '%') OR ir.business_code LIKE CONCAT(#{request.query}, '%'))
            </if>
            <if test="request.positionName != null and request.positionName != ''">
                AND p.name LIKE CONCAT(#{request.positionName}, '%')
            </if>
            <if test="request.positionCode != null and request.positionCode != ''">
                AND ir.business_code = #{request.positionCode}
            </if>
            <if test="request.region != null and request.region.size() > 0">
                AND ct.area_code IN
                <foreach collection="request.region" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="request.country != null and request.country.size() > 0">
                AND ct.country_code IN
                <foreach collection="request.country" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="request.positionType != null and request.positionType.size() > 0">
                AND p.type IN
                <foreach collection="request.positionType" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="request.positionCategory != null and request.positionCategory.size() > 0">
                AND (
                <foreach collection="request.positionCategory" item="item" separator=" OR ">
                    JSON_CONTAINS(p.position_category, #{item})
                </foreach>
                )
            </if>
            <if test="request.positionConstructionType != null and request.positionConstructionType.size() > 0">
                AND ir.business_type IN
                <foreach collection="request.positionConstructionType" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="request.taskStatus != null and request.taskStatus.size() > 0">
                AND ir.task_status IN
                <foreach collection="request.taskStatus" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="request.inspenctionStatus != null and request.inspenctionStatus.size() > 0">
                AND ir.inspection_status IN
                <foreach collection="request.inspenctionStatus" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="request.owner != null and request.owner != ''">
                AND ir.inspection_owner LIKE CONCAT(#{request.owner}, '%')
            </if>
            <if test="request.positionLocation != null and request.positionLocation.size() > 0">
                AND p.position_location IN
                <foreach collection="request.positionLocation" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="request.displayStandardization != null and request.displayStandardization.size() > 0">
                AND p.display_capacity_expansion_status IN
                <foreach collection="request.displayStandardization" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="request.storeLimitedRange != null">
                AND ir.store_limited_range = #{request.storeLimitedRange}
            </if>
            <if test="request.createTimeStart != null">
                AND ir.business_creation_time &gt;= #{request.createTimeStart}
            </if>
            <if test="request.createTimeEnd != null">
                AND ir.business_creation_time &lt;= #{request.createTimeEnd}
            </if>
            <if test="request.province != null and request.province.size() > 0">
                AND p.province_code IN
                <foreach collection="request.province" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="request.city != null and request.city.size() > 0">
                AND p.city_code IN
                <foreach collection="request.city" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="request.organization != null and request.organization.size() > 0">
                AND p.organization_code IN
                <foreach collection="request.organization" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY
            p.id DESC
    </select>

    <!-- 查询巡检记录汇总数据 -->
    <select id="getInspectionSummary" resultType="com.mi.info.intl.retail.intlretail.service.api.position.dto.InspectionSummaryDTO">
        SELECT
             COUNT(1) AS totalInspectionCount,

    <!-- 完成率 = 任务完成阵地 / (阵地总数 - 未下发阵地数) -->
    IFNULL(
        ROUND(
            SUM(CASE WHEN task_status = 1 THEN 1 ELSE 0 END)  /
            (COUNT(1) - SUM(CASE WHEN inspection_status = 0 THEN 1 ELSE 0 END)),
        4),
    0) AS completionRate,

    <!-- 核检率 = (任务未完成核验未通过 + 任务完成核验通过 + 无需完成核验通过) / (任务完成阵地 + 无需完成阵地 + 任务未完成核验未通过) -->
    IFNULL(
        ROUND(
            (SUM(CASE WHEN  inspection_status = 1 THEN 1 ELSE 0 END) +
             SUM(CASE WHEN  inspection_status = 2 THEN 1 ELSE 0 END))  /
            (SUM(CASE WHEN inspection_status = 1 THEN 1 ELSE 0 END) +
             SUM(CASE WHEN inspection_status = 2 THEN 1 ELSE 0 END) +
             SUM(CASE WHEN inspection_status = 3 THEN 1 ELSE 0 END)),
        4),
    0) AS verificationRate,

    <!-- 检核通过率 = (任务完成核验通过 + 无需完成核验通过) / (任务未完成核验未通过 + 任务完成核验通过 + 无需完成核验通过) -->
    IFNULL(
        ROUND(
            SUM(CASE WHEN inspection_status = 1 THEN 1 ELSE 0 END) /
            (SUM(CASE WHEN inspection_status = 2 THEN 1 ELSE 0 END) +
             SUM(CASE WHEN inspection_status = 1 THEN 1 ELSE 0 END)),
        4),
    0) AS passRate,

    <!-- 未下发数量 = 巡检状态为未下发 -->
    SUM(CASE WHEN inspection_status = 0 THEN 1 ELSE 0 END) AS notIssuedCount,

    <!-- 未完成巡检的阵地数量 = 任务状态为未完成  -->
    SUM(CASE WHEN task_status = 3 THEN 1 ELSE 0 END) AS undoCount,

    <!-- 未完成原因：下发未完成的数量 = 任务状态为未完成，巡检状态为未完成 -->
    SUM(CASE WHEN task_status = 3 AND inspection_status = 4 THEN 1 ELSE 0 END) AS undoIncompleteCount,

    <!-- 未完成原因：核验未通过数量 = 巡检状态=核验未通过 -->
    SUM(CASE WHEN task_status = 3 AND inspection_status = 2 THEN 1 ELSE 0 END) AS undoRefuseCount,

    <!-- 已完成巡检的阵地数量 = 任务状态为已完成 -->
    SUM(CASE WHEN task_status = 1 THEN 1 ELSE 0 END) AS completedCount,

    <!-- 已完成但待核验的数量 = 任务状态为已完成，核验状态为待核验 -->
    SUM(CASE WHEN task_status = 1 AND inspection_status = 3 THEN 1 ELSE 0 END) AS completedVerifyingCount,

    <!-- 已完成且核验通过的数量 = 任务状态为已完成，核验状态为核验通过 -->
    SUM(CASE WHEN task_status = 1 AND inspection_status = 1 THEN 1 ELSE 0 END) AS completedPassedCount,

    <!-- 无需完成巡检的阵地数量 = 任务状态为无需完成 -->
    SUM(CASE WHEN task_status = 2 THEN 1 ELSE 0 END) AS noNeedToDoCount,

    <!-- 无需完成原因：待核验的数量 = 任务状态为无需完成，核验状态为待核验 -->
    SUM(CASE WHEN task_status = 2 AND inspection_status = 3 THEN 1 ELSE 0 END) AS noNeedToDoVerifyingCount,

    <!-- 无需完成原因：核验通过的数量 = 任务状态为无需完成，核验状态为核验通过 -->
    SUM(CASE WHEN task_status = 2 AND inspection_status = 1 THEN 1 ELSE 0 END) AS noNeedToDoPassedCount

    FROM intl_inspection_record ir
    INNER JOIN
        intl_rms_position p ON p.crpscode = ir.business_code
    LEFT JOIN
        intl_rms_country_timezone ct ON p.country_id = ct.country_id
    <where>
        <if test="request.query != null and request.query != ''">
            AND (p.name LIKE CONCAT(#{request.query}, '%') OR ir.business_code LIKE CONCAT(#{request.query}, '%'))
        </if>
        <if test="request.positionName != null and request.positionName != ''">
            AND p.name LIKE CONCAT(#{request.positionName}, '%')
        </if>
        <if test="request.positionCode != null and request.positionCode != ''">
            AND ir.business_code = #{request.positionCode}
        </if>
        <if test="request.region != null and request.region.size() > 0">
            AND ct.area_code IN
            <foreach collection="request.region" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="request.country != null and request.country.size() > 0">
            AND ct.country_code IN
            <foreach collection="request.country" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="request.positionType != null and request.positionType.size() > 0">
            AND p.type IN
            <foreach collection="request.positionType" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="request.positionCategory != null and request.positionCategory.size() > 0">
            AND (
            <foreach collection="request.positionCategory" item="item" separator=" OR ">
                JSON_CONTAINS(p.position_category, #{item})
            </foreach>
            )
        </if>
        <if test="request.positionConstructionType != null and request.positionConstructionType.size() > 0">
            AND ir.business_type IN
            <foreach collection="request.positionConstructionType" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="request.taskStatus != null and request.taskStatus.size() > 0">
            AND ir.task_status IN
            <foreach collection="request.taskStatus" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="request.inspenctionStatus != null and request.inspenctionStatus.size() > 0">
            AND ir.inspection_status IN
            <foreach collection="request.inspenctionStatus" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="request.owner != null and request.owner != ''">
            AND ir.inspection_owner LIKE CONCAT(#{request.owner}, '%')
        </if>
        <if test="request.positionLocation != null and request.positionLocation.size() > 0">
            AND p.position_location IN
            <foreach collection="request.positionLocation" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="request.displayStandardization != null and request.displayStandardization.size() > 0">
            AND p.display_capacity_expansion_status IN
            <foreach collection="request.displayStandardization" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="request.storeLimitedRange != null and request.storeLimitedRange != ''">
            AND ir.store_limited_range = #{request.storeLimitedRange}
        </if>
        <if test="request.createTimeStart != null">
            AND ir.business_creation_time &gt;= #{request.createTimeStart}
        </if>
        <if test="request.createTimeEnd != null">
            AND ir.business_creation_time &lt;= #{request.createTimeEnd}
        </if>
        <if test="request.province != null and request.province.size() > 0">
            AND p.province_code IN
            <foreach collection="request.province" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="request.city != null and request.city.size() > 0">
            AND p.city_code IN
            <foreach collection="request.city" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="request.organization != null and request.organization.size() > 0">
            AND p.organization_code IN
            <foreach collection="request.organization" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </where>
</select>


    <resultMap id="findActiveTasksWithActiveRulesMap" type="com.mi.info.intl.retail.intlretail.service.api.position.dto.UnRemindedTaskDTO">
        <result property="id" column="id"/>
        <result property="businessCode" column="business_code"/>
        <result property="businessType" column="business_type"/>
        <result property="inspectionOwnerMiId" column="inspection_owner_miId"/>
        <result property="inspectionStatus" column="inspection_status"/>
        <result property="taskStatus" column="task_status"/>
        <result property="ruleCode" column="rule_code"/>
        <result property="ruleName" column="rule_name"/>
        <result property="taskBatchId" column="task_batch_id"/>
        <result property="taskDefId" column="task_def_id"/>
        <result property="reminderDays" column="reminder_days"/>
    </resultMap>

    <!-- 查询未完成任务状态且规则状态为激活的巡检记录, deadline_stamp 阵地巡检是0L -->
    <select id="findActiveTasksWithActiveRules" resultMap="findActiveTasksWithActiveRulesMap">
        SELECT
        ir.id,
        ir.business_code,
        ir.business_type,
        ir.inspection_owner_miId,
        ir.inspection_status,
        ir.task_status,
        rc.rule_code,
        rc.rule_name,
        rc.task_batch_id,
        rc.task_def_id,
        rc.country,
        rc.reminder_days
        FROM intl_inspection_record ir
        JOIN intl_inspection_rule rc ON ir.rule_code = rc.rule_code
        WHERE ir.task_status = 3
        AND ir.reminder_time BETWEEN
        UNIX_TIMESTAMP(NOW()) * 1000
        AND UNIX_TIMESTAMP(NOW() + INTERVAL 1 HOUR) * 1000
        AND rc.rule_status = 1
        AND (ir.deadline_stamp = 0  or ir.deadline_stamp > UNIX_TIMESTAMP(NOW()) * 1000)
    </select>

    <!-- 通过国家列表查询未下发的巡检记录 -->
    <select id="selectPendingInspectionsByCountries" resultMap="InspectionWithRuleResultMap">

        SELECT
            ir.rule_code as rule_code,
            ir.business_code as business_code,
            ir.business_type as business_type,

            ir.business_creation_time as business_creation_time,
            ir.construction_action_code as construction_action_code,
            ir.task_status as task_status,
            ir.task_completion_time as task_completion_time,
            ir.inspection_status as inspection_status,
            ir.verification_time as verification_time,
            ir.inspection_owner as inspection_owner,
            ir.inspection_owner_miId as inspection_owner_miId,
            ir.verify_status as verify_status,
            ir.task_create_instance_time as task_create_instance_time,
            ir.reminder_time as reminder_time,
            ir.id as id,
            rc.id as rc_id,
            rc.country as rc_country,
            rc.task_def_id as rc_task_def_id,
            rc.task_batch_id as rc_task_batch_id,
            rc.rule_code as rc_rule_code,
            rc.rule_name as rc_rule_name,
            rc.rule_status as rc_rule_status,
            rc.start_time as rc_start_time,
            rc.end_time as rc_end_time,
            rc.allow_photo_from_gallery as rc_allow_photo_from_gallery,
            rc.creator as rc_creator,
            rc.creation_time as rc_creation_time,
            rc.modifier as rc_modifier,
            rc.modification_time as rc_modification_time,
            rc.reminder_days as rc_reminder_days
        FROM
            intl_inspection_record ir
        INNER JOIN
            intl_inspection_rule rc ON ir.rule_code = rc.rule_code
        WHERE
            (ir.inspection_status = 0 or  ir.inspection_status = 4)
        and ir.business_code is not null
            AND rc.rule_status = 1  <!-- 规则状态为进行中 -->
            <if test="countries != null and countries.size() > 0">
                AND rc.country IN
                <foreach collection="countries" item="country" open="(" separator="," close=")">
                    #{country}
                </foreach>
            </if>
            <if test="businessTypes != null and businessTypes.size() > 0">
                AND ir.business_type IN
                <foreach collection="businessTypes" item="businessType" open="(" separator="," close=")">
                    #{businessType}
                </foreach>
            </if>
        ORDER BY
            ir.created_time DESC
    </select>
    <select id="pageMaterialInspectionRecordItem"
            resultType="com.mi.info.intl.retail.intlretail.service.api.material.dto.InspectionRecordPageItem">
        select a.id,
            a.created_time,
            a.upload_data,
            a.inspection_status as storeStatus,
            a.task_status,
            a.verifier,
            a.store_limited_range,
            a.report_distance,
            a.verifier_miid as verifierMiId,
            a.inspection_owner,
            a.inspection_owner_miId,

            b.country       as countryCode,
            b.region        as regionCode,
            b.project       as projectCode,
            b.task_type,
            b.task_name,

            c.code          as positionCode,
            c.name          as positionName,
            c.channel_type_name  as channel,
            c.level_name    as storeGrade,
            c.store_name,

            e.code as storeCode,

            g.project as onSaleProjectCode,

            h.covered

        from intl_inspection_record a
        join intl_inspection_rule b on a.rule_config_id = b.id
        join intl_rms_position c on a.business_id = c.id
        left join intl_rms_store e on e.store_id = c.store_id
        left join intl_store_sales_model g on a.business_code = g.business_code
        left join intl_store_material_status h on a.business_code = h.business_code and a.business_type = h.task_type
        <where>
            a.enable = 1
            <if test="r.positionCode != null and r.positionCode != ''">
                and a.business_code like concat(#{r.positionCode}, '%')
            </if>
            <if test="r.verifier != null and r.verifier != ''">
                and a.verifier like concat(#{r.verifier}, '%')
            </if>
            <if test="r.inspectionStatus != null and r.inspectionStatus.size() > 0">
                and a.inspection_status in
                <foreach collection="r.inspectionStatus" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="r.createTimeStart != null">
                and a.created_time >= #{r.createTimeStart}
            </if>
            <if test="r.createTimeEnd != null">
                and a.created_time &lt;= #{r.createTimeEnd}
            </if>
            <if test="r.storeLimitedRange != null and r.storeLimitedRange.size()>0">
                and a.store_limited_range in
                <foreach collection="r.storeLimitedRange" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="r.createUserName != null and r.createUserName != ''">
                and a.inspection_owner like concat(#{r.createUserName}, '%')
            </if>
            <if test="r.takePhoto != null">
                <choose>
                    <when test="r.takePhoto == 1">and a.upload_data is not null</when>
                    <otherwise>and a.upload_data is null</otherwise>
                </choose>
            </if>
            <if test="r.projectCode != null and r.projectCode != ''">
                AND JSON_CONTAINS(b.project, JSON_QUOTE(#{r.projectCode}), '$')
            </if>
            <if test="r.country != null and r.country.size()>0">
                and b.country in
                <foreach collection="r.country" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="r.taskType != null and r.taskType.size() > 0">
                and b.task_type in
                <foreach collection="r.taskType" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="r.channel != null and r.channel.size()>0">
                and c.channel_type_name in
                <foreach collection="r.channel" open="(" close=")" separator="," item="item">
                        #{item}
                </foreach>
            </if>
            <if test="r.storeGrade != null and r.storeGrade.size()>0">
                and c.level_name in
                <foreach collection="r.storeGrade" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="r.positionName != null and r.positionName != ''">
                and c.name like concat(#{r.positionName}, '%')
            </if>
            <if test="r.storeName != null and r.storeName != ''">
                and c.store_name like concat(#{r.storeName}, '%')
            </if>
            <if test="r.storeCode != null and r.storeCode != ''">
                and e.code like concat(#{r.storeCode}, '%')
            </if>
            <if test="r.materialCovered != null and r.materialCovered.size()>0">
                and h.covered in
                <foreach collection="r.materialCovered" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="r.businessType != null and r.businessType.size()>0">
                and a.business_type in
                <foreach collection="r.businessType" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
        </where>
        order by a.id desc
    </select>

    <select id="pageMaterialInspection" resultType="com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialInspectionItem">
        SELECT
            ir.id as materialInspectionId,
            ir.task_instance_id AS taskInstanceId,
            p.name AS positionName,
            p.store_name AS storeName,
            ir.task_status AS taskStatus,
            ir.inspection_status AS inspectionStatus,
            rc.allow_photo_from_gallery AS allowPhotoFromGallery,
            rc.task_name as taskName,
            ir.business_code AS positionCode,
            ir.business_type as businessType,
            rc.task_type AS taskType,
            ir.deadline_stamp AS deadline,
            rc.region as region,
            rc.country AS country,
            ms.covered AS covered
        FROM
            intl_inspection_record ir
        LEFT JOIN
            intl_rms_position p ON p.code = ir.business_code
        LEFT JOIN
           intl_inspection_rule rc ON ir.rule_code = rc.rule_code
        LEFT JOIN
           intl_store_material_status ms ON ms.business_code = ir.business_code and ir.business_type = ms.task_type
        WHERE
         ir.inspection_owner = #{request.owner}
        and ir.enable = 1
        <if test="request.positionName != null and request.positionName != ''">
            AND p.name LIKE CONCAT(#{request.positionName}, '%')
        </if>
        <if test="request.positionCode != null and request.positionCode != ''">
            AND ir.business_code = #{request.positionCode}
        </if>
        <if test="request.inspectionStatus != null and request.inspectionStatus.size() > 0">
            AND ir.inspection_status IN
            <foreach collection="request.inspectionStatus" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="request.businessTypes != null and request.businessTypes.size() > 0">
            AND ir.business_type IN
            <foreach collection="request.businessTypes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="request.localTimestamp != null">
          AND ir.deadline_stamp <![CDATA[>=]]> #{request.localTimestamp}
        </if>
        ORDER BY ir.id DESC
    </select>

    <select id="getNoCompletedList" resultType="com.mi.info.intl.retail.intlretail.service.api.material.dto.TaskRemindDTO">
        select
            ir.task_instance_id as taskInstanceId,
            ir.inspection_owner_miId as miId,
            rc.country AS country,
            ir.period_start_time_stamp as periodStartTimeStamp,
            ir.period_end_time_stamp as periodEndTimeStamp,
            ir.deadline_stamp as deadline,
            ir.reminder_time as reminderTime,
            rc.cycle_type AS cycleType
        FROM
        intl_inspection_record ir
        LEFT JOIN
        intl_inspection_rule rc ON ir.rule_code = rc.rule_code
        where ir.task_status = 3 and ir.expire = 0 and ir.enable = 1
        <if test="miId != null">
            and inspection_owner = #{miId}
        </if>
    </select>

    <select id="getTaskTypeSummary" resultMap="CoverageStatisticsMap">
        SELECT
        rule.task_type,
        target.country,
        rule.region,
        target.product_line,
        target.project,
        target.target_type,
        rule.id AS ruleId,
        target.id AS target_id,
        target.posm_store_coverage,
        target.dummy_store_coverage,
        target.price_tag_coverage_target,
        target.ldu_store_coverage,
        irp.id AS irp_id,
        irp.position_id AS rms_position_id,
        irp.code AS rms_code,
        irp.name AS rms_name,
        irp.store_id AS rms_store_id,
        irp.store_name AS rms_store_name,
        irp.abbreviation AS rms_abbreviation,
        irp.state AS rms_state,
        irp.state_name AS rms_state_name,
        irp.distributor_id AS rms_distributor_id,
        irp.distributor_name AS rms_distributor_name,
        irp.account_id AS rms_account_id,
        irp.account_name AS rms_account_name,
        irp.retailer_id AS rms_retailer_id,
        irp.retailer_name AS rms_retailer_name,
        irp.channel_type AS rms_channel_type,
        irp.channel_type_name AS rms_channel_type_name,
        irp.type AS rms_type,
        irp.type_name AS rms_type_name,
        irp.level AS rms_level,
        irp.level_name AS rms_level_name,
        irp.is_promotion_store AS rms_is_promotion_store,
        irp.country_id AS rms_country_id,
        irp.country_name AS rms_country_name,
        irp.city_id AS rms_city_id,
        irp.city_name AS rms_city_name,
        irp.address AS rms_address,
        irp.modified_on AS rms_modified_on,
        irp.created_on AS rms_created_on,
        irp.owner_id AS rms_owner_id,
        irp.owner_name AS rms_owner_name,
        irp.state_code AS rms_state_code,
        irp.area AS rms_area,
        irp.area_code AS rms_area_code,
        irp.created_at AS rms_created_at,
        irp.updated_at AS rms_updated_at,
        irp.position_category AS rms_position_category,
        irp.furniture_ttl AS rms_furniture_ttl,
        irp.display_capacity_expansion_status AS rms_display_capacity_expansion_status,
        irp.position_location AS rms_position_location,
        irp.position_longitude AS rms_position_longitude,
        irp.position_latitude AS rms_position_latitude,
        record.id AS recordId,
        record.inspection_status AS verifyStatus,
        record.flag_completion AS flagCompletion
        FROM
        intl_inspection_rule rule
        JOIN intl_inspection_rule_relation rel ON
        rule.id = rel.rule_id
        JOIN
        intl_new_product_target target ON
        rel.country = target.country AND  rel.project = target.project
        LEFT JOIN
        intl_inspection_record record ON
        rule.id = record.rule_config_id
        INNER JOIN intl_rms_position irp
        ON
        record.business_code = irp.code
        WHERE
        record.created_time &gt;= #{query.taskStartTime} AND record.created_time &lt;= #{query.taskEndTime}
        <if test="query.taskType != null and query.taskType != ''">
            AND rule.task_type = #{query.taskType}
        </if>
        <if test="query.countryCode != null and !query.countryCode.isEmpty()">
            AND target.country IN
            <foreach item="item" collection="query.countryCode" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.targetType!= null and query.targetType != ''">
            AND target.target_type = #{query.targetType}
        </if>
        <if test="query.region != null and query.region != ''">
            AND rule.region = #{query.region}
        </if>
        <if test="query.productLine != null and query.productLine != ''">
            AND target.product_line = #{query.productLine}
        </if>
        <if test="query.projectCode != null and query.projectCode != ''">
            AND target.project = #{query.projectCode}
        </if>
    </select>

    <!-- 查询规则及其关联的巡检记录 -->
    <select id="getInspectionStatsByCountry" resultType="com.mi.info.intl.retail.intlretail.service.api.ldu.dto.CoverageStatisticsDto">
        SELECT
        rule.task_type,
        target.product_line as productLine,
        target.product_line_name as productLineName,
        target.country,
        target.project as projectCode,
        target.target_type,
        rule.region,
        rule.region,
        rule.id as ruleId,
        target.id AS targetId,
        target.category AS product_category,
        target.category_name,
        target.posm_store_coverage,
        target.dummy_store_coverage,
        target.price_tag_coverage_target,
        target.ldu_store_coverage,
        record.business_code,
        record.inspection_status,
        record.verify_status,
        record.task_status
        FROM
        intl_inspection_rule rule
        JOIN intl_inspection_rule_relation rel ON
        rule.id = rel.rule_id
        JOIN
        intl_new_product_target target ON
        rel.country = target.country AND rel.project = target.project
        LEFT JOIN
        intl_inspection_record record  ON rule.id = record.rule_config_id
        WHERE
        record.created_time &gt;= #{query.taskStartTime}
        AND record.created_time &lt;= #{query.taskEndTime}
        <if test="query.taskType != null and query.taskType != ''">
            AND rule.task_type = #{query.taskType}
        </if>
        <if test="query.targetType!= null and query.targetType != ''">
            AND target.target_type = #{query.targetType}
        </if>
        <if test="query.countryCode != null and !query.countryCode.isEmpty()">
            AND target.country IN
            <foreach item="item" collection="query.countryCode" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.region != null and query.region != ''">
            AND rule.region = #{query.region}
        </if>
        <if test="query.productLine != null and query.productLine != ''">
            AND target.product_line = #{query.productLine}
        </if>
        <if test="query.projectCode != null and query.projectCode != ''">
            AND target.project = #{query.projectCode}
        </if>
        GROUP BY
        rule.task_type,
        target.product_line,
        target.country,
        target.project ,
        target.target_type
        <if test="query.pageSize != null and query.offset != null">
            LIMIT #{query.pageSize} OFFSET #{query.offset}
        </if>
    </select>

    <select id="getInspectionStatsByCountryCount" resultType="java.lang.Long">
        SELECT COUNT(1) FROM (
        SELECT
        rule.task_type,
        target.product_line,
        target.country,
        target.project as projectCode,
        target.target_type
        FROM
        intl_inspection_rule rule
        JOIN intl_inspection_rule_relation rel ON
        rule.id = rel.rule_id
        JOIN
        intl_new_product_target target ON
        rel.country = target.country AND rel.project = target.project
        LEFT JOIN
        intl_inspection_record record  ON rule.id = record.rule_config_id
        WHERE
        record.created_time &gt;= #{query.taskStartTime}
        AND record.created_time &lt;= #{query.taskEndTime}
        <if test="query.taskType != null and query.taskType != ''">
            AND rule.task_type = #{query.taskType}
        </if>
        <if test="query.countryCode != null and !query.countryCode.isEmpty()">
            AND target.country IN
            <foreach item="item" collection="query.countryCode" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.targetType!= null and query.targetType != ''">
            AND target.target_type = #{query.targetType}
        </if>
        <if test="query.region != null and query.region != ''">
            AND rule.region = #{query.region}
        </if>
        <if test="query.productLine != null and query.productLine != ''">
            AND target.product_line = #{query.productLine}
        </if>
        <if test="query.projectCode != null and query.projectCode != ''">
            AND target.project = #{query.projectCode}
        </if>
        GROUP BY
        rule.task_type,
        target.product_line,
        target.country,
        target.project,
        target.target_type
        ) result
    </select>

    <select id="getInspectionByRuleIdList" resultType="com.mi.info.intl.retail.intlretail.service.api.ldu.dto.IntlRmsPositionDto">
        SELECT
        rule.task_type AS taskType,
        target.country AS country,
        target.product_line AS productLine,
        target.project AS project,
        target.target_type AS targetType,
        rule.id AS ruleId,
        irp.id AS irpId,
        irp.position_id AS positionId,
        irp.code AS code,
        irp.name AS name,
        irp.store_id AS storeId,
        irp.store_name AS storeName,
        irp.abbreviation AS abbreviation,
        irp.state AS state,
        irp.state_name AS stateName,
        irp.distributor_id AS distributorId,
        irp.distributor_name AS distributorName,
        irp.account_id AS accountId,
        irp.account_name AS accountName,
        irp.retailer_id AS retailerId,
        irp.retailer_name AS retailerName,
        irp.channel_type AS channelType,
        irp.channel_type_name AS channelTypeName,
        irp.type AS type,
        irp.type_name AS typeName,
        irp.level AS level,
        irp.level_name AS levelName,
        irp.is_promotion_store AS isPromotionStore,
        irp.country_id AS countryId,
        irp.country_name AS countryName,
        irp.city_id AS cityId,
        irp.city_name AS cityName,
        irp.address AS address,
        irp.modified_on AS modifiedOn,
        irp.created_on AS createdOn,
        irp.owner_id AS ownerId,
        irp.owner_name AS ownerName,
        irp.state_code AS stateCode,
        irp.area AS area,
        irp.area_code AS areaCode,
        irp.created_at AS createdAt,
        irp.updated_at AS updatedAt,
        irp.position_category AS positionCategory,
        irp.furniture_ttl AS furnitureTtl,
        irp.display_capacity_expansion_status AS displayCapacityExpansionStatus,
        irp.position_location AS positionLocation,
        irp.position_longitude AS positionLongitude,
        irp.position_latitude AS positionLatitude,
        record.inspection_status AS verifyStatus,
        record.flag_completion AS flagCompletion
        FROM
        intl_inspection_rule rule
        JOIN intl_inspection_rule_relation rel ON
        rule.id = rel.rule_id
        JOIN
        intl_new_product_target target ON
        rel.country = target.country AND rel.project = target.project
        LEFT JOIN
        intl_inspection_record record  ON rule.id = record.rule_config_id
        INNER JOIN
        intl_rms_position irp ON record.business_code = irp.code
        WHERE
        rule_config_id IN
        <foreach collection="ruleConfigIds" item="item" open="(" separator="," close=")">
            #{item.ruleId}
        </foreach>
        <if test="query.taskType != null and query.taskType != ''">
            AND rule.task_type = #{query.taskType}
        </if>
        <if test="query.countryCode != null and !query.countryCode.isEmpty()">
            AND target.country IN
            <foreach item="item" collection="query.countryCode" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.targetType!= null and query.targetType != ''">
            AND target.target_type = #{query.targetType}
        </if>
        <if test="query.region != null and query.region != ''">
            AND rule.region = #{query.region}
        </if>
        <if test="query.productLine != null and query.productLine != ''">
            AND target.product_line = #{query.productLine}
        </if>
        <if test="query.projectCode != null and query.projectCode != ''">
            AND target.project = #{query.projectCode}
        </if>
        <if test="query.taskStartTime != null and query.taskStartTime != ''">
            AND record.created_time &gt;= #{query.taskStartTime}
        </if>
        <if test="query.taskEndTime != null and query.taskEndTime != ''">
            AND record.created_time &lt;= #{query.taskEndTime}
        </if>
    </select>

    <select id="getStoreModelList" resultType="java.lang.String">
        select b.project
        from intl_inspection_record a
                 inner join intl_inspection_rule b
                            on a.rule_config_id = b.id
        where a.business_code = #{businessCode}
        and  a.enable = 1
        and  b.rule_status = 1
    </select>
</mapper>