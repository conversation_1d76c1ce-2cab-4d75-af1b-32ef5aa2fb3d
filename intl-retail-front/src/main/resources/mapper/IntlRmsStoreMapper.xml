<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.info.intl.retail.org.infra.mapper.IntlRmsStoreMapper">

    <resultMap id="BaseResultMap" type="com.mi.info.intl.retail.org.infra.entity.IntlRmsStore">
        <result property="id" column="id"/>
        <result property="storeId" column="store_id"/>
        <result property="name" column="name"/>
        <result property="type" column="type"/>
        <result property="typeName" column="type_name"/>
        <result property="code" column="code"/>
        <result property="crssCode" column="crss_code"/>
        <result property="retailerName" column="retailer_name"/>
        <result property="retailerId" column="retailer_id"/>
        <result property="retailerIdName" column="retailer_id_name"/>
        <result property="countryId" column="country_id"/>
        <result property="countryIdName" column="country_id_name"/>
        <result property="accountId" column="account_id"/>
        <result property="accountIdName" column="account_id_name"/>
        <result property="distributorId" column="distributor_id"/>
        <result property="distributorIdName" column="distributor_id_name"/>
        <result property="cityId" column="city_id"/>
        <result property="cityCode" column="city_code"/>
        <result property="cityIdName" column="city_id_name"/>
        <result property="address" column="address"/>
        <result property="channelType" column="channel_type"/>
        <result property="channelTypeName" column="channel_type_name"/>
        <result property="grade" column="grade"/>
        <result property="gradeName" column="grade_name"/>
        <result property="operationStatus" column="operation_status"/>
        <result property="operationStatusName" column="operation_status_name"/>
        <result property="hasSr" column="has_sr"/>
        <result property="hasPc" column="has_pc"/>
        <result property="ownerId" column="owner_id"/>
        <result property="ownerIdName" column="owner_id_name"/>
        <result property="createdOn" column="created_on" jdbcType="TIMESTAMP"/>
        <result property="modifiedOn" column="modified_on" jdbcType="TIMESTAMP"/>
        <result property="stateCode" column="state_code"/>
        <result property="createdAt" column="created_at"/>
        <result property="updatedAt" column="updated_at"/>
        <result property="provinceCode" column="province_code"/>
        <result property="provinceLabel" column="province_label"/>
        <result property="countyCode" column="county_code"/>
        <result property="countyLabel" column="county_label"/>
        <result property="countryShortcode" column="country_shortcode"/>
        <result property="gradeCalFlag" column="grade_cal_flag"/>
        <result property="districtOrgCode" column="district_org_code"/>
        <result property="divisionOrgCode" column="division_org_code"/>
        <result property="areaOrgCode" column="area_org_code"/>
        <result property="regionOrgCode" column="region_org_code"/>
        <result property="countryOrgCode" column="country_org_code"/>
        <result property="storeClass" column="store_class"/>

    </resultMap>

    <sql id="Base_Column_List">
        id, store_id, name, type, type_name, code, crss_code, retailer_name, retailer_id, retailer_id_name,
        country_id, country_id_name, account_id, account_id_name, distributor_id, distributor_id_name, city_id,
        city_code, city_id_name, address, channel_type, channel_type_name, grade, grade_name, operation_status,
        operation_status_name, has_sr, has_pc, owner_id, owner_id_name, created_on, modified_on, stateCode, created_at,
        updated_at, province_code, province_label, county_code, county_label, country_shortcode, grade_cal_flag,
        district_org_code, division_org_code, area_org_code, region_org_code, country_org_code, store_class
    </sql>

    <select id="getStoreInfo" resultType="com.mi.info.intl.retail.ldu.dto.StoreInfoDTO">
        SELECT s.retailer_id_name, s.retailer_name, s.channel_type_name, s.name as storeName, s.province_label as
        provinceName, s.city_id_name, s.county_label as countyName,s.code
        FROM intl_rms_store s
        WHERE s.store_id = #{storeId};
    </select>


    <select id="getStoreInfoByStoreIdList" resultType="com.mi.info.intl.retail.api.front.position.dto.IntlPositionDTO">
        SELECT s.store_id,
        s.`code` AS storeCodeRMS,
        s.crss_code AS storeCodeNew,
        l.NAME AS retailerCode
        FROM intl_rms_store s
        LEFT JOIN intl_rms_retailer l ON s.retailer_id = l.retailer_id
        WHERE s.store_id in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>



    <select id="getStoreByPositionCode" resultType="com.mi.info.intl.retail.ldu.dto.StoreInfoDTO">
        SELECT s.retailer_id_name, s.retailer_name, s.channel_type_name, s.name as storeName,
        s.province_label as provinceName, s.city_id_name, s.county_label as countyName,s.crss_code as code,
        p.channel_type_name as channelTypeName,p.name as positionName, p.code as positionCode,p.country_id as countryId
        FROM
        intl_rms_position p
        JOIN
        intl_rms_store s ON p.store_id = s.store_id
        WHERE
        p.code = #{positionCode}
    </select>

    <!-- 根据门店编号更新门店等级 -->
    <update id="updateGradeByStoreId">
        UPDATE intl_rms_store
        SET grade = #{grade},
        grade_name = #{gradeValue},
        grade_cal_flag = #{flag}
        WHERE crss_code = #{storeId}
    </update>


    <select id="pageSelectByCountryCodeAndChannelType" resultMap="BaseResultMap">
        SELECT DISTINCT irs.crss_code
        FROM intl_rms_store irs
        INNER JOIN intl_rms_retailer irr ON irs.retailer_id_name = irr.name
        INNER JOIN intl_rms_country_timezone ict ON irs.country_id = ict.country_id
        WHERE ict.country_code = #{countryCode}
        AND irr.retailer_channel_type = #{channelType}
        AND irs.crss_code IS NOT NULL
        AND TRIM(irs.crss_code) != ''
        AND irs.operation_status_name != 'Closed'
        ORDER BY irs.crss_code
        LIMIT #{offset}, #{limit}
    </select>

    <select id="getStoreByStoreCode" resultType="com.mi.info.intl.retail.ldu.dto.StoreGradeInfoDTO">
        SELECT s.crss_code as crssCode, s.grade_name as gradeName
        FROM intl_rms_store s
        WHERE s.crss_code in
        <if test="list != null and !list.isEmpty()">
            <foreach item="item" collection="list" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>
