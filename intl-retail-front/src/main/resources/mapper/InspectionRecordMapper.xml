<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.info.intl.retail.org.infra.mapper.InspectionRecordMapper">

    <resultMap id="BaseResultMap" type="com.mi.info.intl.retail.org.infra.entity.InspectionRecord">
        <id property="id" column="id"/>
        <result property="ruleCode" column="rule_code"/>
        <result property="businessCode" column="business_code"/>
        <result property="businessType" column="business_type"/>
        <result property="businessCreationTime" column="business_creation_time"/>
        <result property="constructionActionCode" column="construction_action_code"/>
        <result property="taskStatus" column="task_status"/>
        <result property="taskCompletionTime" column="task_completion_time"/>
        <result property="inspectionStatus" column="inspection_status"/>
        <result property="verificationTime" column="verification_time"/>
        <result property="uploadData" column="upload_data"/>
        <result property="inspectionExtension" column="inspection_extension"/>
        <result property="inspectionOwner" column="inspection_owner"/>
        <result property="inspectionOwnerMiId" column="inspection_owner_miId"/>
        <result property="verifyStatus" column="verify_status"/>
        <result property="remark" column="remark"/>
        <result property="disapproveReason" column="disapprove_reason"/>
        <result property="createdBy" column="created_by"/>
        <result property="createdTime" column="created_time"/>
        <result property="updatedBy" column="updated_by"/>
        <result property="updatedTime" column="updated_time"/>
        <result property="taskCreateInstanceTime" column="task_create_instance_time"/>
        <result property="reminderTime" column="reminder_time"/>
        <result property="verifier" column="verifier"/>
        <result property="verifierMiid" column="verifier_miid"/>
        <result property="storeLimitedRange" column="store_limited_range"/>
        <result property="ruleConfigId" column="rule_config_id"/>
        <result property="taskInstanceId" column="task_instance_id"/>
        <result property="periodStartTimeStamp" column="period_start_time_stamp"/>
        <result property="periodEndTimeStamp" column="period_end_time_stamp"/>
        <result property="expire" column="expire"/>
        <result property="enable" column="enable"/>
        <result property="series" column="series"/>
        <result property="positionType" column="position_type"/>
        <result property="flagCompletion" column="flag_completion"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,rule_code,business_code,business_type,business_creation_time,
        construction_action_code,task_status,task_completion_time,inspection_status,verification_time,
        upload_data,inspection_owner,inspection_owner_miId,verify_status,
        remark,disapprove_reason,created_by,created_time,updated_by,updated_time
        ,verifier,verifier_miid,store_limited_range,rule_config_id,task_instance_id,period_start_time_stamp,
        period_end_time_stamp,expire,enable,series,position_type ,flag_completion,inspection_extension
    </sql>

    <update id="updateVerifyStatus">
        update intl_inspection_record
        set verification_time = #{e.verificationTime},
            verifier_miid     = #{e.verifierMiid},
            verifier          = #{e.verifier},
            disapprove_reason = #{e.disapproveReason},
            updated_by        = #{e.updatedBy},
            updated_time      = #{e.updatedTime},
            task_status       = #{e.taskStatus},
            inspection_status = #{e.inspectionStatus},
            task_completion_time = #{e.taskCompletionTime}
        where id = #{e.id}
          and inspection_status = #{preInspectionStatus}
    </update>
    <update id="updateNotFinished">
        UPDATE intl_inspection_record
        SET
            task_status       = #{e.taskStatus},
            inspection_status = #{e.inspectionStatus} ,
            flag_completion = #{e.flagCompletion},
            task_completion_time = 0
        WHERE id = #{e.id}
    </update>
    
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO intl_inspection_record (
            rule_code, business_code, business_type, business_creation_time,
            task_status,  inspection_status,
            upload_data, inspection_extension, inspection_owner, inspection_owner_miId,
            created_by, created_time,reminder_time,
            updated_by, updated_time, task_create_instance_time,
            rule_config_id,
            task_instance_id, period_start_time_stamp, period_end_time_stamp,
            deadline_stamp, expire, enable, position_type,
            task_batch_id,business_id
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.ruleCode}, #{item.businessCode}, #{item.businessType}, #{item.businessCreationTime},
                #{item.taskStatus}, #{item.inspectionStatus},
                #{item.uploadData}, #{item.inspectionExtension}, #{item.inspectionOwner}, #{item.inspectionOwnerMiId},
                #{item.createdBy}, #{item.createdTime}, #{item.reminderTime},
                #{item.updatedBy}, #{item.updatedTime}, #{item.taskCreateInstanceTime},
                #{item.ruleConfigId},
                #{item.taskInstanceId}, #{item.periodStartTimeStamp}, #{item.periodEndTimeStamp},
                #{item.deadlineStamp}, #{item.expire}, #{item.enable}, #{item.positionType},
                #{item.taskBatchId},#{item.businessId}
            )
        </foreach>
    </insert>
</mapper>