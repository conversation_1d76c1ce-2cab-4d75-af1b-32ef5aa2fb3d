<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.info.intl.retail.org.infra.mapper.RmsRetailerMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.mi.info.intl.retail.retailer.entity.IntlRmsRetailer">
        <id column="id" property="id"/>
        <result column="retailer_id" property="retailerId"/>
        <result column="name" property="name"/>
        <result column="crm_code" property="crmCode"/>
        <result column="retailer_name" property="retailerName"/>
        <result column="from_crm" property="fromCrm"/>
        <result column="retailer_for_short" property="retailerForShort"/>
        <result column="key_retailer" property="keyRetailer"/>
        <result column="retailer_grade" property="retailerGrade"/>
        <result column="country_id" property="countryId"/>
        <result column="country_name" property="countryName"/>
        <result column="country_code" property="countryCode"/>
        <result column="province_code" property="provinceCode"/>
        <result column="province_name" property="provinceName"/>
        <result column="city_code" property="cityCode"/>
        <result column="city_name" property="cityName"/>
        <result column="county_code" property="countyCode"/>
        <result column="county_name" property="countyName"/>
        <result column="address" property="address"/>
        <result column="retailer_channel_type_name" property="retailerChannelTypeName"/>
        <result column="retailer_channel_type" property="retailerChannelType"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="state_code" property="stateCode"/>
        <result column="country_shortcode" property="countryShortcode"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , retailer_id, name, crm_code, retailer_name, from_crm, retailer_for_short,
        key_retailer, retailer_grade, country_id, country_name, country_code,
        province_code, province_name, city_code, city_name, county_code, county_name,
        address, retailer_channel_type_name, retailer_channel_type, created_at, updated_at, state_code, country_shortcode
    </sql>

    <select id="queryRetailerList" resultType="com.mi.info.intl.retail.retailer.entity.IntlRmsRetailer">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        intl_rms_retailer
        <where>
            <if test="param.countryId != null and param.countryId != ''">
                AND country_id = #{param.countryId}
            </if>
            <if test="param.channelType != null">
                AND retailer_channel_type = #{param.channelType}
            </if>
            <if test="param.search != null and param.search != ''">
                AND (name LIKE CONCAT('%', #{param.search}, '%')
                OR retailer_name LIKE CONCAT('%', #{param.search}, '%'))
            </if>
            ORDER BY id DESC
            LIMIT 50
        </where>
    </select>
    <select id="queryRetailerListByCodes" resultType="com.mi.info.intl.retail.retailer.entity.IntlRmsRetailer">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        intl_rms_retailer WHERE `name` IN
        <foreach item="item" collection="codes" separator="," open="(" close=")" index="index">
            #{item}
        </foreach>
    </select>
</mapper>