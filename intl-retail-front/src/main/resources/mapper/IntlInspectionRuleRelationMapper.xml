<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.info.intl.retail.org.infra.mapper.IntlInspectionRuleRelationMapper">

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO intl_inspection_rule_relation
        (rule_id, country, project, create_time, update_time)
        VALUES
        <foreach collection="inspectionRuleRelations" item="item" separator=",">
            (
            #{item.ruleId},
            #{item.country},
            #{item.project},
            #{item.createTime},
            #{item.updateTime}
            )
        </foreach>
    </insert>


    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="inspectionRuleRelations" item="item" separator=";">
            UPDATE intl_inspection_rule_relation
            <set>
                <if test="item.country != null">country = #{item.country},</if>
                <if test="item.project != null">project = #{item.project},</if>
                <if test="item.updateTime != null">update_time = #{item.updateTime},</if>
            </set>
            WHERE id = #{item.id}
        </foreach>
    </update>

    <select id="selectByRuleId"  resultType="com.mi.info.intl.retail.org.infra.entity.IntlInspectionRuleRelation">
        SELECT id,
               rule_id     AS ruleId,
               country,
               project,
               create_time AS createTime,
               update_time AS updateTime
        FROM intl_inspection_rule_relation
        WHERE rule_id = #{id}
    </select>
</mapper>