<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.info.intl.retail.management.mapper.StoreGradeMapper">

    <!-- 查询零售商渠道类型和等级计算标志综合统计 -->
    <select id="selectRetailerChannelGradeCount" resultType="com.mi.info.intl.retail.model.RetailerChannelGradeCount">
        SELECT
        irr.retailer_channel_type as retailerChannelType,
        irs.grade_cal_flag as gradeCalFlag,
        COUNT(*) as count
        FROM intl_rms_store irs
        LEFT JOIN intl_rms_retailer irr ON irs.retailer_id = irr.retailer_id
        left join intl_rms_country_timezone ict on irs.country_id = ict.country_id
        <where>
        <if test="countryCode != null and countryCode != '' and countryCode != 'GLOBAL'">
            and ict.country_code = #{countryCode}
        </if>
        and irs.crss_code != '' and irs.operation_status = 100000000
        </where>
        GROUP BY irr.retailer_channel_type, irs.grade_cal_flag
    </select>

    <!-- 查询门店等级综合统计 -->
    <select id="selectStoreGradeCompleteCount" resultType="com.mi.info.intl.retail.model.StoreGradeCompleteCount">
        SELECT 
            irs.grade_name as grade,
            COUNT(*) as count
        FROM intl_rms_store irs
        LEFT JOIN intl_rms_retailer irr ON irs.retailer_id = irr.retailer_id
        left join intl_rms_country_timezone ict on irs.country_id = ict.country_id
        where ict.country_code = #{countryCode} and irs.crss_code != '' and irs.operation_status = 100000000
        and irs.grade_cal_flag = 1
        <if test="retailerChannelType != null">
        and irr.retailer_channel_type = #{retailerChannelType} 
        </if>
        <if test="retailerCode != null and retailerCode != ''">
        and irr.name = #{retailerCode}
        </if>
        GROUP BY irs.grade_name
    </select>

    <select id="getStoreRetailerByStoreCodes" resultType="com.mi.info.intl.retail.model.StoreRetailer">
        SELECT
        irs.`crss_code` as storeCode,
        irr.`name` as retailerCode,
        irr.retailer_name as retailerName,
        irr.retailer_channel_type as channelType
        FROM intl_rms_store irs
        LEFT JOIN intl_rms_retailer irr ON irs.retailer_id = irr.retailer_id
        WHERE irs.`crss_code` IN
        <foreach collection="storeCodes" item="storeCode" open="(" separator="," close=")">
            #{storeCode}
        </foreach>
    </select>


</mapper>